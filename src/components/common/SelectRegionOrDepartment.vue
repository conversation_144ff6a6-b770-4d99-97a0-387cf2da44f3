<script setup lang="ts">
import {onMounted, ref} from 'vue'
import {GetRegion, GetDepartmentSimple} from '@/api/OrganizeApi'
import {useUserStore} from '@/stores/useUserStore'
import {AuthorizationRoles, AuthorizationRolesMap} from '@/define/Authorization'

enum Level {
	City = 1,
	District = 2,
	Street = 3,
	Community = 4,
}

enum TransferType {
	Region = '选择区域',
	Department = '选择部门',
}

const emits = defineEmits(['update:modelValue', 'update:role', 'update:rightRaws'])
const props = defineProps({
	type: {
		type: String,
		default: TransferType.Region,
	},
	enableRoles: {
		type: Boolean,
		default: true,
	},
})

const userInfo = useUserStore().getUserInfo
const userDepartment = useUserStore().getCurrentDepartment

const transferTitle = ref(props.type)
const transferValue = ref([])
const transferRole = ref('')
const transferData: any = ref([])
const currentTransferData = ref([])
const transferPagination = ref({
	total: 0,
	size: 10,
	page: 1,
})

const level = ref(0)
const levelOptions = ref([])
const cascade = ref({})
const historyLastArray: any = ref([])

const cascadeProps = ref([])
const historyIds = ref([])

const onTransferChange = (val: any, direction: any, movedKeys: any, raws: any) => {
	console.log('Transfer change', val, direction, movedKeys, raws)
	currentTransferData.value = raws
	emits('update:modelValue', val)
	emits('update:role', transferRole.value)
	emits('update:rightRaws', raws)
}

const onLevelChange = (val: any) => {
	let temp: any = []

	const defautlConfig = {
		type: 'select',
		method: 'GET',
		cascadeUrl: `/api/platform/region/parent/${userDepartment.cityRegion.id}`,
		cascadeParams: {},
		beforeInitOptions: (val: any, next: any, item: any) => {
			next.cascadeUrl = `/api/platform/region/parent/${val}`
		},
		options: [],
		filterable: true,
	}

	cascade.value = {}
	transferData.value = []

	if (transferTitle.value === TransferType.Region) {
		if (val === Level.City) {
			// 目前固定市级
			transferData.value = [
				{
					key: userDepartment?.cityRegion.id,
					label: userDepartment?.cityRegion.name,
					disabled: false,
					raw: JSON.parse(JSON.stringify(userDepartment?.cityRegion)),
				},
			] as any
		}

		if (val === Level.District) {
			onCascadeChange({district: userDepartment?.cityRegion.id})
		}

		if (val === Level.Street) {
			temp = [
				{
					...defautlConfig,
					prop: 'district',
					placeholder: '请选择区县',
					multiple: true,
					collapseTags: true,
				},
			]
		}

		if (val === Level.Community) {
			temp = [
				{...defautlConfig, prop: 'district', placeholder: '请选择区县'},
				{
					...defautlConfig,
					prop: 'street',
					placeholder: '请选择街道',
					multiple: true,
					collapseTags: true,
				},
			]
		}
	} else if (transferTitle.value === TransferType.Department) {
		temp = [
			{
				...defautlConfig,
				prop: 'city',
				placeholder: '请选择城市',
				// 目前固定市级
				options: [
					{
						value: userDepartment.cityRegion.id,
						label: userDepartment.cityRegion.name,
						raw: JSON.parse(JSON.stringify(userDepartment.cityRegion)),
					},
				],
			},
			{...defautlConfig, prop: 'district', placeholder: '请选择区县'},
			{...defautlConfig, prop: 'street', placeholder: '请选择街道'},
			{
				...defautlConfig,
				prop: 'community',
				placeholder: '请选择社区',
				multiple: true,
				collapseTags: true,
			},
		]
	}

	cascadeProps.value = temp
}

const onCascadeChange = async (val: any) => {
	const last: any = Object.values(val).pop()
	let isMultiple = false

	if (transferTitle.value === TransferType.Region) {
		if (last && last[0]) {
			const promise = []
			if (typeof last === 'string') {
				promise.push(GetRegion(last))
			} else if (Array.isArray(last)) {
				isMultiple = true

				last.forEach((item) => {
					if (!historyLastArray.value.some((i: any) => i === item)) {
						promise.push(GetRegion(item))
					}
				})

				historyLastArray.value = last
			}

			const result = await Promise.all(promise)
			const temp: any = result.map((item: any) => item.data.items).flat()

			if (isMultiple) {
				const newData: any = temp.map((item: any) => ({
					key: item.id,
					label: item.name,
					disabled: false,
					raw: JSON.parse(JSON.stringify(item)),
				}))
				transferData.value = transferData.value
					.concat(newData)
					.filter((item: any) => last.join().includes(item.raw.parentId))
			} else {
				transferData.value = temp.map((item: any) => ({
					key: item.id,
					label: item.name,
					disabled: false,
					raw: JSON.parse(JSON.stringify(item)),
				}))
			}
		} else {
			transferData.value = []
			historyLastArray.value = []
		}
	} else if (transferTitle.value === TransferType.Department) {
		let regionIds: any = []

		if (val.city) {
			regionIds = [userDepartment.cityRegion.id]
		}

		if (val.district) {
			regionIds = [val.district]
		}

		if (val.street) {
			regionIds = [val.street]
		}

		if (val.community && val.community.length > 0) {
			regionIds = val.community
		}

		if (regionIds.length === 0) {
			transferData.value = []
			return
		}

		historyIds.value = regionIds
		transferPagination.value.page = 1
		transferPagination.value.size = 10
		GetDepartmentList(regionIds, true)
	}
}

const GetDepartmentList = (regionIds: any, isChange: boolean = false) => {
	GetDepartmentSimple({
		filter: '',
		skipCount: transferPagination.value.size * (transferPagination.value.page - 1),
		maxResultCount: transferPagination.value.size,
		regionIds,
	}).then((res) => {
		let temp: any = []
		res.data.items.forEach((item: any) => {
			temp.push({
				key: item.id,
				label: item.name,
				disabled: false,
				raw: JSON.parse(JSON.stringify(item)),
			})
		})
		// if (isChange) {
		// 	transferData.value = temp
		// } else {
		// 	const ids = transferData.value.map((item: any) => item.key)
		// 	temp = temp.filter((item: any) => !ids.includes(item.key))
		// 	transferData.value.push(...temp)
		// }
		transferData.value = temp
		transferPagination.value.total = res.data.totalCount
	})
}

const onPaginationChange = (val: any, type: any) => {
	if (type === 'size') {
		transferPagination.value.page = 1
		transferPagination.value.size = val
		GetDepartmentList(historyIds.value, true)
	} else {
		transferPagination.value.page = val
		GetDepartmentList(historyIds.value)
	}
}

const onFormItemChange = () => {
	emits('update:modelValue', transferValue.value)
	emits('update:role', transferRole.value)
}

const initOptions = () => {
	const levelTemp: any = []

	if (
		!userInfo?.city ||
		(userInfo.city && !userInfo.district && !userInfo.street && !userInfo.community)
	) {
		level.value = Level.City
		levelTemp.push({label: '市级', value: Level.City})
	}

	if (!userInfo?.district) {
		level.value = Level.District
		levelTemp.push({label: '区县', value: Level.District})
	}

	if (!userInfo?.street) {
		level.value = Level.Street
		levelTemp.push({label: '街道', value: Level.Street})
	}

	if (!userInfo?.community) {
		level.value = Level.Community
		levelTemp.push({label: '社区', value: Level.Community})
	}

	levelOptions.value = levelTemp
	onLevelChange(transferTitle.value === TransferType.Region ? level.value : Level.Community)
}

onMounted(() => {
	initOptions()
})
</script>
<template>
	<Transfer
		v-model="transferValue"
		:data="transferData"
		:enbable-collect="false"
		:query-placeholder="
			transferTitle === TransferType.Region
				? '输入关键字过滤下列区域名称'
				: '输入关键字过滤下列部门名称'
		"
		class="condition-transfer-component"
		@transfer-change="onTransferChange"
	>
		<template #collect-left>
			<template v-if="transferTitle === TransferType.Region">
				<span class="level-title">选择层级:</span>
				<el-select v-model="level" style="width: 140px" mr-10px @change="onLevelChange">
					<el-option
						v-for="item of levelOptions as any"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					/>
				</el-select>
			</template>
			<span
				v-if="level !== Level.District && level !== Level.City"
				class="level-title"
				ml-10px
			>
				选择区域:
			</span>

			<Cascade
				v-model="cascade"
				:key="level"
				:options="cascadeProps"
				@change="onCascadeChange"
				class="condition-cascade-component"
			></Cascade>
		</template>

		<template #left-footer>
			<!-- <div
				class="transfer-page"
				v-if="transferTitle === TransferType.Department && transferData.length > 0"
			>
				<Pagination
					size="small"
					layout="prev, pager, next"
					:total="transferPagination.total"
					:page-size="transferPagination.size"
					:current-page="transferPagination.page"
					justify-content="center"
					@size-change="onPaginationChange($event, 'size')"
					@current-change="onPaginationChange($event, 'page')"
				></Pagination>
			</div> -->
		</template>

		<template #footer>
			<div v-if="enableRoles" class="roles">
				<FormItem
					v-model="transferRole"
					:items="[
						{
							props: 'role',
							label: '选择角色:',
							type: 'select',
							labelWidth: 80,
							placeholder: '请选择角色',
							options: AuthorizationRolesMap.filter(
								(item) => item.value !== AuthorizationRoles.Head
							),
						},
					]"
					@change="onFormItemChange"
				></FormItem>
			</div>
		</template>
	</Transfer>
</template>
<style scoped lang="scss">
.condition-transfer-component {
	.level-title {
		margin-right: 10px;
		white-space: nowrap;
	}
	.transfer-page .pageination-component {
		padding-top: 8px;
		justify-content: center !important;
	}
}
.condition-cascade-component {
	margin-right: 0px;
	.el-form-item {
		margin-bottom: 0 !important;
	}
}
</style>

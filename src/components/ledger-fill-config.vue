<script setup lang="ts">
import {onMounted, ref, watch} from 'vue'
import {useMScroll} from '@/hooks/useMScroll'

interface Props {
	data: any
	fields: any
	statisticsFields: any
	searchPlaceholder?: string
	ledgerType?: number // 0 明细, 1 统计
}
const emits = defineEmits([
	'onChecked',
	'onDragStart',
	'onDeleteField',
	'onClickSetting',
	'onScrollBottom',
	'onSearch',
])

const props: Props = withDefaults(defineProps<Props>(), {
	searchPlaceholder: '请输入搜索内容',
	ledgerType: 0,
})

let timer = 0

watch(
	() => props.data,
	(newVal) => {
		ledgerList.value = newVal
		timer = +new Date()
	}
)

watch(
	() => [props.fields, props.statisticsFields],
	(newVal) => {
		ledgerFields.value = props.ledgerType === 0 ? newVal[0] : newVal[1]
	}
)

const ledgerList = ref<any[]>([])
const ledgerFields = ref<any[]>([])
const searchVal = ref('')

const onCheckLedger = (item: any) => {
	ledgerList.value.forEach((f) => {
		if (item === null || item === '') {
			f.__checked = false
		} else if (f.ledgerId !== item.ledgerId) {
			f.__checked = false
		} else if (!f.hasOwnProperty('__checked')) {
			f.__checked = true
		}
	})
	emits('onChecked', item, item?.__checked)
}

const onDragStart = (item: any) => {
	emits('onDragStart', item)
}

const onDeleteLinkedField = (field: any) => {
	emits('onDeleteField', field)
}

const onSetting = (ledger: any) => {
	emits('onClickSetting', ledger)
}

const onScroll = (val: any) => {
	const el = document.querySelector('.ledger-fill-config .el-scrollbar__wrap')
	if (useMScroll(el).isScrollBottom(100)) {
		emits('onScrollBottom')
	}
}
const onSearch = (val: string) => {
	emits('onSearch', val)
}
onMounted(() => {})

defineExpose({
	setChecked: (item: any) => onCheckLedger(item),
})
</script>
<template>
	<div class="ledger-fill-config">
		<div class="left">
			<div class="search">
				<el-input
					v-model="searchVal"
					@keyup.enter="onSearch(searchVal)"
					:placeholder="props.searchPlaceholder"
				>
					<template #prefix>
						<el-icon class="el-input__icon">
							<search />
						</el-icon>
					</template>
				</el-input>
			</div>

			<el-scrollbar ref="linkedScrollRef" style="height: calc(100% - 52px)" @scroll="onScroll">
				<div class="list">
					<el-skeleton v-if="ledgerList?.length === 0" :rows="3" p10px style="border: none" />
					<template v-for="(item, index) of ledgerList" :key="timer">
						<!-- v-show="item.ledger.name.includes(searchVal)" -->
						<div class="item">
							<el-checkbox
								v-model="item.__checked"
								:label="item.name"
								@change="onCheckLedger(item)"
								class="linke-title"
							>
								<span>{{ item.ledger.name }}</span>
								<el-icon
									v-if="ledgerFields[index]?.fields.some((s:any) => s.__checked)"
									title="有关联字段"
								>
									<Link />
								</el-icon>
							</el-checkbox>

							<span class="svg-set" @click.stop="onSetting(item)" title="添加规则">
								<svg
									:key="index"
									v-if="true"
									xmlns="http://www.w3.org/2000/svg"
									width="15"
									height="15"
									viewBox="0 0 24 24"
								>
									<path
										fill="currentColor"
										d="M19.14 12.94c.04-.3.06-.61.06-.94c0-.32-.02-.64-.07-.94l2.03-1.58a.49.49 0 0 0 .12-.61l-1.92-3.32a.488.488 0 0 0-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54a.484.484 0 0 0-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58a.49.49 0 0 0-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6s3.6 1.62 3.6 3.6s-1.62 3.6-3.6 3.6z"
									/>
								</svg>
							</span>

							<el-skeleton
								v-if="ledgerFields[index]?.fields.filter((f:any) => f.isListField).length === 0 && item.__checked"
								:rows="1"
								p10px
							/>

							<div
								v-else
								class="child"
								:class="{open: item.__checked}"
								:style="{
									'max-height': `${item.__checked ? ledgerFields[index]?.fields.length * 40 : 0}px`,
								}"
							>
								<!-- 拖动的元素 -->
								<div
									draggable="true"
									class="item"
									v-for="(field, i) of ledgerFields[index]?.fields.filter((f: any) => f.isListField)"
									:key="i"
									:class="{linked: field?.__checked}"
									@dragstart="onDragStart(field)"
								>
									<strong color="#f00" v-if="true">{{ field?.__column }}</strong>
									<span
										class="rule-svg"
										:title="field.displayName"
										:class="{js: field.__type === 'calculation', tj: field.__type === 'dimension'}"
									>
										{{ field.displayName }}
									</span>
									<i v-if="field?.__column" @click="onDeleteLinkedField(field)">&times;</i>
								</div>
							</div>
						</div>
					</template>
				</div>
			</el-scrollbar>
		</div>
	</div>
</template>
<style scoped lang="scss">
.ledger-fill-config {
	border-radius: 5px;
	border: 1px solid var(--z-line);
	user-select: none;
	display: flex;
	height: 100%;
	margin-right: 10px;
	overflow: hidden;

	.left {
		height: 100%;
		width: 100%;
	}

	.search {
		align-items: center;
		border-bottom: 1px solid var(--z-line);
		display: flex;
		padding: 10px;
		white-space: nowrap;

		span {
			font-size: 13px;
			i {
				margin-top: -1px;
			}
		}
	}

	.linke-title {
		display: flex;
	}

	.item {
		position: relative;

		.svg-set {
			background: #efefef;
			color: #666;
			cursor: pointer;
			height: 40px;
			line-height: 44px;
			position: absolute;
			right: 0;
			top: 0;
			text-align: center;
			width: 40px;
			svg {
				transition: all 0.3s linear;
			}
			&:hover {
				svg {
					transform: scale(1.4) rotate(90deg);
				}
			}
		}
	}

	.list {
		height: 100%;

		> div {
			border-bottom: 1px solid var(--z-line);
			cursor: pointer;
			> label {
				background-color: #fafafa;
				height: 40px;
				padding: 5px 10px;
				// margin-top: -1px;
				width: 100%;
			}
		}

		.el-checkbox__input {
			position: relative;
			top: -0.5px;
		}

		:deep(.el-checkbox__label) {
			display: flex;
			overflow: hidden;
			width: calc(100% - 50px);
			i {
				color: #f00;
				font-size: 15px;
				margin-left: 10px;
			}
			span {
				overflow: hidden;
				text-overflow: ellipsis;
				width: 100%;
			}
		}
	}

	.child {
		opacity: 0;
		transition: all 0.15s linear;
		overflow: hidden;
		max-height: 0;

		&.open {
			background-color: #fefefe;
			opacity: 1;
		}

		.item {
			align-items: center;
			border-top: 1px solid transparent;
			border-bottom: 1px solid transparent;
			cursor: move;
			color: #333;
			display: flex;
			height: 40px;
			line-height: 40px;
			margin-bottom: -1px;
			text-indent: 40px;
			position: relative;
			white-space: nowrap;
			transition: all 0.3s linear;

			&:first-child {
				border-top: 1px solid var(--z-line) !important;
			}

			&.linked {
				background-color: #faecd8;
				border-top-color: #e8d0af;
				border-bottom-color: #e8d0af;
			}

			strong {
				height: 40px;
				position: absolute;
				left: -30px;
			}

			span {
				border-left: 5px solid transparent;
				height: 40px;
				font-size: 13px;
				flex: 1;
				max-width: calc(100% - 35px);
				overflow: hidden;
				text-overflow: ellipsis;
				text-indent: 45px;
			}

			i {
				cursor: pointer;
				color: var(--z-line);
				font-size: 18px;
				height: 40px;
				position: absolute;
				top: -2px;
				text-indent: 0;
				text-align: center;
				transition: all 0.15s linear;
				right: 0;
				width: 40px;

				&:hover {
					color: var(--z-main);
					transform: scale(1.2);
				}
			}

			&:hover {
				// border-top: 1px solid #e8d0af;
				// transform: translate3d(0, 1px, 0);
				// box-shadow: 0 6px 6px -6px rgba(0, 0, 0, 0.12) inset;
				background-color: #f8e3c5;
			}

			.tj {
				border-left: 5px solid #f78989;
			}
			.js {
				border-left: 5px solid #66b1ff;
			}
		}
	}
	.excel-comp {
		.title {
			padding: 5px !important;
		}

		.sheet-main {
			height: calc(100% - 40px) !important;
		}
	}

	.excel_tips {
		i {
			margin-right: 5px;
		}

		align-items: center;
		display: flex;
		font-size: 12px;
	}
}
</style>

<script lang="ts" setup>
import {computed, onMounted, ref, toRaw, watch} from 'vue'
import {useUserStore} from '@/stores/useUserStore'
import {ElMessage} from 'element-plus'
enum FieldType {
	String = 'string',
	Int = 'int',
	Date = 'date',
	DateTime = 'datetime',
	Decimal = 'decimal',
	Radio = 'radio',
	Checkbox = 'checkbox',
}
enum WhereType {
	And = 0,
	Or = 1,
	Null = -1,
}
enum ConditionType {
	Equal = 0,
	GreaterThan = 2,
	GreaterThanOrEqual = 3,
	LessThan = 4,
	LessThanOrEqual = 5,
	In = 1,
	NotIn = 13,
	NoEqual = 10,
}
enum CalculationEnum {
	AggregateCount = '计数',
	AggregateSum = '求和',
	AggregateAvg = '平均',
	AggregateMax = '最大值',
	AggregateMin = '最小值',
}
interface Props {
	ledger?: any
	ledgerType?: number
	visible?: boolean

	defaultFilterFields?: any[]
	defaultDimensionFields?: any[]
	defalutCalculationFields?: any[]
	defalutCollapse?: any[]

	isDialog?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	ledger: null,
	ledgerType: 0,
	visible: false,
	isDialog: true,

	defalutCollapse: ['1'] as any,
	defalutFilterFields: [] as any,
})

const emits = defineEmits(['output', 'deleteDimension'])
const collapse = computed(() => props.defalutCollapse)
const conditionTypeList = [
	{label: '等于', value: ConditionType.Equal},
	{label: '大于', value: ConditionType.GreaterThan},
	{label: '大于等于', value: ConditionType.GreaterThanOrEqual},
	{label: '小于', value: ConditionType.LessThan},
	{label: '小于等于', value: ConditionType.LessThanOrEqual},
	{label: '包含', value: ConditionType.In},
	{label: '不包含', value: ConditionType.NotIn},
	{label: '不等于', value: ConditionType.NoEqual},
]

watch(
	() => props.defaultFilterFields,
	(val) => {
		if (val?.length !== 0) {
			filterFields.value = JSON.parse(JSON.stringify(val))
		} else {
			filterFields.value = [
				{
					field: null,
					conditionType: null,
					value: [],

					type: FieldType.String,
					whereType: WhereType.Null,
					raw: {multiple: null},
				},
			]
		}
	}
)

watch(
	() => props.defaultDimensionFields,
	(val) => {
		if (val?.length !== 0) {
			dimensionFields.value = JSON.parse(JSON.stringify(val))
			collapse.value?.push('2')
		} else {
			dimensionFields.value = []
		}
	}
)

watch(
	() => props.defalutCalculationFields,
	(val) => {
		if (val?.length !== 0) {
			calculationFields.value = JSON.parse(JSON.stringify(val))
			collapse.value?.push('3')
		} else {
			calculationFields.value = [{field: null, flag: null, raw: null}]
		}
	}
)

const fields = computed(() => {
	let __fields: any = []
	if (props.ledger?.departmentFields && props.ledger?.departmentFields.length > 0) {
		__fields = props.ledger.departmentFields
	} else {
		__fields = props.ledger?.fields || []
	}
	return toRaw(__fields.filter((f: any) => f.isListField))
})

const filterFields: any = ref([
	{
		field: null,
		conditionType: null,
		value: [],

		type: FieldType.String,
		whereType: WhereType.Null,
		raw: {multiple: null},
	},
])
const onFilterFieldAdd = () => {
	filterFields.value.push({
		field: null,
		conditionType: null,
		value: [],

		type: FieldType.String,
		whereType: WhereType.And,
		raw: {multiple: null},
	})
}
const onFilterFieldDel = (index: number) => filterFields.value.splice(index, 1)
const onFilterFieldSelectChange = (item: any) => {
	const currentField = fields.value.find((f: any) => f.name === item.field)
	item.raw = currentField
	item.type = currentField?.type
	item.value = []
}
const onChangeConditionType = (item: any) => {
	item.value = []
}

const dimensionFields: any = ref([])
const currentDimensionField: any = ref(null)
const onDimensionAdd = () => {
	const field = fields.value.find((f: any) => f.name === currentDimensionField.value)
	if (dimensionFields.value.some((f: any) => f.field === field?.displayName)) {
		ElMessage.warning('该字段已存在')
		return
	}
	if (!currentDimensionField.value) {
		ElMessage.warning('请选择统计维度字段')
		return
	}
	dimensionFields.value.push({
		field: field.displayName,
		value: [[]],
		default: 0,
		raw: toRaw(field),
	})
	currentDimensionField.value = null
}
const onDeleteDimension = (index: number) => {
	emits('deleteDimension', toRaw(dimensionFields.value.splice(index, 1)[0]))
}
const onDimensionAddDiy = (index: number, dimensionIndex: number, raw: any) => {
	dimensionFields.value[index].value.push([])
}
const onDeleteDimensionDiy = (index: number, dimensionIndex: number) => {
	dimensionFields.value[index].value.splice(dimensionIndex, 1)
}

const calculationFields: any = ref([{field: null, flag: null, raw: null}])
const onCalculationAdd = () => {
	calculationFields.value.push({field: null, flag: null, raw: null})
}
const onDeleteCalculation = (index: number) => calculationFields.value.splice(index, 1)
const onChangeCalculation = (key: any, calculation: any) => {
	const field = fields.value.find((f: any) => f.name === key)
	calculation.raw = field
}

const onSaveRules = () => {
	emits('output', {
		filterFields: toRaw(filterFields.value),
		dimensionFields: toRaw(dimensionFields.value),
		calculationFields: toRaw(calculationFields.value),
	})
}

onMounted(() => {})

defineExpose({
	getRules: () => ({
		filterFields: toRaw(filterFields.value),
		dimensionFields: toRaw(dimensionFields.value),
		calculationFields: toRaw(calculationFields.value),
	}),
})
</script>
<template>
	<Dialog width="670px" v-model="props.visible" v-bind="$attrs" @click-confirm="onSaveRules">
		<!-- <template #body> -->
		<div class="report-filter">
			<el-collapse v-model="collapse" class="report-filter-collapse">
				<!-- 筛选规则 -->
				<el-collapse-item title="筛选规则" name="1">
					<div class="filter-list">
						<!-- 其他字段 -->
						<div class="filter-item" v-for="(item, index) of filterFields" :key="index">
							<el-select clearable v-model="item.field" @change="onFilterFieldSelectChange(item)">
								<template v-for="field of (fields as any)">
									<el-option
										:disabled="item.name === field.name"
										:value="field.name"
										:label="field.displayName"
									/>
								</template>
							</el-select>

							<el-select
								v-model="item.conditionType"
								clearable
								@change="onChangeConditionType(item)"
							>
								<el-option
									v-for="condition of conditionTypeList"
									:value="condition.value"
									:label="condition.label"
								/>
							</el-select>

							<div class="filter-value">
								<!-- string -->
								<el-input
									v-if="item.type === FieldType.String && item?.raw?.multiple === null"
									v-model.number="item.value[0]"
									placeholder="请输入条件"
									clearable
								/>

								<!-- int, decimal  -->
								<el-input-number
									v-if="item.type === FieldType.Int || item.type === FieldType.Decimal"
									v-model="item.value[0]"
									controls-position="right"
								/>

								<!-- string/radio -->
								<el-select
									v-if="item.type === FieldType.String && item?.raw?.multiple === false"
									v-model="item.value[0]"
									:placeholder="`请选择${item?.raw?.displayName}`"
									clearable
								>
									<el-option v-for="option of item?.raw.options" :value="option" :label="option" />
								</el-select>

								<!-- string/checkbox -->
								<el-checkbox-group
									v-if="item.type === FieldType.String && item?.raw?.multiple"
									v-model="item.value"
								>
									<el-checkbox
										border
										v-for="option of item?.raw.options"
										:label="option"
										style="margin-right: 10px"
									/>
								</el-checkbox-group>

								<!-- date -->
								<el-date-picker
									v-if="item.type === FieldType.Date || item.type === FieldType.DateTime"
									value-format="YYYY-MM-DD"
									v-model="item.value[0]"
									type="date"
									placeholder="请选择日期"
									style="width: 100%"
								/>
							</div>

							<el-button
								type="danger"
								v-if="index >= 0 && index < filterFields.length - 1"
								@click="onFilterFieldDel(index)"
							>
								<el-icon>
									<Minus />
								</el-icon>
							</el-button>
							<el-button
								type="primary"
								v-if="index === filterFields.length - 1"
								@click="onFilterFieldAdd"
							>
								<el-icon>
									<Plus />
								</el-icon>
							</el-button>
						</div>
					</div>
				</el-collapse-item>

				<!-- 统计维度 -->
				<el-collapse-item v-if="ledgerType === 1" title="统计维度" name="2">
					<div class="dimension">
						<div class="list">
							<template v-for="(dimension, index) of dimensionFields">
								<div class="item" v-if="dimension?.raw?.type === 'string'">
									<el-tag
										class="report-filter-tag"
										size="large"
										closable
										@close="onDeleteDimension(index)"
									>
										{{ dimension.field }}
									</el-tag>
								</div>

								<div
									class="item full"
									v-if="
										dimension?.raw?.type === 'int' ||
										dimension?.raw?.type === 'decimal' ||
										dimension?.raw?.type === 'date' ||
										dimension?.raw?.type === 'datetime'
									"
								>
									<el-tag
										class="report-filter-tag"
										size="large"
										closable
										@close="onDeleteDimension(index)"
										style="width: 189px"
										>{{ dimension.field }}</el-tag
									>
									<el-radio v-model="dimension.default" :label="0" border>默认所有</el-radio>
									<el-radio v-model="dimension.default" :label="1" border>自定义维度</el-radio>

									<!-- 自定义维度 -->
									<template v-if="dimension.default === 1">
										<div class="dimension-diy">
											<template v-for="(dimensionValue, dimensionIndex) of dimension.value">
												<div class="dimension-diy-item">
													<!-- 数字/浮点 -->
													<template
														v-if="
															dimension?.raw?.type === 'int' || dimension?.raw?.type === 'decimal'
														"
													>
														<el-input-number
															v-model="dimensionValue[0]"
															controls-position="right"
															style="width: 30%"
														/>
														<span class="sp">~</span>
														<el-input-number
															v-model="dimensionValue[1]"
															controls-position="right"
															style="width: 30%"
														/>
													</template>

													<!-- 日期 -->
													<template
														v-if="
															dimension?.raw?.type === 'date' || dimension?.raw?.type === 'datetime'
														"
													>
														<el-date-picker
															v-model="dimensionValue[0]"
															type="daterange"
															range-separator="-"
															value-format="YYYY-MM-DD"
															start-placeholder="请选择开始日期"
															end-placeholder="请选择结束日期"
															style="width: 64%"
														/>
													</template>

													<!-- 添加自定义维度 -->
													<el-button
														type="danger"
														v-if="
															dimensionIndex >= 0 && dimensionIndex < dimension.value.length - 1
														"
														@click="onDeleteDimensionDiy(index, dimensionIndex)"
													>
														<el-icon><Minus /></el-icon>
													</el-button>
													<el-button
														v-if="dimensionIndex === dimension.value.length - 1"
														type="primary"
														@click="onDimensionAddDiy(index, dimensionIndex, dimension.raw)"
													>
														<el-icon><Plus /></el-icon>
													</el-button>
												</div>
											</template>
										</div>
									</template>
								</div>
							</template>
						</div>

						<!-- 添加维度字段 -->
						<div class="dimension-add">
							<el-select v-model="currentDimensionField" clearable>
								<template v-for="field of (fields as any)">
									<el-option
										:value="field.name"
										:label="field.displayName"
										:disabled="calculationFields.some((f:any) => f.field === field.name) || dimensionFields.some((f:any) => f.field === field.displayName)"
									/>
								</template>
							</el-select>
							<el-button type="primary" @click="onDimensionAdd">
								<el-icon>
									<Plus />
								</el-icon>
							</el-button>
						</div>
					</div>
				</el-collapse-item>

				<!-- 计算规则 -->
				<el-collapse-item v-if="ledgerType === 1" title="计算规则" name="3">
					<div class="calculation">
						<div class="calculation-list">
							<div
								class="calculation-item"
								v-for="calculation, index of (calculationFields as any)"
							>
								<el-select
									clearable
									v-model="calculation.field"
									@change="onChangeCalculation($event, calculation)"
								>
									<template v-for="field of (fields as any)">
										<el-option
											:value="field.name"
											:label="field.displayName"
											:disabled="calculationFields.some((f:any) => f.field === field.name) || dimensionFields.some((f:any) => f.field === field.displayName)"
										/>
									</template>
								</el-select>

								<el-select v-model="calculation.flag" clearable style="width: 100px" ml-10px>
									<template v-for="[key, value] of Object.entries(CalculationEnum)">
										<template v-if="calculation?.raw?.type === 'string'">
											<el-option
												v-if="value === CalculationEnum.AggregateCount"
												:value="key"
												:label="value"
											/>
										</template>
										<template v-else>
											<el-option :value="key" :label="value" />
										</template>
									</template>
								</el-select>

								<el-button
									v-if="index !== calculationFields.length - 1"
									type="danger"
									@click="onDeleteCalculation(index)"
								>
									<el-icon>
										<Minus />
									</el-icon>
								</el-button>

								<el-button
									v-if="index === calculationFields.length - 1"
									type="primary"
									@click="onCalculationAdd"
								>
									<el-icon>
										<Plus />
									</el-icon>
								</el-button>
							</div>
						</div>
					</div>
				</el-collapse-item>
			</el-collapse>
		</div>
		<!-- </template> -->
	</Dialog>
</template>
<style scoped lang="scss">
.report-filter {
	border-radius: 5px;
	border: var(--z-border);
	display: flex;
	flex-direction: column;
	user-select: none;
	overflow: hidden;

	.filter-list {
		padding: 10px 10px 0 10px;
	}

	.filter-item {
		display: flex;

		> *:not(button) {
			min-height: 32px;
			margin-bottom: 10px;

			&:nth-child(1) {
				margin-right: 10px;
				width: 200px;
			}

			&:nth-child(2) {
				margin-right: 10px;
				width: 200px;
			}

			&:nth-child(3) {
				width: 400px;

				> * {
					width: 100%;
				}
			}

			:deep(.el-checkbox) {
				margin-bottom: 10px;
			}
		}

		&:last-child > * {
			margin-bottom: 0;
		}

		button {
			margin-left: 10px;
			height: 32px;
		}
	}

	.filter-value {
		display: flex;

		.sp {
			height: 30px;
			line-height: 30px;
			padding: 0 10px;
			text-align: center;
			width: 46px !important;
		}
	}
}

.dimension {
	user-select: none;
	padding: 10px 10px 0 10px;

	&-add button {
		margin-left: 10px;
		height: 32px;
	}

	.list {
		display: flex;
		flex-wrap: wrap;

		.item {
			align-items: center;
			display: flex;
			flex-wrap: wrap;
			margin-right: 10px;
			margin-bottom: 10px;

			&.full {
				> label {
					margin-left: 10px;
					margin-right: 0px;
				}

				width: 100%;
			}
		}
	}

	&-diy {
		background: #fafafa;
		border-radius: 5px;
		border: var(--z-border);
		display: flex;
		flex-wrap: wrap;
		margin: 10px 0 0 0;
		padding: 10px 10px 0 10px;
		width: 100%;

		&-item {
			align-items: center;
			display: flex;
			margin-bottom: 10px;
			width: 100%;

			> button {
				margin-left: 10px;
				height: 32px;
			}
		}

		.sp {
			margin: 0 10px;
		}
	}
}

.calculation {
	@extend .dimension;

	&-item {
		display: flex;
		&:not(:last-child) {
			margin-bottom: 10px;
		}

		.label {
			margin-right: 10px;
		}
	}

	&-add button {
		margin-left: 10px;
		height: 32px;
	}

	button {
		margin-left: 10px;
		height: 32px;
	}
}
</style>

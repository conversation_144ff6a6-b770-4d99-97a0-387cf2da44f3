<template>
  <div class="automatic-comparison-settings">
    <Block
      title="自动比对设置"
      :enable-fixed-height="true"
      :enable-expand-content="true"
      :enable-back-button="false"
      @height-changed="onBlockHeightChanged"
    >
      <template #topRight>
        <el-button type="primary" @click="handleSave">
          <el-icon><DocumentAdd /></el-icon>
          保存配置
        </el-button>
        <el-button @click="handleReturn">
          返回
        </el-button>
      </template>

      <!-- Tab切换 -->
      <el-tabs v-model="activeTab" class="comparison-tabs">
        <el-tab-pane label="比对周期设置" name="cycle">
          <ComparisonCycleSettings 
            :height="contentHeight"
            @height-changed="onContentHeightChanged"
          />
        </el-tab-pane>
        
        <el-tab-pane label="比对类型配置" name="type">
          <div class="tab-placeholder">
            <el-empty description="比对类型配置功能开发中..." />
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="数据筛选设置" name="filter">
          <div class="tab-placeholder">
            <el-empty description="数据筛选设置功能开发中..." />
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="其他设置" name="other">
          <div class="tab-placeholder">
            <el-empty description="其他设置功能开发中..." />
          </div>
        </el-tab-pane>
      </el-tabs>
    </Block>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentAdd } from '@element-plus/icons-vue'
import ComparisonCycleSettings from './components/ComparisonCycleSettings.vue'

// 当前激活的tab
const activeTab = ref('cycle')

// 内容高度
const contentHeight = ref(600)

// 块高度变化事件
const onBlockHeightChanged = (height: number) => {
  contentHeight.value = height - 120
}

// 内容高度变化事件
const onContentHeightChanged = (height: number) => {
  // 可以在这里处理子组件的高度变化
}

// 保存配置
const handleSave = () => {
  ElMessage.success('配置保存成功')
}

// 返回
const handleReturn = () => {
  // 这里可以添加返回逻辑，比如路由跳转
  window.history.back()
}
</script>

<route>
{
  meta: {
    title: '自动比对设置'
  }
}
</route>

<style scoped lang="scss">
.automatic-comparison-settings {
  height: 100%;
  
  .comparison-tabs {
    height: 100%;
    
    :deep(.el-tabs__content) {
      height: calc(100% - 40px);
      overflow: hidden;
    }
    
    :deep(.el-tab-pane) {
      height: 100%;
    }
  }
  
  .tab-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
  }
}
</style>

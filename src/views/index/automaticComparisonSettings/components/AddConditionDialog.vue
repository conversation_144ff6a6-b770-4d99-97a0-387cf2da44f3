<template>
  <Dialog
    v-model="visible"
    title="添加筛选条件"
    :enable-confirm="true"
    :loading="loading"
    width="600"
    confirm-text="确定"
    cancel-text="取消"
    @click-confirm="handleConfirm"
    @click-cancel="handleCancel"
    @close="handleClose"
  >
    <div class="add-condition-dialog">
      <el-form :model="formData" label-width="120px" :rules="rules" ref="formRef">
        <el-form-item label="筛选条件类型：" prop="type">
          <el-select 
            v-model="formData.type" 
            placeholder="请选择筛选条件类型" 
            style="width: 100%;"
            @change="onTypeChange"
          >
            <el-option
              v-for="type in conditionTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            >
              <div style="display: flex; align-items: center;">
                <el-icon style="margin-right: 8px;"><component :is="type.icon" /></el-icon>
                {{ type.label }}
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 动态表单内容 -->
        <div v-if="formData.type">
          <!-- 指定时间段 -->
          <template v-if="formData.type === 'timeRange'">
            <el-form-item label="开始日期：" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                type="date"
                placeholder="选择开始日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%;"
              />
            </el-form-item>
            <el-form-item label="结束日期：" prop="endDate">
              <el-date-picker
                v-model="formData.endDate"
                type="date"
                placeholder="选择结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%;"
              />
            </el-form-item>
          </template>

          <!-- 指定填报人 -->
          <template v-else-if="formData.type === 'reporter'">
            <el-form-item label="选择填报人：" prop="reporters">
              <el-input
                v-model="formData.reporters"
                placeholder="请输入填报人姓名，多个用逗号分隔"
                type="textarea"
                :rows="3"
              />
              <div class="form-hint">例如：张三, 李四, 王五</div>
            </el-form-item>
          </template>

          <!-- 指定填报部门 -->
          <template v-else-if="formData.type === 'department'">
            <el-form-item label="选择部门：" prop="departments">
              <el-select
                v-model="formData.departments"
                multiple
                placeholder="请选择部门"
                style="width: 100%;"
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option
                  v-for="dept in departmentOptions"
                  :key="dept.value"
                  :label="dept.label"
                  :value="dept.value"
                />
              </el-select>
              <div class="form-hint">可选择多个部门</div>
            </el-form-item>
          </template>

          <!-- 指定填报区域 -->
          <template v-else-if="formData.type === 'region'">
            <el-form-item label="选择填报区域：" prop="regions">
              <el-select
                v-model="formData.regions"
                multiple
                placeholder="请选择区域"
                style="width: 100%;"
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option
                  v-for="region in regionOptions"
                  :key="region.value"
                  :label="region.label"
                  :value="region.value"
                />
              </el-select>
              <div class="form-hint">可选择多个区域</div>
            </el-form-item>
          </template>

          <!-- 指定数据质量情况 -->
          <template v-else-if="formData.type === 'dataQuality'">
            <el-form-item label="质量字段：" prop="qualityField">
              <el-select v-model="formData.qualityField" placeholder="请选择质量字段" style="width: 100%;">
                <el-option label="完整性" value="completeness" />
                <el-option label="准确性" value="accuracy" />
                <el-option label="一致性" value="consistency" />
                <el-option label="及时性" value="timeliness" />
                <el-option label="有效性" value="validity" />
              </el-select>
            </el-form-item>
            <el-form-item label="字段值：" prop="qualityValue">
              <el-input
                v-model="formData.qualityValue"
                placeholder="请输入质量字段对应的值"
              />
            </el-form-item>
          </template>

          <!-- 指定数据质量情况详情 -->
          <template v-else-if="formData.type === 'dataQualityDetail'">
            <el-form-item label="质量字段：" prop="detailQualityField">
              <el-select v-model="formData.detailQualityField" placeholder="请选择质量字段" style="width: 100%;">
                <el-option label="完整性" value="completeness" />
                <el-option label="准确性" value="accuracy" />
                <el-option label="一致性" value="consistency" />
                <el-option label="及时性" value="timeliness" />
                <el-option label="有效性" value="validity" />
              </el-select>
            </el-form-item>
            <el-form-item label="色值内容：" prop="colorValue">
              <el-input
                v-model="formData.colorValue"
                placeholder="请输入色值内容"
              />
              <div class="form-hint">用于标识数据质量状态的颜色值</div>
            </el-form-item>
          </template>

          <!-- 指定数据来源 -->
          <template v-else-if="formData.type === 'dataSource'">
            <el-form-item label="数据来源：" prop="dataSources">
              <el-input
                v-model="formData.dataSources"
                placeholder="请输入数据来源标志点，多个用逗号分隔"
                type="textarea"
                :rows="3"
              />
              <div class="form-hint">例如：系统A, 系统B, 外部接口</div>
            </el-form-item>
          </template>
        </div>
      </el-form>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Calendar,
  User,
  OfficeBuilding,
  Location,
  DataAnalysis,
  Document,
  Connection
} from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'confirm'])

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 加载状态
const loading = ref(false)

// 表单引用
const formRef = ref()

// 部门选项数据
const departmentOptions = [
  { label: '技术部', value: '技术部' },
  { label: '市场部', value: '市场部' },
  { label: '财务部', value: '财务部' },
  { label: '人事部', value: '人事部' },
  { label: '行政部', value: '行政部' },
  { label: '销售部', value: '销售部' },
  { label: '客服部', value: '客服部' },
  { label: '研发部', value: '研发部' },
  { label: '运营部', value: '运营部' },
  { label: '法务部', value: '法务部' },
  { label: '采购部', value: '采购部' },
  { label: '质量部', value: '质量部' }
]

// 区域选项数据
const regionOptions = [
  { label: '北京市', value: '北京市' },
  { label: '上海市', value: '上海市' },
  { label: '广州市', value: '广州市' },
  { label: '深圳市', value: '深圳市' },
  { label: '杭州市', value: '杭州市' },
  { label: '南京市', value: '南京市' },
  { label: '武汉市', value: '武汉市' },
  { label: '成都市', value: '成都市' },
  { label: '西安市', value: '西安市' },
  { label: '重庆市', value: '重庆市' },
  { label: '天津市', value: '天津市' },
  { label: '苏州市', value: '苏州市' },
  { label: '青岛市', value: '青岛市' },
  { label: '大连市', value: '大连市' },
  { label: '厦门市', value: '厦门市' }
]

// 筛选条件类型选项
const conditionTypes = [
  {
    value: 'timeRange',
    label: '指定时间段',
    icon: 'Calendar'
  },
  {
    value: 'reporter',
    label: '指定填报人',
    icon: 'User'
  },
  {
    value: 'department',
    label: '指定填报部门',
    icon: 'OfficeBuilding'
  },
  {
    value: 'region',
    label: '指定填报区域',
    icon: 'Location'
  },
  {
    value: 'dataQuality',
    label: '指定数据质量情况',
    icon: 'DataAnalysis'
  },
  {
    value: 'dataQualityDetail',
    label: '指定数据质量情况详情',
    icon: 'Document'
  },
  {
    value: 'dataSource',
    label: '指定数据来源',
    icon: 'Connection'
  }
]

// 表单数据
const formData = reactive({
  type: '',
  title: '',
  icon: '',
  // 时间段相关
  startDate: '',
  endDate: '',
  // 填报人相关
  reporters: '',
  // 部门相关
  departments: [],
  // 区域相关
  regions: [],
  // 数据质量相关
  qualityField: '',
  qualityValue: '',
  // 数据质量详情相关
  detailQualityField: '',
  colorValue: '',
  // 数据来源相关
  dataSources: ''
})

// 表单验证规则
const rules = computed(() => {
  const baseRules = {
    type: [
      { required: true, message: '请选择筛选条件类型', trigger: 'change' }
    ]
  }

  // 根据类型动态添加验证规则
  if (formData.type === 'timeRange') {
    return {
      ...baseRules,
      startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
      endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }]
    }
  } else if (formData.type === 'reporter') {
    return {
      ...baseRules,
      reporters: [{ required: true, message: '请输入填报人', trigger: 'blur' }]
    }
  } else if (formData.type === 'department') {
    return {
      ...baseRules,
      departments: [{ required: true, message: '请选择部门', trigger: 'change' }]
    }
  } else if (formData.type === 'region') {
    return {
      ...baseRules,
      regions: [{ required: true, message: '请选择区域', trigger: 'change' }]
    }
  } else if (formData.type === 'dataQuality') {
    return {
      ...baseRules,
      qualityField: [{ required: true, message: '请选择质量字段', trigger: 'change' }],
      qualityValue: [{ required: true, message: '请输入字段值', trigger: 'blur' }]
    }
  } else if (formData.type === 'dataQualityDetail') {
    return {
      ...baseRules,
      detailQualityField: [{ required: true, message: '请选择质量字段', trigger: 'change' }],
      colorValue: [{ required: true, message: '请输入色值内容', trigger: 'blur' }]
    }
  } else if (formData.type === 'dataSource') {
    return {
      ...baseRules,
      dataSources: [{ required: true, message: '请输入数据来源', trigger: 'blur' }]
    }
  }

  return baseRules
})

// 类型变化处理
const onTypeChange = (type: string) => {
  const selectedType = conditionTypes.find(t => t.value === type)
  if (selectedType) {
    formData.title = selectedType.label
    formData.icon = selectedType.icon
  }
  
  // 清空其他字段
  formData.startDate = ''
  formData.endDate = ''
  formData.reporters = ''
  formData.departments = []
  formData.regions = []
  formData.qualityField = ''
  formData.qualityValue = ''
  formData.detailQualityField = ''
  formData.colorValue = ''
  formData.dataSources = ''
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    type: '',
    title: '',
    icon: '',
    startDate: '',
    endDate: '',
    reporters: '',
    departments: [],
    regions: [],
    qualityField: '',
    qualityValue: '',
    detailQualityField: '',
    colorValue: '',
    dataSources: ''
  })
  formRef.value?.clearValidate()
}

// 确认
const handleConfirm = async () => {
  try {
    await formRef.value?.validate()
    
    // 验证时间段的逻辑
    if (formData.type === 'timeRange' && formData.startDate && formData.endDate) {
      if (new Date(formData.startDate) >= new Date(formData.endDate)) {
        ElMessage.warning('结束日期必须大于开始日期')
        return
      }
    }
    
    emit('confirm', { ...formData })
    resetForm()
  } catch (error) {
    ElMessage.warning('请完善表单信息')
  }
}

// 取消
const handleCancel = () => {
  resetForm()
  emit('update:modelValue', false)
}

// 关闭
const handleClose = () => {
  resetForm()
  emit('update:modelValue', false)
}

// 监听弹窗关闭，重置表单
watch(visible, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})
</script>

<style scoped lang="scss">
.add-condition-dialog {
  .el-form {
    .el-form-item {
      margin-bottom: 24px;
    }
  }

  .form-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
}
</style>

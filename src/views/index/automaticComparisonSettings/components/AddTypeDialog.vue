<template>
  <Dialog
    v-model="visible"
    :title="title"
    :enable-confirm="true"
    :loading="loading"
    width="600"
    confirm-text="确定"
    cancel-text="取消"
    @click-confirm="handleConfirm"
    @click-cancel="handleCancel"
    @close="handleClose"
  >
    <div class="add-type-dialog">
      <el-form :model="formData" label-width="120px" :rules="rules" ref="formRef">
        <el-form-item label="选择数据类型：" prop="dataType">
          <el-select 
            v-model="formData.dataType" 
            placeholder="请选择数据类型" 
            style="width: 100%;"
            clearable
          >
            <el-option
              v-for="type in dataTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="选择比对规则：" prop="compareRule">
          <el-select 
            v-model="formData.compareRule" 
            placeholder="请选择比对规则" 
            style="width: 100%;"
            clearable
          >
            <el-option
              v-for="rule in compareRules"
              :key="rule.value"
              :label="rule.label"
              :value="rule.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="启用状态：">
          <el-switch
            v-model="formData.status"
            :active-value="true"
            :inactive-value="false"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue: boolean
  title: string
  formData?: any
  mode: 'add' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  formData: () => ({})
})

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel'])

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 加载状态
const loading = ref(false)

// 表单引用
const formRef = ref()

// 数据类型选项
const dataTypes = [
  { label: '短文本', value: '短文本' },
  { label: '长文本', value: '长文本' },
  { label: '整数', value: '整数' },
  { label: '小数', value: '小数' },
  { label: '单选', value: '单选' },
  { label: '多选', value: '多选' },
  { label: '年月日时间', value: '年月日时间' },
  { label: '日期时间', value: '日期时间' },
  { label: '居民身份证号', value: '居民身份证号' },
  { label: '护照号码', value: '护照号码' },
  { label: '驾驶证号码', value: '驾驶证号码' },
  { label: '港澳来往内地通行证', value: '港澳来往内地通行证' },
  { label: '台湾来往内地通行证', value: '台湾来往内地通行证' },
  { label: '残疾人证件号', value: '残疾人证件号' },
  { label: '社保卡号', value: '社保卡号' },
  { label: '性别', value: '性别' },
  { label: '出生日期', value: '出生日期' },
  { label: '电话号码', value: '电话号码' },
  { label: '年龄', value: '年龄' },
  { label: '邮箱', value: '邮箱' },
  { label: '填报人', value: '填报人' },
  { label: '填报部门', value: '填报部门' }
]

// 比对规则选项
const compareRules = [
  { label: '完全匹配', value: '完全匹配' },
  { label: '关键词匹配', value: '关键词匹配' }
]

// 表单数据
const formData = reactive({
  dataType: '',
  compareRule: '',
  status: true
})

// 表单验证规则
const rules = {
  dataType: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ],
  compareRule: [
    { required: true, message: '请选择比对规则', trigger: 'change' }
  ]
}

// 监听props变化，初始化表单数据
watch(() => props.formData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData, newData)
  } else {
    // 重置表单
    Object.assign(formData, {
      dataType: '',
      compareRule: '',
      status: true
    })
  }
}, { immediate: true })

// 确认
const handleConfirm = async () => {
  try {
    // 表单验证
    await formRef.value?.validate()
    
    // 检查数据类型是否已存在（新增时）
    if (props.mode === 'add') {
      // 这里可以添加重复检查逻辑
      // 暂时跳过，实际项目中可以调用API检查
    }
    
    emit('confirm', { ...formData })
  } catch (error) {
    ElMessage.warning('请完善表单信息')
  }
}

// 取消
const handleCancel = () => {
  emit('cancel')
}

// 关闭
const handleClose = () => {
  emit('update:modelValue', false)
}
</script>

<style scoped lang="scss">
.add-type-dialog {
  .el-form {
    .el-form-item {
      margin-bottom: 24px;
    }
  }
}
</style>

<template>
  <div class="data-filter-settings">
    <!-- 筛选模式选择 -->
    <div class="filter-mode-section">
      <h3 class="section-title">
        <el-icon><Filter /></el-icon>
        筛选模式
        <el-tooltip content="选择数据筛选的模式" placement="top">
          <el-icon class="help-icon"><QuestionFilled /></el-icon>
        </el-tooltip>
      </h3>
      
      <div class="mode-cards">
        <div 
          v-for="mode in filterModes" 
          :key="mode.value"
          :class="['mode-card', { active: selectedMode === mode.value }]"
          @click="selectMode(mode.value)"
        >
          <div class="mode-icon">
            <el-icon><component :is="mode.icon" /></el-icon>
          </div>
          <div class="mode-content">
            <h4 class="mode-title">{{ mode.label }}</h4>
            <p class="mode-desc">{{ mode.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 指定筛选条件 -->
    <div class="filter-conditions-section">
      <div class="section-header">
        <h3 class="section-title">指定筛选条件</h3>
        <el-button type="primary" size="small" @click="addCondition">
          <el-icon><Plus /></el-icon>
          添加筛选条件
        </el-button>
      </div>
      
      <div class="conditions-hint">
        已添加的筛选条件 ({{ filterConditions.length }})
      </div>

      <!-- 筛选条件列表 -->
      <div class="conditions-list">
        <div 
          v-for="(condition, index) in filterConditions" 
          :key="condition.id"
          class="condition-item"
        >
          <div class="condition-header">
            <el-icon class="condition-icon"><component :is="condition.icon" /></el-icon>
            <span class="condition-title">{{ condition.title }}</span>
            <div class="condition-actions">
              <el-button type="primary" size="small" text @click="editCondition(condition, index)">
                编辑
              </el-button>
              <el-button type="danger" size="small" text @click="removeCondition(index)">
                删除
              </el-button>
            </div>
          </div>
          
          <div class="condition-content">
            <!-- 指定时间段 -->
            <div v-if="condition.type === 'timeRange'" class="time-range-config">
              <div class="time-item">
                <label>开始日期</label>
                <el-date-picker
                  v-model="condition.startDate"
                  type="date"
                  placeholder="选择开始日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </div>
              <div class="time-item">
                <label>结束日期</label>
                <el-date-picker
                  v-model="condition.endDate"
                  type="date"
                  placeholder="选择结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </div>
            </div>

            <!-- 指定填报人 -->
            <div v-else-if="condition.type === 'reporter'" class="reporter-config">
              <label>选择填报人</label>
              <el-input
                v-model="condition.reporters"
                placeholder="张三, 李四, 王五"
                type="textarea"
                :rows="2"
              />
            </div>

            <!-- 指定填报部门 -->
            <div v-else-if="condition.type === 'department'" class="department-config">
              <label>选择部门</label>
              <el-select
                v-model="condition.departments"
                multiple
                placeholder="请选择部门"
                style="width: 100%;"
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option
                  v-for="dept in departmentOptions"
                  :key="dept.value"
                  :label="dept.label"
                  :value="dept.value"
                />
              </el-select>
            </div>

            <!-- 指定填报区域 -->
            <div v-else-if="condition.type === 'region'" class="region-config">
              <label>选择填报区域</label>
              <el-select
                v-model="condition.regions"
                multiple
                placeholder="请选择区域"
                style="width: 100%;"
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option
                  v-for="region in regionOptions"
                  :key="region.value"
                  :label="region.label"
                  :value="region.value"
                />
              </el-select>
            </div>

            <!-- 指定数据质量情况 -->
            <div v-else-if="condition.type === 'dataQuality'" class="quality-config">
              <div class="quality-item">
                <label>质量字段</label>
                <el-select v-model="condition.qualityField" placeholder="选择字段">
                  <el-option label="完整性" value="completeness" />
                  <el-option label="准确性" value="accuracy" />
                  <el-option label="一致性" value="consistency" />
                </el-select>
              </div>
              <div class="quality-item">
                <label>字段值</label>
                <el-input v-model="condition.qualityValue" placeholder="输入质量值" />
              </div>
            </div>

            <!-- 指定数据质量情况详情 -->
            <div v-else-if="condition.type === 'dataQualityDetail'" class="quality-detail-config">
              <div class="quality-detail-item">
                <label>质量字段</label>
                <el-select v-model="condition.detailQualityField" placeholder="选择字段">
                  <el-option label="完整性" value="completeness" />
                  <el-option label="准确性" value="accuracy" />
                  <el-option label="一致性" value="consistency" />
                </el-select>
              </div>
              <div class="quality-detail-item">
                <label>色值内容</label>
                <el-input v-model="condition.colorValue" placeholder="输入色值内容" />
              </div>
            </div>

            <!-- 指定数据来源 -->
            <div v-else-if="condition.type === 'dataSource'" class="source-config">
              <label>数据来源标志点</label>
              <el-input
                v-model="condition.dataSources"
                placeholder="系统A, 系统B, 外部接口"
                type="textarea"
                :rows="2"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filterConditions.length === 0" class="empty-conditions">
        <el-empty description="暂无筛选条件，点击上方按钮添加" />
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button @click="resetSettings">重置</el-button>
      <el-button type="primary" @click="saveSettings">保存设置</el-button>
    </div>

    <!-- 添加条件弹窗 -->
    <AddConditionDialog
      v-model="showAddDialog"
      @confirm="handleAddCondition"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Filter, 
  QuestionFilled, 
  Plus,
  Database,
  DocumentCopy,
  TrendCharts,
  Calendar,
  User,
  OfficeBuilding,
  Location,
  DataAnalysis,
  Document,
  Connection
} from '@element-plus/icons-vue'
import AddConditionDialog from './AddConditionDialog.vue'

interface Props {
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  height: 600
})

// 筛选模式选项
const filterModes = [
  {
    value: 'full',
    label: '全量数据',
    description: '比对全部数据内容',
    icon: 'Database'
  },
  {
    value: 'partial',
    label: '部分数据',
    description: '根据条件筛选部分数据进行比对',
    icon: 'DocumentCopy'
  },
  {
    value: 'incremental',
    label: '增量数据',
    description: '仅比对新增或变更的数据',
    icon: 'TrendCharts'
  }
]

// 选中的筛选模式
const selectedMode = ref('partial')

// 部门选项数据
const departmentOptions = [
  { label: '技术部', value: '技术部' },
  { label: '市场部', value: '市场部' },
  { label: '财务部', value: '财务部' },
  { label: '人事部', value: '人事部' },
  { label: '行政部', value: '行政部' },
  { label: '销售部', value: '销售部' },
  { label: '客服部', value: '客服部' },
  { label: '研发部', value: '研发部' },
  { label: '运营部', value: '运营部' },
  { label: '法务部', value: '法务部' },
  { label: '采购部', value: '采购部' },
  { label: '质量部', value: '质量部' }
]

// 区域选项数据
const regionOptions = [
  { label: '北京市', value: '北京市' },
  { label: '上海市', value: '上海市' },
  { label: '广州市', value: '广州市' },
  { label: '深圳市', value: '深圳市' },
  { label: '杭州市', value: '杭州市' },
  { label: '南京市', value: '南京市' },
  { label: '武汉市', value: '武汉市' },
  { label: '成都市', value: '成都市' },
  { label: '西安市', value: '西安市' },
  { label: '重庆市', value: '重庆市' },
  { label: '天津市', value: '天津市' },
  { label: '苏州市', value: '苏州市' },
  { label: '青岛市', value: '青岛市' },
  { label: '大连市', value: '大连市' },
  { label: '厦门市', value: '厦门市' }
]

// 筛选条件列表
const filterConditions = ref<any[]>([
  {
    id: 1,
    type: 'timeRange',
    title: '指定时间段',
    icon: 'Calendar',
    startDate: '2023-06-01',
    endDate: '2023-06-30'
  },
  {
    id: 2,
    type: 'reporter',
    title: '指定填报人',
    icon: 'User',
    reporters: '张三, 李四, 王五'
  },
  {
    id: 3,
    type: 'department',
    title: '指定填报部门',
    icon: 'OfficeBuilding',
    departments: ['技术部', '市场部', '财务部']
  },
  {
    id: 4,
    type: 'region',
    title: '指定填报区域',
    icon: 'Location',
    regions: ['北京市', '上海市', '广州市']
  },
  {
    id: 5,
    type: 'dataQuality',
    title: '指定数据质量情况',
    icon: 'DataAnalysis',
    qualityField: 'completeness',
    qualityValue: '完整性字段'
  },
  {
    id: 6,
    type: 'dataQualityDetail',
    title: '指定数据质量情况详情',
    icon: 'Document',
    detailQualityField: 'accuracy',
    colorValue: '输入色值内容'
  },
  {
    id: 7,
    type: 'dataSource',
    title: '指定数据来源',
    icon: 'Connection',
    dataSources: '系统A, 系统B, 外部接口'
  }
])

// 弹窗控制
const showAddDialog = ref(false)

// 选择筛选模式
const selectMode = (mode: string) => {
  selectedMode.value = mode
  ElMessage.success(`已选择${filterModes.find(m => m.value === mode)?.label}模式`)
}

// 添加筛选条件
const addCondition = () => {
  showAddDialog.value = true
}

// 处理添加条件
const handleAddCondition = (conditionData: any) => {
  const newCondition = {
    id: Date.now(),
    ...conditionData
  }
  filterConditions.value.push(newCondition)
  ElMessage.success('筛选条件添加成功')
}

// 编辑条件
const editCondition = (condition: any, index: number) => {
  ElMessage.info('编辑功能开发中...')
}

// 删除条件
const removeCondition = (index: number) => {
  filterConditions.value.splice(index, 1)
  ElMessage.success('筛选条件删除成功')
}

// 重置设置
const resetSettings = () => {
  selectedMode.value = 'partial'
  filterConditions.value = []
  ElMessage.success('设置已重置')
}

// 保存设置
const saveSettings = () => {
  const settings = {
    filterMode: selectedMode.value,
    conditions: filterConditions.value
  }
  console.log('保存的设置:', settings)
  ElMessage.success('设置保存成功')
}
</script>

<style scoped lang="scss">
.data-filter-settings {
  padding: 20px;
  height: 100%;
  overflow-y: auto;

  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;

    .help-icon {
      color: #909399;
      cursor: help;
    }
  }

  .filter-mode-section {
    margin-bottom: 32px;

    .mode-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 16px;

      .mode-card {
        display: flex;
        align-items: center;
        padding: 20px;
        border: 2px solid #e4e7ed;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        }

        &.active {
          border-color: #409eff;
          background-color: #f0f9ff;
        }

        .mode-icon {
          font-size: 32px;
          color: #409eff;
          margin-right: 16px;
        }

        .mode-content {
          .mode-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin: 0 0 4px 0;
          }

          .mode-desc {
            font-size: 14px;
            color: #606266;
            margin: 0;
          }
        }
      }
    }
  }

  .filter-conditions-section {
    margin-bottom: 32px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .conditions-hint {
      font-size: 14px;
      color: #606266;
      margin-bottom: 16px;
    }

    .conditions-list {
      .condition-item {
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        margin-bottom: 16px;
        overflow: hidden;

        .condition-header {
          display: flex;
          align-items: center;
          padding: 16px;
          background-color: #f8f9fa;
          border-bottom: 1px solid #e4e7ed;

          .condition-icon {
            font-size: 18px;
            color: #409eff;
            margin-right: 8px;
          }

          .condition-title {
            font-weight: 600;
            color: #303133;
            flex: 1;
          }

          .condition-actions {
            display: flex;
            gap: 8px;
          }
        }

        .condition-content {
          padding: 16px;

          .time-range-config,
          .quality-config,
          .quality-detail-config {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
          }

          .time-item,
          .quality-item,
          .quality-detail-item {
            label {
              display: block;
              font-size: 14px;
              color: #606266;
              margin-bottom: 8px;
            }
          }

          .reporter-config,
          .department-config,
          .region-config,
          .source-config {
            label {
              display: block;
              font-size: 14px;
              color: #606266;
              margin-bottom: 8px;
            }
          }
        }
      }
    }

    .empty-conditions {
      text-align: center;
      padding: 40px;
    }
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;
  }
}
</style>

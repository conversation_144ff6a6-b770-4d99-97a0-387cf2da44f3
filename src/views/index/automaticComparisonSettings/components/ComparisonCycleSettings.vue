<template>
  <div class="comparison-cycle-settings">
    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-button type="primary" @click="handleAddCycle">
        <el-icon><Plus /></el-icon>
        新增比对时间
      </el-button>
    </div>

    <!-- 表格 -->
    <TableV2
      ref="tableRef"
      :defaultTableData="tableData"
      :columns="columns"
      :enable-toolbar="false"
      :enable-own-button="true"
      :enable-selection="false"
      :enable-index="true"
      :height="tableHeight"
      :buttons="buttons"
      :loading="loading"
      @click-button="onTableClickButton"
    >
      <!-- 开始时间列自定义显示 -->
      <template #startTime="{ row }">
        <span>{{ formatTimeByType(row, 'start') }}</span>
      </template>

      <!-- 结束时间列自定义显示 -->
      <template #endTime="{ row }">
        <span>{{ formatTimeByType(row, 'end') }}</span>
      </template>

      <!-- 启用状态列自定义显示 -->
      <template #status="{ row }">
        <el-switch
          v-model="row.status"
          :active-value="true"
          :inactive-value="false"
          @change="handleStatusChange(row)"
        />
      </template>
    </TableV2>

    <!-- 分页组件 -->
    <Pagination
      :total="pagination.total"
      :current-page="pagination.page"
      :page-size="pagination.size"
      @current-change="onPaginationChange($event, 'page')"
      @size-change="onPaginationChange($event, 'size')"
    />

    <!-- 新增/编辑弹窗 -->
    <AddCycleDialog
      v-model="showAddDialog"
      :title="dialogTitle"
      :form-data="currentFormData"
      :mode="dialogMode"
      @confirm="handleDialogConfirm"
      @cancel="handleDialogCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import AddCycleDialog from './AddCycleDialog.vue'

interface Props {
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  height: 600
})

// 表格高度
const tableHeight = computed(() => props.height - 120)

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref<any[]>([])

// 分页配置
const pagination = reactive({
  total: 0,
  page: 1,
  size: 10
})

// 弹窗相关
const showAddDialog = ref(false)
const dialogMode = ref<'add' | 'edit'>('add')
const currentFormData = ref<any>({})

// 弹窗标题
const dialogTitle = computed(() => {
  return dialogMode.value === 'add' ? '新增比对时间' : '编辑比对时间'
})

// 表格列配置
const columns = ref([
  {
    prop: 'timeType',
    label: '时间类型',
    align: 'center'
  },
  {
    prop: 'startTime',
    label: '开始时间',
    align: 'center',
    slot: 'startTime'
  },
  {
    prop: 'endTime',
    label: '结束时间',
    align: 'center',
    slot: 'endTime'
  },
  {
    prop: 'status',
    label: '启用状态',
    align: 'center',
    slot: 'status'
  },
  {
    prop: 'updateTime',
    label: '更新时间',
    align: 'center'
  },
  {
    prop: 'creator',
    label: '创建人',
    align: 'center'
  }
])

// 表格操作按钮
const buttons = ref([
  {
    label: '编辑',
    code: 'edit',
    type: 'primary',
    size: 'small'
  },
  {
    label: '删除',
    code: 'delete',
    type: 'danger',
    size: 'small'
  }
])

// 全部模拟数据
const allMockData = [
  {
    id: 1,
    timeType: '每日',
    startTime: '2025-7-22 11:03:58',
    endTime: '2025-7-22 11:03:58',
    status: true,
    updateTime: '2025-7-22 11:03:58',
    creator: '张三'
  },
  {
    id: 2,
    timeType: '每周',
    startTime: '2025-7-22 11:03:58',
    endTime: '2025-7-22 11:03:58',
    status: true,
    updateTime: '2025-7-22 11:03:58',
    creator: '张三'
  },
  {
    id: 3,
    timeType: '每半月',
    startTime: '2025-7-22 11:03:58',
    endTime: '2025-7-22 11:03:58',
    status: true,
    updateTime: '2025-7-22 11:03:58',
    creator: '张三'
  },
  {
    id: 4,
    timeType: '每月',
    startTime: '2025-7-22 11:03:58',
    endTime: '2025-7-22 11:03:58',
    status: false,
    updateTime: '2025-7-22 11:03:58',
    creator: '张三'
  },
  {
    id: 5,
    timeType: '每季度',
    startTime: '2025-7-22 11:03:58',
    endTime: '2025-7-22 11:03:58',
    status: false,
    updateTime: '2025-7-22 11:03:58',
    creator: '张三'
  },
  {
    id: 6,
    timeType: '每半年',
    startTime: '2025-7-22 11:03:58',
    endTime: '2025-7-22 11:03:58',
    status: false,
    updateTime: '2025-7-22 11:03:58',
    creator: '张三'
  },
  {
    id: 7,
    timeType: '每年',
    startTime: '2025-7-22 11:03:58',
    endTime: '2025-7-22 11:03:58',
    status: false,
    updateTime: '2025-7-22 11:03:58',
    creator: '张三'
  },
  {
    id: 8,
    timeType: '自定义',
    startTime: '2025-7-22 11:03:58',
    endTime: '2025-7-22 11:03:58',
    status: false,
    updateTime: '2025-7-22 11:03:58',
    creator: '张三'
  }
]

// 初始化模拟数据
const initMockData = () => {
  tableData.value = [...allMockData]
  pagination.total = allMockData.length
}

// 新增比对时间
const handleAddCycle = () => {
  dialogMode.value = 'add'
  currentFormData.value = {}
  showAddDialog.value = true
}

// 表格按钮点击事件
const onTableClickButton = ({ btn, row }: any) => {
  if (btn.code === 'edit') {
    dialogMode.value = 'edit'
    currentFormData.value = { ...row }
    showAddDialog.value = true
  } else if (btn.code === 'delete') {
    handleDelete(row)
  }
}

// 删除操作
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除这条比对时间配置吗？', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const tableIndex = tableData.value.findIndex(item => item.id === row.id)
    const allDataIndex = allMockData.findIndex(item => item.id === row.id)

    if (tableIndex > -1) {
      tableData.value.splice(tableIndex, 1)
    }
    if (allDataIndex > -1) {
      allMockData.splice(allDataIndex, 1)
    }

    pagination.total = allMockData.length
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

// 状态切换
const handleStatusChange = (row: any) => {
  ElMessage.success(`已${row.status ? '启用' : '禁用'}该比对时间配置`)
}

// 分页变化
const onPaginationChange = (val: number, type: 'page' | 'size') => {
  if (type === 'page') {
    pagination.page = val
  } else {
    pagination.size = val
    pagination.page = 1 // 重置到第一页
  }

  // 重新加载当前页数据
  const startIndex = (pagination.page - 1) * pagination.size
  const endIndex = startIndex + pagination.size
  tableData.value = allMockData.slice(startIndex, endIndex)
}

// 弹窗确认
const handleDialogConfirm = (formData: any) => {
  if (dialogMode.value === 'add') {
    // 新增
    const newId = Math.max(...allMockData.map(item => item.id)) + 1
    const newItem = {
      ...formData,
      id: newId,
      updateTime: new Date().toLocaleString('zh-CN'),
      creator: '当前用户'
    }
    allMockData.unshift(newItem)
    tableData.value.unshift(newItem)
    pagination.total = allMockData.length
    ElMessage.success('新增成功')
  } else {
    // 编辑
    const tableIndex = tableData.value.findIndex(item => item.id === formData.id)
    const allDataIndex = allMockData.findIndex(item => item.id === formData.id)

    const updatedItem = {
      ...formData,
      updateTime: new Date().toLocaleString('zh-CN')
    }

    if (tableIndex > -1) {
      tableData.value[tableIndex] = updatedItem
    }
    if (allDataIndex > -1) {
      allMockData[allDataIndex] = updatedItem
    }
    ElMessage.success('编辑成功')
  }
  showAddDialog.value = false
}

// 弹窗取消
const handleDialogCancel = () => {
  showAddDialog.value = false
}

// 根据时间类型格式化时间显示
const formatTimeByType = (row: any, type: 'start' | 'end') => {
  const timeType = row.timeType

  switch (timeType) {
    case '每日':
      return type === 'start' ? `每日 ${row.startHour || 0}时` : `每日 ${row.endHour || 0}时`
    case '每周':
      const weekdayMap: Record<string, string> = {
        '1': '周一', '2': '周二', '3': '周三', '4': '周四',
        '5': '周五', '6': '周六', '7': '周日'
      }
      if (type === 'start') {
        const weekday = weekdayMap[row.startWeekday] || '周一'
        return `${weekday} ${row.startHour || 0}时`
      } else {
        const weekday = weekdayMap[row.endWeekday] || '周一'
        return `${weekday} ${row.endHour || 0}时`
      }
    case '每半月':
      if (type === 'start') {
        return `上半月${row.firstHalfStartDay || 1}日${row.firstHalfStartHour || 0}时，下半月${row.secondHalfStartDay || 16}日${row.secondHalfStartHour || 0}时`
      } else {
        return `上半月${row.firstHalfEndDay || 1}日${row.firstHalfEndHour || 0}时，下半月${row.secondHalfEndDay || 16}日${row.secondHalfEndHour || 0}时`
      }
    case '每月':
      return type === 'start'
        ? `每月${row.startMonthDay || 1}号 ${row.startHour || 0}时`
        : `每月${row.endMonthDay || 1}号 ${row.endHour || 0}时`
    case '每季度':
      if (type === 'start') {
        return `Q1:${row.q1StartDate || ''}, Q2:${row.q2StartDate || ''}, Q3:${row.q3StartDate || ''}, Q4:${row.q4StartDate || ''}`
      } else {
        return `Q1:${row.q1EndDate || ''}, Q2:${row.q2EndDate || ''}, Q3:${row.q3EndDate || ''}, Q4:${row.q4EndDate || ''}`
      }
    case '每半年':
      return type === 'start'
        ? `上半年:${row.firstHalfStartDate || ''}, 下半年:${row.secondHalfStartDate || ''}`
        : `上半年:${row.firstHalfEndDate || ''}, 下半年:${row.secondHalfEndDate || ''}`
    case '每年':
      return type === 'start'
        ? `每年${row.yearlyStartDate || ''} ${row.startHour || 0}时`
        : `每年${row.yearlyEndDate || ''} ${row.endHour || 0}时`
    case '自定义':
      return type === 'start' ? row.customStartTime || '' : row.customEndTime || ''
    default:
      return type === 'start' ? row.startTime : row.endTime
  }
}

onMounted(() => {
  initMockData()
})
</script>

<style scoped lang="scss">
.comparison-cycle-settings {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .action-bar {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>

<template>
  <div class="comparison-type-settings">
    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-button type="primary" @click="handleAddType">
        <el-icon><Plus /></el-icon>
        新增比对类型
      </el-button>
    </div>

    <!-- 表格 -->
    <TableV2
      ref="tableRef"
      :defaultTableData="tableData"
      :columns="columns"
      :enable-toolbar="false"
      :enable-own-button="true"
      :enable-selection="false"
      :enable-index="true"
      :height="tableHeight"
      :buttons="buttons"
      :loading="loading"
      @click-button="onTableClickButton"
    >
      <!-- 启用状态列自定义显示 -->
      <template #status="{ row }">
        <el-switch
          v-model="row.status"
          :active-value="true"
          :inactive-value="false"
          @change="handleStatusChange(row)"
        />
      </template>
    </TableV2>

    <!-- 分页组件 -->
    <Pagination
      :total="pagination.total"
      :current-page="pagination.page"
      :page-size="pagination.size"
      @current-change="onPaginationChange($event, 'page')"
      @size-change="onPaginationChange($event, 'size')"
    />

    <!-- 新增/编辑弹窗 -->
    <AddTypeDialog
      v-model="showAddDialog"
      :title="dialogTitle"
      :form-data="currentFormData"
      :mode="dialogMode"
      @confirm="handleDialogConfirm"
      @cancel="handleDialogCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import AddTypeDialog from './AddTypeDialog.vue'

interface Props {
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  height: 600
})

// 表格高度
const tableHeight = computed(() => props.height - 120)

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref<any[]>([])

// 分页配置
const pagination = reactive({
  total: 0,
  page: 1,
  size: 10
})

// 弹窗相关
const showAddDialog = ref(false)
const dialogMode = ref<'add' | 'edit'>('add')
const currentFormData = ref<any>({})

// 弹窗标题
const dialogTitle = computed(() => {
  return dialogMode.value === 'add' ? '新增比对类型' : '编辑比对类型'
})

// 表格列配置
const columns = ref([
  {
    prop: 'dataType',
    label: '数据类型',
    align: 'center'
  },
  {
    prop: 'compareRule',
    label: '比对规则',
    align: 'center'
  },
  {
    prop: 'status',
    label: '启用状态',
    align: 'center',
    slot: 'status'
  },
  {
    prop: 'updateTime',
    label: '更新时间',
    align: 'center'
  },
  {
    prop: 'creator',
    label: '创建人',
    align: 'center'
  }
])

// 表格操作按钮
const buttons = ref([
  {
    label: '编辑',
    code: 'edit',
    type: 'primary',
    size: 'small'
  },
  {
    label: '删除',
    code: 'delete',
    type: 'danger',
    size: 'small'
  }
])

// 全部模拟数据
const allMockData = [
  {
    id: 1,
    dataType: '短文本',
    compareRule: '完全匹配',
    status: true,
    updateTime: '2025-7-22 11:03:58',
    creator: '王建国'
  },
  {
    id: 2,
    dataType: '长文本',
    compareRule: '关键词匹配',
    status: true,
    updateTime: '2025-7-21 14:25:30',
    creator: '李明华'
  },
  {
    id: 3,
    dataType: '整数',
    compareRule: '完全匹配',
    status: true,
    updateTime: '2025-7-20 16:42:15',
    creator: '陈雪梅'
  },
  {
    id: 4,
    dataType: '小数',
    compareRule: '完全匹配',
    status: false,
    updateTime: '2025-7-19 09:18:42',
    creator: '刘志强'
  },
  {
    id: 5,
    dataType: '单选',
    compareRule: '完全匹配',
    status: true,
    updateTime: '2025-7-18 13:55:20',
    creator: '张晓丽'
  },
  {
    id: 6,
    dataType: '多选',
    compareRule: '关键词匹配',
    status: false,
    updateTime: '2025-7-17 10:30:45',
    creator: '赵文博'
  },
  {
    id: 7,
    dataType: '年月日时间',
    compareRule: '完全匹配',
    status: true,
    updateTime: '2025-7-16 15:22:18',
    creator: '孙海燕'
  },
  {
    id: 8,
    dataType: '日期时间',
    compareRule: '完全匹配',
    status: false,
    updateTime: '2025-7-15 08:45:33',
    creator: '马俊杰'
  },
  {
    id: 9,
    dataType: '居民身份证号',
    compareRule: '完全匹配',
    status: true,
    updateTime: '2025-7-14 12:10:25',
    creator: '周敏'
  },
  {
    id: 10,
    dataType: '护照号码',
    compareRule: '完全匹配',
    status: true,
    updateTime: '2025-7-13 17:35:50',
    creator: '吴亮'
  },
  {
    id: 11,
    dataType: '性别',
    compareRule: '完全匹配',
    status: true,
    updateTime: '2025-7-12 11:28:15',
    creator: '郑小红'
  },
  {
    id: 12,
    dataType: '电话号码',
    compareRule: '完全匹配',
    status: false,
    updateTime: '2025-7-11 14:52:40',
    creator: '何建平'
  },
  {
    id: 13,
    dataType: '邮箱',
    compareRule: '关键词匹配',
    status: true,
    updateTime: '2025-7-10 09:15:22',
    creator: '林晓峰'
  },
  {
    id: 14,
    dataType: '填报人',
    compareRule: '完全匹配',
    status: false,
    updateTime: '2025-7-09 16:38:45',
    creator: '黄丽娟'
  },
  {
    id: 15,
    dataType: '填报部门',
    compareRule: '关键词匹配',
    status: true,
    updateTime: '2025-7-08 13:20:10',
    creator: '谢志华'
  }
]

// 初始化模拟数据
const initMockData = () => {
  tableData.value = [...allMockData]
  pagination.total = allMockData.length
}

// 新增比对类型
const handleAddType = () => {
  dialogMode.value = 'add'
  currentFormData.value = {}
  showAddDialog.value = true
}

// 表格按钮点击事件
const onTableClickButton = ({ btn, row }: any) => {
  if (btn.code === 'edit') {
    dialogMode.value = 'edit'
    currentFormData.value = { ...row }
    showAddDialog.value = true
  } else if (btn.code === 'delete') {
    handleDelete(row)
  }
}

// 删除操作
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除这条比对类型配置吗？', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const tableIndex = tableData.value.findIndex(item => item.id === row.id)
    const allDataIndex = allMockData.findIndex(item => item.id === row.id)
    
    if (tableIndex > -1) {
      tableData.value.splice(tableIndex, 1)
    }
    if (allDataIndex > -1) {
      allMockData.splice(allDataIndex, 1)
    }
    
    pagination.total = allMockData.length
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

// 状态切换
const handleStatusChange = (row: any) => {
  ElMessage.success(`已${row.status ? '启用' : '禁用'}该比对类型配置`)
}

// 分页变化
const onPaginationChange = (val: number, type: 'page' | 'size') => {
  if (type === 'page') {
    pagination.page = val
  } else {
    pagination.size = val
    pagination.page = 1 // 重置到第一页
  }
  
  // 重新加载当前页数据
  const startIndex = (pagination.page - 1) * pagination.size
  const endIndex = startIndex + pagination.size
  tableData.value = allMockData.slice(startIndex, endIndex)
}

// 弹窗确认
const handleDialogConfirm = (formData: any) => {
  if (dialogMode.value === 'add') {
    // 新增
    const newId = Math.max(...allMockData.map(item => item.id)) + 1
    const currentUser = JSON.parse(localStorage.getItem('currentUserInfo') as string)?.name || '当前用户'
    const newItem = {
      ...formData,
      id: newId,
      updateTime: new Date().toLocaleString('zh-CN'),
      creator: currentUser
    }
    allMockData.unshift(newItem)
    tableData.value.unshift(newItem)
    pagination.total = allMockData.length
    ElMessage.success('新增成功')
  } else {
    // 编辑
    const tableIndex = tableData.value.findIndex(item => item.id === formData.id)
    const allDataIndex = allMockData.findIndex(item => item.id === formData.id)
    
    const updatedItem = {
      ...formData,
      updateTime: new Date().toLocaleString('zh-CN')
    }
    
    if (tableIndex > -1) {
      tableData.value[tableIndex] = updatedItem
    }
    if (allDataIndex > -1) {
      allMockData[allDataIndex] = updatedItem
    }
    ElMessage.success('编辑成功')
  }
  showAddDialog.value = false
}

// 弹窗取消
const handleDialogCancel = () => {
  showAddDialog.value = false
}

onMounted(() => {
  initMockData()
})
</script>

<style scoped lang="scss">
.comparison-type-settings {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .action-bar {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>

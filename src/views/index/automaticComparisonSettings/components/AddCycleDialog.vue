<template>
  <Dialog
    v-model="visible"
    :title="title"
    :enable-confirm="true"
    :loading="loading"
    width="800"
    confirm-text="确定"
    cancel-text="取消"
    @click-confirm="handleConfirm"
    @click-cancel="handleCancel"
    @close="handleClose"
  >
    <div class="add-cycle-dialog">
      <!-- 时间类型选择 -->
      <div class="time-type-selection">
        <div class="type-buttons">
          <el-button
            v-for="type in timeTypes"
            :key="type.value"
            :type="selectedType === type.value ? 'primary' : 'default'"
            @click="selectTimeType(type.value)"
            class="type-button"
          >
            {{ type.label }}
          </el-button>
        </div>
      </div>

      <!-- 配置表单 -->
      <div class="config-form" v-if="selectedType">
        <h4 class="form-title">设置({{ getSelectedTypeLabel() }})</h4>
        
        <!-- 每日配置 -->
        <div v-if="selectedType === 'daily'" class="form-content">
          <el-form :model="formData" label-width="150px">
            <el-form-item label="自动比对开始时间：">
              <div class="hour-config">
                <el-input-number
                  v-model="formData.startHour"
                  :min="0"
                  :max="23"
                  placeholder="小时"
                  style="width: 100px;"
                />
                <span style="margin-left: 8px;">时</span>
              </div>
            </el-form-item>
            <el-form-item label="自动比对结束时间：">
              <div class="hour-config">
                <el-input-number
                  v-model="formData.endHour"
                  :min="0"
                  :max="23"
                  placeholder="小时"
                  style="width: 100px;"
                />
                <span style="margin-left: 8px;">时</span>
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 每周配置 -->
        <div v-else-if="selectedType === 'weekly'" class="form-content">
          <el-form :model="formData" label-width="150px">
            <el-form-item label="自动比对开始时间：">
              <div class="week-config">
                <el-select v-model="formData.startWeekday" placeholder="请选择星期" style="width: 120px;">
                  <el-option label="周一" value="1" />
                  <el-option label="周二" value="2" />
                  <el-option label="周三" value="3" />
                  <el-option label="周四" value="4" />
                  <el-option label="周五" value="5" />
                  <el-option label="周六" value="6" />
                  <el-option label="周日" value="7" />
                </el-select>
                <el-input-number
                  v-model="formData.startHour"
                  :min="0"
                  :max="23"
                  placeholder="小时"
                  style="width: 100px; margin-left: 12px;"
                />
                <span style="margin-left: 8px;">时</span>
              </div>
            </el-form-item>
            <el-form-item label="自动比对结束时间：">
              <div class="week-config">
                <el-select v-model="formData.endWeekday" placeholder="请选择星期" style="width: 120px;">
                  <el-option label="周一" value="1" />
                  <el-option label="周二" value="2" />
                  <el-option label="周三" value="3" />
                  <el-option label="周四" value="4" />
                  <el-option label="周五" value="5" />
                  <el-option label="周六" value="6" />
                  <el-option label="周日" value="7" />
                </el-select>
                <el-input-number
                  v-model="formData.endHour"
                  :min="0"
                  :max="23"
                  placeholder="小时"
                  style="width: 100px; margin-left: 12px;"
                />
                <span style="margin-left: 8px;">时</span>
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 每半月配置 -->
        <div v-else-if="selectedType === 'halfMonth'" class="form-content">
          <el-form :model="formData" label-width="150px">
            <el-form-item label="自动比对开始时间：">
              <div class="half-month-config">
                <div class="period-item">
                  <span class="period-label">上半月</span>
                  <el-input-number
                    v-model="formData.firstHalfStartDay"
                    :min="1"
                    :max="15"
                    placeholder="日期"
                    style="width: 100px;"
                  />
                  <span style="margin-left: 8px;">日</span>
                  <el-input-number
                    v-model="formData.firstHalfStartHour"
                    :min="0"
                    :max="23"
                    placeholder="小时"
                    style="width: 100px; margin-left: 12px;"
                  />
                  <span style="margin-left: 8px;">时</span>
                </div>
                <div class="period-item">
                  <span class="period-label">下半月</span>
                  <el-input-number
                    v-model="formData.secondHalfStartDay"
                    :min="16"
                    :max="31"
                    placeholder="日期"
                    style="width: 100px;"
                  />
                  <span style="margin-left: 8px;">日</span>
                  <el-input-number
                    v-model="formData.secondHalfStartHour"
                    :min="0"
                    :max="23"
                    placeholder="小时"
                    style="width: 100px; margin-left: 12px;"
                  />
                  <span style="margin-left: 8px;">时</span>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="自动比对结束时间：">
              <div class="half-month-config">
                <div class="period-item">
                  <span class="period-label">上半月</span>
                  <el-input-number
                    v-model="formData.firstHalfEndDay"
                    :min="1"
                    :max="15"
                    placeholder="日期"
                    style="width: 100px;"
                  />
                  <span style="margin-left: 8px;">日</span>
                  <el-input-number
                    v-model="formData.firstHalfEndHour"
                    :min="0"
                    :max="23"
                    placeholder="小时"
                    style="width: 100px; margin-left: 12px;"
                  />
                  <span style="margin-left: 8px;">时</span>
                </div>
                <div class="period-item">
                  <span class="period-label">下半月</span>
                  <el-input-number
                    v-model="formData.secondHalfEndDay"
                    :min="16"
                    :max="31"
                    placeholder="日期"
                    style="width: 100px;"
                  />
                  <span style="margin-left: 8px;">日</span>
                  <el-input-number
                    v-model="formData.secondHalfEndHour"
                    :min="0"
                    :max="23"
                    placeholder="小时"
                    style="width: 100px; margin-left: 12px;"
                  />
                  <span style="margin-left: 8px;">时</span>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 每月配置 -->
        <div v-else-if="selectedType === 'monthly'" class="form-content">
          <el-form :model="formData" label-width="150px">
            <el-form-item label="自动比对开始时间：">
              <div class="monthly-config">
                <span>每月</span>
                <el-input-number
                  v-model="formData.startMonthDay"
                  :min="1"
                  :max="31"
                  placeholder="日期"
                  style="width: 100px; margin: 0 8px;"
                />
                <span>号</span>
                <el-input-number
                  v-model="formData.startHour"
                  :min="0"
                  :max="23"
                  placeholder="小时"
                  style="width: 100px; margin-left: 12px;"
                />
                <span style="margin-left: 8px;">时</span>
              </div>
            </el-form-item>
            <el-form-item label="自动比对结束时间：">
              <div class="monthly-config">
                <span>每月</span>
                <el-input-number
                  v-model="formData.endMonthDay"
                  :min="1"
                  :max="31"
                  placeholder="日期"
                  style="width: 100px; margin: 0 8px;"
                />
                <span>号</span>
                <el-input-number
                  v-model="formData.endHour"
                  :min="0"
                  :max="23"
                  placeholder="小时"
                  style="width: 100px; margin-left: 12px;"
                />
                <span style="margin-left: 8px;">时</span>
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 每季度配置 -->
        <div v-else-if="selectedType === 'quarterly'" class="form-content">
          <el-form :model="formData" label-width="150px">
            <el-form-item label="自动比对开始时间：">
              <div class="quarterly-config">
                <div class="quarter-row">
                  <div class="quarter-item">
                    <span class="quarter-label">第一季度</span>
                    <el-date-picker
                      v-model="formData.q1StartDate"
                      type="date"
                      placeholder="请选择日期"
                      format="MM-DD"
                      value-format="MM-DD"
                    />
                  </div>
                  <div class="quarter-item">
                    <span class="quarter-label">第二季度</span>
                    <el-date-picker
                      v-model="formData.q2StartDate"
                      type="date"
                      placeholder="请选择日期"
                      format="MM-DD"
                      value-format="MM-DD"
                    />
                  </div>
                </div>
                <div class="quarter-row">
                  <div class="quarter-item">
                    <span class="quarter-label">第三季度</span>
                    <el-date-picker
                      v-model="formData.q3StartDate"
                      type="date"
                      placeholder="请选择日期"
                      format="MM-DD"
                      value-format="MM-DD"
                    />
                  </div>
                  <div class="quarter-item">
                    <span class="quarter-label">第四季度</span>
                    <el-date-picker
                      v-model="formData.q4StartDate"
                      type="date"
                      placeholder="请选择日期"
                      format="MM-DD"
                      value-format="MM-DD"
                    />
                  </div>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="自动比对结束时间：">
              <div class="quarterly-config">
                <div class="quarter-row">
                  <div class="quarter-item">
                    <span class="quarter-label">第一季度</span>
                    <el-date-picker
                      v-model="formData.q1EndDate"
                      type="date"
                      placeholder="请选择日期"
                      format="MM-DD"
                      value-format="MM-DD"
                    />
                  </div>
                  <div class="quarter-item">
                    <span class="quarter-label">第二季度</span>
                    <el-date-picker
                      v-model="formData.q2EndDate"
                      type="date"
                      placeholder="请选择日期"
                      format="MM-DD"
                      value-format="MM-DD"
                    />
                  </div>
                </div>
                <div class="quarter-row">
                  <div class="quarter-item">
                    <span class="quarter-label">第三季度</span>
                    <el-date-picker
                      v-model="formData.q3EndDate"
                      type="date"
                      placeholder="请选择日期"
                      format="MM-DD"
                      value-format="MM-DD"
                    />
                  </div>
                  <div class="quarter-item">
                    <span class="quarter-label">第四季度</span>
                    <el-date-picker
                      v-model="formData.q4EndDate"
                      type="date"
                      placeholder="请选择日期"
                      format="MM-DD"
                      value-format="MM-DD"
                    />
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 每半年配置 -->
        <div v-else-if="selectedType === 'halfYear'" class="form-content">
          <el-form :model="formData" label-width="150px">
            <el-form-item label="自动比对开始时间：">
              <div class="half-year-config">
                <div class="period-item">
                  <span class="period-label">上半年</span>
                  <el-date-picker
                    v-model="formData.firstHalfStartDate"
                    type="date"
                    placeholder="请选择日期"
                    format="MM-DD"
                    value-format="MM-DD"
                  />
                </div>
                <div class="period-item">
                  <span class="period-label">下半年</span>
                  <el-date-picker
                    v-model="formData.secondHalfStartDate"
                    type="date"
                    placeholder="请选择日期"
                    format="MM-DD"
                    value-format="MM-DD"
                  />
                </div>
              </div>
            </el-form-item>
            <el-form-item label="自动比对结束时间：">
              <div class="half-year-config">
                <div class="period-item">
                  <span class="period-label">上半年</span>
                  <el-date-picker
                    v-model="formData.firstHalfEndDate"
                    type="date"
                    placeholder="请选择日期"
                    format="MM-DD"
                    value-format="MM-DD"
                  />
                </div>
                <div class="period-item">
                  <span class="period-label">下半年</span>
                  <el-date-picker
                    v-model="formData.secondHalfEndDate"
                    type="date"
                    placeholder="请选择日期"
                    format="MM-DD"
                    value-format="MM-DD"
                  />
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 每年配置 -->
        <div v-else-if="selectedType === 'yearly'" class="form-content">
          <el-form :model="formData" label-width="150px">
            <el-form-item label="自动比对开始时间：">
              <div class="yearly-config">
                <el-date-picker
                  v-model="formData.yearlyStartDate"
                  type="date"
                  placeholder="请选择开始日期"
                  format="MM-DD"
                  value-format="MM-DD"
                />
                <el-input-number
                  v-model="formData.startHour"
                  :min="0"
                  :max="23"
                  placeholder="小时"
                  style="width: 100px; margin-left: 12px;"
                />
                <span style="margin-left: 8px;">时</span>
              </div>
            </el-form-item>
            <el-form-item label="自动比对结束时间：">
              <div class="yearly-config">
                <el-date-picker
                  v-model="formData.yearlyEndDate"
                  type="date"
                  placeholder="请选择结束日期"
                  format="MM-DD"
                  value-format="MM-DD"
                />
                <el-input-number
                  v-model="formData.endHour"
                  :min="0"
                  :max="23"
                  placeholder="小时"
                  style="width: 100px; margin-left: 12px;"
                />
                <span style="margin-left: 8px;">时</span>
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 自定义配置 -->
        <div v-else-if="selectedType === 'custom'" class="form-content">
          <el-form :model="formData" label-width="150px">
            <el-form-item label="自动比对开始时间：">
              <el-date-picker
                v-model="formData.customStartTime"
                type="datetime"
                placeholder="请选择开始时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="自动比对结束时间：">
              <el-date-picker
                v-model="formData.customEndTime"
                type="datetime"
                placeholder="请选择结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue: boolean
  title: string
  formData?: any
  mode: 'add' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  formData: () => ({})
})

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel'])

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 加载状态
const loading = ref(false)

// 时间类型选项
const timeTypes = [
  { label: '每日', value: 'daily' },
  { label: '每周', value: 'weekly' },
  { label: '每半月', value: 'halfMonth' },
  { label: '每月', value: 'monthly' },
  { label: '每季度', value: 'quarterly' },
  { label: '每半年', value: 'halfYear' },
  { label: '每年', value: 'yearly' },
  { label: '自定义', value: 'custom' }
]

// 选中的时间类型
const selectedType = ref('')

// 表单数据
const formData = reactive({
  timeType: '',
  status: true,
  // 每日字段
  startHour: 0,
  endHour: 0,
  // 每周字段
  startWeekday: '',
  endWeekday: '',
  // 每半月字段
  firstHalfStartDay: 1,
  firstHalfStartHour: 0,
  secondHalfStartDay: 16,
  secondHalfStartHour: 0,
  firstHalfEndDay: 1,
  firstHalfEndHour: 0,
  secondHalfEndDay: 16,
  secondHalfEndHour: 0,
  // 每月字段
  startMonthDay: 1,
  endMonthDay: 1,
  // 每季度字段
  q1StartDate: '',
  q2StartDate: '',
  q3StartDate: '',
  q4StartDate: '',
  q1EndDate: '',
  q2EndDate: '',
  q3EndDate: '',
  q4EndDate: '',
  // 每半年字段
  firstHalfStartDate: '',
  secondHalfStartDate: '',
  firstHalfEndDate: '',
  secondHalfEndDate: '',
  // 每年字段
  yearlyStartDate: '',
  yearlyEndDate: '',
  // 自定义字段
  customStartTime: '',
  customEndTime: ''
})

// 选择时间类型
const selectTimeType = (type: string) => {
  selectedType.value = type
  formData.timeType = getSelectedTypeLabel()
}

// 获取选中类型的标签
const getSelectedTypeLabel = () => {
  const type = timeTypes.find(t => t.value === selectedType.value)
  return type ? type.label : ''
}

// 监听props变化，初始化表单数据
watch(() => props.formData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData, newData)
    // 根据timeType设置selectedType
    const type = timeTypes.find(t => t.label === newData.timeType)
    if (type) {
      selectedType.value = type.value
    }
  } else {
    // 重置表单
    Object.assign(formData, {
      timeType: '',
      status: true,
      startHour: 0,
      endHour: 0,
      startWeekday: '',
      endWeekday: '',
      firstHalfStartDay: 1,
      firstHalfStartHour: 0,
      secondHalfStartDay: 16,
      secondHalfStartHour: 0,
      firstHalfEndDay: 1,
      firstHalfEndHour: 0,
      secondHalfEndDay: 16,
      secondHalfEndHour: 0,
      startMonthDay: 1,
      endMonthDay: 1,
      q1StartDate: '',
      q2StartDate: '',
      q3StartDate: '',
      q4StartDate: '',
      q1EndDate: '',
      q2EndDate: '',
      q3EndDate: '',
      q4EndDate: '',
      firstHalfStartDate: '',
      secondHalfStartDate: '',
      firstHalfEndDate: '',
      secondHalfEndDate: '',
      yearlyStartDate: '',
      yearlyEndDate: '',
      customStartTime: '',
      customEndTime: ''
    })
    selectedType.value = ''
  }
}, { immediate: true })

// 确认
const handleConfirm = () => {
  if (!selectedType.value) {
    ElMessage.warning('请选择时间类型')
    return
  }
  
  // 构建提交数据
  const submitData = {
    ...formData,
    timeType: getSelectedTypeLabel(),
    // 根据不同类型构建显示用的开始和结束时间
    startTime: getDisplayStartTime(),
    endTime: getDisplayEndTime()
  }
  
  emit('confirm', submitData)
}

// 获取显示用的开始时间
const getDisplayStartTime = () => {
  const now = new Date()
  const dateStr = now.toLocaleDateString('zh-CN')

  switch (selectedType.value) {
    case 'daily':
      return `每日 ${formData.startHour}时`
    case 'weekly':
      const startWeekdayText = getWeekdayText(formData.startWeekday)
      return `${startWeekdayText} ${formData.startHour}时`
    case 'halfMonth':
      return `上半月${formData.firstHalfStartDay}日${formData.firstHalfStartHour}时，下半月${formData.secondHalfStartDay}日${formData.secondHalfStartHour}时`
    case 'monthly':
      return `每月${formData.startMonthDay}号 ${formData.startHour}时`
    case 'quarterly':
      return `Q1:${formData.q1StartDate}, Q2:${formData.q2StartDate}, Q3:${formData.q3StartDate}, Q4:${formData.q4StartDate}`
    case 'halfYear':
      return `上半年:${formData.firstHalfStartDate}, 下半年:${formData.secondHalfStartDate}`
    case 'yearly':
      return `每年${formData.yearlyStartDate} ${formData.startHour}时`
    case 'custom':
      return formData.customStartTime || ''
    default:
      return ''
  }
}

// 获取显示用的结束时间
const getDisplayEndTime = () => {
  const now = new Date()
  const dateStr = now.toLocaleDateString('zh-CN')

  switch (selectedType.value) {
    case 'daily':
      return `每日 ${formData.endHour}时`
    case 'weekly':
      const endWeekdayText = getWeekdayText(formData.endWeekday)
      return `${endWeekdayText} ${formData.endHour}时`
    case 'halfMonth':
      return `上半月${formData.firstHalfEndDay}日${formData.firstHalfEndHour}时，下半月${formData.secondHalfEndDay}日${formData.secondHalfEndHour}时`
    case 'monthly':
      return `每月${formData.endMonthDay}号 ${formData.endHour}时`
    case 'quarterly':
      return `Q1:${formData.q1EndDate}, Q2:${formData.q2EndDate}, Q3:${formData.q3EndDate}, Q4:${formData.q4EndDate}`
    case 'halfYear':
      return `上半年:${formData.firstHalfEndDate}, 下半年:${formData.secondHalfEndDate}`
    case 'yearly':
      return `每年${formData.yearlyEndDate} ${formData.endHour}时`
    case 'custom':
      return formData.customEndTime || ''
    default:
      return ''
  }
}

// 获取星期文本
const getWeekdayText = (weekday: string) => {
  const weekdayMap: Record<string, string> = {
    '1': '周一',
    '2': '周二',
    '3': '周三',
    '4': '周四',
    '5': '周五',
    '6': '周六',
    '7': '周日'
  }
  return weekdayMap[weekday] || ''
}

// 取消
const handleCancel = () => {
  emit('cancel')
}

// 关闭
const handleClose = () => {
  emit('update:modelValue', false)
}
</script>

<style scoped lang="scss">
.add-cycle-dialog {
  .time-type-selection {
    margin-bottom: 24px;
    
    .type-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      
      .type-button {
        min-width: 80px;
      }
    }
  }
  
  .config-form {
    .form-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
    
    .form-content {
      .hour-config,
      .week-config,
      .monthly-config,
      .yearly-config {
        display: flex;
        align-items: center;
      }

      .half-month-config,
      .half-year-config {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .period-item {
          display: flex;
          align-items: center;
          gap: 12px;

          .period-label {
            min-width: 60px;
            font-weight: 500;
          }
        }
      }

      .quarterly-config {
        .quarter-row {
          display: flex;
          gap: 24px;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .quarter-item {
            display: flex;
            align-items: center;
            gap: 12px;

            .quarter-label {
              min-width: 80px;
              font-weight: 500;
            }
          }
        }
      }
    }
  }
}
</style>

<script setup lang="ts" name="DataAnalysisEvaluation">
import { useVisualizedManageStore } from '@/stores/visualizedStore'
import { columns } from './config'
import ConnectTaskTable from './components/connectTaskTable.vue'
import UnBindTaskTable from './components/unBindTaskTable.vue'
import ForwardRecord from './components/forwardRecord.vue'
import BindTask from './components/bindTask.vue'
import TableChart from './components/tableChart.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/useUserStore'
import dayjs from 'dayjs'

const userStore = useUserStore()
const isRecommendWords = ref(false)

const showChart = ref(false) // 切换图表展示
function toggleChartShow() {
	showChart.value = !showChart.value
}
// Store
const visualizedStore = useVisualizedManageStore()
const showRules = ref(false)
const selectionLists = ['按时完成率', '平均延误时长', '任务积压率', '数据错误率']
// 搜索表单配置
const searchFormProp = ref([
	{
		label: '表格名称',
		prop: 'name',
		type: 'text',
		placeholder: '请输入表格名称',
	},
])
const searchForm = ref({ name: '' })

// 加载状态
const loading = ref(false)
const searchLoading = ref(false)
const paginationLoading = ref(false)

// 表格ref
const tableRef = ref()
const tableHeight = ref(0) // 表格高度
const currentRow = ref(null)

// 表格按钮配置
const buttons = [
	{ label: '取消关联任务', type: 'primary', code: 'unbind' },
	{ label: '关联任务', type: 'primary', code: 'connect' },
	{ label: '转发', type: 'primary', code: 'forward' },
]

// 关联/解绑
// 表格按钮配置
const connectUnbindDialog = ref(false)
const connectUnbindTitle = ref('')
const currentTab = ref<any>('ConnectTaskTable')
const btnArr = ref<any>([])
const isAllConnect = ref(false) // 判断是否批量关联
function connectUnbindChoose(num: Number, str: String) {
	btnArr.value = []
	taskRows.value = []
	if (str == '关联') {
		connectUnbindTitle.value = '请选择关联任务'
		connectUnbindDialog.value = true
		currentTab.value = ConnectTaskTable
		btnArr.value.push({ label: '关联任务', type: 'primary', code: 'connect' })
		if (num == 0) {
			isAllConnect.value = true
		} else if (num == 1) {
			isAllConnect.value = false
		}
	} else {
		connectUnbindTitle.value = '请选择解绑任务'
		connectUnbindDialog.value = true
		currentTab.value = UnBindTaskTable
		btnArr.value.push({ label: '取消关联', type: 'primary', code: 'unbind' })
		if (num == 0) {

		} else if (num == 1) {

		}
	}
}
function closeConnectUnbindLog() {
	connectUnbindDialog.value = false
}
const taskRows = ref<any>([])
function handleOk() {
	if (taskRows.value.length <= 0) {
		ElMessage.info("请选择任务")
		return
	}
	ElMessageBox.confirm(
		'确定关联选中任务吗?',
		'提示',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}
	)
		.then(() => {
			// 关联全部
			if (isAllConnect.value) {
				for (const item1 of currentTableData.value) {
					for (const item2 of taskRows.value) {
						item1.relatedTasks.push(item2)
					}
				}
			} else if (!isAllConnect.value && !isSingleConnect.value) {  // 关联选择
				if (selectionList.value.length > 0) {
					for (const item1 of selectionList.value) {
						for (const item2 of taskRows.value) {
							item1.relatedTasks.push(item2)
						}
					}
				}
			} else if (!isAllConnect.value && isSingleConnect.value) { // 单个关联
				for (const item of currentTableData.value) {
					if (item.id == currentRow.id) {
						for (const item1 of taskRows.value) {
							item.relatedTasks.push(item1)
						}
					}
				}
			}
			connectUnbindDialog.value = false
		})
		.catch(() => {

		})

}

// 转发
const forwardDialog = ref(false)
const forwardFormRef = ref(null)
const forwardForm: any = ref({
	name: '',
	remark: ''
})
const forwardFormProps = ref([
	{
		label: '接收人',
		prop: 'name',
		type: 'select',
		options: [
			{ value: '张人生', label: '张人生' },
			{ value: '胡宗昌', label: '胡宗昌' },
			{ value: '刘学义', label: '刘学义' },
		],
	},
	{ label: '转发备注', prop: 'remark', type: 'textarea' },
])
const submitType = ref(0)
// 弹框表单校验规则
const forwardFormRules = {
	name: [{ required: true, message: '请输入接收人', trigger: 'blur' }],
}
const curRows = reactive<any>({})
// 弹窗表单数据
function forwardChoose(num: Number, row: any) {
	if (num == 1 && selectionList.value.length < 0) {
		ElMessage.info("请选择数据")
		return
	}
	forwardDialog.value = true
	submitType.value = num
	if (!!row) {
		Object.assign(curRows, row)
	}
	// if (num == 0) { // 转发全部
	// 	forwardDialog.value = true
	// } else if (num == 1) {	// 转发选中
	// 	if (selectionList.value && selectionList.value.length > 0) {
	// 		selectionList.value
	// 	} else {
	// 		ElMessage.info("请选择数据")
	// 		return
	// 	}
	// } else {  // 转发单个
	// 	forwardDialog.value = true
	// 	// row
	// }
}
function handleForwardOk() {
	forwardFormRef.value.validate((valid: boolean) => {
		if (valid) {
			console.log(forwardForm.value, 'forwardForm...');
			console.log(curRows, 'curRows...');
			const date = new Date()
			// 当前时间
			const formatted = dayjs(date).format('YYYY-MM-DD HH:mm:ss')
			let forwardArr = []
			// 添加全部
			if (submitType.value == 0) {
				for (const item of currentTableData.value) {
					let obj = {
						id: item.id,
						name: item.name,
						forwardPerson: userStore.getUserInfo?.name || '',
						receivePerson: forwardForm.value.name,
						createTime: formatted
					}
					forwardArr.push(obj)
				}
			} else if (submitType.value == 1) {  // 添加选择的
				if (selectionList.value && selectionList.value.length > 0) {
					for (const item of selectionList.value) {
						let obj = {
							id: item.id,
							name: item.name,
							forwardPerson: userStore.getUserInfo?.name || '',
							receivePerson: forwardForm.value.name,
							createTime: formatted
						}
						forwardArr.push(obj)
					}
				} else {
					ElMessage.info("请选择数据")
					return
				}
			} else if (submitType.value == -1) {  // 添加单条
				let obj = {
					id: curRows.id,
					name: curRows.name,
					forwardPerson: userStore.getUserInfo?.name || '',
					receivePerson: forwardForm.value.name,
					createTime: formatted
				}
				forwardArr.push(obj)
			}
			visualizedStore.addForwardRecord(forwardArr)
		}
	})
}
function closeForwardLog() {

}

// 转发记录
const forwardRecordDialog = ref(false)
const forwardRef = ref(null)
function openForwardRecordForm() {
	forwardRecordDialog.value = true
	userStore.getUserInfo?.name
}


// 分页
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})

// 列表请求参数
const reqParams = reactive({
	name: '',
	skipCount: 0,
	maxResultCount: 10,
})

// 防抖函数
const debounce = (func: Function, delay: number) => {
	let timeoutId: NodeJS.Timeout
	return (...args: any[]) => {
		clearTimeout(timeoutId)
		timeoutId = setTimeout(() => func.apply(null, args), delay)
	}
}
// 实时搜索函数
const performSearch = async (keyword: string) => {
	try {
		// 注意：searchLoading.value = true 已在watch中设置

		// 模拟搜索延迟 - 先显示Loading再获取数据
		await new Promise((resolve) => setTimeout(resolve, 800))

		// 在Loading期间获取过滤数据
		const filteredData = visualizedStore.getFilteredEvaluations(keyword)

		// 更新分页总数
		pagination.total = filteredData.length
		pagination.page = 1 // 重置到第一页

		// 确保数据更新完成后再关闭Loading
		await nextTick()

		return filteredData
	} catch (error) {
		console.error('搜索失败:', error)
		ElMessage.error('搜索失败，请重试')
		return []
	} finally {
		searchLoading.value = false
	}
}

// 防抖搜索
const debouncedSearch = debounce(performSearch, 500)

// 存储当前显示的数据
const currentTableData = ref<any[]>([])

// 块高度变化事件时修改表格高度
const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 175
}

// 查询
const onSearch = async () => {
	try {
		// 设置Loading状态
		loading.value = true

		// 模拟查询延迟，增加真实性
		await new Promise((resolve) => setTimeout(resolve, 1200))

		pagination.page = 1
		reqParams.skipCount = 0
		reqParams.maxResultCount = pagination.size
		// 其他查询参数
		reqParams.name = searchForm.value.name
		tableRef.value.reload()
	} catch (error) {
		console.error('查询失败:', error)
		ElMessage.error('查询失败，请重试')
	} finally {
		loading.value = false
	}
}

// 表格操作点击事件, row 当前行数据
const isSingleConnect = ref(false)

const onTableClickButton = ({ row, btn }: any) => {
	currentRow.value = row
	if (btn.code === 'unbind') {
		showConnectTask(row)
	} else if (btn.code === 'connect') {
		connectUnbindTitle.value = '请选择关联任务'
		connectUnbindDialog.value = true
		currentTab.value = ConnectTaskTable
		btnArr.value.push({ label: '关联任务', type: 'primary', code: 'connect' })
	} else if (btn.code === 'forward') {
		forwardChoose(-1, row)
	}
}

// 分页事件
const onPaginationChange = async (val: any, type: any) => {
	try {
		paginationLoading.value = true

		// 模拟分页加载延迟
		await new Promise((resolve) => setTimeout(resolve, 600))

		if (type == 'page') {
			pagination.page = val
			reqParams.skipCount = (val - 1) * pagination.size
		} else {
			pagination.size = val
			reqParams.maxResultCount = pagination.size
			pagination.page = 1 // 改变每页大小时重置到第一页
		}
		getList()
		tableRef.value.reload()
	} catch (error) {
		console.error('分页操作失败:', error)
		ElMessage.error('分页操作失败，请重试')
	} finally {
		paginationLoading.value = false
	}
}

function getList() {
	let resultData = visualizedStore.getPaginatedVisualizedList(
		pagination.page,
		pagination.size,
		searchForm.value.name
	)
	currentTableData.value = resultData.data || []
	pagination.total = resultData.total || 0
}

const selectionList = ref([])
const selectionChange = (selection: any) => {
	selectionList.value = selection
}

// 根据选中的行显示关联的任务数
const bindDialogTable = ref(false)
const taskList = ref<any>([])
function showConnectTask(row: any) {
	taskRows.value = []
	bindDialogTable.value = true
	Object.assign(curRows, row)
	taskList.value = row.relatedTasks || []
}
function handleBindOk() {
	if (taskRows.value.length <= 0) {
		ElMessage.info('请选择任务')
		return
	}
	ElMessageBox.confirm(
		'确定取消关联吗?',
		'提示',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}
	)
		.then(() => {
			if (curRows.relatedTasks && curRows.relatedTasks.length > 0) {
				for (const item of currentTableData.value) {
					if (item.id == curRows.id) {
						let arrIds = taskRows.value.map(item1 => item1.id)
						item.relatedTasks = item.relatedTasks.filter(item2 => !arrIds.includes(item2.id))
					}
				}
			}
			bindDialogTable.value = false
		})
		.catch(() => {

		})


}

// 监听搜索表单变化，实现实时搜索
watch(
	() => searchForm.value.name,
	(newValue) => {
		// 立即设置Loading状态，然后执行搜索
		searchLoading.value = true
		debouncedSearch(newValue || '')
	},
	{ immediate: false }
)

// 初始化数据
onMounted(() => {
	visualizedStore.initializeData()
	// 初始化当前表格数据
	getList()
})
</script>

<template>
	<div class="data-analysis-evaluation">
		<Block title="数据分析评估" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
			<template #expand>
				<!-- 搜索 -->
				<div class="search">
					<Form :props="searchFormProp" v-model="searchForm" :column-count="2" :label-width="74"
						:enable-reset="false" confirm-text="查询" button-vertical="flowing" :loading="loading"
						@submit="onSearch"></Form>
				</div>
			</template>
			<!-- 功能按钮区域 -->
			<div class="function-buttons" style="margin: 0 0 16px 0; display: flex; gap: 12px">
				<el-dropdown class="mg-left-10 mg-right-10">
					<el-button type="success">
						批量关联<el-icon class="el-icon--right"><arrow-down /></el-icon>
					</el-button>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item @click="connectUnbindChoose(0, '关联')">关联全部
							</el-dropdown-item>
							<el-dropdown-item @click="connectUnbindChoose(1, '关联')">关联选择
							</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
				<el-dropdown class="mg-left-10 mg-right-10">
					<el-button type="success">
						批量解绑<el-icon class="el-icon--right"><arrow-down /></el-icon>
					</el-button>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item @click="connectUnbindChoose(0, '解绑')">解绑全部
							</el-dropdown-item>
							<el-dropdown-item @click="connectUnbindChoose(1, '绑联')">解绑选择
							</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
				<el-dropdown class="mg-left-10 mg-right-10">
					<el-button type="primary">
						可视化表格转发<el-icon class="el-icon--right"><arrow-down /></el-icon>
					</el-button>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item @click="forwardChoose(0)">转发全部
							</el-dropdown-item>
							<el-dropdown-item @click="forwardChoose(1)">转发选中
							</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
				<el-button type="primary" :loading="loading" @click="openForwardRecordForm">
					<i class="i-majesticons-inbox-out-line"></i>
					转发记录
				</el-button>
				<el-button type="primary">
					<i class="i-majesticons-trash-line"></i>
					转发权限设置
				</el-button>
				<el-button type="primary" @click="showRules = true">
					<i class="i-majesticons-image-line"></i>
					校验规则
				</el-button>
				<el-button type="primary" class="mg-left-10" @click="isRecommendWords = true">
					推荐词
				</el-button>
				<el-button type="primary" class="mg-left-10" @click="toggleChartShow"> 图表展示 </el-button>
			</div>

			<!-- 主内容区域 - 应用搜索Loading -->
			<div class="main-content" v-loading="searchLoading" element-loading-background="rgba(255, 255, 255, 0.8)">
				<template v-if="!showChart">
					<!-- 列表 -->
					<TableV2 ref="tableRef" :columns="columns" :stripe="false" :req-params="reqParams"
						@selection-change="selectionChange" :enable-selection="true" :enable-toolbar="false"
						:enable-own-button="false" :height="tableHeight" :buttons="buttons"
						:defaultTableData="currentTableData" :loading="loading || paginationLoading"
						@loading="loading = $event" @click-button="onTableClickButton">
						<template #relatedTasks="{ row }">
							<span class="click_words" @click="showConnectTask(row)">
								{{
									row.relatedTasks.length || '-'
								}}
							</span>
						</template>
						<template #forwardTimes="{ row }">
							<span class="click_words">
								{{ row.forwardTimes || '-' }}
							</span>
						</template>
					</TableV2>

					<!-- 分页 -->
					<Pagination :total="pagination.total" :current-page="pagination.page" :page-size="pagination.size"
						:disabled="paginationLoading || searchLoading" @current-change="onPaginationChange($event, 'page')"
						@size-change="onPaginationChange($event, 'size')"></Pagination>
				</template>

				<template v-else>
					<!-- 图表展示 -->
					  <TableChart />
				</template>
			</div>
		</Block>
		<!-- 关联任务选择 -->
		<Dialog v-model="connectUnbindDialog" :title="connectUnbindTitle" :destroy-on-close="true" :loading="loading"
			loading-text="保存中" @closed="closeConnectUnbindLog" @clickConfirm="handleOk">
			<component :is="currentTab" v-model:checkRows="taskRows" :selectRowId="selectionList" />
		</Dialog>
		<!-- 关联任务取消 -->
		<Dialog v-model="bindDialogTable" title="关联任务" :destroy-on-close="true" :loading="loading" loading-text="保存中"
			@closed="closeBindLog" @clickConfirm="handleBindOk" confirm-text="取消关联">
			<BindTask :taskList="taskList" v-model:checkRows="taskRows" />
		</Dialog>
		<!-- 转发弹窗 -->
		<Dialog v-model="forwardDialog" title="选择接收人" :destroy-on-close="true" :loading="loading"
			@closed="closeForwardLog" @clickConfirm="handleForwardOk" close-text="取消">
			<Form ref="forwardFormRef" v-model="forwardForm" :props="forwardFormProps" :rules="forwardFormRules"
				:enable-button="false">
			</Form>
		</Dialog>
		<!-- 转发记录 -->
		<Dialog v-model="forwardRecordDialog" title="转发记录" height="600" width="1000" :destroy-on-close="true"
			:loading="loading">
			<ForwardRecord ref="forwardRef" />
		</Dialog>
	</div>
</template>
<route>
	{
		meta: {
			title: '可视化表格',
		},
	}
</route>
<style scoped lang="scss">
.data-analysis-evaluation {
	.main-content {
		position: relative;
		min-height: 400px;

		// 主内容区域Loading样式优化
		:deep(.el-loading-mask) {
			background-color: rgba(255, 255, 255, 0.9);
			border-radius: 8px;
			z-index: 1000;
		}

		:deep(.el-loading-spinner) {
			margin-top: -50px;
		}

		:deep(.el-loading-text) {
			color: #409eff;
			font-size: 14px;
			margin-top: 10px;
		}
	}

	.pagination-container {
		position: relative;
		padding: 20px 0;

		// 分页禁用状态样式
		:deep(.el-pagination.is-disabled) {
			opacity: 0.6;
			pointer-events: none;
		}

		:deep(.el-pagination__total),
		:deep(.el-pagination__sizes),
		:deep(.el-pagination__jump) {
			color: #606266;
		}
	}
}

.click_words {
	color: #1b0ffc;
	cursor: pointer;
}

:deep(.el-table th.el-table__cell) {
	background-color: #ffffff !important;
}
</style>

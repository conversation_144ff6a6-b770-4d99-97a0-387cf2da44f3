<script setup lang="ts" name="TableChart">
import { computed, ref } from 'vue'

const option1 = computed(() => {
	const o = {
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow',
			},
		},
		grid: {
			left: '1%',
			right: '8%',
			bottom: '1%',
			top: '5%',
			containLabel: true,
		},
		xAxis: {
			type: 'value',
			name: '单位',  // 数值度量标签
			nameLocation: 'end',
			axisLabel: { margin: 10 },
			min: 0,
			max: 350,
			interval: 50,
			data: [0, 50, 100, 150, 200, 250, 300]
		},
		yAxis: {
			type: 'category',
			// name: '时间',  // 类别标签
			inverse: true,  // 确保最高值在顶部
			axisLine: { show: true },
			axisTick: { show: false },
			data: [  // 5个类别的名称（示例用）
			'2023-01', '2023-02', '2023-03', '2023-04', '2023-05'
			],
		},
		series: [
			{
				type: 'bar',
				// name: '单位',
				barWidth: 15,
				data: [340, 300, 260, 220, 200]
			},
		],
	}
	return o
})
const option2 = computed(() => {
	// if (!props.data?.db || props.data.db.length === 0) return null

	// const content = JSON.parse(props.data.content)
	// const {db} = props.data

	// const keys = db.map((d) => d[content.x])
	// const values = db.map((d) => d[content.y])

	const o = {
		tooltip: {
			trigger: 'axis',
		},
		grid: {
			left: '3%',
			right: '5%',
			bottom: '3%',
			top: '5%',
			containLabel: true,
		},
		xAxis: {
			type: 'category',
			boundaryGap: false,
			data: [1,2,3,4,5,6,7,8,9,10, 11, 12],
		},
		yAxis: {
			type: 'value',
		},
		series: [
			{
				type: 'line',
				stack: 'Total',
				name: '单位',
				data: [100,450,360,680,230,850,320,820,430,240, 540, 300],
			},
		],
	}
	return o
})

const option3 = computed(() => {
	const o = {
		tooltip: {
			trigger: 'axis',
		},
		grid: {
			left: '3%',
			right: '5%',
			bottom: '3%',
			top: '5%',
			containLabel: true,
		},
		xAxis: {
			type: 'category',
			boundaryGap: false,
			data: [1,2,3,4,5,6,7,8,9,10, 11, 12],
		},
		yAxis: {
			type: 'value',
		},
		series: [
			{
				type: 'line',
				stack: 'Total',
				name: '单位',
				data: [100,450,360,680,230,850,320,820,430,240, 540, 300],
			},
		],
	}
	return o
})


</script>

<template>
    <div class="chart-box">
		<div class="chart-line">
			<template v-if="option1">
				<h4>表格新增趋势</h4>
				<Charts width="100%" height="500px" :option="option1"></Charts>
			</template>
			<div v-else>
				<LoadingTransition text="正在生成图表..." align="left" />
			</div>
		</div>
		<div class="chart-line">
			<template v-if="option2">
				<h4>表格转发趋势</h4>
				<Charts width="100%" height="500px" :option="option2"></Charts>
			</template>
			<div v-else>
				<LoadingTransition text="正在生成图表..." align="left" />
			</div>
		</div>
		<div class="chart-line">
			<template v-if="option3">
				<h4>任务关联趋势</h4>
				<Charts width="100%" height="500px" :option="option3"></Charts>
			</template>
			<div v-else>
				<LoadingTransition text="正在生成图表..." align="left" />
			</div>
		</div>
	</div>
</template>
<style scoped lang="scss">
.chart-box{
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	width: 100%;
	height: 100%;
	.chart-line {
		width: 30%;
		h4 {
			font-size: 14px;
			padding: 10px 0;
			text-align: Left;
		}
	}
}

</style>
<template>
	<div
		class="business-table-data"
		:class="{
			'custom-table-stripe': pageStyles.showStripe,
			'custom-table-hover': pageStyles.showHover,
			'custom-table-border': pageStyles.showBorder,
			'custom-table-no-header': !pageStyles.showHeader,
		}"
	>
		<Block
			:title="t('pageTitle')"
			:enable-fixed-height="true"
			:fixed-height="true"
			:enable-expand-content="true"
			@content-expand="expendSearch"
			@height-changed="onBlockHeightChanged"
		>
			<template #topRight>
				<div class="top-actions">
					<el-button size="small" type="primary" @click="onClickAddWorkflow"
						>新建流程</el-button
					>
					<el-button size="small" type="primary" @click="onSearch" :loading="loading">{{
						t('query')
					}}</el-button>
					<el-button size="small" type="primary" @click="onKeepInStorage">{{
						t('saveFilter')
					}}</el-button>
				</div>
			</template>

			<template #expand>
				<!-- 搜索区域 -->
				<div class="search">
					<Form
						:props="searchFormProp"
						v-model="searchForm"
						:column-count="3"
						:label-width="0"
						:enable-reset="false"
						confirm-text="查询"
						button-vertical="flowing"
						:loading="searchLoading"
						@submit="onSearch"
					/>
				</div>
			</template>

			<!-- 操作按钮区域 -->
			<div class="action-buttons">
				<el-button size="small" type="primary" @click="onAdd">{{ t('add') }}</el-button>
				<el-button size="small" type="primary" @click="onDataSave">{{
					t('dataInfo')
				}}</el-button>
				<el-button size="small" type="primary" @click="onBatchImport">{{
					t('batchImport')
				}}</el-button>
				<el-button
					size="small"
					type="primary"
					@click="onBatchExport"
					:loading="exportLoading"
					>{{ t('batchExport') }}</el-button
				>
				<el-button size="small" type="primary" @click="onBatchEdit">{{
					t('batchEdit')
				}}</el-button>
				<el-button size="small" type="danger" @click="onBatchDelete">{{
					t('batchDelete')
				}}</el-button>
				<el-button size="small" type="primary" @click="onMultiDimensionClassify">{{
					t('multiDimensionClassify')
				}}</el-button>
				<el-dropdown @command="handleMoreActions" trigger="click">
					<el-button size="small" type="primary">
						{{ t('moreActions') }}
						<el-icon class="el-icon--right"><arrow-down /></el-icon>
					</el-button>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item command="businessRules"
								>业务数据规则</el-dropdown-item
							>
							<el-dropdown-item command="dynamicUpdate"
								>动态更新提醒</el-dropdown-item
							>
							<el-dropdown-item command="infoTemplate">信息化模板</el-dropdown-item>
							<el-dropdown-item command="statisticsAnalysis"
								>统计分析</el-dropdown-item
							>
							<el-dropdown-item command="infoRecommend"
								>信息关联推荐</el-dropdown-item
							>
							<el-dropdown-item command="historyRecord"
								>历史操作记录</el-dropdown-item
							>
							<el-dropdown-item command="permissionManage">权限管理</el-dropdown-item>
							<el-dropdown-item command="permissionRecord"
								>权限日志记录</el-dropdown-item
							>
							<el-dropdown-item command="backupRestore">备份与恢复</el-dropdown-item>
							<el-dropdown-item command="personalSettings"
								>个性化设置</el-dropdown-item
							>
							<el-dropdown-item command="infoSchedule">信息定时任务</el-dropdown-item>
							<el-dropdown-item command="customAngle">角色自定义</el-dropdown-item>
							<el-dropdown-item command="permissionRules"
								>权限继承规则</el-dropdown-item
							>
							<el-dropdown-item command="translationSettings"
								>翻译设置</el-dropdown-item
							>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
			</div>

			<!-- 主内容区域 -->
			<TableV2
				ref="tableRef"
				class="highlight-enabled-table"
				:key="`table-${pageStyles.tableResizable}-${pageStyles.showBorder}-${pageStyles.showStripe}-${pageStyles.showHeader}`"
				:defaultTableData="tableData"
				:columns="columns"
				:enable-toolbar="false"
				:enable-own-button="false"
				:enable-selection="true"
				:auto-height="true"
				:buttons="buttons"
				:loading="loading"
				:enableIndex="pageStyles.showIndex"
				:resizable="pageStyles.tableResizable"
				:border="pageStyles.showBorder"
				:show-header="pageStyles.showHeader"
				:style="{fontSize: pageStyles.fontSize}"
				@click-button="onTableClickButton"
				@selection-change="handleSelectionChange"
			>
				<!-- 所属分类列自定义显示 -->
				<template #所属分类="{row}">
					<span class="category-cell" style="cursor: pointer">{{ row.所属分类 }}</span>
				</template>

				<!-- 移动端展示列自定义显示 -->
				<template #移动端展示="{row}">
					<el-switch v-model="row.移动端展示" @change="onMobileDisplayChange(row)" />
				</template>

				<!-- 审核状态列自定义显示 -->
				<template #审核状态="{row}">
					<el-tag
						:type="
							row.审核状态 === '已审核' || row.审核状态 === 'Approved'
								? 'success'
								: row.审核状态 === '待审核' || row.审核状态 === 'Pending'
								? 'warning'
								: 'danger'
						"
						size="small"
					>
						{{ t(row.审核状态) }}
					</el-tag>
				</template>
			</TableV2>

			<!-- 分页 -->
			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				:disabled="paginationLoading"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			/>
		</Block>

		<!-- 新增/编辑对话框 -->
		<Dialog
			v-model="showDialog"
			:title="currentRow ? '编辑业务表数据' : '新增业务表数据'"
			:destroy-on-close="true"
			:loading="dialogLoading"
			loading-text="保存中"
			@closed=";(currentRow = null), (dialogForm = {})"
			@click-confirm="onDialogConfirm"
			width="800px"
		>
			<Form
				ref="dialogFormRef"
				v-model="dialogForm"
				:props="dialogFormProps"
				:rules="dialogFormRules"
				:enable-button="false"
			/>

			<!-- 数据信息输入框 -->
			<div class="data-info-section">
				<el-form-item label="数据信息" prop="数据信息" required>
					<el-input
						v-model="dialogForm.数据信息"
						type="textarea"
						placeholder="请输入数据信息"
						:rows="6"
						maxlength="500"
						show-word-limit
						resize="vertical"
					/>
				</el-form-item>
			</div>

			<!-- 审核流程区域 -->
			<div class="workflow-section">
				<div class="workflow-row">
					<label class="workflow-label">审核流程：</label>
					<el-select
						v-model="dialogForm.审核流程"
						placeholder="请选择"
						style="width: 200px"
					>
						<el-option label="标准流程" value="标准流程"></el-option>
						<el-option label="快速流程" value="快速流程"></el-option>
						<el-option label="严格流程" value="严格流程"></el-option>
					</el-select>

					<el-button type="primary" size="small" style="margin-left: 12px">
						新建流程
					</el-button>

					<el-button type="text" size="small" style="margin-left: 8px">
						查看流程详情
					</el-button>
				</div>
			</div>

			<!-- 自定义底部按钮 -->
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="showDialog = false">取消</el-button>
					<el-button type="primary" @click="onDialogConfirm" :loading="dialogLoading">
						确认
					</el-button>
				</div>
			</template>
		</Dialog>

		<!-- 批量导入弹窗 -->
		<Dialog
			v-model="batchImportDialogVisible"
			title="批量导入"
			width="700px"
			:destroy-on-close="true"
			:loading="importLoading"
			loading-text="导入中"
			confirm-text="上传"
			@click-confirm="uploadFile"
		>
			<div class="batch-import-content">
				<!-- 模板下载 -->
				<div class="template-download">
					<el-button type="primary" @click="downloadTemplate">
						<el-icon><Download /></el-icon>
						下载导入模板
					</el-button>
				</div>

				<!-- 文件上传 -->
				<div class="file-upload">
					<el-upload
						class="upload-demo"
						drag
						:file-list="importFileList"
						:before-upload="handleFileChange"
						:on-remove="handleFileRemove"
						:on-change="handleFileSelect"
						:limit="1"
						accept=".xlsx,.xls"
						:auto-upload="false"
					>
						<el-icon class="el-icon--upload"><UploadFilled /></el-icon>
						<div class="el-upload__text">点击或将文件拖拽到这里上传</div>
						<template #tip> </template>
					</el-upload>

					<!-- 显示选中的文件名 -->
					<div v-if="selectedFileName" class="selected-file">
						<div class="file-info">
							<el-icon><Document /></el-icon>
							<span class="file-name">{{ selectedFileName }}</span>
							<el-button type="text" size="small" @click="clearSelectedFile">
								<el-icon><Close /></el-icon>
							</el-button>
						</div>
					</div>
				</div>

				<!-- 导入说明 -->
				<div class="import-tips">
					<el-alert title="导入说明" type="info" :closable="false" show-icon>
						<div>
							<p>• 导入模式为业务表中已有数据更新，不支持新增数据</p>
							<p>• 请确保数据格式与模板一致，避免导入失败</p>
							<p>• 建议在非业务高峰期进行批量导入操作</p>
						</div>
					</el-alert>
				</div>
			</div>
		</Dialog>

		<!-- 导入须知弹窗 -->
		<el-dialog
			v-model="showImportRules"
			title="导入须知："
			width="500px"
			:show-close="true"
			:close-on-click-modal="true"
			class="import-rules-dialog"
		>
			<div class="import-rules-content">
				<div class="rule-item">
					<span class="rule-number">1.</span>
					<span class="rule-text">导入文件后缀名为xls或xlsx，文件大小为50M</span>
				</div>
				<div class="rule-item">
					<span class="rule-number">2.</span>
					<span class="rule-text">数据请勿放在合并的单元格中</span>
				</div>
				<div class="rule-item">
					<span class="rule-number">3.</span>
					<span class="rule-text"
						>单元格填入内容为多选项时，各选项之前用中文分号；隔开</span
					>
				</div>
				<div class="rule-item">
					<span class="rule-number">4.</span>
					<span class="rule-text">导入模板中标红的列为必填项，必填才能导入成功</span>
				</div>
			</div>
		</el-dialog>

		<!-- 批量修改弹窗 -->
		<Dialog
			v-model="batchEditDialogVisible"
			title="批量修改"
			width="600px"
			:destroy-on-close="true"
			:loading="batchEditLoading"
			loading-text="修改中"
			confirm-text="确定修改"
			@click-confirm="onBatchEditConfirm"
		>
			<div class="batch-edit-content">
				<div class="batch-edit-header">
					<el-icon><Edit /></el-icon>
					<span>批量修改 {{ selectedRows.length }} 条数据</span>
				</div>

				<div class="batch-edit-tip">
					<el-alert
						title="请选择要修改的字段并设置新值，未选择的字段将保持原值不变"
						type="info"
						:closable="false"
						show-icon
					/>
				</div>

				<div class="batch-edit-form">
					<!-- 所属分类 -->
					<div class="form-item">
						<el-checkbox v-model="batchEditFields.所属分类" class="field-checkbox">
							所属分类
						</el-checkbox>
						<el-select
							v-model="batchEditForm.所属分类"
							placeholder="请选择所属分类"
							:disabled="!batchEditFields.所属分类"
							class="field-input"
						>
							<el-option label="基础数据管理" value="基础数据管理" />
							<el-option label="业务数据管理" value="业务数据管理" />
							<el-option label="系统配置数据" value="系统配置数据" />
							<el-option label="用户权限数据" value="用户权限数据" />
							<el-option label="日志审计数据" value="日志审计数据" />
							<el-option label="统计分析数据" value="统计分析数据" />
						</el-select>
					</div>

					<!-- 所属标签 -->
					<div class="form-item">
						<el-checkbox v-model="batchEditFields.所属标签" class="field-checkbox">
							所属标签
						</el-checkbox>
						<el-select
							v-model="batchEditForm.所属标签"
							placeholder="请选择所属标签"
							:disabled="!batchEditFields.所属标签"
							class="field-input"
						>
							<el-option label="用户信息" value="用户信息" />
							<el-option label="组织架构" value="组织架构" />
							<el-option label="办事流程" value="办事流程" />
							<el-option label="表单模板" value="表单模板" />
							<el-option label="证照管理" value="证照管理" />
						</el-select>
					</div>

					<!-- 表描述 -->
					<div class="form-item">
						<el-checkbox v-model="batchEditFields.表描述" class="field-checkbox">
							表描述
						</el-checkbox>
						<el-input
							v-model="batchEditForm.表描述"
							placeholder="请输入表描述"
							:disabled="!batchEditFields.表描述"
							class="field-input"
						/>
					</div>

					<!-- 描述信息备份 -->
					<div class="form-item">
						<el-checkbox v-model="batchEditFields.描述信息备份" class="field-checkbox">
							描述信息备份
						</el-checkbox>
						<el-input
							v-model="batchEditForm.描述信息备份"
							placeholder="请输入描述信息备份"
							:disabled="!batchEditFields.描述信息备份"
							class="field-input"
						/>
					</div>

					<!-- 移动端展示 -->
					<div class="form-item">
						<el-checkbox v-model="batchEditFields.移动端展示" class="field-checkbox">
							移动端展示
						</el-checkbox>
						<el-switch
							v-model="batchEditForm.移动端展示"
							:disabled="!batchEditFields.移动端展示"
							class="field-input"
						/>
					</div>
				</div>
			</div>
		</Dialog>

		<!-- 多维度分类弹窗 -->
		<Dialog
			v-model="multiDimensionDialogVisible"
			title="多维度分类"
			width="900px"
			:destroy-on-close="true"
			:loading="multiDimensionLoading"
			loading-text="处理中"
			@click-confirm="onMultiDimensionDialogConfirm"
		>
			<div class="multi-dimension-content">
				<div class="multi-dimension-header">
					<el-button size="small" type="primary" @click="onMultiDimensionAdd"
						>新增</el-button
					>
				</div>

				<div class="multi-dimension-table">
					<el-table
						:data="multiDimensionList"
						border
						style="width: 100%"
						size="default"
						:loading="multiDimensionPaginationLoading"
						element-loading-text="加载中..."
					>
						<el-table-column prop="序号" label="序号" width="80" align="center" />
						<el-table-column
							prop="维度分类名称"
							label="维度分类名称"
							min-width="180"
							show-overflow-tooltip
						/>
						<el-table-column
							prop="维度说明"
							label="维度说明"
							min-width="300"
							show-overflow-tooltip
						/>
						<el-table-column label="操作" width="120" align="center" fixed="right">
							<template #default="{row}">
								<el-button
									type="primary"
									link
									size="small"
									@click="onMultiDimensionEdit(row)"
									>编辑</el-button
								>
								<el-button
									type="danger"
									link
									size="small"
									@click="onMultiDimensionDelete(row)"
									>删除</el-button
								>
							</template>
						</el-table-column>
					</el-table>
				</div>

				<!-- 分页组件 -->
				<div class="multi-dimension-pagination">
					<Pagination
						:total="multiDimensionPagination.total"
						:current-page="multiDimensionPagination.page"
						:page-size="multiDimensionPagination.size"
						:disabled="multiDimensionPaginationLoading"
						@current-change="onMultiDimensionPaginationChange($event, 'page')"
						@size-change="onMultiDimensionPaginationChange($event, 'size')"
					/>
				</div>
			</div>
		</Dialog>

		<!-- 新增多维度分类弹窗 -->
		<Dialog
			v-model="multiDimensionAddDialogVisible"
			:title="currentMultiDimensionRow ? '编辑多维度分类' : '新增多维度分类'"
			width="600px"
			:destroy-on-close="true"
			:loading="multiDimensionLoading"
			loading-text="保存中"
			@closed="onMultiDimensionDialogClosed"
			@click-confirm="onMultiDimensionConfirm"
		>
			<Form
				ref="multiDimensionFormRef"
				v-model="multiDimensionForm"
				:props="multiDimensionFormProps"
				:rules="multiDimensionFormRules"
				:enable-button="false"
				:label-width="120"
			/>
		</Dialog>

		<!-- 业务数据信息规则弹窗 -->
		<Dialog
			v-model="businessRulesDialogVisible"
			title="业务数据信息规则"
			width="700px"
			:destroy-on-close="true"
			@click-confirm="onBusinessRulesConfirm"
		>
			<div class="business-rules-content">
				<!-- 动态更新提醒 -->
				<div class="rule-section">
					<div class="rule-item">
						<label class="rule-label">动态更新提醒</label>
						<el-switch v-model="businessRulesForm.动态更新提醒" />
					</div>
				</div>

				<!-- 提醒类型 -->
				<div class="rule-section">
					<div class="rule-item">
						<label class="rule-label">提醒类型</label>
						<div class="checkbox-group">
							<el-checkbox v-model="businessRulesForm.审核通过提醒"
								>审核通过提醒</el-checkbox
							>
							<el-checkbox v-model="businessRulesForm.审核失败提醒"
								>审核失败提醒</el-checkbox
							>
							<el-checkbox v-model="businessRulesForm.信息删除提醒"
								>信息删除提醒</el-checkbox
							>
						</div>
					</div>
				</div>

				<!-- 提醒频率 -->
				<div class="rule-section">
					<div class="rule-item">
						<label class="rule-label">提醒频率</label>
						<el-select
							v-model="businessRulesForm.提醒频率"
							placeholder="请选择"
							style="width: 200px"
						>
							<el-option label="请选择（单选）" value=""></el-option>
							<el-option label="立即提醒" value="immediate"></el-option>
							<el-option label="每小时" value="hourly"></el-option>
							<el-option label="每天" value="daily"></el-option>
							<el-option label="每周" value="weekly"></el-option>
						</el-select>
					</div>
				</div>

				<!-- 提醒方式 -->
				<div class="rule-section">
					<div class="rule-item">
						<label class="rule-label">提醒方式</label>
						<div class="checkbox-group">
							<el-checkbox v-model="businessRulesForm.站内信">站内信</el-checkbox>
							<el-checkbox v-model="businessRulesForm.邮件证消息"
								>邮件证消息</el-checkbox
							>
							<el-checkbox v-model="businessRulesForm.邮件证DING消息"
								>邮件证DING消息</el-checkbox
							>
						</div>
					</div>
				</div>

				<!-- 实时更新提醒 -->
				<div class="rule-section">
					<div class="rule-item">
						<label class="rule-label">实时更新提醒</label>
						<el-switch v-model="businessRulesForm.实时更新提醒" />
					</div>
				</div>

				<!-- 信息共享 -->
				<div class="rule-section">
					<div class="rule-item">
						<label class="rule-label">信息共享</label>
						<el-switch v-model="businessRulesForm.信息共享" />
					</div>
				</div>

				<!-- 共享人员 -->
				<div class="rule-section">
					<div class="rule-item">
						<label class="rule-label">共享人员</label>
						<el-select
							v-model="businessRulesForm.共享人员"
							placeholder="请选择（多选）"
							style="width: 200px"
						>
							<el-option label="请选择（多选）" value=""></el-option>
							<el-option label="管理员" value="admin"></el-option>
							<el-option label="普通用户" value="user"></el-option>
							<el-option label="审核员" value="auditor"></el-option>
						</el-select>
					</div>
				</div>

				<!-- 权限 -->
				<div class="rule-section">
					<div class="rule-item">
						<label class="rule-label">权限</label>
						<el-select
							v-model="businessRulesForm.权限"
							placeholder="请选择可多选"
							style="width: 200px"
						>
							<el-option label="请选择可多选" value=""></el-option>
							<el-option label="查看权限" value="view"></el-option>
							<el-option label="编辑权限" value="edit"></el-option>
							<el-option label="删除权限" value="delete"></el-option>
							<el-option label="管理权限" value="manage"></el-option>
						</el-select>
					</div>
				</div>

				<!-- 信息定时更新 -->
				<div class="rule-section">
					<div class="rule-item">
						<label class="rule-label">信息定时更新</label>
						<el-switch v-model="businessRulesForm.信息定时更新" />
					</div>
				</div>

				<!-- 更新频率 -->
				<div class="rule-section">
					<div class="rule-item">
						<label class="rule-label">更新频率</label>
						<el-select
							v-model="businessRulesForm.更新频率"
							placeholder="请选择"
							style="width: 200px"
						>
							<el-option label="请选择" value=""></el-option>
							<el-option label="每小时" value="hourly"></el-option>
							<el-option label="每天" value="daily"></el-option>
							<el-option label="每周" value="weekly"></el-option>
							<el-option label="每月" value="monthly"></el-option>
						</el-select>
					</div>
				</div>

				<!-- 下次更新时间 -->
				<div class="rule-section">
					<div class="rule-item">
						<label class="rule-label">下次更新时间</label>
						<el-select
							v-model="businessRulesForm.下次更新时间"
							placeholder="请选择"
							style="width: 200px"
						>
							<el-option label="请选择" value=""></el-option>
							<el-option label="1小时后" value="1hour"></el-option>
							<el-option label="明天" value="tomorrow"></el-option>
							<el-option label="下周" value="nextweek"></el-option>
							<el-option label="下月" value="nextmonth"></el-option>
						</el-select>
					</div>
				</div>

				<!-- 导入格式类型设置 -->
				<div class="rule-section">
					<div class="rule-item">
						<label class="rule-label">导入格式类型设置</label>
						<div class="format-setting">
							<el-checkbox v-model="businessRulesForm.导入xlsx格式">xlsx</el-checkbox>
						</div>
					</div>
				</div>

				<!-- 导出格式类型设置 -->
				<div class="rule-section">
					<div class="rule-item">
						<label class="rule-label">导出格式类型设置</label>
						<div class="format-setting">
							<el-checkbox v-model="businessRulesForm.导出xlsx格式">xlsx</el-checkbox>
						</div>
					</div>
				</div>

				<!-- 信息加密存储 -->
				<div class="rule-section">
					<div class="rule-item">
						<label class="rule-label">信息加密存储</label>
						<el-switch v-model="businessRulesForm.信息加密存储" />
					</div>
				</div>

				<!-- 加密算法 -->
				<div class="rule-section">
					<div class="rule-item">
						<label class="rule-label">加密算法</label>
						<el-select
							v-model="businessRulesForm.加密算法"
							placeholder="请选择"
							style="width: 200px"
						>
							<el-option label="请选择" value=""></el-option>
							<el-option label="AES-256" value="aes256"></el-option>
							<el-option label="RSA-2048" value="rsa2048"></el-option>
							<el-option label="DES" value="des"></el-option>
						</el-select>
					</div>
				</div>

				<!-- 秘钥存储方式 -->
				<div class="rule-section">
					<div class="rule-item">
						<label class="rule-label">秘钥存储方式</label>
						<div class="radio-group">
							<el-radio-group v-model="businessRulesForm.秘钥存储方式">
								<el-radio value="manual">手动输入秘钥</el-radio>
								<el-radio value="auto">自动生成秘钥</el-radio>
							</el-radio-group>
						</div>
					</div>
				</div>

				<!-- 加密秘钥 -->
				<div class="rule-section" v-if="businessRulesForm.秘钥存储方式 === 'manual'">
					<div class="rule-item">
						<label class="rule-label">加密秘钥</label>
						<el-input
							v-model="businessRulesForm.加密秘钥"
							placeholder="请输入"
							style="width: 200px"
						/>
					</div>
				</div>

				<!-- 加密文件存储路径 -->
				<div class="rule-section">
					<div class="rule-item">
						<label class="rule-label">加密文件存储路径</label>
						<el-input
							v-model="businessRulesForm.加密文件存储路径"
							placeholder="请输入"
							style="width: 200px"
						/>
					</div>
				</div>
			</div>
		</Dialog>

		<!-- 审核管理弹窗 -->
		<Dialog
			v-model="auditManageDialogVisible"
			title="信息审核"
			width="500px"
			:destroy-on-close="true"
		>
			<div class="audit-dialog-content">
				<!-- 温馨提示 -->
				<div class="audit-tip">
					<span class="tip-text"
						>温馨提示：审核通过审核意见可见可填项，审核驳回，审核意见必须填</span
					>
				</div>

				<!-- 审核表单 -->
				<el-form
					ref="auditFormRef"
					:model="auditForm"
					label-width="100px"
					class="audit-form"
				>
					<!-- 申核结果 -->
					<el-form-item label="申核结果：" required>
						<el-radio-group v-model="auditForm.result">
							<el-radio value="pass">通过</el-radio>
							<el-radio value="reject">驳回</el-radio>
						</el-radio-group>
					</el-form-item>

					<!-- 申核意见 -->
					<el-form-item label="申核意见：" required>
						<el-input
							v-model="auditForm.opinion"
							type="textarea"
							placeholder="请输入"
							:rows="4"
							maxlength="500"
							show-word-limit
						/>
					</el-form-item>
				</el-form>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="handleAuditCancel">取消</el-button>
					<el-button type="primary" @click="handleAuditConfirm">确认</el-button>
				</div>
			</template>
		</Dialog>

		<!-- 权限继承规则弹窗 -->
		<Dialog
			v-model="permissionRulesDialogVisible"
			title="权限继承规则"
			width="600px"
			:destroy-on-close="true"
			:visible-confirm-button="true"
			:visible-cancel-button="true"
			confirm-text="确定"
			cancel-text="取消"
			@click-confirm="handlePermissionRulesConfirm"
			@click-cancel="handlePermissionRulesCancel"
		>
			<div class="permission-rules-content">
				<!-- 基础继承规则 -->
				<div class="rule-section">
					<h3 class="section-title">基础继承规则</h3>
					<div class="radio-group">
						<el-radio-group v-model="permissionRulesForm.基础继承规则">
							<el-radio value="上级角色自动继承下级角色权限"
								>上级角色自动继承下级角色权限</el-radio
							>
							<el-radio value="下级角色自动继承上级角色部分权限"
								>下级角色自动继承上级角色部分权限</el-radio
							>
						</el-radio-group>
					</div>
				</div>
			</div>
		</Dialog>

		<!-- 角色自定义弹窗 -->
		<Dialog
			v-model="roleCustomDialogVisible"
			title="角色自定义"
			width="1200px"
			:destroy-on-close="true"
			:visible-confirm-button="true"
			:visible-cancel-button="true"
			confirm-text="确定"
			cancel-text="取消"
			@click-confirm="handleRoleCustomConfirm"
			@click-cancel="handleRoleCustomCancel"
		>
			<div class="role-custom-content">
				<div class="role-custom-header">
					<el-button type="primary" @click="handleRoleAdd">新增</el-button>
				</div>

				<div class="role-custom-table">
					<el-table :data="roleList" border style="width: 100%">
						<el-table-column prop="序号" label="序号" width="80" align="center" />
						<el-table-column prop="角色名称" label="角色名称" width="150" />
						<el-table-column prop="权限显示" label="权限" />
						<el-table-column label="操作" width="150" align="center">
							<template #default="{row}">
								<el-button type="primary" size="small" @click="handleRoleEdit(row)"
									>修改</el-button
								>
								<el-button type="danger" size="small" @click="handleRoleDelete(row)"
									>删除</el-button
								>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</div>
		</Dialog>

		<!-- 角色新增弹窗 -->
		<Dialog
			v-model="roleAddDialogVisible"
			title="角色新增"
			width="500px"
			:destroy-on-close="true"
			:visible-confirm-button="true"
			:visible-cancel-button="true"
			confirm-text="确定"
			cancel-text="取消"
			@click-confirm="handleRoleAddConfirm"
			@click-cancel="handleRoleAddCancel"
		>
			<div class="role-add-content">
				<el-form :model="roleCustomForm" label-width="100px">
					<el-form-item label="角色名称" required>
						<el-input
							v-model="roleCustomForm.角色名称"
							placeholder="请输入"
							style="width: 100%"
						/>
					</el-form-item>

					<el-form-item label="上级角色">
						<el-select
							v-model="roleCustomForm.上级角色"
							placeholder="请选择（角色名称，申请次数）"
							style="width: 100%"
						>
							<el-option
								v-for="option in parentRoleOptions"
								:key="option.value"
								:label="option.label"
								:value="option.value"
							/>
						</el-select>
					</el-form-item>

					<el-form-item label="权限" required>
						<el-select
							v-model="roleCustomForm.权限"
							placeholder="请选择（多选）"
							style="width: 100%"
							multiple
						>
							<el-option
								v-for="option in permissionOptions.filter(
									(opt) => opt.value !== ''
								)"
								:key="option.value"
								:label="option.label"
								:value="option.value"
							/>
						</el-select>
					</el-form-item>
				</el-form>
			</div>
		</Dialog>

		<!-- 角色编辑弹窗 -->
		<Dialog
			v-model="roleEditDialogVisible"
			title="角色编辑"
			width="500px"
			:destroy-on-close="true"
			:visible-confirm-button="true"
			:visible-cancel-button="true"
			confirm-text="确定"
			cancel-text="取消"
			@click-confirm="handleRoleEditConfirm"
			@click-cancel="handleRoleEditCancel"
		>
			<div class="role-edit-content">
				<el-form :model="roleEditForm" label-width="100px">
					<el-form-item label="角色名称" required>
						<el-input
							v-model="roleEditForm.角色名称"
							placeholder="请输入角色名称"
							style="width: 100%"
						/>
					</el-form-item>

					<el-form-item label="上级角色">
						<el-select
							v-model="roleEditForm.上级角色"
							placeholder="请选择（角色名称，申请次数）"
							style="width: 100%"
						>
							<el-option
								v-for="option in parentRoleOptions"
								:key="option.value"
								:label="option.label"
								:value="option.value"
							/>
						</el-select>
					</el-form-item>

					<el-form-item label="权限" required>
						<el-select
							v-model="roleEditForm.权限"
							placeholder="请选择权限（多选）"
							style="width: 100%"
							multiple
						>
							<el-option
								v-for="option in permissionOptions.filter(
									(opt) => opt.value !== ''
								)"
								:key="option.value"
								:label="option.label"
								:value="option.value"
							/>
						</el-select>
					</el-form-item>
				</el-form>
			</div>
		</Dialog>

		<!-- 数据信息管理弹窗 -->
		<Dialog
			v-model="dataInfoDialogVisible"
			title="数据信息管理"
			width="1200px"
			:destroy-on-close="true"
			:loading="dataInfoLoading"
			loading-text="加载中"
			@closed="onDataInfoDialogClosed"
		>
			<div class="data-info-content">
				<!-- 数据统计概览 -->
				<div class="data-overview">
					<div class="overview-cards">
						<div class="overview-card">
							<div class="card-icon">
								<el-icon><Document /></el-icon>
							</div>
							<div class="card-content">
								<div class="card-title">总数据量</div>
								<div class="card-value">{{ dataInfoStats.total }}</div>
							</div>
						</div>
						<div class="overview-card">
							<div class="card-icon">
								<el-icon><Check /></el-icon>
							</div>
							<div class="card-content">
								<div class="card-title">有效数据</div>
								<div class="card-value">{{ dataInfoStats.valid }}</div>
							</div>
						</div>
						<div class="overview-card">
							<div class="card-icon">
								<el-icon><Warning /></el-icon>
							</div>
							<div class="card-content">
								<div class="card-title">异常数据</div>
								<div class="card-value">{{ dataInfoStats.invalid }}</div>
							</div>
						</div>
						<div class="overview-card">
							<div class="card-icon">
								<el-icon><Clock /></el-icon>
							</div>
							<div class="card-content">
								<div class="card-title">最后更新</div>
								<div class="card-value">{{ dataInfoStats.lastUpdate }}</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 数据操作工具栏 -->
				<div class="data-toolbar">
					<div class="toolbar-left">
						<el-button
							size="small"
							type="primary"
							@click="onDataRefresh"
							:loading="dataInfoLoading"
						>
							<el-icon><Refresh /></el-icon>
							刷新数据
						</el-button>
						<el-button
							size="small"
							type="success"
							@click="onDataExport"
							:loading="dataExportLoading"
						>
							<el-icon><Download /></el-icon>
							导出数据
						</el-button>
						<el-button
							size="small"
							type="warning"
							@click="onDataValidate"
							:loading="dataValidateLoading"
						>
							<el-icon><CircleCheck /></el-icon>
							数据校验
						</el-button>
					</div>
					<div class="toolbar-right">
						<el-input
							v-model="dataSearchKeyword"
							placeholder="搜索数据..."
							size="small"
							style="width: 200px"
							clearable
							@input="onDataSearch"
						>
							<template #prefix>
								<el-icon><Search /></el-icon>
							</template>
						</el-input>
					</div>
				</div>

				<!-- 数据表格 -->
				<div class="data-table">
					<el-table
						:data="dataInfoList"
						border
						style="width: 100%"
						size="default"
						:loading="dataInfoTableLoading"
						element-loading-text="加载数据中..."
						max-height="400"
					>
						<el-table-column type="selection" width="55" align="center" />
						<el-table-column prop="id" label="ID" width="80" align="center" />
						<el-table-column
							prop="name"
							label="数据名称"
							min-width="150"
							show-overflow-tooltip
						/>
						<el-table-column prop="type" label="数据类型" width="100" align="center">
							<template #default="{row}">
								<el-tag :type="getDataTypeTagType(row.type)" size="small">{{
									row.type
								}}</el-tag>
							</template>
						</el-table-column>
						<el-table-column prop="status" label="状态" width="100" align="center">
							<template #default="{row}">
								<el-tag :type="getDataStatusTagType(row.status)" size="small">{{
									row.status
								}}</el-tag>
							</template>
						</el-table-column>
						<el-table-column prop="size" label="数据大小" width="100" align="center" />
						<el-table-column
							prop="createTime"
							label="创建时间"
							width="160"
							align="center"
						/>
						<el-table-column
							prop="updateTime"
							label="更新时间"
							width="160"
							align="center"
						/>
						<el-table-column label="操作" width="180" align="center" fixed="right">
							<template #default="{row}">
								<el-button type="primary" link size="small" @click="onDataView(row)"
									>查看</el-button
								>
								<el-button type="warning" link size="small" @click="onDataEdit(row)"
									>编辑</el-button
								>
								<el-button
									type="danger"
									link
									size="small"
									@click="onDataDelete(row)"
									>删除</el-button
								>
							</template>
						</el-table-column>
					</el-table>
				</div>

				<!-- 分页组件 -->
				<div class="data-pagination">
					<Pagination
						:total="dataInfoPagination.total"
						:current-page="dataInfoPagination.page"
						:page-size="dataInfoPagination.size"
						:disabled="dataInfoTableLoading"
						@current-change="onDataInfoPaginationChange($event, 'page')"
						@size-change="onDataInfoPaginationChange($event, 'size')"
					/>
				</div>
			</div>
		</Dialog>

		<!-- 动态更新提醒弹窗 -->
		<Dialog
			v-model="dynamicUpdateDialogVisible"
			title="动态更新提醒"
			width="700px"
			:destroy-on-close="true"
			:visible-confirm-button="true"
			:visible-cancel-button="true"
			confirm-text="确定"
			cancel-text="取消"
			@click-confirm="dynamicUpdateDialogVisible = false"
			@click-cancel="dynamicUpdateDialogVisible = false"
		>
			<div class="dynamic-update-content">
				<el-table
					:data="dynamicUpdateList"
					style="width: 100%"
					:header-cell-style="{background: '#f5f7fa', color: '#606266'}"
				>
					<el-table-column prop="序号" label="序号" width="80" align="center" />
					<el-table-column prop="提醒内容" label="提醒内容" min-width="200" />
					<el-table-column prop="提醒时间" label="提醒时间" width="120" align="center" />
					<el-table-column prop="操作人" label="操作人" width="100" align="center" />
				</el-table>
			</div>
		</Dialog>

		<!-- 信息定时任务弹窗 -->
		<Dialog
			v-model="infoScheduleDialogVisible"
			title="信息定时任务"
			width="600px"
			:destroy-on-close="true"
			:visible-confirm-button="true"
			:visible-cancel-button="true"
			confirm-text="确定"
			cancel-text="取消"
			@click-confirm="handleInfoScheduleConfirm"
			@click-cancel="handleInfoScheduleCancel"
		>
			<div class="info-schedule-content">
				<el-form
					:model="infoScheduleForm"
					label-width="120px"
					:rules="infoScheduleFormRules"
					ref="infoScheduleFormRef"
				>
					<el-form-item label="任务名称" prop="任务名称" required>
						<el-input
							v-model="infoScheduleForm.任务名称"
							placeholder="请输入"
							style="width: 100%"
						/>
					</el-form-item>

					<el-form-item label="任务描述内容" prop="任务描述内容" required>
						<el-input
							v-model="infoScheduleForm.任务描述内容"
							placeholder="请输入"
							style="width: 100%"
						/>
					</el-form-item>

					<el-form-item label="任务开始时间" prop="任务开始时间" required>
						<el-date-picker
							v-model="infoScheduleForm.任务开始时间"
							type="datetime"
							placeholder="请输入"
							style="width: 100%"
							format="YYYY-MM-DD HH:mm:ss"
							value-format="YYYY-MM-DD HH:mm:ss"
						/>
					</el-form-item>

					<el-form-item label="执行规则" prop="执行规则" required>
						<el-select
							v-model="infoScheduleForm.执行规则"
							placeholder="每天"
							style="width: 100%"
						>
							<el-option label="每天" value="每天" />
							<el-option label="每周" value="每周" />
							<el-option label="每月" value="每月" />
							<el-option label="每年" value="每年" />
						</el-select>
					</el-form-item>

					<el-form-item label="任务结束时间" prop="任务结束时间" required>
						<div class="end-time-section">
							<div class="radio-group">
								<el-radio-group v-model="infoScheduleForm.结束时间类型">
									<el-radio value="无期限">无期限</el-radio>
									<el-radio value="结束时间">结束时间</el-radio>
								</el-radio-group>
							</div>

							<div
								v-if="infoScheduleForm.结束时间类型 === '结束时间'"
								class="end-time-input"
							>
								<el-date-picker
									v-model="infoScheduleForm.结束时间"
									type="datetime"
									placeholder="请选择，具体到年月日时分秒"
									style="width: 100%"
									format="YYYY-MM-DD HH:mm:ss"
									value-format="YYYY-MM-DD HH:mm:ss"
								/>
							</div>

							<div class="warning-tip">
								<span style="color: #e6a23c">选中无期限，结束时间感</span>
							</div>
						</div>
					</el-form-item>
				</el-form>
			</div>
		</Dialog>

		<!-- 信息个性化设置弹窗 -->
		<Dialog
			v-model="personalSettingsDialogVisible"
			title="信息个性化设置"
			width="600px"
			:destroy-on-close="true"
			:visible-confirm-button="true"
			:visible-cancel-button="true"
			confirm-text="确定"
			cancel-text="取消"
			@click-confirm="handlePersonalSettingsConfirm"
			@click-cancel="handlePersonalSettingsCancel"
		>
			<div class="personal-settings-content">
				<!-- 界面设计管理 -->
				<div class="setting-section">
					<div class="setting-item">
						<label class="setting-label">界面设计管理</label>
						<div class="setting-control">
							<span class="control-label">字体大小</span>
							<el-input
								v-model="personalSettingsForm.字体大小"
								placeholder="请输入"
								style="width: 120px; margin-left: 12px"
								size="small"
							/>
						</div>
					</div>
				</div>

				<!-- 操作管理 -->
				<div class="setting-section">
					<div class="setting-item">
						<label class="setting-label">操作管理</label>
						<div class="setting-control">
							<span class="control-label">是否支持用户调整表格宽度</span>
							<el-switch
								v-model="personalSettingsForm.支持调整表格宽度"
								style="margin-left: 12px"
								@change="onTableWidthSettingChange"
							/>
							<span class="switch-label">{{
								personalSettingsForm.支持调整表格宽度 ? '允许' : '禁止'
							}}</span>
						</div>
					</div>
				</div>

				<!-- 数据个性化展示 -->
				<div class="setting-section">
					<div class="setting-item">
						<label class="setting-label">数据个性化展示</label>
						<div class="setting-control">
							<el-select
								v-model="personalSettingsForm.数据展示方式"
								placeholder="多选：网格线、行号、列标"
								style="width: 100%"
								multiple
								@change="onDataDisplayChange"
							>
								<el-option label="网格线" value="网格线" />
								<el-option label="行号" value="行号" />
								<el-option label="列标" value="列标" />
								<el-option label="斑马纹" value="斑马纹" />
								<el-option label="悬停高亮" value="悬停高亮" />
							</el-select>
						</div>
					</div>
				</div>
			</div>
		</Dialog>

		<!-- 翻译设置弹窗 -->
		<Dialog
			v-model="translationSettingsDialogVisible"
			:title="t('translationSettings')"
			width="500px"
			:destroy-on-close="true"
			:visible-confirm-button="true"
			:visible-cancel-button="true"
			:confirm-text="t('confirm')"
			:cancel-text="t('cancel')"
			@click-confirm="handleTranslationSettingsConfirm"
			@click-cancel="handleTranslationSettingsCancel"
		>
			<div class="translation-settings-content">
				<!-- 翻译设置开关 -->
				<div class="setting-section">
					<div class="setting-item">
						<label class="setting-label">{{ t('translationEnable') }}</label>
						<div class="setting-control">
							<el-switch
								v-model="translationSettingsForm.翻译设置"
								style="margin-left: 12px"
							/>
						</div>
					</div>
				</div>

				<!-- 默认翻译语言 -->
				<div class="setting-section">
					<div class="setting-item">
						<label class="setting-label">{{ t('defaultLanguage') }}</label>
						<div class="setting-control">
							<el-select
								v-model="translationSettingsForm.默认翻译语言"
								placeholder="请选择"
								style="width: 200px; margin-left: 12px"
							>
								<el-option label="中文" value="zh" />
								<el-option label="English" value="en" />
							</el-select>
						</div>
					</div>
				</div>
			</div>
		</Dialog>

		<!-- 信息锁定弹窗 -->
		<el-dialog
			v-model="infoLockDialogVisible"
			title="信息锁定"
			width="400px"
			:show-close="true"
			:close-on-click-modal="false"
			class="info-lock-dialog"
		>
			<div class="info-lock-content">
				<div class="lock-message">
					<p>锁定后，信息将无法修改。是否确定锁定？</p>
				</div>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="handleInfoLockCancel">取消</el-button>
					<el-button type="primary" @click="handleInfoLockConfirm">确定</el-button>
				</div>
			</template>
		</el-dialog>

		<!-- 信息解锁弹窗 -->
		<el-dialog
			v-model="infoUnlockDialogVisible"
			title="信息解锁"
			width="400px"
			:show-close="true"
			:close-on-click-modal="false"
			class="info-unlock-dialog"
		>
			<div class="info-unlock-content">
				<div class="unlock-message">
					<p>解锁后，信息可以进行修改。是否确定解锁？</p>
				</div>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="handleInfoLockCancel">取消</el-button>
					<el-button type="primary" @click="handleInfoUnlockConfirm">确定</el-button>
				</div>
			</template>
		</el-dialog>

		<!-- 版本注释弹窗 -->
		<el-dialog
			v-model="versionNotesDialogVisible"
			title="版本注释"
			width="900px"
			:show-close="true"
			:close-on-click-modal="false"
			class="version-notes-dialog"
		>
			<div class="version-notes-content">
				<el-form ref="versionNotesFormRef" :model="{versionNotesList}" class="version-form">
					<div class="version-table">
						<el-table
							:data="versionNotesList"
							border
							style="width: 100%"
							size="default"
						>
							<el-table-column prop="序号" label="序号" width="80" align="center" />
							<el-table-column
								prop="版本名称"
								label="版本名称"
								width="180"
								align="center"
							>
								<template #default="{row, $index}">
									<el-form-item
										:prop="`versionNotesList.${$index}.版本名称`"
										:rules="versionNotesRules.版本名称"
										class="table-form-item"
									>
										<el-input
											v-model="row.版本名称"
											placeholder="如：V1.0.0"
											size="small"
											maxlength="20"
										/>
									</el-form-item>
								</template>
							</el-table-column>
							<el-table-column prop="注释" label="注释" min-width="350">
								<template #default="{row, $index}">
									<el-form-item
										:prop="`versionNotesList.${$index}.注释`"
										:rules="versionNotesRules.注释"
										class="table-form-item"
									>
										<el-input
											v-model="row.注释"
											type="textarea"
											placeholder="请详细描述本版本的更新内容，至少10个字符"
											size="small"
											:rows="2"
											maxlength="200"
											show-word-limit
										/>
									</el-form-item>
								</template>
							</el-table-column>
							<el-table-column label="操作" width="120" align="center">
								<template #default="{row, $index}">
									<el-button
										type="primary"
										link
										size="small"
										@click="handleVersionAdd"
										>添加</el-button
									>
									<el-button
										type="danger"
										link
										size="small"
										@click="handleVersionDelete($index)"
										:disabled="versionNotesList.length <= 1"
									>
										删除
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</el-form>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="handleVersionNotesCancel">取消</el-button>
					<el-button type="primary" @click="handleVersionNotesConfirm">确定</el-button>
				</div>
			</template>
		</el-dialog>

		<!-- 批注弹窗 -->
		<el-dialog
			v-model="commentsDialogVisible"
			title="批注"
			width="900px"
			:show-close="true"
			:close-on-click-modal="false"
			class="comments-dialog"
		>
			<div class="comments-content">
				<!-- 新增按钮 -->
				<div class="add-comment-section">
					<el-button
						type="primary"
						size="default"
						@click="handleCommentsAdd"
						style="margin-bottom: 16px"
					>
						添加批注
					</el-button>
				</div>

				<!-- 批注表格 -->
				<div class="comments-table">
					<el-table :data="commentsList" border style="width: 100%" size="default">
						<el-table-column prop="序号" label="序号" width="80" align="center" />
						<el-table-column
							prop="选中内容"
							label="选中内容"
							width="180"
							align="center"
						>
							<template #default="{row}">
								<el-select
									v-model="row.选中内容"
									placeholder="请选择"
									style="width: 100%"
								>
									<el-option label="字段描述" value="字段描述" />
									<el-option label="字段名称" value="字段名称" />
									<el-option label="所属分类" value="所属分类" />
									<el-option label="所属标签" value="所属标签" />
								</el-select>
							</template>
						</el-table-column>
						<el-table-column prop="批注内容" label="批注内容" min-width="350">
							<template #default="{row}">
								<el-input
									v-model="row.批注内容"
									type="textarea"
									:rows="3"
									placeholder="请输入"
									style="width: 100%"
								/>
							</template>
						</el-table-column>
						<el-table-column prop="操作" label="操作" width="100" align="center">
							<template #default="{$index}">
								<el-button
									type="danger"
									link
									size="small"
									@click="handleCommentsDelete($index)"
									:disabled="commentsList.length <= 1"
								>
									删除
								</el-button>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="handleCommentsCancel">取消</el-button>
					<el-button type="primary" @click="handleCommentsConfirm">确认</el-button>
				</div>
			</template>
		</el-dialog>

		<!-- 突出显示设置弹窗 -->
		<el-dialog
			v-model="highlightSettingsDialogVisible"
			title="列颜色设置"
			width="600px"
			:show-close="true"
			:close-on-click-modal="false"
			class="highlight-settings-dialog"
		>
			<div class="highlight-settings-content">
				<!-- 新增按钮 -->
				<div class="add-rule-section">
					<el-button
						type="primary"
						size="default"
						@click="handleHighlightRuleAdd"
						style="margin-bottom: 16px"
					>
						<el-icon><Plus /></el-icon>
						新增列颜色设置
					</el-button>
				</div>

				<!-- 突出显示规则列表 -->
				<div class="highlight-rules-section">
					<div
						v-for="(rule, index) in highlightSettingsForm.highlightRules"
						:key="rule.id"
						class="rule-item"
					>
						<div class="rule-row">
							<div class="field-section">
								<div class="field-group">
									<label class="field-label">选择列</label>
									<el-select
										v-model="rule.fieldName"
										placeholder="请选择表格列"
										style="width: 200px"
										size="default"
									>
										<el-option
											v-for="column in availableColumns"
											:key="column.prop"
											:label="column.label"
											:value="column.prop"
										/>
									</el-select>
								</div>
								<div class="color-group">
									<label class="color-label">字体颜色</label>
									<el-color-picker
										v-model="rule.fontColor"
										size="default"
										:predefine="[
											'#ff4d4f',
											'#52c41a',
											'#1890ff',
											'#722ed1',
											'#fa8c16',
											'#13c2c2',
											'#eb2f96',
											'#f5222d',
											'#a0d911',
											'#fadb14',
										]"
										@change="onColorChange(rule)"
									/>
								</div>
								<div class="action-group">
									<el-button
										type="danger"
										size="default"
										@click="handleHighlightRuleDelete(index)"
										:disabled="highlightSettingsForm.highlightRules.length <= 1"
									>
										<el-icon><Delete /></el-icon>
										删除
									</el-button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="handleHighlightSettingsCancel">取消</el-button>
					<el-button type="primary" @click="handleHighlightSettingsConfirm"
						>确认</el-button
					>
				</div>
			</template>
		</el-dialog>

		<!-- 数据备份与恢复弹窗 -->
		<Dialog
			v-model="backupRestoreDialogVisible"
			title="数据备份与恢复"
			width="700px"
			:destroy-on-close="true"
			:loading="backupRestoreLoading"
			loading-text="保存中"
			@click-confirm="onBackupRestoreConfirm"
		>
			<div class="backup-restore-content">
				<!-- 业务表备份规则 -->
				<div class="backup-section">
					<h3 class="section-title">业务表备份规则</h3>

					<div class="form-item">
						<label class="form-label">数据备份开始时间</label>
						<el-date-picker
							v-model="backupRestoreForm.数据备份开始时间"
							type="datetime"
							placeholder="请选择日期"
							style="width: 100%"
							format="YYYY-MM-DD HH:mm:ss"
							value-format="YYYY-MM-DD HH:mm:ss"
						/>
					</div>

					<div class="form-item">
						<label class="form-label">全量备份频率</label>
						<el-select
							v-model="backupRestoreForm.全量备份频率"
							placeholder="下拉选择：每小时、每日、每周、每月"
							style="width: 100%"
						>
							<el-option label="每小时" value="每小时" />
							<el-option label="每日" value="每日" />
							<el-option label="每周" value="每周" />
							<el-option label="每月" value="每月" />
						</el-select>
					</div>

					<div class="form-item">
						<label class="form-label">增量备份频率</label>
						<el-select
							v-model="backupRestoreForm.增量备份频率"
							placeholder="下拉选择：每小时、每日、每周、每月"
							style="width: 100%"
						>
							<el-option label="每小时" value="每小时" />
							<el-option label="每日" value="每日" />
							<el-option label="每周" value="每周" />
							<el-option label="每月" value="每月" />
						</el-select>
					</div>

					<div class="form-item">
						<label class="form-label">备份数据清理策略</label>
						<el-select
							v-model="backupRestoreForm.备份数据清理策略"
							placeholder="下拉选择：保留30天、保留60天、保留120天"
							style="width: 100%"
						>
							<el-option label="保留30天" value="保留30天" />
							<el-option label="保留60天" value="保留60天" />
							<el-option label="保留120天" value="保留120天" />
						</el-select>
					</div>
				</div>

				<!-- 业务表恢复启动规则 -->
				<div class="restore-section">
					<h3 class="section-title">业务表恢复启动规则</h3>

					<div class="form-item">
						<label class="form-label">请选择恢复至指定时期</label>
						<el-date-picker
							v-model="backupRestoreForm.恢复至指定时期"
							type="datetime"
							placeholder="请选择日期"
							style="width: 100%"
							format="YYYY-MM-DD HH:mm:ss"
							value-format="YYYY-MM-DD HH:mm:ss"
						/>
					</div>

					<div class="form-item">
						<label class="form-label">请选择恢复执行时间</label>
						<el-date-picker
							v-model="backupRestoreForm.恢复执行时间"
							type="datetime"
							placeholder="请选择日期"
							style="width: 100%"
							format="YYYY-MM-DD HH:mm:ss"
							value-format="YYYY-MM-DD HH:mm:ss"
						/>
					</div>
				</div>
			</div>
		</Dialog>

		<!-- 历史操作记录弹窗 -->
		<Dialog
			v-model="historyRecordDialogVisible"
			title="历史操作记录"
			width="800px"
			:destroy-on-close="true"
			:visible-confirm-button="true"
			:visible-cancel-button="true"
			confirm-text="确定"
			cancel-text="取消"
			@click-confirm="historyRecordDialogVisible = false"
			@click-cancel="historyRecordDialogVisible = false"
		>
			<div class="history-record-content">
				<el-tabs v-model="historyRecordActiveTab" class="history-tabs">
					<!-- 操作历史记录标签页 -->
					<el-tab-pane label="操作历史记录" name="操作历史记录">
						<div class="operation-history-table">
							<el-table
								:data="
									operationHistoryList.slice(
										(operationHistoryPagination.page - 1) *
											operationHistoryPagination.size,
										operationHistoryPagination.page *
											operationHistoryPagination.size
									)
								"
								border
								style="width: 100%"
								:header-cell-style="{background: '#f5f7fa', color: '#606266'}"
								:loading="historyRecordLoading"
								element-loading-text="加载中..."
							>
								<el-table-column
									prop="序号"
									label="序号"
									width="80"
									align="center"
								/>
								<el-table-column prop="操作内容" label="操作内容" min-width="200" />
								<el-table-column
									prop="操作人"
									label="操作人"
									width="120"
									align="center"
								/>
								<el-table-column
									prop="操作时间"
									label="操作时间"
									width="160"
									align="center"
								/>
							</el-table>

							<!-- 分页 -->
							<div class="operation-history-pagination">
								<Pagination
									:total="operationHistoryPagination.total"
									:current-page="operationHistoryPagination.page"
									:page-size="operationHistoryPagination.size"
									:disabled="historyRecordLoading"
									@current-change="
										(page) => (operationHistoryPagination.page = page)
									"
									@size-change="
										(size) => {
											operationHistoryPagination.size = size
											operationHistoryPagination.page = 1
										}
									"
								/>
							</div>
						</div>
					</el-tab-pane>

					<!-- 授权历史记录标签页 -->
					<el-tab-pane label="授权历史记录" name="授权历史记录">
						<div class="authorization-history-table">
							<el-table
								:data="
									authorizationHistoryList.slice(
										(authorizationHistoryPagination.page - 1) *
											authorizationHistoryPagination.size,
										authorizationHistoryPagination.page *
											authorizationHistoryPagination.size
									)
								"
								border
								style="width: 100%"
								:header-cell-style="{background: '#f5f7fa', color: '#606266'}"
								:loading="historyRecordLoading"
								element-loading-text="加载中..."
							>
								<el-table-column
									prop="序号"
									label="序号"
									width="80"
									align="center"
								/>
								<el-table-column prop="授权条件" label="授权条件" min-width="200" />
								<el-table-column
									prop="授权时间"
									label="授权时间"
									width="160"
									align="center"
								/>
							</el-table>

							<!-- 分页 -->
							<div class="authorization-history-pagination">
								<Pagination
									:total="authorizationHistoryPagination.total"
									:current-page="authorizationHistoryPagination.page"
									:page-size="authorizationHistoryPagination.size"
									:disabled="historyRecordLoading"
									@current-change="
										(page) => (authorizationHistoryPagination.page = page)
									"
									@size-change="
										(size) => {
											authorizationHistoryPagination.size = size
											authorizationHistoryPagination.page = 1
										}
									"
								/>
							</div>
						</div>
					</el-tab-pane>
				</el-tabs>
			</div>
		</Dialog>

		<!-- 权限管理弹窗 -->
		<Dialog
			v-model="permissionManageDialogVisible"
			title="权限管理"
			width="600px"
			:destroy-on-close="true"
			:visible-confirm-button="true"
			:visible-cancel-button="true"
			confirm-text="确定"
			cancel-text="取消"
			@click-confirm="permissionManageDialogVisible = false"
			@click-cancel="permissionManageDialogVisible = false"
		>
			<div class="permission-manage-content">
				<!-- 角色管理 -->
				<div class="role-section">
					<div class="role-item">
						<span class="role-label">角色管理</span>
						<el-select
							v-model="selectedRole"
							placeholder="请选择"
							style="width: 200px; margin-left: 12px"
						>
							<el-option label="请选择" value="请选择" />
							<el-option label="管理员" value="管理员" />
							<el-option label="普通用户" value="普通用户" />
							<el-option label="审核员" value="审核员" />
						</el-select>
					</div>
				</div>

				<!-- 授权人员 -->
				<div class="personnel-section">
					<div class="personnel-header">
						<div class="personnel-label">授权人员：</div>
						<div class="personnel-search">
							<el-input
								v-model="personnelSearchKeyword"
								placeholder="请输入姓名搜索"
								size="small"
								style="width: 200px"
								clearable
								@input="handlePersonnelSearch"
							>
								<template #prefix>
									<el-icon><Search /></el-icon>
								</template>
							</el-input>
						</div>
					</div>

					<div class="personnel-table">
						<el-table
							:data="personnelList"
							border
							style="width: 100%"
							size="small"
							:loading="personnelTableLoading"
							element-loading-text="加载中..."
							@selection-change="handlePersonnelSelectionChange"
							:default-checked-keys="selectedPersonnelIds"
						>
							<el-table-column type="selection" width="55" align="center" />
							<el-table-column prop="name" label="姓名" width="100" align="center" />
							<el-table-column
								prop="department"
								label="部门"
								width="120"
								align="center"
							/>
							<el-table-column
								prop="position"
								label="职位"
								min-width="120"
								align="center"
							/>
						</el-table>
					</div>

					<!-- 分页 -->
					<div class="personnel-pagination">
						<Pagination
							:total="personnelPagination.total"
							:current-page="personnelPagination.page"
							:page-size="personnelPagination.size"
							:disabled="personnelTableLoading"
							@current-change="
								(page) => handlePersonnelPaginationChange(page, 'page')
							"
							@size-change="(size) => handlePersonnelPaginationChange(size, 'size')"
						/>
					</div>
				</div>

				<!-- 操作权限 -->
				<div class="operations-section">
					<div class="operations-label">操作权限：</div>
					<div class="operations-list">
						<el-checkbox
							v-for="operation in permissionOperations"
							:key="operation.value"
							v-model="operation.checked"
							class="operation-checkbox"
						>
							{{ operation.label }}
						</el-checkbox>
					</div>
				</div>
			</div>
		</Dialog>

		<!-- 信息关联推荐弹窗 -->
		<Dialog
			v-model="infoRecommendDialogVisible"
			title="信息关联推荐"
			width="900px"
			:destroy-on-close="true"
			:visible-confirm-button="true"
			:visible-cancel-button="true"
			confirm-text="确定"
			cancel-text="取消"
			@click-confirm="infoRecommendDialogVisible = false"
			@click-cancel="infoRecommendDialogVisible = false"
		>
			<div class="info-recommend-content">
				<!-- 新增按钮 -->
				<div class="recommend-header">
					<el-button type="primary" size="small" @click="openRecommendDetail({}, 'add')">
						新增
					</el-button>
				</div>

				<!-- 推荐列表表格 -->
				<div class="recommend-table">
					<el-table
						:data="recommendList"
						border
						style="width: 100%"
						:loading="infoRecommendLoading"
						element-loading-text="加载中..."
						:header-cell-style="{background: '#f5f7fa', color: '#606266'}"
					>
						<el-table-column prop="序号" label="序号" width="80" align="center" />
						<el-table-column
							prop="推荐名称"
							label="推荐名称"
							width="120"
							align="center"
						/>
						<el-table-column
							prop="推荐内容"
							label="推荐内容"
							width="150"
							align="center"
						/>
						<el-table-column prop="附件" label="附件" width="100" align="center">
							<template #default="scope">
								<el-link
									v-if="scope.row.附件名称"
									type="primary"
									:underline="false"
									@click="downloadAttachment(scope.row)"
								>
									下载
								</el-link>
								<span v-else style="color: #909399">无</span>
							</template>
						</el-table-column>
						<el-table-column
							prop="创建时间"
							label="创建时间"
							width="120"
							align="center"
						/>
						<el-table-column prop="操作人" label="操作人" width="100" align="center" />
						<el-table-column label="操作" width="200" align="center">
							<template #default="scope">
								<el-button
									type="primary"
									size="small"
									@click="openRecommendDetail(scope.row, 'view')"
								>
									查看
								</el-button>
								<el-button
									type="warning"
									size="small"
									@click="openRecommendDetail(scope.row, 'edit')"
								>
									修改
								</el-button>
								<el-button
									type="danger"
									size="small"
									@click="deleteRecommendItem(scope.row)"
								>
									删除
								</el-button>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</div>
		</Dialog>

		<!-- 信息关联推荐详情弹窗 -->
		<Dialog
			v-model="infoRecommendDetailVisible"
			:title="isViewMode ? '查看推荐详情' : '信息关联推荐'"
			width="600px"
			:destroy-on-close="true"
			:visible-confirm-button="!isViewMode"
			:visible-cancel-button="true"
			confirm-text="确定"
			:cancel-text="isViewMode ? '关闭' : '取消'"
			@click-confirm="saveRecommendDetail"
			@click-cancel="infoRecommendDetailVisible = false"
		>
			<div class="recommend-detail-content">
				<el-form :model="recommendDetailForm" label-width="100px">
					<el-form-item label="推荐名称">
						<el-input
							v-model="recommendDetailForm.推荐名称"
							placeholder="请输入推荐名称"
							:disabled="isViewMode"
						/>
					</el-form-item>

					<el-form-item label="推荐内容">
						<el-input
							v-model="recommendDetailForm.推荐内容"
							type="textarea"
							:rows="4"
							placeholder="请输入推荐内容"
							:disabled="isViewMode"
						/>
					</el-form-item>

					<el-form-item label="附件">
						<div v-if="isViewMode && recommendDetailForm.附件名称">
							<el-link
								type="primary"
								:underline="false"
								@click="downloadAttachment(currentRecommendItem)"
							>
								{{ recommendDetailForm.附件名称 }}
							</el-link>
							<span style="margin-left: 10px; color: #909399; font-size: 12px">
								({{ formatFileSize(recommendDetailForm.附件大小) }})
							</span>
						</div>
						<div
							v-else-if="isViewMode && !recommendDetailForm.附件名称"
							style="color: #909399"
						>
							无附件
						</div>
						<div v-else>
							<el-upload
								ref="uploadRef"
								action="https://jsonplaceholder.typicode.com/posts/"
								:auto-upload="false"
								:show-file-list="false"
								accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"
								:before-upload="beforeRecommendUpload"
								:on-change="handleRecommendFileSelect"
								:limit="1"
								drag
								class="upload-dragger"
							>
								<div class="upload-dragger-content">
									<el-icon class="el-icon--upload"><upload-filled /></el-icon>
									<div class="el-upload__text">
										将文件拖到此处，或<em>点击上传</em>
									</div>
									<div class="el-upload__tip">
										支持 PDF、Word、文本、图片格式，文件大小不超过 10MB
									</div>
								</div>
							</el-upload>

							<!-- 显示已选择的文件 -->
							<div v-if="recommendDetailForm.附件名称" style="margin-top: 10px">
								<el-tag type="success" closable @close="removeSelectedFile">
									{{ recommendDetailForm.附件名称 }}
									<span style="margin-left: 5px; font-size: 12px">
										({{ formatFileSize(recommendDetailForm.附件大小) }})
									</span>
								</el-tag>
							</div>
						</div>
					</el-form-item>
				</el-form>
			</div>
		</Dialog>

		<!-- 权限日志记录弹窗 -->
		<Dialog
			v-model="permissionRecordDialogVisible"
			title="权限日志记录"
			width="700px"
			:destroy-on-close="true"
			:visible-confirm-button="true"
			confirm-text="确定"
			cancel-text="取消"
			@click-confirm="permissionRecordDialogVisible = false"
			@click-cancel="permissionRecordDialogVisible = false"
		>
			<div class="permission-record-content">
				<div class="record-table">
					<el-table
						:data="
							permissionRecordList.slice(
								(permissionRecordPagination.page - 1) *
									permissionRecordPagination.size,
								permissionRecordPagination.page * permissionRecordPagination.size
							)
						"
						border
						style="width: 100%"
						:header-cell-style="{background: '#f5f7fa', color: '#606266'}"
						:loading="permissionRecordLoading"
						element-loading-text="加载中..."
					>
						<el-table-column prop="序号" label="序号" width="80" align="center" />
						<el-table-column
							prop="操作用户"
							label="操作用户"
							width="150"
							align="center"
						/>
						<el-table-column
							prop="操作类型"
							label="操作类型"
							width="150"
							align="center"
						/>
						<el-table-column
							prop="操作时间"
							label="操作时间"
							min-width="180"
							align="center"
						/>
					</el-table>
				</div>

				<!-- 分页 -->
				<div class="record-pagination">
					<Pagination
						:total="permissionRecordPagination.total"
						:current-page="permissionRecordPagination.page"
						:page-size="permissionRecordPagination.size"
						:disabled="permissionRecordLoading"
						@current-change="(page) => (permissionRecordPagination.page = page)"
						@size-change="
							(size) => {
								permissionRecordPagination.size = size
								permissionRecordPagination.page = 1
							}
						"
					/>
				</div>
			</div>
		</Dialog>

		<!-- 信息化模板管理对话框 -->
		<Dialog
			v-model="infoTemplateDialogVisible"
			title="信息化模板管理"
			width="900px"
			:destroy-on-close="true"
			@close="closeInfoTemplateDialog"
		>
			<div class="template-management">
				<div class="template-table">
					<el-table :data="currentTemplateData" style="width: 100%" stripe>
						<el-table-column prop="序号" label="序号" width="80" align="center" />
						<el-table-column
							prop="模板名称"
							label="模板名称"
							min-width="180"
							show-overflow-tooltip
						/>
						<el-table-column
							prop="模板描述"
							label="模板描述"
							min-width="300"
							show-overflow-tooltip
						/>
						<el-table-column label="操作" width="150" align="center" fixed="right">
							<template #default="{row}">
								<el-button
									type="primary"
									size="small"
									link
									@click="applyTemplate(row)"
								>
									应用
								</el-button>
								<el-button
									type="danger"
									size="small"
									link
									@click="deleteTemplate(row)"
								>
									删除
								</el-button>
							</template>
						</el-table-column>
					</el-table>

					<!-- 分页组件 -->
					<div class="template-pagination">
						<el-pagination
							v-model:current-page="templateCurrentPage"
							v-model:page-size="templatePageSize"
							:page-sizes="[5, 10, 20, 50]"
							:total="templateTotal"
							layout="total, sizes, prev, pager, next, jumper"
							background
							@size-change="handleTemplateSizeChange"
							@current-change="handleTemplateCurrentChange"
						/>
					</div>
				</div>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="closeInfoTemplateDialog">取消</el-button>
					<el-button type="primary" @click="closeInfoTemplateDialog">确定</el-button>
				</div>
			</template>
		</Dialog>

		<!-- 多用户协助对话框 -->
		<MultiUserCollaborationDialog
			v-model="collaborationDialogVisible"
			:row-data="currentCollaborationRow"
			:personnel-list="allPersonnelList"
			@save="handleCollaborationSave"
		/>

		<!-- 关联业务数据弹窗 -->
		<el-dialog
			v-model="relationDialogVisible"
			title="关联业务数据"
			width="600px"
			:show-close="true"
			:close-on-click-modal="false"
			class="relation-dialog"
		>
			<div class="relation-content">
				<div class="form-item">
					<label class="form-label">选择业务数据：</label>
					<el-select
						v-model="selectedRelationData"
						placeholder="请选择要关联的业务数据"
						style="width: 100%"
						size="default"
						filterable
						:loading="relationDataLoading"
					>
						<el-option
							v-for="item in relationDataList"
							:key="item.id"
							:label="item.name"
							:value="item.id"
						>
							<div class="relation-option">
								<div class="option-name">{{ item.name }}</div>
								<div class="option-desc">{{ item.description }}</div>
							</div>
						</el-option>
					</el-select>
				</div>

				<div v-if="selectedRelationData" class="selected-info">
					<h4>选中的业务数据信息：</h4>
					<div class="info-item">
						<span class="info-label">数据名称：</span>
						<span class="info-value">{{ getSelectedRelationInfo()?.name }}</span>
					</div>
					<div class="info-item">
						<span class="info-label">数据描述：</span>
						<span class="info-value">{{ getSelectedRelationInfo()?.description }}</span>
					</div>
					<div class="info-item">
						<span class="info-label">数据类型：</span>
						<span class="info-value">{{ getSelectedRelationInfo()?.type }}</span>
					</div>
					<div class="info-item">
						<span class="info-label">创建时间：</span>
						<span class="info-value">{{ getSelectedRelationInfo()?.createTime }}</span>
					</div>
				</div>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="handleRelationCancel">取消</el-button>
					<el-button
						type="primary"
						@click="handleRelationConfirm"
						:disabled="!selectedRelationData"
						:loading="relationConfirmLoading"
					>
						确认关联
					</el-button>
				</div>
			</template>
		</el-dialog>

		<!-- 流程配置弹窗 -->
		<Dialog
			v-model="workflowDialogVisible"
			:title="isEditingWorkflow ? '编辑流程' : '新建流程'"
			:destroy-on-close="true"
			:loading="workflowLoading"
			loading-text="保存中"
			width="800px"
			@closed="handleWorkflowDialogClosed"
			@click-confirm="handleWorkflowConfirm"
		>
			<div class="workflow-dialog-content">
				<!-- 基本信息 -->
				<div class="workflow-basic-info">
					<h4 class="section-title">基本信息</h4>
					<el-form :model="workflowForm" label-width="100px" size="small">
						<el-row :gutter="20">
							<el-col :span="12">
								<el-form-item label="流程名称" required>
									<el-input
										v-model="workflowForm.name"
										placeholder="请输入流程名称"
										maxlength="50"
										show-word-limit
									/>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="审核模式">
									<el-select
										v-model="workflowForm.auditMode"
										placeholder="请选择审核模式"
									>
										<el-option label="串行审核" value="serial" />
										<el-option label="并行审核" value="parallel" />
										<el-option label="会签审核" value="countersign" />
									</el-select>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row>
							<el-col :span="24">
								<el-form-item label="流程描述">
									<el-input
										v-model="workflowForm.description"
										type="textarea"
										placeholder="请输入流程描述"
										:rows="3"
										maxlength="200"
										show-word-limit
									/>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row>
							<el-col :span="12">
								<el-form-item label="审核规则">
									<el-select
										v-model="workflowForm.auditRule"
										placeholder="请选择审核规则"
									>
										<el-option label="一票通过" value="unanimous" />
										<el-option label="一票否决" value="veto" />
										<el-option label="多数通过" value="majority" />
									</el-select>
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>
				</div>

				<!-- 审核模式详细配置 -->
				<div class="audit-mode-config">
					<h4 class="section-title">审核模式详细配置</h4>

					<div class="mode-description">
						<el-alert
							:title="getAuditModeDescription(workflowForm.auditMode)"
							type="info"
							:closable="false"
							show-icon
						/>
					</div>

					<el-form
						:model="workflowForm"
						label-width="120px"
						size="small"
						style="margin-top: 16px"
					>
						<el-row :gutter="20" v-if="workflowForm.auditMode === 'serial'">
							<el-col :span="12">
								<el-form-item label="超时处理">
									<el-select
										v-model="workflowForm.timeoutAction"
										placeholder="请选择超时处理方式"
									>
										<el-option label="自动通过" value="auto_pass" />
										<el-option label="自动拒绝" value="auto_reject" />
										<el-option label="转交上级" value="escalate" />
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="允许退回">
									<el-switch v-model="workflowForm.allowReturn" />
								</el-form-item>
							</el-col>
						</el-row>

						<el-row :gutter="20" v-if="workflowForm.auditMode === 'parallel'">
							<el-col :span="12">
								<el-form-item label="最少通过数">
									<el-input-number
										v-model="workflowForm.minApprovalCount"
										:min="1"
										:max="10"
										placeholder="最少通过人数"
										style="width: 100%"
									/>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="通过比例">
									<el-select
										v-model="workflowForm.approvalRatio"
										placeholder="请选择通过比例"
									>
										<el-option label="50%以上" value="50" />
										<el-option label="60%以上" value="60" />
										<el-option label="70%以上" value="70" />
										<el-option label="80%以上" value="80" />
										<el-option label="100%全部" value="100" />
									</el-select>
								</el-form-item>
							</el-col>
						</el-row>

						<el-row :gutter="20" v-if="workflowForm.auditMode === 'countersign'">
							<el-col :span="12">
								<el-form-item label="会签类型">
									<el-select
										v-model="workflowForm.countersignType"
										placeholder="请选择会签类型"
									>
										<el-option label="全员会签" value="all" />
										<el-option label="部分会签" value="partial" />
										<el-option label="顺序会签" value="sequential" />
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="会签完成条件">
									<el-select
										v-model="workflowForm.countersignCondition"
										placeholder="请选择完成条件"
									>
										<el-option label="全部同意" value="all_agree" />
										<el-option label="多数同意" value="majority_agree" />
										<el-option label="任一同意" value="any_agree" />
									</el-select>
								</el-form-item>
							</el-col>
						</el-row>

						<el-row>
							<el-col :span="24">
								<el-form-item label="特殊配置">
									<el-checkbox-group v-model="workflowForm.specialConfig">
										<el-checkbox label="allowDelegate"
											>允许委托他人审核</el-checkbox
										>
										<el-checkbox label="requireComment"
											>审核时必须填写意见</el-checkbox
										>
										<el-checkbox label="notifyApplicant"
											>审核结果通知申请人</el-checkbox
										>
										<el-checkbox label="recordAuditLog"
											>记录详细审核日志</el-checkbox
										>
									</el-checkbox-group>
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>
				</div>

				<!-- 流程节点配置 -->
				<div class="workflow-nodes-config">
					<h4 class="section-title">
						流程节点配置
						<el-button
							type="primary"
							size="small"
							@click="addWorkflowNode"
							style="float: right"
						>
							添加节点
						</el-button>
					</h4>

					<div class="nodes-list" v-if="workflowForm.nodes.length > 0">
						<div
							v-for="(node, index) in workflowForm.nodes"
							:key="node.id"
							class="node-item"
						>
							<div class="node-header">
								<span class="node-number">节点 {{ index + 1 }}</span>
								<div class="node-actions">
									<el-button
										type="text"
										size="small"
										@click="moveNodeUp(index)"
										:disabled="index === 0"
									>
										上移
									</el-button>
									<el-button
										type="text"
										size="small"
										@click="moveNodeDown(index)"
										:disabled="index === workflowForm.nodes.length - 1"
									>
										下移
									</el-button>
									<el-button
										type="text"
										size="small"
										@click="removeWorkflowNode(index)"
										style="color: #f56c6c"
									>
										删除
									</el-button>
								</div>
							</div>

							<el-form :model="node" label-width="100px" size="small">
								<el-row :gutter="20">
									<el-col :span="8">
										<el-form-item label="节点名称" required>
											<el-input
												v-model="node.name"
												placeholder="请输入节点名称"
												maxlength="30"
											/>
										</el-form-item>
									</el-col>
									<el-col :span="8">
										<el-form-item label="节点类型">
											<el-select
												v-model="node.type"
												placeholder="请选择节点类型"
											>
												<el-option label="审核节点" value="audit" />
												<el-option label="审批节点" value="approval" />
												<el-option label="会签节点" value="countersign" />
												<el-option label="通知节点" value="notify" />
											</el-select>
										</el-form-item>
									</el-col>
									<el-col :span="8">
										<el-form-item label="处理时限">
											<el-input-number
												v-model="node.timeLimit"
												:min="1"
												:max="30"
												placeholder="天数"
												style="width: 100%"
											/>
										</el-form-item>
									</el-col>
								</el-row>
								<el-row>
									<el-col :span="24">
										<el-form-item label="节点描述">
											<el-input
												v-model="node.description"
												type="textarea"
												placeholder="请输入节点描述"
												:rows="2"
												maxlength="100"
											/>
										</el-form-item>
									</el-col>
								</el-row>

								<!-- 审核人配置 -->
								<el-row>
									<el-col :span="24">
										<el-form-item label="审核人员">
											<div class="auditor-config">
												<div class="auditor-header">
													<el-button
														type="primary"
														size="small"
														@click="openAuditorSelector(index)"
													>
														选择审核人
													</el-button>
													<span
														class="auditor-count"
														v-if="
															node.auditors &&
															node.auditors.length > 0
														"
													>
														已选择 {{ node.auditors.length }} 人
													</span>
												</div>

												<div
													class="selected-auditors"
													v-if="node.auditors && node.auditors.length > 0"
												>
													<el-tag
														v-for="auditor in node.auditors"
														:key="auditor.id"
														closable
														@close="removeAuditor(index, auditor.id)"
														style="
															margin-right: 8px;
															margin-bottom: 8px;
														"
													>
														{{ auditor.name }} ({{
															auditor.department
														}})
													</el-tag>
												</div>

												<div v-else class="no-auditors">
													<span style="color: #909399; font-size: 12px"
														>暂未选择审核人员</span
													>
												</div>
											</div>
										</el-form-item>
									</el-col>
								</el-row>
							</el-form>
						</div>
					</div>

					<div v-else class="empty-nodes">
						<el-empty
							description="暂无流程节点，请点击上方按钮添加节点"
							:image-size="80"
						/>
					</div>
				</div>
			</div>
		</Dialog>

		<!-- 审核人选择弹窗 -->
		<Dialog
			v-model="auditorSelectorVisible"
			title="选择审核人员"
			width="600px"
			@click-confirm="confirmAuditorSelection"
		>
			<div class="auditor-selector-content">
				<!-- 搜索框 -->
				<div class="auditor-search">
					<el-input
						v-model="auditorSearchKeyword"
						placeholder="请输入姓名搜索"
						size="small"
						style="width: 200px"
						clearable
						@input="handleAuditorSearch"
					>
						<template #prefix>
							<el-icon><Search /></el-icon>
						</template>
					</el-input>
				</div>

				<!-- 人员列表 -->
				<div class="auditor-table">
					<el-table
						:data="currentAuditorList"
						border
						style="width: 100%"
						max-height="300"
						@selection-change="handleAuditorSelectionChange"
					>
						<el-table-column type="selection" width="55" align="center" />
						<el-table-column prop="name" label="姓名" width="100" align="center" />
						<el-table-column
							prop="department"
							label="部门"
							width="150"
							align="center"
						/>
						<el-table-column prop="position" label="职位" width="120" align="center" />
						<el-table-column prop="phone" label="联系电话" width="130" align="center" />
					</el-table>
				</div>

				<!-- 分页 -->
				<div class="auditor-pagination">
					<Pagination
						:total="auditorPagination.total"
						:current-page="auditorPagination.page"
						:page-size="auditorPagination.size"
						@current-change="onAuditorPaginationChange($event, 'page')"
						@size-change="onAuditorPaginationChange($event, 'size')"
					/>
				</div>
			</div>
		</Dialog>

		<!-- 快捷链接设置主弹窗 -->
		<el-dialog
			v-model="quickLinkDialogVisible"
			title="快捷链接设置"
			width="600px"
			:show-close="true"
			:close-on-click-modal="false"
			class="quick-link-dialog"
		>
			<div class="quick-link-content">
				<!-- 新增快捷链接按钮 -->
				<div class="quick-link-header">
					<el-button type="primary" @click="openQuickLinkDetailDialog('新增快捷链接')">
						新增快捷链接
					</el-button>
				</div>

				<!-- 快捷链接分类 -->
				<div class="quick-link-categories">
					<div class="category-row">
						<div class="category-item" @click="openQuickLinkDetailDialog('审核管理')">
							<div class="category-card">
								<span class="category-title">审核管理</span>
							</div>
						</div>
						<div class="category-item" @click="openQuickLinkDetailDialog('权限管理')">
							<div class="category-card">
								<span class="category-title">权限管理</span>
							</div>
						</div>
						<div class="category-item" @click="openQuickLinkDetailDialog('多用户协作')">
							<div class="category-card">
								<span class="category-title">多用户协作</span>
							</div>
						</div>
					</div>
					<div class="category-row">
						<div class="category-item" @click="openQuickLinkDetailDialog('翻译设置')">
							<div class="category-card">
								<span class="category-title">翻译设置</span>
							</div>
						</div>
						<div class="category-item" @click="openQuickLinkDetailDialog('显示设置')">
							<div class="category-card">
								<span class="category-title">显示设置</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="quickLinkDialogVisible = false">取消</el-button>
					<el-button type="primary" @click="handleQuickLinkConfirm">确定</el-button>
				</div>
			</template>
		</el-dialog>

		<!-- 快捷链接设置详情弹窗 -->
		<el-dialog
			v-model="quickLinkDetailDialogVisible"
			title="快捷链接设置"
			width="500px"
			:show-close="true"
			:close-on-click-modal="false"
			class="quick-link-detail-dialog"
		>
			<div class="quick-link-detail-content">
				<el-form :model="quickLinkForm" label-width="100px">
					<el-form-item label="链接名称">
						<el-select
							v-model="quickLinkForm.链接名称"
							placeholder="请选择"
							style="width: 100%"
						>
							<el-option label="审核管理" value="审核管理" />
							<el-option label="权限管理" value="权限管理" />
							<el-option label="多用户协作" value="多用户协作" />
							<el-option label="翻译设置" value="翻译设置" />
							<el-option label="显示设置" value="显示设置" />
						</el-select>
					</el-form-item>
					<el-form-item label="链接URL">
						<el-input
							v-model="quickLinkForm.链接URL"
							placeholder="请输入"
							style="width: 100%"
						/>
					</el-form-item>
				</el-form>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="quickLinkDetailDialogVisible = false">取消</el-button>
					<el-button type="primary" @click="handleQuickLinkDetailConfirm">确定</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="BusinessTableData">
import {ref, reactive, computed, onMounted, onUnmounted, nextTick, watch} from 'vue'
import {useRouter} from 'vue-router'
import {ElMessage, ElMessageBox} from 'element-plus'
import {
	Upload,
	Download,
	UploadFilled,
	QuestionFilled,
	Edit,
	ArrowDown,
	Document,
	Close,
	Search,
	Plus,
	Delete,
} from '@element-plus/icons-vue'
import * as ExcelJS from 'exceljs'
import {saveAs} from 'file-saver'
import {uploadFile} from '@/plugin/upload'

import {useBusinessTableDataStore} from '@/stores/useBusinessTableDataStore'
import {useWorkflowStore} from '@/stores/useWorkflowStore'
import MultiUserCollaborationDialog from '@/components/MultiUserCollaborationDialog.vue'
import type {BusinessTableDataRecord} from '@/stores/useBusinessTableDataStore'

// Store和Router
const businessTableDataStore = useBusinessTableDataStore()
const workflowStore = useWorkflowStore()
const router = useRouter()

// 搜索表单配置
const searchFormProp = ref([
	{label: '', prop: '所属标签', type: 'text', placeholder: '请输入所属标签'},
	{label: '', prop: '字段信息查询', type: 'text', placeholder: '请输入字段描述'},
	{label: '', prop: '表名搜索', type: 'text', placeholder: '请输入表表述'},
])

// 搜索表单数据
const searchForm = ref({
	所属标签: '',
	字段信息查询: '',
	表名搜索: '',
})

// 保存的筛选条件
const savedFilterConditions = ref({
	所属标签: '',
	字段信息查询: '',
	表名搜索: '',
})

// 从localStorage加载保存的筛选条件
const loadSavedConditions = async () => {
	try {
		const saved = localStorage.getItem('businessTableData_filterConditions')
		console.log('尝试加载保存的筛选条件:', saved) // 调试信息

		if (saved) {
			const conditions = JSON.parse(saved)
			console.log('解析的条件:', conditions) // 调试信息

			savedFilterConditions.value = {...conditions}

			// 使用nextTick确保DOM更新后再填充表单
			await nextTick()
			searchForm.value = {...conditions}

			console.log('填充后的搜索表单:', searchForm.value) // 调试信息

			// 统计加载的条件数量
			const nonEmptyCount = Object.values(conditions).filter((value) => value !== '').length
			const totalCount = Object.keys(conditions).length

			if (nonEmptyCount === 0) {
				ElMessage.success('已自动加载保存的筛选条件（全部为空）')
			} else if (nonEmptyCount === totalCount) {
				ElMessage.success(`已自动加载 ${nonEmptyCount} 个保存的筛选条件`)
			} else {
				ElMessage.success(
					`已自动加载保存的筛选条件（${nonEmptyCount} 个有值，${
						totalCount - nonEmptyCount
					} 个为空）`
				)
			}
		} else {
			console.log('没有找到保存的筛选条件') // 调试信息
		}
	} catch (error) {
		console.error('加载保存的筛选条件失败:', error)
	}
}

// 清除保存的筛选条件
const clearSavedConditions = () => {
	try {
		localStorage.removeItem('businessTableData_filterConditions')
		savedFilterConditions.value = {
			所属标签: '',
			字段信息查询: '',
			表名搜索: '',
		}
		ElMessage.success('已清除保存的筛选条件')
	} catch (error) {
		console.error('清除保存的筛选条件失败:', error)
		ElMessage.error('清除失败，请重试')
	}
}

// 加载状态
const loading = ref(false)
const searchLoading = ref(false)
const dialogLoading = ref(false)
const paginationLoading = ref(false)

// 防抖定时器
let searchDebounceTimer: NodeJS.Timeout | null = null

// 批量导入相关
const batchImportDialogVisible = ref(false)
const importLoading = ref(false)
const importFileList = ref<any[]>([])
const selectedFileName = ref('')
const showImportRules = ref(false)

// 批量修改相关
const batchEditDialogVisible = ref(false)
const batchEditLoading = ref(false)
const batchEditForm = ref({
	所属分类: '',
	所属标签: '',
	表描述: '',
	描述信息备份: '',
	移动端展示: false,
})
const batchEditFields = ref({
	所属分类: false,
	所属标签: false,
	表描述: false,
	描述信息备份: false,
	移动端展示: false,
})

// 批量导出相关
const exportLoading = ref(false)

// 多维度分类相关
const multiDimensionDialogVisible = ref(false)
const multiDimensionAddDialogVisible = ref(false)
const multiDimensionLoading = ref(false)
const multiDimensionPaginationLoading = ref(false)

// 多维度分类分页
const multiDimensionPagination = ref({
	page: 1,
	size: 10,
	total: 0,
})

// 数据信息管理相关
const dataInfoDialogVisible = ref(false)
const dataInfoLoading = ref(false)
const dataInfoTableLoading = ref(false)
const dataExportLoading = ref(false)
const dataValidateLoading = ref(false)
const dataSearchKeyword = ref('')

// 数据信息分页
const dataInfoPagination = ref({
	page: 1,
	size: 10,
	total: 0,
})

// 数据统计信息
const dataInfoStats = ref({
	total: 0,
	valid: 0,
	invalid: 0,
	lastUpdate: '',
})

// 业务数据规则相关
const businessRulesDialogVisible = ref(false)

// 审核管理相关
const auditManageDialogVisible = ref(false)
const auditForm = ref({
	result: 'reject', // 'pass' | 'reject'
	opinion: '',
})
const auditFormRef = ref()
const currentAuditRow = ref(null)

// 备份与恢复相关
const backupRestoreDialogVisible = ref(false)
const backupRestoreLoading = ref(false)
const backupRestoreForm = ref({
	数据备份开始时间: '',
	全量备份频率: '',
	增量备份频率: '',
	备份数据清理策略: '',
	恢复至指定时期: '',
	恢复执行时间: '',
})

// 历史操作记录相关
const historyRecordDialogVisible = ref(false)
const historyRecordLoading = ref(false)
const historyRecordActiveTab = ref('操作历史记录')
const operationHistoryList = ref([])
const authorizationHistoryList = ref([])
const operationHistoryPagination = ref({
	page: 1,
	size: 10,
	total: 0,
})
const authorizationHistoryPagination = ref({
	page: 1,
	size: 10,
	total: 0,
})

// 权限管理相关
const permissionManageDialogVisible = ref(false)
const permissionManageLoading = ref(false)
const personnelTableLoading = ref(false)
const selectedRole = ref('请选择')
const personnelSearchKeyword = ref('')
const selectedPersonnelIds = ref([])
const permissionOperations = ref([
	{label: '查看', value: 'view', checked: true},
	{label: '新增', value: 'add', checked: false},
	{label: '编辑', value: 'edit', checked: true},
	{label: '删除', value: 'delete', checked: false},
])

// 人员列表数据
const personnelList = ref([])
const personnelPagination = ref({
	page: 1,
	size: 10,
	total: 0,
})

// 所有人员数据
const allPersonnelList = ref([
	{id: 1, name: '陈志强', department: '技术部', position: '开发工程师', selected: false},
	{id: 2, name: '林雅婷', department: '产品部', position: '产品经理', selected: true},
	{id: 3, name: '刘建华', department: '设计部', position: 'UI设计师', selected: false},
	{id: 4, name: '王晓敏', department: '测试部', position: '测试工程师', selected: false},
	{id: 5, name: '张文博', department: '运营部', position: '运营专员', selected: false},
	{id: 6, name: '李春梅', department: '市场部', position: '市场专员', selected: false},
	{id: 7, name: '赵国庆', department: '财务部', position: '财务专员', selected: false},
	{id: 8, name: '孙丽娟', department: '人事部', position: '人事专员', selected: false},
	{id: 9, name: '周建军', department: '技术部', position: '前端工程师', selected: false},
	{id: 10, name: '吴海燕', department: '技术部', position: '后端工程师', selected: false},
	{id: 11, name: '郑明辉', department: '产品部', position: '产品助理', selected: false},
	{id: 12, name: '钱秀兰', department: '设计部', position: '交互设计师', selected: false},
	{id: 13, name: '马志远', department: '技术部', position: '架构师', selected: false},
	{id: 14, name: '黄丽华', department: '运营部', position: '运营经理', selected: false},
	{id: 15, name: '徐建国', department: '市场部', position: '市场经理', selected: false},
	{id: 16, name: '朱晓红', department: '财务部', position: '会计师', selected: false},
	{id: 17, name: '何志明', department: '人事部', position: '招聘专员', selected: false},
	{id: 18, name: '沈雅芳', department: '设计部', position: '视觉设计师', selected: false},
])

// 多用户协助相关
const collaborationDialogVisible = ref(false)
const currentCollaborationRow = ref(null)

// 信息关联推荐相关
const infoRecommendDialogVisible = ref(false)
const infoRecommendLoading = ref(false)
const infoRecommendDetailVisible = ref(false)
const recommendList = ref([])

// 信息关联推荐详情相关
const currentRecommendItem = ref(null)
const recommendDetailForm = ref({
	推荐名称: '',
	推荐内容: '',
	附件: null,
	附件名称: '',
	附件大小: 0,
})
const uploadFileList = ref([])
const isEditMode = ref(false)
const isViewMode = ref(false)
const uploadRef = ref(null)

// 权限日志记录相关
const permissionRecordDialogVisible = ref(false)
const permissionRecordLoading = ref(false)
const permissionRecordList = ref([])
const permissionRecordPagination = ref({
	page: 1,
	size: 10,
	total: 0,
})

// 权限继承规则相关
const permissionRulesDialogVisible = ref(false)
const permissionRulesForm = ref({
	基础继承规则: '上级角色自动继承下级角色权限', // 默认选择第一个选项
})

// 角色自定义相关
const roleCustomDialogVisible = ref(false)
const roleAddDialogVisible = ref(false)
const roleEditDialogVisible = ref(false)
const roleCustomForm = ref({
	角色名称: '',
	上级角色: '',
	权限: [],
})
const roleEditForm = ref({
	id: '',
	角色名称: '',
	上级角色: '',
	权限: [],
})
const editingRoleIndex = ref(-1)

// 角色列表数据
const roleList = ref([
	{
		id: '1',
		序号: 1,
		角色名称: '管理人员',
		权限: ['查看'],
		权限显示: '查看',
		创建时间: '2024-01-15 10:30',
		创建人: '王明',
	},
	{
		id: '2',
		序号: 2,
		角色名称: '审核人员',
		权限: ['查看', '编辑', '删除'],
		权限显示: '查看、编辑、删除',
		创建时间: '2024-01-16 14:20',
		创建人: '李华',
	},
	{
		id: '3',
		序号: 3,
		角色名称: '操作人',
		权限: ['新增', '编辑'],
		权限显示: '新增、编辑',
		创建时间: '2024-01-17 09:15',
		创建人: '张伟',
	},
])

// 上级角色选项
const parentRoleOptions = ref([
	{label: '请选择（角色名称，申请次数）', value: ''},
	{label: '管理人员', value: '管理人员'},
	{label: '审核人员', value: '审核人员'},
	{label: '操作人', value: '操作人'},
])

// 权限选项
const permissionOptions = ref([
	{label: '请选择（多选）', value: ''},
	{label: '查看', value: '查看'},
	{label: '编辑', value: '编辑'},
	{label: '删除', value: '删除'},
	{label: '新增', value: '新增'},
])
const businessRulesForm = ref({
	动态更新提醒: true,
	审核通过提醒: true,
	审核失败提醒: false,
	信息删除提醒: false,
	提醒频率: '',
	站内信: true,
	邮件证消息: false,
	邮件证DING消息: false,
	实时更新提醒: true,
	信息共享: true,
	共享人员: '',
	权限: '',
	信息定时更新: true,
	更新频率: '',
	下次更新时间: '',
	导入xlsx格式: true,
	导出xlsx格式: true,
	信息加密存储: true,
	加密算法: '',
	秘钥存储方式: 'auto',
	加密秘钥: '',
	加密文件存储路径: '',
})

// 动态更新提醒相关
const dynamicUpdateDialogVisible = ref(false)
const dynamicUpdateList = ref([
	{
		序号: 1,
		提醒内容: '审核失败',
		提醒时间: '2025.7.8',
		操作人: '陈静',
	},
	{
		序号: 2,
		提醒内容: '审核失败',
		提醒时间: '2025.7.8',
		操作人: '杨磊',
	},
	{
		序号: 3,
		提醒内容: '审核失败',
		提醒时间: '2025.7.8',
		操作人: '黄敏',
	},
])

// 信息定时任务相关
const infoScheduleDialogVisible = ref(false)
const infoScheduleFormRef = ref()
const infoScheduleForm = ref({
	任务名称: '',
	任务描述内容: '',
	任务开始时间: '',
	执行规则: '每天',
	结束时间类型: '无期限',
	结束时间: '',
})

// 信息定时任务表单验证规则
const infoScheduleFormRules = {
	任务名称: [{required: true, message: '请输入任务名称', trigger: 'blur'}],
	任务描述内容: [{required: true, message: '请输入任务描述内容', trigger: 'blur'}],
	任务开始时间: [{required: true, message: '请输入任务开始时间', trigger: 'blur'}],
	执行规则: [{required: true, message: '请选择执行规则', trigger: 'change'}],
}

// 信息个性化设置相关
const personalSettingsDialogVisible = ref(false)
const personalSettingsForm = ref({
	字体大小: '14px',
	支持调整表格宽度: true,
	数据展示方式: ['网格线', '行号', '列标', '斑马纹', '悬停高亮'],
})

// 从localStorage读取保存的翻译设置
const loadTranslationSettings = () => {
	try {
		const saved = localStorage.getItem('translationSettings')
		if (saved) {
			const settings = JSON.parse(saved)
			return {
				翻译设置: settings.enabled || false,
				默认翻译语言: settings.language || 'zh',
			}
		}
	} catch (error) {
		console.error('读取翻译设置失败:', error)
	}
	return {
		翻译设置: false,
		默认翻译语言: 'zh',
	}
}

// 保存翻译设置到localStorage
const saveTranslationSettings = (settings: any) => {
	try {
		const toSave = {
			enabled: settings.翻译设置,
			language: settings.默认翻译语言,
		}
		localStorage.setItem('translationSettings', JSON.stringify(toSave))
	} catch (error) {
		console.error('保存翻译设置失败:', error)
	}
}

// 初始化翻译设置
const initialSettings = loadTranslationSettings()

// 当前语言设置
const currentLanguage = ref(initialSettings.翻译设置 ? initialSettings.默认翻译语言 : 'zh')

// 翻译设置相关
const translationSettingsDialogVisible = ref(false)
const translationSettingsForm = ref({
	翻译设置: initialSettings.翻译设置,
	默认翻译语言: initialSettings.默认翻译语言,
})

// 信息锁定/解锁相关
const infoLockDialogVisible = ref(false)
const infoUnlockDialogVisible = ref(false)
const currentLockRow = ref<BusinessTableDataRecord | null>(null)

// 版本注释相关
const versionNotesDialogVisible = ref(false)
const currentVersionRow = ref<BusinessTableDataRecord | null>(null)
const versionNotesFormRef = ref()

// 突出显示设置相关
const highlightSettingsDialogVisible = ref(false)
const currentHighlightRow = ref<BusinessTableDataRecord | null>(null)

// 批注相关
const commentsDialogVisible = ref(false)
const currentCommentsRow = ref<BusinessTableDataRecord | null>(null)
const commentsFormRef = ref()

// 本地存储键名
const HIGHLIGHT_SETTINGS_STORAGE_KEY = 'businessTableData_highlightSettings'
const COMMENTS_STORAGE_KEY = 'businessTableData_comments'

// 获取突出显示设置
const getHighlightSettings = () => {
	try {
		const settings = localStorage.getItem(HIGHLIGHT_SETTINGS_STORAGE_KEY)
		if (settings) {
			return JSON.parse(settings)
		}
	} catch (error) {
		console.error('获取突出显示设置失败:', error)
	}
	// 默认设置 - 只显示一个列的配置
	return {
		highlightRules: [
			{
				id: 1,
				fieldName: '所属分类',
				fontColor: '#1890ff',
				isActive: true,
			},
		],
	}
}

// 保存突出显示设置
const saveHighlightSettings = (settings: any) => {
	try {
		localStorage.setItem(HIGHLIGHT_SETTINGS_STORAGE_KEY, JSON.stringify(settings))
	} catch (error) {
		console.error('保存突出显示设置失败:', error)
	}
}

const highlightSettingsForm = ref(getHighlightSettings())

// 关联业务数据相关
const relationDialogVisible = ref(false)
const currentRelationRow = ref<BusinessTableDataRecord | null>(null)
const selectedRelationData = ref('')
const relationDataLoading = ref(false)

// 快捷链接设置相关
const quickLinkDialogVisible = ref(false)
const quickLinkDetailDialogVisible = ref(false)
const currentQuickLinkRow = ref<BusinessTableDataRecord | null>(null)
const quickLinkForm = ref({
	链接名称: '',
	链接URL: '',
})
const relationConfirmLoading = ref(false)

// 业务数据列表 - 真实数据
const relationDataList = ref([
	{
		id: 'BD001',
		name: '用户行为分析数据',
		description: '包含用户访问、点击、停留时间等行为数据',
		type: '行为数据',
		createTime: '2024-01-15 10:30:00',
		status: '活跃',
	},
	{
		id: 'BD002',
		name: '商品销售数据',
		description: '商品销量、价格、库存等销售相关数据',
		type: '销售数据',
		createTime: '2024-01-20 14:20:00',
		status: '活跃',
	},
	{
		id: 'BD003',
		name: '客户信息数据',
		description: '客户基本信息、联系方式、偏好等数据',
		type: '客户数据',
		createTime: '2024-01-25 09:15:00',
		status: '活跃',
	},
	{
		id: 'BD004',
		name: '财务报表数据',
		description: '收入、支出、利润等财务相关数据',
		type: '财务数据',
		createTime: '2024-02-01 16:45:00',
		status: '活跃',
	},
	{
		id: 'BD005',
		name: '库存管理数据',
		description: '商品库存、入库、出库等管理数据',
		type: '库存数据',
		createTime: '2024-02-05 11:30:00',
		status: '活跃',
	},
	{
		id: 'BD006',
		name: '营销活动数据',
		description: '促销活动、广告投放、效果分析等数据',
		type: '营销数据',
		createTime: '2024-02-10 13:20:00',
		status: '活跃',
	},
	{
		id: 'BD007',
		name: '供应商管理数据',
		description: '供应商信息、采购记录、评价等数据',
		type: '供应链数据',
		createTime: '2024-02-15 08:45:00',
		status: '活跃',
	},
	{
		id: 'BD008',
		name: '员工绩效数据',
		description: '员工工作表现、考核结果、培训记录等数据',
		type: '人力资源数据',
		createTime: '2024-02-20 15:10:00',
		status: '活跃',
	},
])
const versionNotesList = ref([
	{
		序号: 1,
		版本名称: 'V1.0.0',
		注释: '初始版本发布，包含基础数据管理功能，支持数据的增删改查操作',
		操作: '',
	},
	{
		序号: 2,
		版本名称: 'V1.1.0',
		注释: '新增批量导入导出功能，优化数据检索性能，修复已知的数据同步问题',
		操作: '',
	},
	{
		序号: 3,
		版本名称: 'V1.2.0',
		注释: '增加权限管理模块，支持多级权限控制，新增审核流程功能',
		操作: '',
	},
])

// 批注数据
const commentsList = ref([
	{
		序号: 1,
		选中内容: '字段描述',
		批注内容: '字段描述内容建议更改为xxx',
		操作: '',
	},
	{
		序号: 2,
		选中内容: '字段描述',
		批注内容: '字段描述内容建议更改为xxx',
		操作: '',
	},
])

// 版本注释表单验证规则
const versionNotesRules = {
	版本名称: [
		{required: true, message: '请输入版本名称', trigger: 'blur'},
		{pattern: /^V\d+\.\d+(\.\d+)?$/, message: '版本名称格式应为 V1.0.0', trigger: 'blur'},
	],
	注释: [
		{required: true, message: '请输入版本注释', trigger: 'blur'},
		{min: 10, message: '版本注释至少需要10个字符', trigger: 'blur'},
		{max: 200, message: '版本注释不能超过200个字符', trigger: 'blur'},
	],
}

// 可用字段列表 - 从表格列中获取
const availableColumns = computed(() => {
	return [
		{prop: '所属分类', label: t('所属分类')},
		{prop: '所属标签', label: t('所属标签')},
		{prop: '字段信息描述', label: t('字段信息描述')},
		{prop: '表描述', label: t('表描述')},
		{prop: '描述信息备份', label: t('描述信息备份')},
		{prop: '移动端展示', label: t('移动端展示')},
		{prop: '审核状态', label: t('审核状态')},
	]
})

// 本地存储键名
const LOCK_STATUS_STORAGE_KEY = 'businessTableData_lockStatus'

// 获取锁定状态
const getLockStatus = (id: string): boolean => {
	try {
		const lockStatus = JSON.parse(localStorage.getItem(LOCK_STATUS_STORAGE_KEY) || '{}')
		return lockStatus[id] === true
	} catch {
		return false
	}
}

// 设置锁定状态
const setLockStatus = (id: string, isLocked: boolean) => {
	try {
		const lockStatus = JSON.parse(localStorage.getItem(LOCK_STATUS_STORAGE_KEY) || '{}')
		if (isLocked) {
			lockStatus[id] = true
		} else {
			delete lockStatus[id]
		}
		localStorage.setItem(LOCK_STATUS_STORAGE_KEY, JSON.stringify(lockStatus))
	} catch (error) {
		console.error('保存锁定状态失败:', error)
	}
}

// 翻译字典
const translations = {
	zh: {
		// 页面标题和主要按钮
		pageTitle: '业务数据描述',
		query: '查询',
		saveFilter: '保存筛选条件',
		add: '新增',
		dataInfo: '数据信息',
		batchImport: '批量导入',
		batchExport: '批量导出',
		batchEdit: '批量编辑',
		batchDelete: '批量删除',
		multiDimensionClassify: '多维度分类',
		moreActions: '更多操作',

		// 表格列头
		序号: '序号',
		所属分类: '所属分类',
		所属标签: '所属标签',
		字段信息描述: '字段信息描述',
		表描述: '表描述',
		描述信息备份: '描述信息备份',
		移动端展示: '移动端展示',
		审核状态: '审核状态',
		创建时间: '创建时间',
		创建人: '创建人',
		操作: '操作',

		// 操作按钮
		edit: '编辑',
		delete: '删除',
		preview: '预览',
		modify: '修改',
		quickLink: '快捷连接',
		more: '更多',

		// 翻译设置弹窗
		translationSettings: '翻译设置',
		translationEnable: '翻译设置',
		defaultLanguage: '默认翻译语言',
		confirm: '确认',
		cancel: '取消',

		// 消息提示
		saveSuccess: '翻译设置保存成功',
		saveFailed: '保存失败，请重试',

		// 审核状态
		已审核: '已审核',
		待审核: '待审核',
		审核失败: '审核失败',
	},
	en: {
		// 页面标题和主要按钮
		pageTitle: 'Business Table Data Definition',
		query: 'Query',
		saveFilter: 'Save Filter',
		add: 'Add',
		dataInfo: 'Data Info',
		batchImport: 'Batch Import',
		batchExport: 'Batch Export',
		batchEdit: 'Batch Edit',
		batchDelete: 'Batch Delete',
		multiDimensionClassify: 'Multi-Dimension Classify',
		moreActions: 'More Actions',

		// 表格列头
		序号: 'No.',
		所属分类: 'Category',
		所属标签: 'Tag',
		字段信息描述: 'Field Description',
		表描述: 'Table Description',
		描述信息备份: 'Description Backup',
		移动端展示: 'Mobile Display',
		审核状态: 'Audit Status',
		创建时间: 'Create Time',
		创建人: 'Creator',
		操作: 'Actions',

		// 操作按钮
		edit: 'Edit',
		delete: 'Delete',
		preview: 'Preview',
		modify: 'Modify',
		quickLink: 'Quick Link',
		more: 'More',

		// 翻译设置弹窗
		translationSettings: 'Translation Settings',
		translationEnable: 'Translation Enable',
		defaultLanguage: 'Default Language',
		confirm: 'Confirm',
		cancel: 'Cancel',

		// 消息提示
		saveSuccess: 'Translation settings saved successfully',
		saveFailed: 'Save failed, please try again',

		// 审核状态
		已审核: 'Approved',
		待审核: 'Pending',
		审核失败: 'Rejected',
	},
}

// 翻译函数
const t = (key: string): string => {
	const lang = currentLanguage.value as 'zh' | 'en'
	const langDict = translations[lang]
	return (langDict as any)?.[key] || key
}

// 页面样式状态
const pageStyles = ref({
	fontSize: '14px',
	tableResizable: true,
	showBorder: true,
	showIndex: true,
	showHeader: true,
	showStripe: true,
	showHover: true,
})

// 多维度分类原始数据
const multiDimensionAllList = ref([
	{
		id: '1',
		序号: 1,
		维度分类名称: '时间维度',
		维度说明: '按年、季度、月、周、日等时间单位对数据进行分类统计分析',
	},
	{
		id: '2',
		序号: 2,
		维度分类名称: '空间维度',
		维度说明: '按省、市、区县、街道等地理区域对数据进行分类统计分析',
	},
	{
		id: '3',
		序号: 3,
		维度分类名称: '业务维度',
		维度说明: '按业务类型、业务流程、业务状态等业务属性对数据进行分类统计分析',
	},
	{
		id: '4',
		序号: 4,
		维度分类名称: '组织维度',
		维度说明: '按部门、岗位、人员等组织架构对数据进行分类统计分析',
	},
	{
		id: '5',
		序号: 5,
		维度分类名称: '主题维度',
		维度说明: '按数据主题、业务主题、分析主题等对数据进行分类统计分析',
	},
	{
		id: '6',
		序号: 6,
		维度分类名称: '渠道维度',
		维度说明: '按销售渠道、推广渠道、服务渠道等对数据进行分类统计分析',
	},
	{
		id: '7',
		序号: 7,
		维度分类名称: '设备维度',
		维度说明: '按设备类型、设备品牌、设备型号等对数据进行分类统计分析',
	},
	{
		id: '8',
		序号: 8,
		维度分类名称: '内容维度',
		维度说明: '按内容类型、内容标签、内容质量等对数据进行分类统计分析',
	},
	{
		id: '9',
		序号: 9,
		维度分类名称: '行为维度',
		维度说明: '按用户行为、操作类型、访问路径等对数据进行分类统计分析',
	},
	{
		id: '10',
		序号: 10,
		维度分类名称: '状态维度',
		维度说明: '按数据状态、处理状态、审核状态等对数据进行分类统计分析',
	},
	{
		id: '11',
		序号: 11,
		维度分类名称: '质量维度',
		维度说明: '按数据质量、服务质量、产品质量等对数据进行分类统计分析',
	},
	{
		id: '12',
		序号: 12,
		维度分类名称: '成本维度',
		维度说明: '按成本类型、成本中心、成本项目等对数据进行分类统计分析',
	},
])

// 多维度分类分页数据
const multiDimensionList = computed(() => {
	const start = (multiDimensionPagination.value.page - 1) * multiDimensionPagination.value.size
	const end = start + multiDimensionPagination.value.size
	return multiDimensionAllList.value.slice(start, end)
})

// 数据信息原始数据
const dataInfoAllList = ref([
	{
		id: 1,
		name: '用户基础信息表',
		type: '基础数据',
		status: '正常',
		size: '2.5MB',
		createTime: '2024-01-15 10:30:00',
		updateTime: '2024-07-15 14:20:00',
	},
	{
		id: 2,
		name: '订单交易记录',
		type: '业务数据',
		status: '正常',
		size: '15.8MB',
		createTime: '2024-02-20 09:15:00',
		updateTime: '2024-07-15 16:45:00',
	},
	{
		id: 3,
		name: '产品库存信息',
		type: '库存数据',
		status: '异常',
		size: '8.2MB',
		createTime: '2024-03-10 11:20:00',
		updateTime: '2024-07-14 18:30:00',
	},
	{
		id: 4,
		name: '财务报表数据',
		type: '财务数据',
		status: '正常',
		size: '12.1MB',
		createTime: '2024-04-05 08:45:00',
		updateTime: '2024-07-15 12:10:00',
	},
	{
		id: 5,
		name: '客户服务记录',
		type: '服务数据',
		status: '正常',
		size: '6.7MB',
		createTime: '2024-05-12 13:25:00',
		updateTime: '2024-07-15 15:55:00',
	},
	{
		id: 6,
		name: '系统日志信息',
		type: '日志数据',
		status: '异常',
		size: '25.3MB',
		createTime: '2024-06-08 07:30:00',
		updateTime: '2024-07-15 17:20:00',
	},
	{
		id: 7,
		name: '营销活动数据',
		type: '营销数据',
		status: '正常',
		size: '4.9MB',
		createTime: '2024-06-20 16:40:00',
		updateTime: '2024-07-15 11:35:00',
	},
	{
		id: 8,
		name: '供应商信息表',
		type: '基础数据',
		status: '正常',
		size: '3.2MB',
		createTime: '2024-07-01 10:15:00',
		updateTime: '2024-07-15 13:25:00',
	},
])

// 数据信息分页数据
const dataInfoList = computed(() => {
	let filteredData = dataInfoAllList.value

	// 搜索过滤
	if (dataSearchKeyword.value) {
		filteredData = filteredData.filter(
			(item) =>
				item.name.toLowerCase().includes(dataSearchKeyword.value.toLowerCase()) ||
				item.type.toLowerCase().includes(dataSearchKeyword.value.toLowerCase())
		)
	}

	const start = (dataInfoPagination.value.page - 1) * dataInfoPagination.value.size
	const end = start + dataInfoPagination.value.size
	return filteredData.slice(start, end)
})
const multiDimensionForm = ref({
	维度分类名称: '',
	备注: '',
})
const currentMultiDimensionRow = ref(null)
const multiDimensionFormRef = ref()

// 多维度分类表单配置
const multiDimensionFormProps = ref([
	{
		label: '维度分类名称',
		prop: '维度分类名称',
		type: 'text',
		placeholder: '请输入维度分类名称',
		maxlength: 50,
		showWordLimit: true,
	},
	{
		label: '备注',
		prop: '备注',
		type: 'textarea',
		placeholder: '请输入备注信息',
		maxlength: 200,
		showWordLimit: true,
		rows: 4,
	},
])

// 多维度分类表单验证规则
const multiDimensionFormRules = {
	维度分类名称: [
		{required: true, message: '请输入维度分类名称', trigger: 'blur'},
		{min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur'},
	],
}

// 表格相关
const tableRef = ref()
const selectedRows = ref<BusinessTableDataRecord[]>([])

// 分页
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})

// 检查是否有保存的筛选条件
const hasSavedConditions = computed(() => {
	return Object.values(savedFilterConditions.value).some((value) => value !== '')
})

// 基础列配置 - 优化列宽和文本显示
const baseColumns = [
	{
		prop: '所属分类',
		label: '所属分类',
		minWidth: 120,
		showOverflowTooltip: true,
	},
	{
		prop: '所属标签',
		label: '所属标签',
		minWidth: 100,
		showOverflowTooltip: true,
	},
	{
		prop: '字段信息描述',
		label: '字段信息描述',
		minWidth: 200,
		showOverflowTooltip: true,
	},
	{
		prop: '表描述',
		label: '表描述',
		minWidth: 250,
		showOverflowTooltip: true,
	},
	{
		prop: '描述信息备份',
		label: '描述信息备份',
		minWidth: 200,
		showOverflowTooltip: true,
	},
	{
		prop: '移动端展示',
		label: '移动端展示',
		minWidth: 100,
		align: 'center',
	},
]

// 动态表格列配置 - 根据resizable开关状态决定是否启用列宽调整
const columns = computed(() => {
	console.log('重新计算列配置，tableResizable:', pageStyles.value.tableResizable)
	console.log('当前语言:', currentLanguage.value)

	// 基础列配置，支持翻译
	const translatedColumns = [
		{
			prop: '所属分类',
			label: t('所属分类'),
			minWidth: 120,
			showOverflowTooltip: true,
		},
		{
			prop: '所属标签',
			label: t('所属标签'),
			minWidth: 100,
			showOverflowTooltip: true,
		},
		{
			prop: '字段信息描述',
			label: t('字段信息描述'),
			minWidth: 200,
			showOverflowTooltip: true,
		},
		{
			prop: '表描述',
			label: t('表描述'),
			minWidth: 250,
			showOverflowTooltip: true,
		},
		{
			prop: '描述信息备份',
			label: t('描述信息备份'),
			minWidth: 200,
			showOverflowTooltip: true,
		},
		{
			prop: '移动端展示',
			label: t('移动端展示'),
			minWidth: 100,
			align: 'center',
		},
		{
			prop: '审核状态',
			label: t('审核状态'),
			minWidth: 100,
			align: 'center',
		},
	]

	return translatedColumns.map((col) => ({
		...col,
		attrs: {
			resizable: pageStyles.value.tableResizable,
		},
		// 确保宽度属性存在#ebeef5 !important
		...(pageStyles.value.tableResizable
			? {
					sortable: false, // 禁用排序以避免冲突
					showOverflowTooltip: true,
			  }
			: {}),
	}))
})

// 表格操作按钮
const buttons = computed(() => [
	{label: t('preview'), type: 'info', code: 'preview'},
	{label: t('modify'), type: 'primary', code: 'edit'},
	{
		label: t('delete'),
		type: 'danger',
		code: 'delete',
		popconfirm: currentLanguage.value === 'zh' ? '确认删除吗?' : 'Confirm delete?',
	},
	{label: t('quickLink'), type: 'primary', code: 'quickLink'},
	{
		label: currentLanguage.value === 'zh' ? '批注' : 'Comments',
		type: 'warning',
		code: 'comments',
	},
	{
		label: currentLanguage.value === 'zh' ? '审核管理' : 'Audit Management',
		type: 'primary',
		code: 'auditManage',
		more: true,
	},
	{
		label: currentLanguage.value === 'zh' ? '多用户协作' : 'Multi-user Collaboration',
		type: 'primary',
		code: 'collaboration',
		more: true,
	},
	{label: t('translationSettings'), type: 'primary', code: 'translation', more: true},
	{
		label: currentLanguage.value === 'zh' ? '信息锁定/解锁' : 'Lock/Unlock',
		type: 'primary',
		code: 'lock',
		more: true,
	},
	{
		label: currentLanguage.value === 'zh' ? '版本注释' : 'Version Notes',
		type: 'primary',
		code: 'version',
		more: true,
	},
	{
		label: currentLanguage.value === 'zh' ? '突出显示设置' : 'Highlight Settings',
		type: 'primary',
		code: 'highlight',
		more: true,
	},
	{
		label: currentLanguage.value === 'zh' ? '关联业务数据' : 'Related Business Data',
		type: 'primary',
		code: 'relation',
		more: true,
	},
])

// 对话框相关
const showDialog = ref(false)
const dialogFormRef = ref()
const currentRow = ref<BusinessTableDataRecord | null>(null)

// 移除富文本编辑器相关变量

// 移除过滤逻辑，现在数据信息使用普通输入框

// 移除富文本编辑器相关方法

// 对话框表单数据
const dialogForm = ref({
	所属分类: '',
	所属标签: '',
	字段名称: '',
	字段描述: '',
	描述信息关联: '',
	表描述: '',
	版本: '',
	优先级设置: '',
	自动更新: true,
	数据信息: '',
	审核流程: '',
})

// 对话框表单配置
const dialogFormProps = ref([
	{
		label: '所属分类',
		prop: '所属分类',
		type: 'select',
		placeholder: '请选择',
		options: [
			{label: '基础数据管理', value: '基础数据管理'},
			{label: '业务数据管理', value: '业务数据管理'},
			{label: '系统配置数据', value: '系统配置数据'},
			{label: '用户权限数据', value: '用户权限数据'},
			{label: '日志审计数据', value: '日志审计数据'},
			{label: '统计分析数据', value: '统计分析数据'},
		],
	},
	{
		label: '所属标签',
		prop: '所属标签',
		type: 'select',
		placeholder: '请选择',
		options: [
			{label: '用户信息', value: '用户信息'},
			{label: '组织架构', value: '组织架构'},
			{label: '办事流程', value: '办事流程'},
			{label: '表单模板', value: '表单模板'},
			{label: '证照管理', value: '证照管理'},
			{label: '字典配置', value: '字典配置'},
			{label: '参数设置', value: '参数设置'},
			{label: '角色权限', value: '角色权限'},
			{label: '数据权限', value: '数据权限'},
			{label: '操作日志', value: '操作日志'},
			{label: '系统日志', value: '系统日志'},
			{label: '业务统计', value: '业务统计'},
			{label: '地区编码', value: '地区编码'},
			{label: '事项清单', value: '事项清单'},
			{label: '接口配置', value: '接口配置'},
		],
	},
	{
		label: '字段名称',
		prop: '字段名称',
		type: 'select',
		placeholder: '请选择',
		options: [
			{label: '用户ID', value: '用户ID'},
			{label: '用户名', value: '用户名'},
			{label: '创建时间', value: '创建时间'},
			{label: '更新时间', value: '更新时间'},
		],
	},
	{
		label: '字段描述',
		prop: '字段描述',
		type: 'textarea',
		placeholder: '请输入',
	},
	{
		label: '描述信息关联',
		prop: '描述信息关联',
		type: 'select',
		placeholder: '请选择',
		options: [
			{label: '用户表', value: '用户表'},
			{label: '订单表', value: '订单表'},
			{label: '产品表', value: '产品表'},
		],
	},
	{
		label: '表描述',
		prop: '表描述',
		type: 'textarea',
		placeholder: '请输入',
	},
	{
		label: '版本',
		prop: '版本',
		type: 'text',
		placeholder: '请输入',
	},
	{
		label: '优先级设置',
		prop: '优先级设置',
		type: 'select',
		placeholder: '请选择 低/中/高',
		options: [
			{label: '低', value: '低'},
			{label: '中', value: '中'},
			{label: '高', value: '高'},
		],
	},
	{
		label: '自动更新',
		prop: '自动更新',
		type: 'switch',
	},
	{
		label: '数据信息',
		prop: '数据信息',
		type: 'textarea',
		placeholder: '请输入数据信息',
		rows: 6,
	},
	{
		label: '审核流程',
		prop: '审核流程',
		type: 'select',
		placeholder: '请选择',
		options: [
			{label: '标准流程', value: '标准流程'},
			{label: '快速流程', value: '快速流程'},
			{label: '严格流程', value: '严格流程'},
		],
	},
])

// 对话框表单验证规则
const dialogFormRules = {
	所属分类: [{required: true, message: '请选择所属分类', trigger: 'change'}],
	所属标签: [{required: true, message: '请选择所属标签', trigger: 'change'}],
	字段名称: [{required: true, message: '请选择字段名称', trigger: 'change'}],
	字段描述: [{required: true, message: '请输入字段描述', trigger: 'blur'}],
	描述信息关联: [{required: true, message: '请选择描述信息关联', trigger: 'change'}],
	表描述: [{required: true, message: '请输入表描述', trigger: 'blur'}],
	版本: [{required: true, message: '请输入版本', trigger: 'blur'}],
	优先级设置: [{required: true, message: '请选择优先级设置', trigger: 'change'}],
	数据信息: [{required: true, message: '请输入数据信息', trigger: 'blur'}],
}

// 表格数据状态
const tableDataState = ref([])

// 执行搜索的函数
const performSearch = async () => {
	try {
		loading.value = true
		searchLoading.value = true

		// 模拟搜索延迟
		await new Promise((resolve) => setTimeout(resolve, 300))

		const filteredData = businessTableDataStore.getFilteredRecords({
			所属标签: searchForm.value.所属标签,
			字段信息查询: searchForm.value.字段信息查询,
			表名搜索: searchForm.value.表名搜索,
		})

		// 更新分页总数
		pagination.total = filteredData.length

		// 分页处理
		const start = (pagination.page - 1) * pagination.size
		const end = start + pagination.size
		tableDataState.value = filteredData.slice(start, end)
	} catch (error) {
		console.error('搜索失败:', error)
	} finally {
		loading.value = false
		searchLoading.value = false
		// 数据加载完成后应用斑马纹
		nextTick(() => {
			applyStripeEffect()
		})
	}
}

// 翻译表格数据内容
const translateTableData = (data: any[]) => {
	if (currentLanguage.value === 'zh') {
		return data // 中文时直接返回原数据
	}

	// 英文翻译映射
	const categoryTranslations = {
		基础数据管理: 'Basic Data Management',
		业务数据管理: 'Business Data Management',
		系统配置数据: 'System Configuration Data',
		用户权限数据: 'User Permission Data',
		日志审计数据: 'Log Audit Data',
		统计分析数据: 'Statistical Analysis Data',
	}

	const tagTranslations = {
		用户信息: 'User Info',
		组织架构: 'Organization',
		办事流程: 'Process',
		表单模板: 'Form Template',
		证照管理: 'License Management',
	}

	const statusTranslations = {
		已审核: 'Approved',
		待审核: 'Pending',
		审核失败: 'Rejected',
	}

	return data.map((item) => ({
		...item,
		所属分类: categoryTranslations[item.所属分类] || item.所属分类,
		所属标签: tagTranslations[item.所属标签] || item.所属标签,
		审核状态: statusTranslations[item.审核状态] || item.审核状态,
		// 可以根据需要添加更多字段的翻译
	}))
}

// 计算属性 - 获取过滤后的表格数据
const tableData = computed(() => {
	return translateTableData(tableDataState.value)
})

// 防抖函数
const debounce = (func: Function, delay: number) => {
	let timeoutId: NodeJS.Timeout
	return (...args: any[]) => {
		clearTimeout(timeoutId)
		timeoutId = setTimeout(() => func.apply(null, args), delay)
	}
}

// 实时搜索防抖函数
const debouncedSearch = debounce(async () => {
	try {
		pagination.page = 1
		await performSearch()
	} catch (error) {
		console.error('实时搜索失败:', error)
	}
}, 300)

// 监听搜索表单变化，实现实时搜索
watch(
	searchForm,
	() => {
		// 立即显示loading状态
		loading.value = true
		searchLoading.value = true
		// 使用防抖搜索
		debouncedSearch()
	},
	{deep: true}
)

// 表格列宽调整控制函数 - 使用TableV2内置resizable属性
const setupColumnResizeControl = () => {
	console.log('设置列宽调整控制，当前状态:', pageStyles.value.tableResizable)

	nextTick(() => {
		if (pageStyles.value.tableResizable) {
			initializeColumnResize()
		} else {
			removeColumnResizeListeners()
		}
		console.log('列宽调整设置已应用')
	})
}

// 初始化列宽调整功能
const initializeColumnResize = () => {
	console.log('开始初始化列宽调整功能...')

	const tableElement = document.querySelector('.business-table-data .el-table')
	console.log('找到表格元素:', !!tableElement)

	if (!tableElement) {
		console.warn('未找到表格元素，尝试其他选择器...')
		const altTableElement = document.querySelector('.el-table')
		console.log('备用选择器找到表格:', !!altTableElement)
		if (!altTableElement) return
	}

	const actualTable = tableElement || document.querySelector('.el-table')
	if (!actualTable) {
		console.error('无法找到表格元素')
		return
	}

	const headers = actualTable.querySelectorAll('th')
	console.log('找到表头数量:', headers.length)

	headers.forEach((header, index) => {
		console.log(`处理表头 ${index + 1}:`, header.textContent?.trim())

		// 移除之前的监听器
		header.removeEventListener('mousedown', handleMouseDown)

		// 添加新的监听器
		header.addEventListener('mousedown', handleMouseDown)

		// 设置数据属性
		header.setAttribute('data-column-index', index.toString())

		// 添加调试样式
		header.style.position = 'relative'
		header.style.userSelect = 'none'

		console.log(`表头 ${index + 1} 初始化完成`)
	})

	console.log('列宽调整功能初始化完成，表头数量:', headers.length)
}

// 移除列宽调整监听器
const removeColumnResizeListeners = () => {
	const tableElement = document.querySelector('.business-table-data .el-table')
	if (!tableElement) return

	const headers = tableElement.querySelectorAll('th')
	headers.forEach((header) => {
		header.removeEventListener('mousedown', handleMouseDown)
	})

	console.log('已移除列宽调整监听器')
}

// 鼠标按下事件处理
const handleMouseDown = (e: Event) => {
	const mouseEvent = e as MouseEvent
	const header = mouseEvent.currentTarget as HTMLElement
	const rect = header.getBoundingClientRect()

	console.log('=== 鼠标按下事件 ===')
	console.log('表头文本:', header.textContent?.trim())
	console.log('鼠标位置:', {x: mouseEvent.clientX, y: mouseEvent.clientY})
	console.log('表头边界:', {left: rect.left, right: rect.right, width: rect.width})
	console.log('距离右边缘距离:', rect.right - mouseEvent.clientX)

	// 拖拽区域检测：右边缘10px范围内可以拖拽（参考测试文件实现）
	const isInResizeArea = mouseEvent.clientX >= rect.right - 10
	console.log('是否在拖拽区域:', isInResizeArea)

	if (isInResizeArea) {
		console.log('✅ 进入拖拽模式')
		e.preventDefault()
		e.stopPropagation()

		resizeState.isResizing = true
		resizeState.startX = mouseEvent.clientX
		resizeState.startWidth = header.offsetWidth
		resizeState.currentColumn = header
		resizeState.columnIndex = parseInt(header.getAttribute('data-column-index') || '-1')

		// 添加样式
		header.classList.add('is-resizing')
		// document.body.style.cursor = 'col-resize'
		document.body.style.userSelect = 'none'

		// 添加全局监听器
		document.addEventListener('mousemove', handleMouseMove)
		document.addEventListener('mouseup', handleMouseUp)

		console.log(
			`🎯 开始调整第${resizeState.columnIndex + 1}列宽度，初始宽度: ${
				resizeState.startWidth
			}px`
		)
	} else {
		console.log('❌ 不在拖拽区域，忽略事件')
	}
}

// 鼠标移动事件处理
const handleMouseMove = (e: MouseEvent) => {
	if (!resizeState.isResizing || !resizeState.currentColumn) return

	e.preventDefault()
	e.stopPropagation()

	const diff = e.clientX - resizeState.startX
	const newWidth = Math.max(80, Math.min(500, resizeState.startWidth + diff))

	console.log(`📏 拖拽中: 移动距离=${diff}px, 新宽度=${newWidth}px`)

	// 更新表头列宽
	resizeState.currentColumn.style.width = newWidth + 'px'
	resizeState.currentColumn.style.minWidth = newWidth + 'px'

	// 同时更新对应的数据列
	const tableElement =
		document.querySelector('.business-table-data .el-table') ||
		document.querySelector('.el-table')
	if (tableElement) {
		const dataCells = tableElement.querySelectorAll(
			`tbody tr td:nth-child(${resizeState.columnIndex + 1})`
		)
		console.log(`更新 ${dataCells.length} 个数据单元格的宽度`)
		dataCells.forEach((cell) => {
			;(cell as HTMLElement).style.width = newWidth + 'px'
			;(cell as HTMLElement).style.minWidth = newWidth + 'px'
		})
	} else {
		console.warn('未找到表格元素，无法更新数据单元格')
	}
}

// 鼠标释放事件处理
const handleMouseUp = () => {
	console.log('=== 鼠标释放事件 ===')
	console.log('当前是否在拖拽:', resizeState.isResizing)

	if (resizeState.isResizing && resizeState.currentColumn) {
		// 移除样式
		resizeState.currentColumn.classList.remove('is-resizing')
		// document.body.style.cursor = ''
		document.body.style.userSelect = ''

		console.log(
			`✅ 完成第${resizeState.columnIndex + 1}列宽度调整，新宽度: ${
				resizeState.currentColumn.offsetWidth
			}px`
		)
	}

	// 重置状态
	resizeState.isResizing = false
	resizeState.currentColumn = null
	resizeState.columnIndex = -1
	resizeState.startX = 0
	resizeState.startWidth = 0

	// 移除全局监听器
	document.removeEventListener('mousemove', handleMouseMove)
	document.removeEventListener('mouseup', handleMouseUp)

	console.log('🔄 拖拽状态已重置')
}

// 流程管理相关
const workflowDialogVisible = ref(false)
const workflowLoading = ref(false)
const currentWorkflow = ref(null)
const isEditingWorkflow = ref(false)
const workflowForm = ref({
	name: '',
	description: '',
	nodes: [],
	auditMode: 'serial', // 'serial' | 'parallel' | 'countersign'
	auditRule: 'majority', // 'unanimous' | 'veto' | 'majority'
	// 串行审核配置
	timeoutAction: 'escalate', // 'auto_pass' | 'auto_reject' | 'escalate'
	allowReturn: true,
	// 并行审核配置
	minApprovalCount: 1,
	approvalRatio: '50',
	// 会签审核配置
	countersignType: 'all', // 'all' | 'partial' | 'sequential'
	countersignCondition: 'all_agree', // 'all_agree' | 'majority_agree' | 'any_agree'
	// 特殊配置
	specialConfig: ['notifyApplicant', 'recordAuditLog'],
})

// 审核人选择相关
const auditorSelectorVisible = ref(false)
const currentNodeIndex = ref(-1)
const auditorSearchKeyword = ref('')
const selectedAuditors = ref([])
const auditorPagination = ref({
	page: 1,
	size: 5,
	total: 0,
})

// 审核人员数据
const auditorList = ref([
	{id: '1', name: '张明华', department: '行政审批科', position: '科长', phone: '138****1234'},
	{id: '2', name: '李晓红', department: '政务服务科', position: '副科长', phone: '139****5678'},
	{id: '3', name: '王建国', department: '信息管理科', position: '主任科员', phone: '136****9012'},
	{id: '4', name: '陈美玲', department: '综合协调科', position: '科员', phone: '137****3456'},
	{id: '5', name: '刘志强', department: '法制监督科', position: '副科长', phone: '135****7890'},
	{id: '6', name: '赵雅琴', department: '窗口服务科', position: '科长', phone: '134****2345'},
	{id: '7', name: '孙德华', department: '电子政务科', position: '主任科员', phone: '133****6789'},
	{id: '8', name: '周丽娟', department: '质量监督科', position: '科员', phone: '132****0123'},
])

// 当前显示的审核人列表（用于搜索和分页）
const currentAuditorList = computed(() => {
	let filteredList = auditorList.value

	// 搜索过滤
	if (auditorSearchKeyword.value) {
		filteredList = filteredList.filter(
			(auditor) =>
				auditor.name.includes(auditorSearchKeyword.value) ||
				auditor.department.includes(auditorSearchKeyword.value)
		)
	}

	// 更新总数
	auditorPagination.value.total = filteredList.length

	// 分页处理
	const start = (auditorPagination.value.page - 1) * auditorPagination.value.size
	const end = start + auditorPagination.value.size
	return filteredList.slice(start, end)
})

// 信息化模板管理相关
const infoTemplateDialogVisible = ref(false)
const templateData = ref([
	{
		id: 1,
		序号: 1,
		模板名称: '企业基础信息采集模板',
		模板描述: '用于采集企业基本信息，包括企业名称、注册地址、法人代表、经营范围等核心数据字段',
	},
	{
		id: 2,
		序号: 2,
		模板名称: '财务数据统计模板',
		模板描述: '专门用于财务数据的标准化采集，涵盖收入、支出、资产负债等财务指标字段',
	},
	{
		id: 3,
		序号: 3,
		模板名称: '人员信息管理模板',
		模板描述: '用于人力资源管理的标准模板，包含员工基本信息、岗位信息、薪资结构等字段',
	},
	{
		id: 4,
		序号: 4,
		模板名称: '项目进度跟踪模板',
		模板描述: '项目管理专用模板，包含项目名称、进度状态、里程碑节点、负责人等关键字段',
	},
	{
		id: 5,
		序号: 5,
		模板名称: '客户关系管理模板',
		模板描述: 'CRM系统标准模板，涵盖客户基本信息、联系方式、业务往来记录等客户管理字段',
	},
	{
		id: 6,
		序号: 6,
		模板名称: '库存管理模板',
		模板描述: '用于库存管理的标准模板，包含商品信息、库存数量、入库出库记录等仓储管理字段',
	},
	{
		id: 7,
		序号: 7,
		模板名称: '销售业绩统计模板',
		模板描述: '销售部门专用模板，涵盖销售额、客户数量、产品销量等销售业绩统计字段',
	},
	{
		id: 8,
		序号: 8,
		模板名称: '设备维护记录模板',
		模板描述: '设备管理专用模板，包含设备编号、维护时间、故障记录、维修成本等设备管理字段',
	},
	{
		id: 9,
		序号: 9,
		模板名称: '供应商信息管理模板',
		模板描述: '供应链管理模板，涵盖供应商基本信息、合作历史、评价等级等供应商管理字段',
	},
	{
		id: 10,
		序号: 10,
		模板名称: '培训记录管理模板',
		模板描述: '人力资源培训专用模板，包含培训课程、参与人员、培训效果评估等培训管理字段',
	},
	{
		id: 11,
		序号: 11,
		模板名称: '质量检测报告模板',
		模板描述: '质量管理部门模板，涵盖检测项目、检测结果、质量标准、改进建议等质量管理字段',
	},
	{
		id: 12,
		序号: 12,
		模板名称: '合同管理模板',
		模板描述: '法务部门专用模板，包含合同编号、签约方信息、合同条款、执行状态等合同管理字段',
	},
	{
		id: 13,
		序号: 13,
		模板名称: '市场调研数据模板',
		模板描述: '市场部门调研模板，涵盖调研对象、市场趋势、竞争分析等市场调研数据字段',
	},
	{
		id: 14,
		序号: 14,
		模板名称: '产品开发进度模板',
		模板描述: '研发部门专用模板，包含产品规划、开发阶段、技术指标、上市时间等产品开发字段',
	},
	{
		id: 15,
		序号: 15,
		模板名称: '成本核算分析模板',
		模板描述: '财务成本分析模板，涵盖直接成本、间接成本、成本分摊、盈亏分析等成本核算字段',
	},
])

// 分页相关状态
const templateCurrentPage = ref(1)
const templatePageSize = ref(5)
const templateTotal = computed(() => templateData.value.length)

// 当前页数据
const currentTemplateData = computed(() => {
	const start = (templateCurrentPage.value - 1) * templatePageSize.value
	const end = start + templatePageSize.value
	return templateData.value.slice(start, end).map((item, index) => ({
		...item,
		序号: start + index + 1,
	}))
})

// 模板编辑状态
const editingTemplate = ref<any>(null)
const isEditingTemplate = ref(false)

// 信息化模板相关方法
const openInfoTemplateDialog = () => {
	infoTemplateDialogVisible.value = true
}

const closeInfoTemplateDialog = () => {
	infoTemplateDialogVisible.value = false
	editingTemplate.value = null
	isEditingTemplate.value = false
}

// 分页事件处理
const handleTemplateSizeChange = (val: number) => {
	templatePageSize.value = val
	templateCurrentPage.value = 1
}

const handleTemplateCurrentChange = (val: number) => {
	templateCurrentPage.value = val
}

// 添加模板
const addTemplate = (template: any) => {
	ElMessage.success(`已应用模板: ${template.模板名称}`)
	// 可以在这里添加实际的模板应用逻辑
}

// 删除模板
const deleteTemplate = (template: any) => {
	ElMessageBox.confirm(`确定要删除模板"${template.模板名称}"吗？删除后将无法恢复。`, '删除确认', {
		confirmButtonText: '确定删除',
		cancelButtonText: '取消',
		type: 'warning',
		confirmButtonClass: 'el-button--danger',
	})
		.then(() => {
			// 从数组中删除该模板
			const index = templateData.value.findIndex((item) => item.id === template.id)
			if (index > -1) {
				templateData.value.splice(index, 1)

				// 检查当前页是否还有数据，如果没有则回到上一页
				const totalPages = Math.ceil(templateData.value.length / templatePageSize.value)
				if (templateCurrentPage.value > totalPages && totalPages > 0) {
					templateCurrentPage.value = totalPages
				}

				ElMessage.success(`模板"${template.模板名称}"已删除`)
			}
		})
		.catch(() => {
			ElMessage.info('已取消删除')
		})
}

// 应用模板
const applyTemplate = (template: any) => {
	addTemplate(template)
}

// 初始化数据
onMounted(async () => {
	console.log('组件挂载中...')
	businessTableDataStore.initializeData()
	workflowStore.initializeData()

	// 加载保存的筛选条件
	await loadSavedConditions()

	// 加载个性化设置
	loadPersonalSettings()

	// 执行初始搜索
	await performSearch()

	// 确保个性化设置在组件完全渲染后应用
	nextTick(() => {
		console.log('组件完全渲染，确保个性化设置已应用')
		// 强制更新一次pageStyles以确保响应式更新
		pageStyles.value = {...pageStyles.value}

		// 立即应用表格宽度调整设置的CSS类
		const tableElement = document.querySelector('.business-table-data')
		if (tableElement) {
			if (pageStyles.value.tableResizable) {
				// tableElement.classList.add('custom-table-resizable')
				console.log('初始化时启用表格列宽调整')
			} else {
				// tableElement.classList.remove('custom-table-resizable')
				console.log('初始化时禁用表格列宽调整')
			}
		}

		// 初始化列宽调整控制
		setupColumnResizeControl()

		// 应用保存的突出显示颜色
		applyHighlightColors()

		// 监听tableResizable变化，动态应用或移除事件拦截
		watch(
			() => pageStyles.value.tableResizable,
			(newValue) => {
				console.log('tableResizable变化:', newValue)

				// 立即更新CSS类
				const tableEl = document.querySelector('.business-table-data')
				if (tableEl) {
					if (newValue) {
						tableEl.classList.add('custom-table-resizable')
					} else {
						tableEl.classList.remove('custom-table-resizable')
					}
				}

				// 重新设置列宽调整控制
				setupColumnResizeControl()
			}
		)
	})
})

// 组件卸载时清理
onUnmounted(() => {
	console.log('组件卸载，清理资源')

	// 清理列宽调整相关的监听器
	removeColumnResizeListeners()

	// 清理全局监听器
	document.removeEventListener('mousemove', handleMouseMove)
	document.removeEventListener('mouseup', handleMouseUp)

	// 清理突出显示样式
	clearHighlightStyles()
})

// Block组件相关函数
const expendSearch = (expanded: boolean) => {
	// 处理搜索区域展开/收起逻辑
	console.log('搜索区域展开状态:', expanded)
}

// 块高度变化事件（表格现在使用自动高度）
const onBlockHeightChanged = () => {
	// 表格现在使用auto-height，无需手动设置高度
}

// 查询
const onSearch = async () => {
	try {
		pagination.page = 1
		await performSearch()
		ElMessage.success('查询完成')
	} catch (error) {
		console.error('查询失败:', error)
		ElMessage.error('查询失败，请重试')
	}
}

// 重置
const onReset = () => {
	searchForm.value = {
		所属标签: '',
		字段信息查询: '',
		表名搜索: '',
	}
	pagination.page = 1
}

// 新建流程
const onClickAddWorkflow = () => {
	workflowDialogVisible.value = true
	currentWorkflow.value = null
	isEditingWorkflow.value = false
	// 重置表单数据
	workflowForm.value = {
		name: '',
		description: '',
		nodes: [],
		auditMode: 'serial',
		auditRule: 'majority',
		// 串行审核配置
		timeoutAction: 'escalate',
		allowReturn: true,
		// 并行审核配置
		minApprovalCount: 1,
		approvalRatio: '50',
		// 会签审核配置
		countersignType: 'all',
		countersignCondition: 'all_agree',
		// 特殊配置
		specialConfig: ['notifyApplicant', 'recordAuditLog'],
	}
}

// 保存筛选条件
const onKeepInStorage = () => {
	try {
		console.log('当前搜索表单数据:', searchForm.value) // 调试信息

		// 获取当前筛选条件（包括空字符串）
		const currentConditions = {
			所属标签: searchForm.value.所属标签?.trim() || '',
			字段信息查询: searchForm.value.字段信息查询?.trim() || '',
			表名搜索: searchForm.value.表名搜索?.trim() || '',
		}

		console.log('处理后的条件:', currentConditions) // 调试信息

		// 保存到localStorage（支持保存空字符串）
		localStorage.setItem(
			'businessTableData_filterConditions',
			JSON.stringify(currentConditions)
		)
		savedFilterConditions.value = {...currentConditions}

		console.log(
			'已保存到localStorage:',
			localStorage.getItem('businessTableData_filterConditions')
		) // 调试信息

		// 统计非空条件数量用于提示
		const nonEmptyCount = Object.values(currentConditions).filter(
			(value) => value !== ''
		).length
		const totalCount = Object.keys(currentConditions).length

		if (nonEmptyCount === 0) {
			ElMessage.success('已保存空筛选条件，下次访问时将清空所有筛选条件')
		} else if (nonEmptyCount === totalCount) {
			ElMessage.success(`已保存 ${nonEmptyCount} 个筛选条件，下次访问时将自动填充`)
		} else {
			ElMessage.success(
				`已保存筛选条件（${nonEmptyCount} 个有值，${
					totalCount - nonEmptyCount
				} 个为空），下次访问时将自动填充`
			)
		}
	} catch (error) {
		console.error('保存筛选条件失败:', error)
		ElMessage.error('保存筛选条件失败，请重试')
	}
}

// 返回
const onReturn = () => {
	ElMessage.info('返回功能开发中...')
}

// 表格操作点击事件
const onTableClickButton = ({row, btn}: any) => {
	if (btn.code === 'preview') {
		onPreview(row)
	} else if (btn.code === 'edit') {
		onEdit(row)
	} else if (btn.code === 'delete') {
		onDelete(row)
	} else if (btn.code === 'quickLink') {
		openQuickLinkDialog(row)
	} else if (btn.code === 'comments') {
		openCommentsDialog(row)
	} else if (btn.code === 'auditManage') {
		handleRowMoreAction('auditManage', row)
	} else if (btn.code === 'collaboration') {
		handleRowMoreAction('collaboration', row)
	} else if (btn.code === 'translation') {
		handleRowMoreAction('translation', row)
	} else if (btn.code === 'lock') {
		handleRowMoreAction('lock', row)
	} else if (btn.code === 'version') {
		handleRowMoreAction('version', row)
	} else if (btn.code === 'highlight') {
		handleRowMoreAction('highlight', row)
	} else if (btn.code === 'relation') {
		handleRowMoreAction('relation', row)
	}
}

// 新增
const onAdd = () => {
	currentRow.value = null
	dialogForm.value = {
		所属分类: '',
		所属标签: '',
		字段名称: '',
		字段描述: '',
		描述信息关联: '',
		表描述: '',
		版本: '',
		优先级设置: '',
		自动更新: true,
		数据信息: '',
		审核流程: '',
	}
	showDialog.value = true
}

// 编辑
const onEdit = (row: BusinessTableDataRecord) => {
	// 检查是否被锁定
	if (getLockStatus(row.id)) {
		ElMessage.warning(`信息"${row.字段信息描述}"已被锁定，无法进行修改操作`)
		return
	}

	currentRow.value = row
	dialogForm.value = {
		所属分类: row.所属分类,
		所属标签: row.所属标签,
		字段名称: row.字段信息描述, // 将字段信息描述映射到字段名称
		字段描述: row.描述信息备份, // 将描述信息备份映射到字段描述
		描述信息关联: '用户表', // 设置默认值为第一个选项
		表描述: row.表描述,
		版本: '1.0',
		优先级设置: '中',
		自动更新: row.移动端展示, // 将移动端展示映射到自动更新
		数据信息: '<p>这是示例数据信息内容</p>', // 示例富文本内容
		审核流程: '',
	}
	showDialog.value = true
}

// 预览
const onPreview = (row: BusinessTableDataRecord) => {
	// 跳转到预览页面
	router.push({
		path: '/businessTableDataDes/preview',
		query: {
			id: row.id,
			free: 'true',
		},
	})
}

// 删除
const onDelete = async (row: BusinessTableDataRecord) => {
	// 检查是否被锁定
	if (getLockStatus(row.id)) {
		ElMessage.warning(`信息"${row.字段信息描述}"已被锁定，无法进行删除操作`)
		return
	}

	try {
		await ElMessageBox.confirm(`确定要删除"${row.字段信息描述}"吗？`, '删除确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})

		// 显示删除Loading状态
		loading.value = true

		// 模拟删除延迟
		await new Promise((resolve) => setTimeout(resolve, 300))

		const success = businessTableDataStore.deleteRecord(row.id)
		if (success) {
			ElMessage.success('删除成功')

			// 添加删除操作的历史记录
			const currentOperator = '张安'
			const currentTime =
				new Date().toLocaleDateString().replace(/\//g, '.') +
				' ' +
				new Date().toLocaleTimeString('en-GB').replace(/:/g, ':')
			const historyRecord = {
				id: `op_${Date.now()}`,
				序号: operationHistoryList.value.length + 1,
				操作内容: `删除了"${row.字段信息描述}"`,
				操作人: currentOperator,
				操作时间: currentTime,
			}
			operationHistoryList.value.unshift(historyRecord)
			operationHistoryPagination.value.total = operationHistoryList.value.length

			// 重新执行搜索以更新数据
			await performSearch()

			// 如果当前页没有数据了，回到上一页
			if (tableData.value.length === 0 && pagination.page > 1) {
				pagination.page--
				await performSearch()
			}
		} else {
			ElMessage.error('删除失败')
		}
	} catch {
		// 用户取消删除
	} finally {
		loading.value = false
	}
}

// 移动端展示切换
const onMobileDisplayChange = (row: BusinessTableDataRecord) => {
	businessTableDataStore.updateRecord(row.id, {移动端展示: row.移动端展示})
	ElMessage.success('移动端展示状态已更新')
}

// 表格选择变化
const handleSelectionChange = (selection: BusinessTableDataRecord[]) => {
	selectedRows.value = selection
}

// 分页变化
const onPaginationChange = async (val: any, type: any) => {
	try {
		paginationLoading.value = true

		if (type === 'page') {
			pagination.page = val
		} else {
			pagination.size = val
			pagination.page = 1 // 改变每页大小时重置到第一页
		}

		// 重新执行搜索以更新分页数据
		await performSearch()
	} catch (error) {
		console.error('分页操作失败:', error)
		ElMessage.error('分页操作失败，请重试')
	} finally {
		paginationLoading.value = false
	}
}

// 操作按钮事件处理函数
const onDataSave = () => {
	// 跳转到数据信息页面
	router.push('/dataInfo')
}

const onBatchImport = () => {
	batchImportDialogVisible.value = true
	importFileList.value = []
	selectedFileName.value = ''
}

const onBatchExport = async () => {
	// 检查是否选中了数据
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请先选择要导出的数据')
		return
	}

	const exportData = selectedRows.value
	const exportType = '选中'

	exportLoading.value = true

	try {
		const workbook = new ExcelJS.Workbook()
		const worksheet = workbook.addWorksheet('业务表数据')

		// 设置表头
		const headers = [
			{header: '序号', key: '序号'},
			{header: '所属分类', key: '所属分类'},
			{header: '所属标签', key: '所属标签'},
			{header: '字段信息描述', key: '字段信息描述'},
			{header: '表描述', key: '表描述'},
			{header: '描述信息备份', key: '描述信息备份'},
			{header: '移动端展示', key: '移动端展示'},
			{header: '创建时间', key: '创建时间'},
			{header: '创建人', key: '创建人'},
		]

		worksheet.columns = headers

		// 设置表头样式
		const headerRow = worksheet.getRow(1)
		headerRow.font = {bold: true}
		headerRow.fill = {
			type: 'pattern',
			pattern: 'solid',
			fgColor: {argb: 'FFE6F3FF'},
		}

		// 添加数据
		exportData.forEach((row, index) => {
			worksheet.addRow({
				序号: index + 1,
				所属分类: row.所属分类,
				所属标签: row.所属标签,
				字段信息描述: row.字段信息描述,
				表描述: row.表描述,
				描述信息备份: row.描述信息备份,
				移动端展示: row.移动端展示 ? '是' : '否',
				创建时间: row.创建时间,
				创建人: row.创建人,
			})
		})

		// 设置所有单元格边框
		worksheet.eachRow((row) => {
			row.eachCell((cell) => {
				cell.border = {
					top: {style: 'thin'},
					left: {style: 'thin'},
					bottom: {style: 'thin'},
					right: {style: 'thin'},
				}
			})
		})

		// 生成文件
		const buffer = await workbook.xlsx.writeBuffer()
		const blob = new Blob([buffer], {
			type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
		})

		// 下载文件
		const fileName = `业务表数据_${exportType}_${new Date().toISOString().slice(0, 10)}.xlsx`
		saveAs(blob, fileName)

		ElMessage.success(`成功导出${exportType} ${exportData.length} 条数据`)

		// 添加批量导出操作的历史记录
		const currentOperator = '张安'
		const currentTime =
			new Date().toLocaleDateString().replace(/\//g, '.') +
			' ' +
			new Date().toLocaleTimeString('en-GB').replace(/:/g, ':')
		const historyRecord = {
			id: `op_${Date.now()}`,
			序号: operationHistoryList.value.length + 1,
			操作内容: `批量导出了 ${exportData.length} 条数据`,
			操作人: currentOperator,
			操作时间: currentTime,
		}
		operationHistoryList.value.unshift(historyRecord)
		operationHistoryPagination.value.total = operationHistoryList.value.length
	} catch (error) {
		console.error('导出失败:', error)
		ElMessage.error('导出失败，请重试')
	} finally {
		exportLoading.value = false
	}
}

const onBatchEdit = () => {
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请先选择要修改的数据')
		return
	}

	// 重置表单和字段选择
	batchEditForm.value = {
		所属分类: '',
		所属标签: '',
		表描述: '',
		描述信息备份: '',
		移动端展示: false,
	}
	batchEditFields.value = {
		所属分类: false,
		所属标签: false,
		表描述: false,
		描述信息备份: false,
		移动端展示: false,
	}

	batchEditDialogVisible.value = true
}

const onBatchDelete = async () => {
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请先选择要删除的数据')
		return
	}

	try {
		await ElMessageBox.confirm(
			`确认批量删除选中的 ${selectedRows.value.length} 条数据吗？此操作不可恢复。`,
			'批量删除确认',
			{
				confirmButtonText: '确定删除',
				cancelButtonText: '取消',
				type: 'warning',
				dangerouslyUseHTMLString: true,
			}
		)

		// 显示删除Loading状态
		loading.value = true

		// 模拟批量删除延迟
		await new Promise((resolve) => setTimeout(resolve, 500))

		// 获取要删除的ID列表
		const idsToDelete = selectedRows.value.map((row) => row.id)
		const deleteCount = idsToDelete.length

		// 执行批量删除
		businessTableDataStore.batchDeleteRecords(idsToDelete)

		// 添加批量删除操作的历史记录
		const currentOperator = '张安'
		const currentTime =
			new Date().toLocaleDateString().replace(/\//g, '.') +
			' ' +
			new Date().toLocaleTimeString('en-GB').replace(/:/g, ':')
		const historyRecord = {
			id: `op_${Date.now()}`,
			序号: operationHistoryList.value.length + 1,
			操作内容: `批量删除了 ${deleteCount} 条数据`,
			操作人: currentOperator,
			操作时间: currentTime,
		}
		operationHistoryList.value.unshift(historyRecord)
		operationHistoryPagination.value.total = operationHistoryList.value.length

		// 清空选择
		selectedRows.value = []

		// 重新执行搜索以更新数据
		await performSearch()

		// 检查当前页是否还有数据，如果没有则回到上一页
		if (tableData.value.length === 0 && pagination.page > 1) {
			pagination.page--
			await performSearch()
		}

		ElMessage.success(`成功删除 ${deleteCount} 条数据`)
	} catch {
		// 用户取消删除
	} finally {
		loading.value = false
	}
}

const onMultiDimensionClassify = () => {
	// 初始化分页数据
	multiDimensionPagination.value.total = multiDimensionAllList.value.length
	multiDimensionPagination.value.page = 1
	multiDimensionDialogVisible.value = true
}

// 多维度分类弹窗确定按钮
const onMultiDimensionDialogConfirm = () => {
	multiDimensionDialogVisible.value = false
}

// 多维度分类分页变化
const onMultiDimensionPaginationChange = async (val: any, type: any) => {
	try {
		multiDimensionPaginationLoading.value = true

		// 模拟分页加载延迟
		await new Promise((resolve) => setTimeout(resolve, 300))

		if (type === 'page') {
			multiDimensionPagination.value.page = val
		} else {
			multiDimensionPagination.value.size = val
			multiDimensionPagination.value.page = 1 // 改变每页大小时重置到第一页
		}
	} catch (error) {
		console.error('分页操作失败:', error)
		ElMessage.error('分页操作失败，请重试')
	} finally {
		multiDimensionPaginationLoading.value = false
	}
}

// 更多操作下拉菜单处理
const handleMoreActions = (command: string) => {
	switch (command) {
		case 'auditManage':
			openAuditManageDialog()
			break
		case 'businessRules':
			businessRulesDialogVisible.value = true
			break
		case 'dynamicUpdate':
			dynamicUpdateDialogVisible.value = true
			break
		case 'infoTemplate':
			openInfoTemplateDialog()
			break
		case 'statisticsAnalysis':
			// 跳转到统计分析页面
			console.log('跳转到统计分析页面')
			router.push('/businessTableDataDes/statistics?free=true')
			break
		case 'infoRecommend':
			infoRecommendDialogVisible.value = true
			loadInfoRecommendData()
			break
		case 'historyRecord':
			historyRecordDialogVisible.value = true
			loadHistoryRecords()
			break
		case 'permissionManage':
			permissionManageDialogVisible.value = true
			loadPermissionManageData()
			break
		case 'permissionRecord':
			permissionRecordDialogVisible.value = true
			loadPermissionRecords()
			break
		case 'backupRestore':
			backupRestoreDialogVisible.value = true
			break
		case 'personalSettings':
			personalSettingsDialogVisible.value = true
			break
		case 'infoSchedule':
			infoScheduleDialogVisible.value = true
			break
		case 'customAngle':
			roleCustomDialogVisible.value = true
			break
		case 'permissionRules':
			permissionRulesDialogVisible.value = true
			break
		case 'translationSettings':
			translationSettingsDialogVisible.value = true
			break
		default:
			ElMessage.info('功能开发中...')
	}
}

// 审核管理相关函数
const openAuditManageDialog = () => {
	// 检查是否有选中的数据
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请先选择需要审核的数据')
		return
	}

	// 重置表单
	auditForm.value = {
		result: 'reject',
		opinion: '',
	}

	// 设置当前审核的行数据
	currentAuditRow.value = selectedRows.value[0] // 取第一个选中的行

	auditManageDialogVisible.value = true
}

const handleAuditConfirm = async () => {
	// 表单验证
	if (auditForm.value.result === 'reject' && !auditForm.value.opinion.trim()) {
		ElMessage.error('审核驳回时，审核意见必须填写')
		return
	}

	try {
		// 模拟审核提交
		await new Promise((resolve) => setTimeout(resolve, 1000))

		const resultText = auditForm.value.result === 'pass' ? '通过' : '驳回'
		ElMessage.success(`审核${resultText}成功`)

		// 关闭弹窗
		auditManageDialogVisible.value = false

		// 重置表单
		auditForm.value = {
			result: 'reject',
			opinion: '',
		}

		// 清空当前审核行
		currentAuditRow.value = null
	} catch (error) {
		ElMessage.error('审核操作失败')
	}
}

const handleAuditCancel = () => {
	auditManageDialogVisible.value = false
}

// 打开行级审核管理弹窗
const openRowAuditManageDialog = (row: BusinessTableDataRecord) => {
	// 重置表单
	auditForm.value = {
		result: 'reject',
		opinion: '',
	}

	// 设置当前审核的行数据
	currentAuditRow.value = row

	auditManageDialogVisible.value = true
}

// 行更多操作处理
const handleRowMoreAction = (command: string, row: BusinessTableDataRecord) => {
	switch (command) {
		case 'auditManage':
			openRowAuditManageDialog(row)
			break
		case 'collaboration':
			openCollaborationDialog(row)
			break
		case 'translation':
			openTranslationSettingsDialog(row)
			break
		case 'lock':
			openInfoLockDialog(row)
			break
		case 'version':
			openVersionNotesDialog(row)
			break
		case 'highlight':
			openHighlightSettingsDialog(row)
			break
		case 'relation':
			ElMessage.info(`"${row.字段信息描述}"关联业务数据功能开发中...`)
			break
		case 'view':
			ElMessage.info(`查看"${row.字段信息描述}"详情功能开发中...`)
			break
		case 'copy':
			ElMessage.info(`复制"${row.字段信息描述}"记录功能开发中...`)
			break
		case 'history':
			ElMessage.info(`查看"${row.字段信息描述}"历史功能开发中...`)
			break
		case 'export':
			ElMessage.info(`导出"${row.字段信息描述}"记录功能开发中...`)
			break
		default:
			break
	}
}

// 打开多用户协助对话框
const openCollaborationDialog = (row: BusinessTableDataRecord) => {
	currentCollaborationRow.value = row
	collaborationDialogVisible.value = true
}

// 打开翻译设置对话框
const openTranslationSettingsDialog = (row: BusinessTableDataRecord) => {
	console.log('打开翻译设置对话框，当前行数据:', row)
	translationSettingsDialogVisible.value = true
}

// 打开信息锁定/解锁对话框
const openInfoLockDialog = (row: BusinessTableDataRecord) => {
	currentLockRow.value = row
	// 从本地存储获取锁定状态
	const isLocked = getLockStatus(row.id)

	if (isLocked) {
		infoUnlockDialogVisible.value = true
	} else {
		infoLockDialogVisible.value = true
	}
}

// 处理信息锁定确认
const handleInfoLockConfirm = () => {
	if (currentLockRow.value) {
		// 保存锁定状态到本地存储
		setLockStatus(currentLockRow.value.id, true)

		ElMessage.success(`信息"${currentLockRow.value.字段信息描述}"已锁定，信息将无法修改`)

		infoLockDialogVisible.value = false
		currentLockRow.value = null
	}
}

// 处理信息解锁确认
const handleInfoUnlockConfirm = () => {
	if (currentLockRow.value) {
		// 保存解锁状态到本地存储
		setLockStatus(currentLockRow.value.id, false)

		ElMessage.success(`信息"${currentLockRow.value.字段信息描述}"已解锁，信息可以进行修改`)

		infoUnlockDialogVisible.value = false
		currentLockRow.value = null
	}
}

// 处理信息锁定/解锁取消
const handleInfoLockCancel = () => {
	infoLockDialogVisible.value = false
	infoUnlockDialogVisible.value = false
	currentLockRow.value = null
}

// 打开版本注释对话框
const openVersionNotesDialog = (row: BusinessTableDataRecord) => {
	currentVersionRow.value = row
	versionNotesDialogVisible.value = true
}

// 处理版本注释确认
const handleVersionNotesConfirm = () => {
	versionNotesFormRef.value.validate((valid: boolean) => {
		if (valid) {
			// 验证通过，保存数据
			ElMessage.success('版本注释保存成功')
			versionNotesDialogVisible.value = false
			currentVersionRow.value = null
		} else {
			// 验证失败
			ElMessage.warning('请检查表单填写是否正确')
		}
	})
}

// 处理版本注释取消
const handleVersionNotesCancel = () => {
	versionNotesDialogVisible.value = false
	currentVersionRow.value = null
	// 重置表单验证状态
	nextTick(() => {
		if (versionNotesFormRef.value) {
			versionNotesFormRef.value.clearValidate()
		}
	})
}

// 添加版本注释
const handleVersionAdd = () => {
	// 生成下一个版本号
	const lastVersion = versionNotesList.value[versionNotesList.value.length - 1]
	let nextVersionName = 'V1.0.0'

	if (lastVersion && lastVersion.版本名称.match(/^V(\d+)\.(\d+)\.(\d+)$/)) {
		const match = lastVersion.版本名称.match(/^V(\d+)\.(\d+)\.(\d+)$/)
		if (match) {
			const major = parseInt(match[1])
			const minor = parseInt(match[2])
			const patch = parseInt(match[3])
			nextVersionName = `V${major}.${minor}.${patch + 1}`
		}
	}

	const newVersion = {
		序号: versionNotesList.value.length + 1,
		版本名称: nextVersionName,
		注释: '',
		操作: '',
	}
	versionNotesList.value.push(newVersion)
}

// 删除版本注释
const handleVersionDelete = (index: number) => {
	if (versionNotesList.value.length <= 1) {
		ElMessage.warning('至少需要保留一个版本记录')
		return
	}

	versionNotesList.value.splice(index, 1)
	// 重新排序序号
	versionNotesList.value.forEach((item, idx) => {
		item.序号 = idx + 1
	})

	// 清除对应的表单验证
	nextTick(() => {
		if (versionNotesFormRef.value) {
			versionNotesFormRef.value.clearValidate()
		}
	})
}

// 打开批注对话框
const openCommentsDialog = (row: BusinessTableDataRecord) => {
	currentCommentsRow.value = row
	// 从本地存储加载批注数据
	loadCommentsFromStorage(row.id)
	commentsDialogVisible.value = true
}

// 处理批注确认
const handleCommentsConfirm = () => {
	// 保存批注数据到本地存储
	saveCommentsToStorage()
	ElMessage.success('批注保存成功')
	commentsDialogVisible.value = false
	currentCommentsRow.value = null
}

// 处理批注取消
const handleCommentsCancel = () => {
	commentsDialogVisible.value = false
	currentCommentsRow.value = null
	// 重置表单验证状态
	nextTick(() => {
		if (commentsFormRef.value) {
			commentsFormRef.value.clearValidate()
		}
	})
}

// 添加批注
const handleCommentsAdd = () => {
	const newComment = {
		序号: commentsList.value.length + 1,
		选中内容: '请选择',
		批注内容: '',
		操作: '',
	}
	commentsList.value.push(newComment)
}

// 删除批注
const handleCommentsDelete = (index: number) => {
	if (commentsList.value.length <= 1) {
		ElMessage.warning('至少需要保留一个批注记录')
		return
	}

	commentsList.value.splice(index, 1)
	// 重新排序序号
	commentsList.value.forEach((item, idx) => {
		item.序号 = idx + 1
	})

	// 清除对应的表单验证
	nextTick(() => {
		if (commentsFormRef.value) {
			commentsFormRef.value.clearValidate()
		}
	})
}

// 从本地存储加载批注数据
const loadCommentsFromStorage = (rowId: string | number) => {
	try {
		const storageKey = `${COMMENTS_STORAGE_KEY}_${rowId}`
		const savedComments = localStorage.getItem(storageKey)
		if (savedComments) {
			commentsList.value = JSON.parse(savedComments)
		} else {
			// 如果没有保存的数据，使用默认数据
			commentsList.value = [
				{
					序号: 1,
					选中内容: '字段描述',
					批注内容: '字段描述内容建议更改为xxx',
					操作: '',
				},
				{
					序号: 2,
					选中内容: '字段描述',
					批注内容: '字段描述内容建议更改为xxx',
					操作: '',
				},
			]
		}
	} catch (error) {
		console.error('加载批注数据失败:', error)
		ElMessage.error('加载批注数据失败')
	}
}

// 保存批注数据到本地存储
const saveCommentsToStorage = () => {
	try {
		if (currentCommentsRow.value) {
			const storageKey = `${COMMENTS_STORAGE_KEY}_${currentCommentsRow.value.id}`
			localStorage.setItem(storageKey, JSON.stringify(commentsList.value))
		}
	} catch (error) {
		console.error('保存批注数据失败:', error)
		ElMessage.error('保存批注数据失败')
	}
}

// 打开突出显示设置对话框
const openHighlightSettingsDialog = (row: BusinessTableDataRecord) => {
	currentHighlightRow.value = row

	// 加载用户保存的设置，以用户当前保留的标签数量为准
	const savedSettings = getHighlightSettings()
	highlightSettingsForm.value = {...savedSettings}

	highlightSettingsDialogVisible.value = true
}

// 处理突出显示设置确认
const handleHighlightSettingsConfirm = () => {
	// 验证不能有重复的列选择
	const fieldNames = highlightSettingsForm.value.highlightRules.map((rule) => rule.fieldName)
	const duplicates = fieldNames.filter((name, index) => fieldNames.indexOf(name) !== index)
	if (duplicates.length > 0) {
		ElMessage.error('不能为同一列设置多个颜色')
		return
	}

	// 保存设置到本地存储
	saveHighlightSettings(highlightSettingsForm.value)

	// 应用颜色到表格
	applyHighlightColors()

	ElMessage.success('列颜色设置保存成功')
	highlightSettingsDialogVisible.value = false
	currentHighlightRow.value = null
}

// 应用突出显示颜色到表格
const applyHighlightColors = () => {
	console.log('开始应用突出显示颜色', highlightSettingsForm.value.highlightRules)

	// 清除之前的样式
	clearHighlightStyles()

	// 应用新的颜色设置
	highlightSettingsForm.value.highlightRules.forEach((rule, index) => {
		console.log(`处理规则 ${index}:`, rule)
		if (rule.fieldName && rule.fontColor && rule.isActive) {
			console.log(`应用颜色: 字段=${rule.fieldName}, 颜色=${rule.fontColor}`)
			addColumnHighlightStyle(rule.fieldName, rule.fontColor, index)
		} else {
			console.log(
				`跳过规则 ${index}: 字段名=${rule.fieldName}, 颜色=${rule.fontColor}, 激活=${rule.isActive}`
			)
		}
	})
}

// 清除突出显示样式
const clearHighlightStyles = () => {
	// 移除之前添加的样式
	const existingStyles = document.querySelectorAll('style[data-highlight-style]')
	existingStyles.forEach((style) => style.remove())
}

// 添加列突出显示样式
const addColumnHighlightStyle = (fieldName: string, fontColor: string, index: number) => {
	const style = document.createElement('style')
	style.setAttribute('data-highlight-style', `highlight-${index}`)

	const columnIndex = getColumnIndex(fieldName)
	console.log(`字段 ${fieldName} 对应的列索引: ${columnIndex}`)

	// 创建CSS规则，为指定列添加字体颜色，兼容TableV2组件
	const cssRule = `
    /* TableV2组件突出显示 - 字体颜色 */
    .highlight-enabled-table .el-table .el-table__body-wrapper .el-table__body tbody tr td:nth-child(${columnIndex}) {
      color: ${fontColor} !important;
      font-weight: 500 !important;
    }
    .highlight-enabled-table .el-table .el-table__header-wrapper .el-table__header thead tr th:nth-child(${columnIndex}) {
      color: ${fontColor} !important;
      font-weight: 600 !important;
    }

    /* 更通用的TableV2选择器 - 字体颜色 */
    .highlight-enabled-table table tbody tr td:nth-child(${columnIndex}) {
      color: ${fontColor} !important;
      font-weight: 500 !important;
    }
    .highlight-enabled-table table thead tr th:nth-child(${columnIndex}) {
      color: ${fontColor} !important;
      font-weight: 600 !important;
    }

    /* 兼容其他表格结构 - 字体颜色 */
    .highlight-enabled-table tr td:nth-child(${columnIndex}) {
      color: ${fontColor} !important;
      font-weight: 500 !important;
    }
    .highlight-enabled-table tr th:nth-child(${columnIndex}) {
      color: ${fontColor} !important;
      font-weight: 600 !important;
    }

    /* 针对表格内的链接和按钮 */
    .highlight-enabled-table tr td:nth-child(${columnIndex}) a,
    .highlight-enabled-table tr td:nth-child(${columnIndex}) .el-button--link {
      color: ${fontColor} !important;
    }

    /* 针对标签组件 */
    .highlight-enabled-table tr td:nth-child(${columnIndex}) .el-tag {
      color: ${fontColor} !important;
      border-color: ${fontColor} !important;
    }

    /* 针对所属分类列的自定义模板 */
    .highlight-enabled-table tr td:nth-child(${columnIndex}) .category-cell {
      color: ${fontColor} !important;
    }
  `

	console.log(`生成的CSS规则:`, cssRule)
	style.textContent = cssRule
	document.head.appendChild(style)
	console.log(`样式已添加到DOM，样式元素:`, style)
}

// 获取列索引
const getColumnIndex = (fieldName: string): number => {
	let index = 1 // 从1开始，因为CSS nth-child是1-based
	console.log(`计算列索引 - 字段名: ${fieldName}`)

	// TableV2组件的列顺序：选择列 + 序号列（如果启用） + 数据列

	// 选择列（enable-selection="true"）
	index++
	console.log(`添加选择列，当前索引: ${index}`)

	// 序号列（如果启用）
	if (pageStyles.value.showIndex) {
		index++
		console.log(`添加序号列，当前索引: ${index}`)
	}

	// 查找字段在可用列中的位置
	const columnIndex = availableColumns.value.findIndex((col) => col.prop === fieldName)
	console.log(`在可用列中查找 ${fieldName}，找到索引: ${columnIndex}`)
	console.log(
		`可用列列表:`,
		availableColumns.value.map((col) => col.prop)
	)

	if (columnIndex !== -1) {
		index += columnIndex
		console.log(`找到字段，最终索引: ${index}`)
	} else {
		// 如果没找到，尝试直接匹配字段名
		const directIndex = [
			'所属分类',
			'所属标签',
			'字段信息描述',
			'表描述',
			'描述信息备份',
			'移动端展示',
			'审核状态',
		].indexOf(fieldName)
		console.log(`直接匹配字段名，找到索引: ${directIndex}`)
		if (directIndex !== -1) {
			index += directIndex
			console.log(`直接匹配成功，最终索引: ${index}`)
		}
	}

	console.log(`字段 ${fieldName} 最终计算的列索引: ${index}`)
	return index
}

// 处理突出显示设置取消
const handleHighlightSettingsCancel = () => {
	// 重新加载设置，放弃更改
	highlightSettingsForm.value = getHighlightSettings()
	highlightSettingsDialogVisible.value = false
	currentHighlightRow.value = null
}

// 颜色变化事件
const onColorChange = (rule: any) => {
	console.log('颜色已更改:', rule.fieldName, rule.fontColor)
	// 立即应用颜色变化
	applyHighlightColors()
}

// 添加突出显示规则
const handleHighlightRuleAdd = () => {
	// 找到未被使用的列
	const usedFields = highlightSettingsForm.value.highlightRules.map((rule) => rule.fieldName)
	const availableField = availableColumns.value.find((col) => !usedFields.includes(col.prop))

	const newRule = {
		id: Date.now(),
		fieldName: availableField
			? availableField.prop
			: availableColumns.value[0]?.prop || '所属分类',
		fontColor: '#1890ff',
		isActive: true,
	}
	highlightSettingsForm.value.highlightRules.push(newRule)

	// 立即应用新规则
	applyHighlightColors()
}

// 删除突出显示规则
const handleHighlightRuleDelete = (index: number) => {
	if (highlightSettingsForm.value.highlightRules.length <= 1) {
		ElMessage.warning('至少需要保留一个列颜色设置')
		return
	}
	highlightSettingsForm.value.highlightRules.splice(index, 1)

	// 立即应用颜色变化
	applyHighlightColors()
	ElMessage.success('列颜色设置已删除')
}

// 保存多用户协助设置
const handleCollaborationSave = (data: any) => {
	const row = currentCollaborationRow.value
	if (row) {
		console.log('多用户协作设置:', {
			rowData: row,
			collaborationData: data,
		})
	}
}

// 对话框确认
const onDialogConfirm = () => {
	// 检查表单引用是否存在
	if (!dialogFormRef.value) {
		ElMessage.error('表单引用不存在')
		return
	}

	// 尝试获取表单实例 - 兼容不同的Form组件
	let formInstance = null

	// 如果是自定义FormComp组件，使用getElFormRef方法
	if (typeof dialogFormRef.value.getElFormRef === 'function') {
		formInstance = dialogFormRef.value.getElFormRef()
	}
	// 如果是akvts Form组件，直接使用validate方法
	else if (typeof dialogFormRef.value.validate === 'function') {
		formInstance = dialogFormRef.value
	}

	if (!formInstance) {
		ElMessage.error('表单实例获取失败')
		return
	}

	formInstance.validate(async (valid: boolean) => {
		if (valid) {
			try {
				dialogLoading.value = true

				// 模拟保存延迟
				await new Promise((resolve) => setTimeout(resolve, 1000))

				// 将新表单数据映射回原来的数据结构
				const mappedData = {
					所属分类: dialogForm.value.所属分类,
					所属标签: dialogForm.value.所属标签,
					字段信息描述: dialogForm.value.字段名称, // 字段名称映射到字段信息描述
					表描述: dialogForm.value.表描述,
					描述信息备份: dialogForm.value.字段描述, // 字段描述映射到描述信息备份
					移动端展示: dialogForm.value.自动更新, // 自动更新映射到移动端展示
				}

				// 当前操作者和时间
				const currentOperator = '张安'
				const currentTime =
					new Date().toLocaleDateString().replace(/\//g, '.') +
					' ' +
					new Date().toLocaleTimeString('en-GB').replace(/:/g, ':')

				if (currentRow.value) {
					// 编辑
					const result = businessTableDataStore.updateRecord(
						currentRow.value.id,
						mappedData
					)
					if (result) {
						ElMessage.success('编辑成功')
						showDialog.value = false

						// 添加编辑操作的历史记录
						const historyRecord = {
							id: `op_${Date.now()}`,
							序号: operationHistoryList.value.length + 1,
							操作内容: `修改了"${mappedData.字段信息描述}"的信息`,
							操作人: currentOperator,
							操作时间: currentTime,
						}
						operationHistoryList.value.unshift(historyRecord)
						operationHistoryPagination.value.total = operationHistoryList.value.length
					} else {
						ElMessage.error('编辑失败')
					}
				} else {
					// 新增
					const result = businessTableDataStore.addRecord(mappedData)
					if (result) {
						ElMessage.success('新增成功')
						showDialog.value = false
						// 跳转到最后一页
						const totalPages = Math.ceil(pagination.total / pagination.size)
						pagination.page = totalPages

						// 添加新增操作的历史记录
						const historyRecord = {
							id: `op_${Date.now()}`,
							序号: operationHistoryList.value.length + 1,
							操作内容: `新增了"${mappedData.字段信息描述}"`,
							操作人: currentOperator,
							操作时间: currentTime,
						}
						operationHistoryList.value.unshift(historyRecord)
						operationHistoryPagination.value.total = operationHistoryList.value.length
					} else {
						ElMessage.error('新增失败')
					}
				}
			} catch (error) {
				ElMessage.error('操作失败')
				console.error(error)
			} finally {
				dialogLoading.value = false
			}
		} else {
			// 表单验证失败
			ElMessage.warning('请检查表单填写是否正确')
		}
	})
}

// 下载导入模板
const downloadTemplate = async () => {
	try {
		const workbook = new ExcelJS.Workbook()
		const worksheet = workbook.addWorksheet('业务表数据模板')

		// 设置表头
		const headers = [
			{header: '所属分类', key: '所属分类', width: 20},
			{header: '所属标签', key: '所属标签', width: 20},
			{header: '字段信息描述', key: '字段信息描述', width: 30},
			{header: '表描述', key: '表描述', width: 30},
			{header: '描述信息备份', key: '描述信息备份', width: 30},
			{header: '移动端展示', key: '移动端展示', width: 15},
		]

		worksheet.columns = headers

		// 设置表头样式
		const headerRow = worksheet.getRow(1)
		headerRow.font = {bold: true}
		headerRow.fill = {
			type: 'pattern',
			pattern: 'solid',
			fgColor: {argb: 'FFE6F3FF'},
		}

		// 设置表头边框
		headerRow.eachCell((cell) => {
			cell.border = {
				top: {style: 'thin'},
				left: {style: 'thin'},
				bottom: {style: 'thin'},
				right: {style: 'thin'},
			}
		})

		// 添加示例数据
		const exampleRow = worksheet.addRow({
			所属分类: '基础数据管理',
			所属标签: '用户信息',
			字段信息描述: 'user_id, user_name, real_name, phone, email, department_id',
			表描述: 'sys_user - 系统用户基础信息表',
			描述信息备份: '存储政务系统用户基本信息，包含登录账号、真实姓名、联系方式等',
			移动端展示: '是',
		})

		// 设置示例数据边框
		exampleRow.eachCell((cell) => {
			cell.border = {
				top: {style: 'thin'},
				left: {style: 'thin'},
				bottom: {style: 'thin'},
				right: {style: 'thin'},
			}
		})

		// 生成文件
		const buffer = await workbook.xlsx.writeBuffer()
		const blob = new Blob([buffer], {
			type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
		})

		// 创建下载链接
		const url = window.URL.createObjectURL(blob)
		const link = document.createElement('a')
		link.href = url

		// 设置文件名
		const fileName = `业务表数据导入模板_${new Date().toISOString().slice(0, 10)}.xlsx`
		link.download = fileName

		// 触发下载
		document.body.appendChild(link)
		link.click()

		// 清理
		document.body.removeChild(link)
		window.URL.revokeObjectURL(url)

		ElMessage.success(`模板下载成功：${fileName}`)
	} catch (error) {
		console.error('模板下载失败:', error)
		ElMessage.error('模板下载失败，请重试')
	}
}

// 验证Excel文件
const validateExcelFile = (file: any): boolean => {
	// 检查文件类型
	const validExtensions = ['.xlsx', '.xls']
	const fileName = file.name.toLowerCase()
	const isValidExtension = validExtensions.some((ext) => fileName.endsWith(ext))

	if (!isValidExtension) {
		ElMessage.error('请上传Excel文件(.xlsx或.xls格式)')
		return false
	}

	// 检查MIME类型
	const validMimeTypes = [
		'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
		'application/vnd.ms-excel', // .xls
	]

	if (file.type && !validMimeTypes.includes(file.type)) {
		console.warn('MIME类型不匹配，但文件扩展名正确，继续处理')
	}

	// 检查文件大小（限制为50MB，与导入须知一致）
	const maxSize = 50 * 1024 * 1024 // 50MB
	if (file.size > maxSize) {
		ElMessage.error('上传文件大小不能超过 50MB!')
		return false
	}

	// 检查文件是否为空
	if (file.size === 0) {
		ElMessage.error('文件为空，请选择有效的Excel文件')
		return false
	}

	return true
}

// 处理文件选择（on-change事件）
const handleFileSelect = (file: any, fileList: any[]) => {
	console.log('文件选择事件:', file, fileList)

	if (!validateExcelFile(file)) {
		return
	}

	// 保存文件信息
	importFileList.value = [file]
	selectedFileName.value = file.name
	ElMessage.success('文件选择成功，点击上传按钮开始导入')
}

// 处理文件上传前验证（before-upload事件）
const handleFileChange = (file: any) => {
	console.log('文件上传前验证:', file)
	return false // 阻止自动上传
}

// 处理文件移除
const handleFileRemove = () => {
	importFileList.value = []
	selectedFileName.value = ''
}

// 清除选中的文件
const clearSelectedFile = () => {
	importFileList.value = []
	selectedFileName.value = ''
	ElMessage.info('已清除选中的文件')
}

// 上传并解析Excel文件
const uploadFile = async () => {
	console.log('开始上传文件:', importFileList.value, selectedFileName.value)

	if (importFileList.value.length === 0 || !selectedFileName.value) {
		ElMessage.warning('请选择要上传的文件')
		return
	}

	// 获取文件对象，优先使用raw属性
	const file = importFileList.value[0].raw || importFileList.value[0]
	console.log('获取到的文件对象:', file)

	if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
		ElMessage.error('请上传Excel文件(.xlsx或.xls格式)')
		return
	}

	importLoading.value = true

	try {
		const buffer = await file.arrayBuffer()
		const workbook = new ExcelJS.Workbook()
		await workbook.xlsx.load(buffer)

		// 尝试多种方式获取工作表
		let worksheet = workbook.getWorksheet(1)
		if (!worksheet && workbook.worksheets.length > 0) {
			worksheet = workbook.worksheets[0]
		}
		if (!worksheet) {
			// 尝试通过名称获取
			worksheet = workbook.getWorksheet('业务表数据模板') || workbook.getWorksheet('Sheet1')
		}

		if (!worksheet) {
			throw new Error('Excel文件中没有找到有效的工作表，请检查文件格式')
		}

		console.log('找到工作表:', worksheet.name, '行数:', worksheet.rowCount)

		const importData: BusinessTableDataRecord[] = []
		const errors: string[] = []

		// 从第2行开始读取数据（第1行是表头）
		const totalRows = worksheet.rowCount
		console.log('工作表总行数:', totalRows)

		if (totalRows <= 1) {
			ElMessage.warning('Excel文件中没有数据行，请检查文件内容')
			return
		}

		worksheet.eachRow((row, rowNumber) => {
			if (rowNumber === 1) return // 跳过表头

			const values = row.values as any[]
			console.log(`第${rowNumber}行数据:`, values)

			// 检查是否为空行
			if (!values || values.length <= 1 || values.every((val) => !val || val === '')) {
				console.log(`第${rowNumber}行为空行，跳过`)
				return
			}

			// Excel中第一列是索引，实际数据从第二列开始
			const 所属分类 = values[1]?.toString().trim() || ''
			const 所属标签 = values[2]?.toString().trim() || ''
			const 字段信息描述 = values[3]?.toString().trim() || ''
			const 表描述 = values[4]?.toString().trim() || ''
			const 描述信息备份 = values[5]?.toString().trim() || ''
			const 移动端展示值 = values[6]?.toString().trim() || ''

			console.log(`第${rowNumber}行解析结果:`, {
				所属分类,
				所属标签,
				字段信息描述,
				表描述,
				描述信息备份,
				移动端展示值,
			})

			// 数据验证
			if (!所属分类) {
				errors.push(`第${rowNumber}行：所属分类不能为空`)
				return
			}

			if (!字段信息描述) {
				errors.push(`第${rowNumber}行：字段信息描述不能为空`)
				return
			}

			// 转换移动端展示值
			let 移动端展示 = false
			if (移动端展示值 === '是' || 移动端展示值 === 'true' || 移动端展示值 === '1') {
				移动端展示 = true
			}

			const newItem: BusinessTableDataRecord = {
				id: `import_${Date.now()}_${rowNumber}`,
				序号: businessTableDataStore.records.length + importData.length + 1,
				所属分类,
				所属标签,
				字段信息描述,
				表描述,
				描述信息备份,
				移动端展示,
				创建时间: new Date().toISOString().split('T')[0],
				创建人: '系统导入',
			}

			importData.push(newItem)
		})

		if (errors.length > 0) {
			// 限制错误信息显示数量，避免过长
			const displayErrors = errors.slice(0, 10)
			const errorMessage =
				displayErrors.join('\n') +
				(errors.length > 10 ? `\n...还有${errors.length - 10}个错误` : '')
			ElMessage.error(`导入失败，发现以下错误：\n${errorMessage}`)
			return
		}

		if (importData.length === 0) {
			ElMessage.warning('没有找到有效的数据，请检查Excel文件内容')
			return
		}

		// 添加到数据存储
		importData.forEach((item) => {
			businessTableDataStore.addRecord(item)
		})

		ElMessage.success(`成功导入 ${importData.length} 条数据`)

		// 添加批量导入操作的历史记录
		const currentOperator = '张安'
		const currentTime =
			new Date().toLocaleDateString().replace(/\//g, '.') +
			' ' +
			new Date().toLocaleTimeString('en-GB').replace(/:/g, ':')
		const historyRecord = {
			id: `op_${Date.now()}`,
			序号: operationHistoryList.value.length + 1,
			操作内容: `批量导入了 ${importData.length} 条数据`,
			操作人: currentOperator,
			操作时间: currentTime,
		}
		operationHistoryList.value.unshift(historyRecord)
		operationHistoryPagination.value.total = operationHistoryList.value.length

		batchImportDialogVisible.value = false
		importFileList.value = []
		selectedFileName.value = ''
	} catch (error) {
		console.error('导入失败:', error)
		console.error('错误详情:', error.stack)

		let errorMessage = '文件解析失败，请检查文件格式'
		if (error.message.includes('工作表')) {
			errorMessage = 'Excel文件格式错误，请确保文件包含有效的工作表'
		} else if (error.message.includes('Corrupt')) {
			errorMessage = 'Excel文件已损坏，请重新生成文件'
		} else if (error.message.includes('format')) {
			errorMessage = '不支持的文件格式，请使用.xlsx或.xls格式'
		}

		ElMessage.error(errorMessage)
	} finally {
		importLoading.value = false
	}
}

// 批量修改确认
const onBatchEditConfirm = async () => {
	// 检查是否选择了要修改的字段
	const selectedFields = Object.keys(batchEditFields.value).filter(
		(key) => batchEditFields.value[key as keyof typeof batchEditFields.value]
	)

	if (selectedFields.length === 0) {
		ElMessage.warning('请至少选择一个要修改的字段')
		return
	}

	try {
		await ElMessageBox.confirm(
			`确认要修改选中的 ${selectedRows.value.length} 条数据的 ${selectedFields.length} 个字段吗？`,
			'批量修改确认',
			{
				confirmButtonText: '确定修改',
				cancelButtonText: '取消',
				type: 'warning',
			}
		)

		batchEditLoading.value = true

		// 构建要更新的数据
		const updateData: Partial<BusinessTableDataRecord> = {}
		selectedFields.forEach((field) => {
			updateData[field as keyof BusinessTableDataRecord] =
				batchEditForm.value[field as keyof typeof batchEditForm.value]
		})

		// 批量更新选中的记录
		let successCount = 0
		selectedRows.value.forEach((row) => {
			const result = businessTableDataStore.updateRecord(row.id, updateData)
			if (result) {
				successCount++
			}
		})

		if (successCount === selectedRows.value.length) {
			ElMessage.success(`成功修改 ${successCount} 条数据`)

			// 添加批量编辑操作的历史记录
			const currentOperator = '张安'
			const currentTime =
				new Date().toLocaleDateString().replace(/\//g, '.') +
				' ' +
				new Date().toLocaleTimeString('en-GB').replace(/:/g, ':')
			const historyRecord = {
				id: `op_${Date.now()}`,
				序号: operationHistoryList.value.length + 1,
				操作内容: `批量修改了 ${successCount} 条数据的 ${selectedFields.length} 个字段`,
				操作人: currentOperator,
				操作时间: currentTime,
			}
			operationHistoryList.value.unshift(historyRecord)
			operationHistoryPagination.value.total = operationHistoryList.value.length

			batchEditDialogVisible.value = false
			selectedRows.value = []
		} else {
			ElMessage.warning(
				`修改完成，成功 ${successCount} 条，失败 ${
					selectedRows.value.length - successCount
				} 条`
			)
		}
	} catch {
		// 用户取消修改
	} finally {
		batchEditLoading.value = false
	}
}

// 多维度分类相关方法
const onMultiDimensionAdd = () => {
	currentMultiDimensionRow.value = null
	multiDimensionForm.value = {
		维度分类名称: '',
		备注: '',
	}
	multiDimensionAddDialogVisible.value = true

	// 确保表单验证状态被清除
	nextTick(() => {
		if (multiDimensionFormRef.value && multiDimensionFormRef.value.getElFormRef) {
			try {
				multiDimensionFormRef.value.getElFormRef().clearValidate()
			} catch (error) {
				console.log('清除表单验证状态失败:', error)
			}
		}
	})
}

const onMultiDimensionEdit = (row) => {
	currentMultiDimensionRow.value = row
	multiDimensionForm.value = {
		维度分类名称: row.维度分类名称,
		备注: row.维度说明,
	}
	multiDimensionAddDialogVisible.value = true

	// 确保表单验证状态被清除
	nextTick(() => {
		if (multiDimensionFormRef.value && multiDimensionFormRef.value.getElFormRef) {
			try {
				multiDimensionFormRef.value.getElFormRef().clearValidate()
			} catch (error) {
				console.log('清除表单验证状态失败:', error)
			}
		}
	})
}

// 多维度分类弹窗关闭处理
const onMultiDimensionDialogClosed = () => {
	currentMultiDimensionRow.value = null
	multiDimensionForm.value = {维度分类名称: '', 备注: ''}

	// 确保表单验证状态被清除
	nextTick(() => {
		if (multiDimensionFormRef.value && multiDimensionFormRef.value.getElFormRef) {
			try {
				multiDimensionFormRef.value.getElFormRef().clearValidate()
			} catch (error) {
				console.log('清除表单验证状态失败:', error)
			}
		}
	})
}

const onMultiDimensionDelete = async (row: any) => {
	try {
		await ElMessageBox.confirm(`确定要删除"${row.维度分类名称}"吗？`, '删除确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})

		// 显示删除Loading状态
		multiDimensionLoading.value = true

		// 模拟删除延迟
		await new Promise((resolve) => setTimeout(resolve, 300))

		const index = multiDimensionAllList.value.findIndex((item) => item.id === row.id)
		if (index !== -1) {
			multiDimensionAllList.value.splice(index, 1)
			// 重新排序
			multiDimensionAllList.value.forEach((item, idx) => {
				item.序号 = idx + 1
			})

			// 更新分页总数
			multiDimensionPagination.value.total = multiDimensionAllList.value.length

			// 如果当前页没有数据了，回到上一页
			const totalPages = Math.ceil(
				multiDimensionPagination.value.total / multiDimensionPagination.value.size
			)
			if (multiDimensionPagination.value.page > totalPages && totalPages > 0) {
				multiDimensionPagination.value.page = totalPages
			}

			ElMessage.success('删除成功')
		} else {
			ElMessage.error('删除失败，未找到该项')
		}
	} catch {
		// 用户取消删除
	} finally {
		multiDimensionLoading.value = false
	}
}

const onMultiDimensionConfirm = async () => {
	try {
		// 简单的数据验证
		if (
			!multiDimensionForm.value.维度分类名称 ||
			multiDimensionForm.value.维度分类名称.trim() === ''
		) {
			ElMessage.warning('请输入维度分类名称')
			return
		}

		if (
			multiDimensionForm.value.维度分类名称.trim().length < 2 ||
			multiDimensionForm.value.维度分类名称.trim().length > 50
		) {
			ElMessage.warning('维度分类名称长度应在2-50个字符之间')
			return
		}

		// 开始保存
		multiDimensionLoading.value = true

		// 模拟保存延迟
		await new Promise((resolve) => setTimeout(resolve, 800))

		if (currentMultiDimensionRow.value) {
			// 编辑
			const index = multiDimensionAllList.value.findIndex(
				(item) => item.id === currentMultiDimensionRow.value!.id
			)
			if (index !== -1) {
				multiDimensionAllList.value[index].维度分类名称 =
					multiDimensionForm.value.维度分类名称
				multiDimensionAllList.value[index].维度说明 =
					multiDimensionForm.value.备注 || '暂无说明'
				ElMessage.success('编辑成功')
			} else {
				ElMessage.error('编辑失败，未找到对应记录')
				return
			}
		} else {
			// 新增
			const newItem = {
				id: Date.now().toString(),
				序号: multiDimensionAllList.value.length + 1,
				维度分类名称: multiDimensionForm.value.维度分类名称,
				维度说明: multiDimensionForm.value.备注 || '暂无说明',
			}
			multiDimensionAllList.value.push(newItem)

			// 更新分页总数
			multiDimensionPagination.value.total = multiDimensionAllList.value.length

			// 如果新增后当前页没有数据，跳转到最后一页
			const totalPages = Math.ceil(
				multiDimensionPagination.value.total / multiDimensionPagination.value.size
			)
			if (multiDimensionPagination.value.page < totalPages) {
				multiDimensionPagination.value.page = totalPages
			}

			ElMessage.success('新增成功')
		}

		// 关闭弹窗
		multiDimensionAddDialogVisible.value = false
	} catch (error) {
		console.error('保存多维度分类失败:', error)
		ElMessage.error('保存失败，请重试')
	} finally {
		multiDimensionLoading.value = false
	}
}

// 业务数据规则确认
const onBusinessRulesConfirm = () => {
	try {
		// 这里可以添加保存业务规则的逻辑
		console.log('业务数据规则设置:', businessRulesForm.value)

		ElMessage.success('业务数据规则设置已保存')
		businessRulesDialogVisible.value = false
	} catch (error) {
		console.error('保存业务规则失败:', error)
		ElMessage.error('保存失败，请重试')
	}
}

// 备份与恢复确认
const onBackupRestoreConfirm = async () => {
	try {
		backupRestoreLoading.value = true

		// 验证必填字段
		if (!backupRestoreForm.value.数据备份开始时间) {
			ElMessage.warning('请选择数据备份开始时间')
			return
		}
		if (!backupRestoreForm.value.全量备份频率) {
			ElMessage.warning('请选择全量备份频率')
			return
		}
		if (!backupRestoreForm.value.增量备份频率) {
			ElMessage.warning('请选择增量备份频率')
			return
		}
		if (!backupRestoreForm.value.备份数据清理策略) {
			ElMessage.warning('请选择备份数据清理策略')
			return
		}

		// 模拟保存延迟
		await new Promise((resolve) => setTimeout(resolve, 1500))

		console.log('备份与恢复设置:', backupRestoreForm.value)

		ElMessage.success('数据备份与恢复设置已保存')
		backupRestoreDialogVisible.value = false
	} catch (error) {
		console.error('保存备份与恢复设置失败:', error)
		ElMessage.error('保存失败，请重试')
	} finally {
		backupRestoreLoading.value = false
	}
}

// 历史操作记录相关方法
const loadHistoryRecords = async () => {
	try {
		historyRecordLoading.value = true

		// 模拟加载延迟
		await new Promise((resolve) => setTimeout(resolve, 800))

		// 模拟操作历史记录数据
		const operationRecords = [
			{
				id: 'op_1',
				序号: 1,
				操作内容: '创建了主表业务数据定义',
				操作人: '张安',
				操作时间: '2025.7.2 10:30:31',
			},
			{
				id: 'op_2',
				序号: 2,
				操作内容: '原主表业务数据定义修改为业务表数据定义',
				操作人: '李华',
				操作时间: '2025.6.2 10:30:31',
			},
			{
				id: 'op_3',
				序号: 3,
				操作内容: '新增字段"移动端展示"',
				操作人: '王明',
				操作时间: '2025.5.15 14:22:45',
			},
			{
				id: 'op_4',
				序号: 4,
				操作内容: '修改字段"表描述"的最大长度为500',
				操作人: '陈静',
				操作时间: '2025.5.10 09:15:27',
			},
			{
				id: 'op_5',
				序号: 5,
				操作内容: '删除字段"临时标记"',
				操作人: '赵敏',
				操作时间: '2025.4.28 16:40:12',
			},
		]

		// 模拟授权历史记录数据
		const authRecords = [
			{id: 'auth_1', 序号: 1, 授权条件: '所属标签', 授权时间: '2025.7.9 10：30'},
			{id: 'auth_2', 序号: 2, 授权条件: '所属标签，所属分类', 授权时间: '2025.7.9 10：30'},
			{id: 'auth_3', 序号: 3, 授权条件: '所属标签，所属分类', 授权时间: '2025.7.9 10：30'},
		]

		operationHistoryList.value = operationRecords
		authorizationHistoryList.value = authRecords

		operationHistoryPagination.value.total = operationRecords.length
		authorizationHistoryPagination.value.total = authRecords.length
	} catch (error) {
		console.error('加载历史操作记录失败:', error)
		ElMessage.error('加载历史操作记录失败，请重试')
	} finally {
		historyRecordLoading.value = false
	}
}

// 权限管理相关方法
const loadPermissionManageData = async () => {
	try {
		permissionManageLoading.value = true

		// 模拟加载延迟
		await new Promise((resolve) => setTimeout(resolve, 500))

		// 初始化选中的人员ID
		selectedPersonnelIds.value = allPersonnelList.value
			.filter((person) => person.selected)
			.map((person) => person.id)

		// 加载人员列表
		await loadPersonnelList()
	} catch (error) {
		console.error('加载权限管理数据失败:', error)
		ElMessage.error('加载权限管理数据失败，请重试')
	} finally {
		permissionManageLoading.value = false
	}
}

// 加载人员列表
const loadPersonnelList = async () => {
	try {
		personnelTableLoading.value = true

		// 模拟加载延迟
		await new Promise((resolve) => setTimeout(resolve, 300))

		// 过滤数据
		let filteredData = [...allPersonnelList.value]

		// 根据搜索关键词过滤
		if (personnelSearchKeyword.value.trim()) {
			const keyword = personnelSearchKeyword.value.trim().toLowerCase()
			filteredData = filteredData.filter(
				(person) =>
					person.name.toLowerCase().includes(keyword) ||
					person.department.toLowerCase().includes(keyword) ||
					person.position.toLowerCase().includes(keyword)
			)
		}

		// 分页处理
		const startIndex = (personnelPagination.value.page - 1) * personnelPagination.value.size
		const endIndex = startIndex + personnelPagination.value.size

		personnelList.value = filteredData.slice(startIndex, endIndex)
		personnelPagination.value.total = filteredData.length
	} catch (error) {
		console.error('加载人员列表失败:', error)
		ElMessage.error('加载人员列表失败，请重试')
	} finally {
		personnelTableLoading.value = false
	}
}

// 处理人员搜索
const handlePersonnelSearch = () => {
	personnelPagination.value.page = 1
	loadPersonnelList()
}

// 处理人员分页变化
const handlePersonnelPaginationChange = (val: number, type: 'page' | 'size') => {
	if (type === 'page') {
		personnelPagination.value.page = val
	} else {
		personnelPagination.value.size = val
		personnelPagination.value.page = 1
	}
	loadPersonnelList()
}

// 处理人员选择变化
const handlePersonnelSelectionChange = (selection: any[]) => {
	selectedPersonnelIds.value = selection.map((item) => item.id)
}

// 处理操作权限变化
const handleOperationPermissionChange = () => {
	const selectedOperations = permissionOperations.value
		.filter((op) => op.checked)
		.map((op) => op.label)
	console.log('选中的操作权限:', selectedOperations)
}

// 信息关联推荐相关方法
const loadInfoRecommendData = async () => {
	try {
		infoRecommendLoading.value = true

		// 模拟加载延迟
		await new Promise((resolve) => setTimeout(resolve, 500))

		// 数据已在初始化时设置
	} catch (error) {
		console.error('加载信息关联推荐数据失败:', error)
		ElMessage.error('加载信息关联推荐数据失败，请重试')
	} finally {
		infoRecommendLoading.value = false
	}
}

// 删除推荐项
const deleteRecommendItem = (item: any) => {
	ElMessageBox.confirm(`确定要删除推荐项"${item.推荐名称}"吗？`, '删除确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			const index = recommendList.value.findIndex((rec) => rec.id === item.id)
			if (index !== -1) {
				recommendList.value.splice(index, 1)
				// 重新排序序号
				recommendList.value.forEach((rec, idx) => {
					rec.序号 = idx + 1
				})
				ElMessage.success('删除成功')
			}
		})
		.catch(() => {
			ElMessage.info('已取消删除')
		})
}

// 打开推荐详情
const openRecommendDetail = (item: any, mode: string = 'add') => {
	if (item.id) {
		// 编辑或查看模式
		isEditMode.value = mode === 'edit'
		isViewMode.value = mode === 'view'
		currentRecommendItem.value = item
		recommendDetailForm.value = {
			推荐名称: item.推荐名称 || '',
			推荐内容: item.推荐内容 || '',
			附件: item.附件数据,
			附件名称: item.附件名称 || '',
			附件大小: item.附件大小 || 0,
		}
		uploadFileList.value = item.附件名称 ? [{name: item.附件名称, url: '#'}] : []
	} else {
		// 新增模式
		isEditMode.value = false
		isViewMode.value = false
		currentRecommendItem.value = null
		recommendDetailForm.value = {
			推荐名称: '',
			推荐内容: '',
			附件: null,
			附件名称: '',
			附件大小: 0,
		}
		uploadFileList.value = []
	}
	infoRecommendDetailVisible.value = true
}

// 处理附件上传前的验证
const beforeUpload = (file: any) => {
	const isValidType =
		[
			'application/pdf',
			'application/msword',
			'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
			'text/plain',
		].includes(file.type) || file.type.startsWith('image/')
	const isLt10M = file.size / 1024 / 1024 < 10

	if (!isValidType) {
		ElMessage.error('只能上传 PDF、Word、文本或图片格式的文件!')
		return false
	}
	if (!isLt10M) {
		ElMessage.error('上传文件大小不能超过 10MB!')
		return false
	}
	return true
}

// 处理推荐文件选择变化
const handleRecommendFileChange = (file: any, fileList: any[]) => {
	console.log('文件选择变化:', file, fileList)

	// 确保文件对象存在
	const rawFile = file.raw || file
	if (!rawFile) {
		console.error('文件对象不存在')
		return
	}

	// 文件验证
	const isValidType =
		[
			'application/pdf',
			'application/msword',
			'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
			'text/plain',
		].includes(rawFile.type) || rawFile.type.startsWith('image/')
	const isLt10M = rawFile.size / 1024 / 1024 < 10

	if (!isValidType) {
		ElMessage.error('只能上传 PDF、Word、文本或图片格式的文件!')
		uploadFileList.value = []
		return
	}
	if (!isLt10M) {
		ElMessage.error('上传文件大小不能超过 10MB!')
		uploadFileList.value = []
		return
	}

	// 读取文件
	const reader = new FileReader()
	reader.onload = (e) => {
		recommendDetailForm.value.附件 = e.target?.result
		recommendDetailForm.value.附件名称 = rawFile.name
		recommendDetailForm.value.附件大小 = rawFile.size
		ElMessage.success('文件选择成功')
	}
	reader.onerror = () => {
		ElMessage.error('文件读取失败')
		uploadFileList.value = []
	}
	reader.readAsDataURL(rawFile)
}

// 处理文件选择
const handleRecommendFileSelect = (file: any, fileList: any[]) => {
	console.log('文件选择事件触发:', file, fileList)

	if (!file || !file.raw) {
		console.log('无效的文件对象')
		return
	}

	const rawFile = file.raw
	console.log('选择的文件:', rawFile.name, rawFile.size, rawFile.type)

	// 手动处理文件上传
	handleFileUpload(rawFile)
}

// 处理文件上传
const handleFileUpload = async (file: File) => {
	console.log('开始处理文件上传:', file.name)

	try {
		// 文件类型和大小验证
		const isValidType =
			[
				'application/pdf',
				'application/msword',
				'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
				'text/plain',
			].includes(file.type) || file.type.startsWith('image/')
		const isLt10M = file.size / 1024 / 1024 < 10

		if (!isValidType) {
			ElMessage.error('只能上传 PDF、Word、文本或图片格式的文件!')
			return
		}
		if (!isLt10M) {
			ElMessage.error('上传文件大小不能超过 10MB!')
			return
		}

		// 读取文件内容
		const reader = new FileReader()
		reader.onload = (e) => {
			recommendDetailForm.value.附件 = e.target?.result
			recommendDetailForm.value.附件名称 = file.name
			recommendDetailForm.value.附件大小 = file.size
			ElMessage.success('文件选择成功')
			console.log('文件读取完成:', file.name)
		}
		reader.onerror = () => {
			ElMessage.error('文件读取失败')
		}
		reader.readAsDataURL(file)
	} catch (error) {
		console.error('文件处理错误:', error)
		ElMessage.error('文件处理失败')
	}
}

// 上传前验证
const beforeRecommendUpload = (file: any) => {
	console.log('上传前验证:', file)

	// 文件类型验证
	const isValidType =
		[
			'application/pdf',
			'application/msword',
			'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
			'text/plain',
		].includes(file.type) || file.type.startsWith('image/')
	const isLt10M = file.size / 1024 / 1024 < 10

	if (!isValidType) {
		ElMessage.error('只能上传 PDF、Word、文本或图片格式的文件!')
		return false
	}
	if (!isLt10M) {
		ElMessage.error('上传文件大小不能超过 10MB!')
		return false
	}

	return true
}

// 自定义上传处理
const customRecommendUpload = async (options: any) => {
	console.log('开始上传文件:', options.file)

	try {
		// 使用通用上传函数
		const res = await uploadFile(options.file, 'recommend')

		if (res) {
			// 更新表单数据
			recommendDetailForm.value.附件名称 = options.file.name
			recommendDetailForm.value.附件大小 = options.file.size
			recommendDetailForm.value.附件 = `/api/files/public/p${res}`

			ElMessage.success('文件上传成功')
		} else {
			ElMessage.error('文件上传失败')
		}
	} catch (error) {
		console.error('文件上传错误:', error)
		ElMessage.error('文件上传失败，请重试')
	}
}

// 移除选中的文件
const removeSelectedFile = () => {
	recommendDetailForm.value.附件 = null
	recommendDetailForm.value.附件名称 = ''
	recommendDetailForm.value.附件大小 = 0
	if (uploadRef.value) {
		uploadRef.value.clearFiles()
	}
	ElMessage.success('文件已移除')
}

// 处理附件移除
const handleRecommendFileRemove = () => {
	recommendDetailForm.value.附件 = null
	recommendDetailForm.value.附件名称 = ''
	recommendDetailForm.value.附件大小 = 0
	uploadFileList.value = []
	ElMessage.success('附件已移除')
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
	if (bytes === 0) return '0 B'
	const k = 1024
	const sizes = ['B', 'KB', 'MB', 'GB']
	const i = Math.floor(Math.log(bytes) / Math.log(k))
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 下载附件
const downloadAttachment = (item: any) => {
	if (!item.附件数据 || !item.附件名称) {
		ElMessage.warning('该项目没有附件')
		return
	}

	try {
		// 创建下载链接
		const link = document.createElement('a')
		link.href = item.附件数据
		link.download = item.附件名称
		document.body.appendChild(link)
		link.click()
		document.body.removeChild(link)
		ElMessage.success('附件下载成功')
	} catch (error) {
		console.error('下载失败:', error)
		ElMessage.error('附件下载失败')
	}
}

// 保存推荐详情
const saveRecommendDetail = () => {
	if (!recommendDetailForm.value.推荐名称.trim()) {
		ElMessage.warning('请输入推荐名称')
		return
	}
	if (!recommendDetailForm.value.推荐内容.trim()) {
		ElMessage.warning('请输入推荐内容')
		return
	}

	const currentTime = new Date().toLocaleDateString().replace(/\//g, '.')
	const currentOperator = '张安'

	if (isEditMode.value && currentRecommendItem.value) {
		// 编辑模式
		currentRecommendItem.value.推荐名称 = recommendDetailForm.value.推荐名称
		currentRecommendItem.value.推荐内容 = recommendDetailForm.value.推荐内容
		currentRecommendItem.value.附件 = recommendDetailForm.value.附件
		currentRecommendItem.value.附件名称 = recommendDetailForm.value.附件名称
		currentRecommendItem.value.附件大小 = recommendDetailForm.value.附件大小
		ElMessage.success('修改成功')
	} else {
		// 新增模式
		const newItem = {
			id: `recommend_${Date.now()}`,
			序号: recommendList.value.length + 1,
			推荐名称: recommendDetailForm.value.推荐名称,
			推荐内容: recommendDetailForm.value.推荐内容,
			附件: recommendDetailForm.value.附件名称 ? '下载' : '无',
			附件名称: recommendDetailForm.value.附件名称,
			附件大小: recommendDetailForm.value.附件大小,
			附件数据: recommendDetailForm.value.附件,
			创建时间: currentTime,
			操作人: currentOperator,
		}
		recommendList.value.unshift(newItem)
		ElMessage.success('新增成功')
	}

	infoRecommendDetailVisible.value = false
}

// 权限日志记录相关方法
const loadPermissionRecords = async () => {
	try {
		permissionRecordLoading.value = true

		// 模拟加载延迟
		await new Promise((resolve) => setTimeout(resolve, 800))

		// 生成真实的权限日志记录数据，只保留原型中的字段
		const records = [
			{
				id: 'record_1',
				序号: 1,
				操作用户: '张安',
				操作类型: '新增角色',
				操作时间: '2025.7.9 14:32',
			},
			{
				id: 'record_2',
				序号: 2,
				操作用户: '张安',
				操作类型: '编辑角色',
				操作时间: '2025.7.9 11:28',
			},
			{
				id: 'record_3',
				序号: 3,
				操作用户: '张安',
				操作类型: '删除角色',
				操作时间: '2025.7.9 09:15',
			},
			{
				id: 'record_4',
				序号: 4,
				操作用户: '李华',
				操作类型: '分配权限',
				操作时间: '2025.7.8 16:45',
			},
			{
				id: 'record_5',
				序号: 5,
				操作用户: '王明',
				操作类型: '撤销权限',
				操作时间: '2025.7.8 13:22',
			},
			{
				id: 'record_6',
				序号: 6,
				操作用户: '刘强',
				操作类型: '查看权限',
				操作时间: '2025.7.7 10:18',
			},
			{
				id: 'record_7',
				序号: 7,
				操作用户: '陈静',
				操作类型: '修改权限',
				操作时间: '2025.7.7 15:36',
			},
			{
				id: 'record_8',
				序号: 8,
				操作用户: '杨帆',
				操作类型: '角色授权',
				操作时间: '2025.7.6 08:42',
			},
			{
				id: 'record_9',
				序号: 9,
				操作用户: '赵敏',
				操作类型: '权限审核',
				操作时间: '2025.7.6 17:29',
			},
			{
				id: 'record_10',
				序号: 10,
				操作用户: '孙涛',
				操作类型: '系统登录',
				操作时间: '2025.7.5 12:05',
			},
			{
				id: 'record_11',
				序号: 11,
				操作用户: '周丽',
				操作类型: '数据导出',
				操作时间: '2025.7.5 14:18',
			},
			{
				id: 'record_12',
				序号: 12,
				操作用户: '吴刚',
				操作类型: '数据导入',
				操作时间: '2025.7.4 09:33',
			},
			{
				id: 'record_13',
				序号: 13,
				操作用户: '马超',
				操作类型: '新增角色',
				操作时间: '2025.7.4 11:25',
			},
			{
				id: 'record_14',
				序号: 14,
				操作用户: '黄敏',
				操作类型: '编辑角色',
				操作时间: '2025.7.3 16:12',
			},
			{
				id: 'record_15',
				序号: 15,
				操作用户: '林峰',
				操作类型: '分配权限',
				操作时间: '2025.7.3 08:47',
			},
			{
				id: 'record_16',
				序号: 16,
				操作用户: '郑雅',
				操作类型: '撤销权限',
				操作时间: '2025.7.2 13:55',
			},
			{
				id: 'record_17',
				序号: 17,
				操作用户: '何军',
				操作类型: '查看权限',
				操作时间: '2025.7.2 10:30',
			},
			{
				id: 'record_18',
				序号: 18,
				操作用户: '张安',
				操作类型: '修改权限',
				操作时间: '2025.7.1 15:20',
			},
			{
				id: 'record_19',
				序号: 19,
				操作用户: '李华',
				操作类型: '角色授权',
				操作时间: '2025.7.1 09:08',
			},
			{
				id: 'record_20',
				序号: 20,
				操作用户: '王明',
				操作类型: '权限审核',
				操作时间: '2025.6.30 17:45',
			},
			{
				id: 'record_21',
				序号: 21,
				操作用户: '刘强',
				操作类型: '系统登录',
				操作时间: '2025.6.30 14:12',
			},
			{
				id: 'record_22',
				序号: 22,
				操作用户: '陈静',
				操作类型: '数据导出',
				操作时间: '2025.6.29 11:38',
			},
			{
				id: 'record_23',
				序号: 23,
				操作用户: '杨帆',
				操作类型: '数据导入',
				操作时间: '2025.6.29 16:22',
			},
			{
				id: 'record_24',
				序号: 24,
				操作用户: '赵敏',
				操作类型: '删除角色',
				操作时间: '2025.6.28 13:15',
			},
			{
				id: 'record_25',
				序号: 25,
				操作用户: '孙涛',
				操作类型: '新增角色',
				操作时间: '2025.6.28 10:42',
			},
		]

		permissionRecordList.value = records
		permissionRecordPagination.value.total = records.length
	} catch (error) {
		console.error('加载权限日志记录失败:', error)
		ElMessage.error('加载权限日志记录失败，请重试')
	} finally {
		permissionRecordLoading.value = false
	}
}

// 权限继承规则相关函数
const handlePermissionRulesConfirm = () => {
	try {
		console.log('权限继承规则设置:', permissionRulesForm.value)

		// 模拟保存延迟
		setTimeout(() => {
			ElMessage.success('权限继承规则设置成功')
			permissionRulesDialogVisible.value = false
		}, 500)
	} catch (error) {
		console.error('保存权限继承规则失败:', error)
		ElMessage.error('设置失败，请重试')
	}
}

const handlePermissionRulesCancel = () => {
	permissionRulesDialogVisible.value = false
}

// 角色自定义相关函数
const handleRoleCustomConfirm = () => {
	try {
		console.log('角色自定义设置:', roleList.value)

		// 模拟保存延迟
		setTimeout(() => {
			ElMessage.success('角色自定义设置成功')
			roleCustomDialogVisible.value = false
		}, 500)
	} catch (error) {
		console.error('保存角色自定义失败:', error)
		ElMessage.error('设置失败，请重试')
	}
}

const handleRoleCustomCancel = () => {
	roleCustomDialogVisible.value = false
}

const handleRoleAdd = () => {
	roleAddDialogVisible.value = true
}

const handleRoleAddConfirm = () => {
	try {
		if (!roleCustomForm.value.角色名称) {
			ElMessage.warning('请输入角色名称')
			return
		}

		if (!roleCustomForm.value.权限 || roleCustomForm.value.权限.length === 0) {
			ElMessage.warning('请选择权限')
			return
		}

		// 添加新角色到列表
		const newRole = {
			id: String(roleList.value.length + 1),
			序号: roleList.value.length + 1,
			角色名称: roleCustomForm.value.角色名称,
			权限: [...roleCustomForm.value.权限],
			权限显示: roleCustomForm.value.权限.join('、'),
			创建时间: new Date()
				.toLocaleString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit',
				})
				.replace(/\//g, '-'),
			创建人: '当前用户',
		}

		roleList.value.push(newRole)

		// 重置表单
		roleCustomForm.value = {
			角色名称: '',
			上级角色: '',
			权限: [],
		}

		ElMessage.success('角色添加成功')
		roleAddDialogVisible.value = false
	} catch (error) {
		console.error('添加角色失败:', error)
		ElMessage.error('添加失败，请重试')
	}
}

const handleRoleAddCancel = () => {
	roleAddDialogVisible.value = false
	// 重置表单
	roleCustomForm.value = {
		角色名称: '',
		上级角色: '',
		权限: [],
	}
}

const handleRoleEdit = (row: any) => {
	// 找到要编辑的角色在列表中的索引
	editingRoleIndex.value = roleList.value.findIndex((item) => item.id === row.id)

	// 预填充编辑表单
	roleEditForm.value = {
		id: row.id,
		角色名称: row.角色名称,
		上级角色: row.上级角色 || '',
		权限: Array.isArray(row.权限) ? [...row.权限] : [row.权限],
	}

	// 打开编辑弹窗
	roleEditDialogVisible.value = true
}

const handleRoleDelete = (row: any) => {
	ElMessageBox.confirm(`确定要删除角色"${row.角色名称}"吗？`, '删除确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			const index = roleList.value.findIndex((item) => item.id === row.id)
			if (index > -1) {
				roleList.value.splice(index, 1)
				// 重新排序序号
				roleList.value.forEach((item, idx) => {
					item.序号 = idx + 1
				})
				ElMessage.success('删除成功')
			}
		})
		.catch(() => {
			ElMessage.info('已取消删除')
		})
}

// 角色编辑相关函数
const handleRoleEditConfirm = () => {
	try {
		if (!roleEditForm.value.角色名称) {
			ElMessage.warning('请输入角色名称')
			return
		}

		if (!roleEditForm.value.权限 || roleEditForm.value.权限.length === 0) {
			ElMessage.warning('请选择权限')
			return
		}

		if (editingRoleIndex.value >= 0) {
			// 更新角色数据
			roleList.value[editingRoleIndex.value] = {
				...roleList.value[editingRoleIndex.value],
				角色名称: roleEditForm.value.角色名称,
				上级角色: roleEditForm.value.上级角色,
				权限: [...roleEditForm.value.权限],
				权限显示: roleEditForm.value.权限.join('、'),
			}

			ElMessage.success('角色修改成功')
			roleEditDialogVisible.value = false

			// 重置编辑表单
			roleEditForm.value = {
				id: '',
				角色名称: '',
				上级角色: '',
				权限: [],
			}
			editingRoleIndex.value = -1
		}
	} catch (error) {
		console.error('修改角色失败:', error)
		ElMessage.error('修改失败，请重试')
	}
}

const handleRoleEditCancel = () => {
	roleEditDialogVisible.value = false
	// 重置编辑表单
	roleEditForm.value = {
		id: '',
		角色名称: '',
		上级角色: '',
		权限: [],
	}
	editingRoleIndex.value = -1
}

// 信息定时任务相关方法
const handleInfoScheduleConfirm = async () => {
	try {
		// 验证表单
		if (!infoScheduleFormRef.value) return

		const valid = await infoScheduleFormRef.value.validate()
		if (!valid) return

		// 验证结束时间
		if (
			infoScheduleForm.value.结束时间类型 === '结束时间' &&
			!infoScheduleForm.value.结束时间
		) {
			ElMessage.warning('请选择结束时间')
			return
		}

		// 模拟保存
		await new Promise((resolve) => setTimeout(resolve, 500))

		ElMessage.success('信息定时任务创建成功')
		infoScheduleDialogVisible.value = false

		// 重置表单
		infoScheduleForm.value = {
			任务名称: '',
			任务描述内容: '',
			任务开始时间: '',
			执行规则: '每天',
			结束时间类型: '无期限',
			结束时间: '',
		}
	} catch (error) {
		console.error('创建信息定时任务失败:', error)
		ElMessage.error('创建失败，请重试')
	}
}

const handleInfoScheduleCancel = () => {
	infoScheduleDialogVisible.value = false
	// 重置表单
	infoScheduleForm.value = {
		任务名称: '',
		任务描述内容: '',
		任务开始时间: '',
		执行规则: '每天',
		结束时间类型: '无期限',
		结束时间: '',
	}
}

// 应用斑马纹效果
const applyStripeEffect = () => {
	const tableContainer = document.querySelector('.business-table-data')
	if (!tableContainer) return

	const rows = tableContainer.querySelectorAll('table tbody tr')
	console.log('找到表格行数:', rows.length)

	rows.forEach((row, index) => {
		if (pageStyles.value.showStripe && index % 2 === 1) {
			// 偶数行（从0开始计数，所以index % 2 === 1是第2、4、6...行）
			row.style.backgroundColor = '#f5f5f5'
			console.log(`应用斑马纹到第${index + 1}行`)
		} else {
			// 移除背景色
			row.style.backgroundColor = ''
		}
	})
}

// 信息个性化设置相关方法
const handlePersonalSettingsConfirm = () => {
	try {
		console.log('应用个性化设置...')

		// 应用字体大小设置
		pageStyles.value.fontSize = personalSettingsForm.value.字体大小

		// 应用表格宽度调整设置
		const newTableResizable = personalSettingsForm.value.支持调整表格宽度
		console.log('应用表格宽度调整设置:', newTableResizable)
		pageStyles.value.tableResizable = newTableResizable

		// 应用数据展示方式设置
		const displayOptions = personalSettingsForm.value.数据展示方式
		console.log('选择的数据展示方式:', displayOptions)

		pageStyles.value.showBorder = displayOptions.includes('网格线')
		pageStyles.value.showIndex = displayOptions.includes('行号')
		pageStyles.value.showHeader = displayOptions.includes('列标')
		pageStyles.value.showStripe = displayOptions.includes('斑马纹')
		pageStyles.value.showHover = displayOptions.includes('悬停高亮')

		console.log('更新后的pageStyles:', JSON.stringify(pageStyles.value))

		// 保存设置到localStorage
		localStorage.setItem(
			'businessTableData_personalSettings',
			JSON.stringify({
				personalSettings: personalSettingsForm.value,
				pageStyles: pageStyles.value,
			})
		)

		// 使用nextTick确保DOM更新
		nextTick(() => {
			console.log('DOM更新完成，开始应用设置...')

			// 立即应用表格宽度调整设置
			const tableElement = document.querySelector('.business-table-data')
			if (tableElement) {
				if (newTableResizable) {
					tableElement.classList.add('custom-table-resizable')
					console.log('已启用表格列宽调整')
				} else {
					tableElement.classList.remove('custom-table-resizable')
					console.log('已禁用表格列宽调整')
				}
			}

			// 应用列宽调整控制
			setupColumnResizeControl()

			// 应用斑马纹效果
			applyStripeEffect()

			ElMessage.success('个性化设置已保存并应用')
			personalSettingsDialogVisible.value = false
		})
	} catch (error) {
		console.error('保存个性化设置失败:', error)
		ElMessage.error('保存失败，请重试')
	}
}

const handlePersonalSettingsCancel = () => {
	personalSettingsDialogVisible.value = false
}

// 翻译设置相关方法
const handleTranslationSettingsConfirm = () => {
	try {
		console.log('保存翻译设置...')
		console.log('翻译设置:', translationSettingsForm.value)

		// 保存到localStorage
		saveTranslationSettings(translationSettingsForm.value)

		// 如果翻译功能开启，则切换到选择的语言
		if (translationSettingsForm.value.翻译设置) {
			currentLanguage.value = translationSettingsForm.value.默认翻译语言
			console.log('切换语言到:', currentLanguage.value)
		} else {
			// 如果翻译功能关闭，则使用中文
			currentLanguage.value = 'zh'
		}

		// 这里可以添加保存到后端的逻辑
		// await saveTranslationSettings(translationSettingsForm.value)

		ElMessage.success(t('saveSuccess'))
		translationSettingsDialogVisible.value = false
	} catch (error) {
		console.error('保存翻译设置失败:', error)
		ElMessage.error(t('saveFailed'))
	}
}

const handleTranslationSettingsCancel = () => {
	translationSettingsDialogVisible.value = false
}

// 快捷链接设置相关方法
const openQuickLinkDialog = (row: BusinessTableDataRecord) => {
	currentQuickLinkRow.value = row
	quickLinkDialogVisible.value = true
}

const openQuickLinkDetailDialog = (linkName: string) => {
	quickLinkForm.value.链接名称 = linkName
	quickLinkForm.value.链接URL = ''
	quickLinkDetailDialogVisible.value = true
}

const handleQuickLinkConfirm = () => {
	ElMessage.success('快捷链接设置已保存')
	quickLinkDialogVisible.value = false
}

const handleQuickLinkDetailConfirm = () => {
	if (!quickLinkForm.value.链接名称) {
		ElMessage.warning('请选择链接名称')
		return
	}
	if (!quickLinkForm.value.链接URL.trim()) {
		ElMessage.warning('请输入链接URL')
		return
	}

	// 保存快捷链接配置到本地存储
	const quickLinkConfig = {
		rowId: currentQuickLinkRow.value?.id,
		链接名称: quickLinkForm.value.链接名称,
		链接URL: quickLinkForm.value.链接URL,
		创建时间: new Date().toISOString(),
		关联数据: currentQuickLinkRow.value?.字段信息描述,
	}

	// 获取现有配置
	const existingConfigs = JSON.parse(localStorage.getItem('quickLinkConfigs') || '[]')
	existingConfigs.push(quickLinkConfig)
	localStorage.setItem('quickLinkConfigs', JSON.stringify(existingConfigs))

	ElMessage.success(`快捷链接"${quickLinkForm.value.链接名称}"设置成功`)
	quickLinkDetailDialogVisible.value = false

	// 重置表单
	quickLinkForm.value = {
		链接名称: '',
		链接URL: '',
	}
}

// 流程配置相关方法
const handleWorkflowConfirm = async () => {
	try {
		// 使用 Store 的验证功能
		const validation = workflowStore.validateWorkflow(workflowForm.value)
		if (!validation.isValid) {
			ElMessage.error(validation.errors[0])
			return
		}

		workflowLoading.value = true

		// 模拟保存延迟
		await new Promise((resolve) => setTimeout(resolve, 500))

		if (isEditingWorkflow.value && currentWorkflow.value) {
			// 编辑模式
			const updated = workflowStore.updateWorkflow(
				currentWorkflow.value.id,
				workflowForm.value
			)
			if (updated) {
				ElMessage.success('流程更新成功')
			} else {
				ElMessage.error('更新失败，流程不存在')
				return
			}
		} else {
			// 新增模式
			const newWorkflow = workflowStore.addWorkflow({
				...workflowForm.value,
				status: '启用',
			})
			if (newWorkflow) {
				ElMessage.success('流程创建成功')
			} else {
				ElMessage.error('创建失败，请重试')
				return
			}
		}

		workflowDialogVisible.value = false
	} catch (error) {
		console.error('保存流程失败:', error)
		ElMessage.error('保存失败，请重试')
	} finally {
		workflowLoading.value = false
	}
}

const handleWorkflowDialogClosed = () => {
	currentWorkflow.value = null
	isEditingWorkflow.value = false
	workflowForm.value = {
		name: '',
		description: '',
		nodes: [],
		auditMode: 'serial',
		auditRule: 'majority',
		// 串行审核配置
		timeoutAction: 'escalate',
		allowReturn: true,
		// 并行审核配置
		minApprovalCount: 1,
		approvalRatio: '50',
		// 会签审核配置
		countersignType: 'all',
		countersignCondition: 'all_agree',
		// 特殊配置
		specialConfig: ['notifyApplicant', 'recordAuditLog'],
	}
}

// 流程节点操作函数
const addWorkflowNode = () => {
	const newNode = {
		id: Date.now().toString(),
		name: '',
		type: 'audit',
		timeLimit: 3,
		description: '',
		auditors: [],
	}
	workflowForm.value.nodes.push(newNode)
}

const removeWorkflowNode = (index: number) => {
	workflowForm.value.nodes.splice(index, 1)
}

const moveNodeUp = (index: number) => {
	if (index > 0) {
		const nodes = workflowForm.value.nodes
		const temp = nodes[index]
		nodes[index] = nodes[index - 1]
		nodes[index - 1] = temp
	}
}

const moveNodeDown = (index: number) => {
	const nodes = workflowForm.value.nodes
	if (index < nodes.length - 1) {
		const temp = nodes[index]
		nodes[index] = nodes[index + 1]
		nodes[index + 1] = temp
	}
}

// 审核人选择相关函数
const openAuditorSelector = (nodeIndex: number) => {
	currentNodeIndex.value = nodeIndex
	auditorSelectorVisible.value = true
	auditorSearchKeyword.value = ''
	auditorPagination.value.page = 1

	// 预选已选择的审核人
	const currentNode = workflowForm.value.nodes[nodeIndex]
	selectedAuditors.value = currentNode.auditors ? [...currentNode.auditors] : []
}

const handleAuditorSearch = () => {
	auditorPagination.value.page = 1
}

const handleAuditorSelectionChange = (selection: any[]) => {
	selectedAuditors.value = selection
}

const confirmAuditorSelection = () => {
	if (currentNodeIndex.value >= 0) {
		const currentNode = workflowForm.value.nodes[currentNodeIndex.value]
		currentNode.auditors = [...selectedAuditors.value]
	}
	auditorSelectorVisible.value = false
}

const removeAuditor = (nodeIndex: number, auditorId: string) => {
	const currentNode = workflowForm.value.nodes[nodeIndex]
	if (currentNode.auditors) {
		currentNode.auditors = currentNode.auditors.filter(
			(auditor: any) => auditor.id !== auditorId
		)
	}
}

const onAuditorPaginationChange = (val: any, type: any) => {
	if (type === 'page') {
		auditorPagination.value.page = val
	} else {
		auditorPagination.value.size = val
		auditorPagination.value.page = 1
	}
}

// 获取审核模式描述
const getAuditModeDescription = (mode: string) => {
	const descriptions = {
		serial: '串行审核：按照节点顺序依次进行审核，前一个节点审核通过后才能进入下一个节点。适用于需要严格按流程执行的场景。',
		parallel:
			'并行审核：多个审核人同时进行审核，根据设定的通过条件决定审核结果。适用于需要多方意见的场景。',
		countersign:
			'会签审核：需要指定的审核人员共同参与决策，支持全员会签、部分会签等多种方式。适用于重要决策场景。',
	}
	return descriptions[mode] || '请选择审核模式'
}

// 表格宽度设置变化处理
const onTableWidthSettingChange = (value: boolean) => {
	console.log('=== 表格宽度调整设置变化 ===')
	console.log('新值:', value)
	console.log('变化前 pageStyles.tableResizable:', pageStyles.value.tableResizable)

	// 立即更新状态
	pageStyles.value.tableResizable = value

	// 立即更新表单中的值，确保同步
	personalSettingsForm.value.支持调整表格宽度 = value

	// 强制触发响应式更新和DOM重新渲染
	nextTick(() => {
		console.log('变化后 pageStyles.tableResizable:', pageStyles.value.tableResizable)

		// 强制重新渲染表格组件
		const tableElement = document.querySelector('.business-table-data')
		if (tableElement) {
			// 切换CSS类来触发样式变化
			if (value) {
				tableElement.classList.add('custom-table-resizable')
				console.log('已添加 custom-table-resizable 类')
			} else {
				tableElement.classList.remove('custom-table-resizable')
				console.log('已移除 custom-table-resizable 类')
			}
		}

		// 手动调用列宽调整控制函数
		setupColumnResizeControl()

		// 延迟检查DOM状态
		setTimeout(() => {
			const table = document.querySelector('.business-table-data .el-table')
			if (table) {
				const headers = table.querySelectorAll('th')
				console.log(`表头数量: ${headers.length}`)
				headers.forEach((th, index) => {
					const cell = th.querySelector('.cell')
					if (cell) {
						const afterElement = window.getComputedStyle(cell, '::after')
						console.log(`表头${index + 1} 调整手柄状态:`, {
							display: afterElement.display,
							cursor: window.getComputedStyle(th).cursor,
							hasResizableClass:
								tableElement?.classList.contains('custom-table-resizable'),
						})
					}
				})
			}
		}, 200)
	})

	ElMessage.info(`表格宽度调整已${value ? '启用' : '禁用'}`)
}

// 数据展示方式变化处理
const onDataDisplayChange = (value: string[]) => {
	console.log('数据展示方式变化:', value)

	pageStyles.value.showBorder = value.includes('网格线')
	pageStyles.value.showIndex = value.includes('行号')
	pageStyles.value.showHeader = value.includes('列标')
	pageStyles.value.showStripe = value.includes('斑马纹')
	pageStyles.value.showHover = value.includes('悬停高亮')

	console.log('更新后的pageStyles:', pageStyles.value)

	// 强制触发响应式更新
	nextTick(() => {
		console.log('DOM更新完成')
	})
}

// 加载保存的个性化设置
const loadPersonalSettings = () => {
	try {
		console.log('加载个性化设置...')
		const saved = localStorage.getItem('businessTableData_personalSettings')

		if (saved) {
			console.log('找到保存的设置')
			const settings = JSON.parse(saved)
			personalSettingsForm.value = {...settings.personalSettings}

			// 确保所有必要的属性都存在
			const loadedStyles = settings.pageStyles || {}
			pageStyles.value = {
				fontSize: loadedStyles.fontSize || '14px',
				tableResizable:
					loadedStyles.tableResizable !== undefined ? loadedStyles.tableResizable : true,
				showBorder: loadedStyles.showBorder !== undefined ? loadedStyles.showBorder : true,
				showIndex: loadedStyles.showIndex !== undefined ? loadedStyles.showIndex : true,
				showHeader: loadedStyles.showHeader !== undefined ? loadedStyles.showHeader : true,
				showStripe: loadedStyles.showStripe !== undefined ? loadedStyles.showStripe : true,
				showHover: loadedStyles.showHover !== undefined ? loadedStyles.showHover : true,
			}

			console.log('加载的pageStyles:', JSON.stringify(pageStyles.value))

			// 确保数据展示方式选项与pageStyles一致
			const displayOptions = []
			if (pageStyles.value.showBorder) displayOptions.push('网格线')
			if (pageStyles.value.showIndex) displayOptions.push('行号')
			if (pageStyles.value.showHeader) displayOptions.push('列标')
			if (pageStyles.value.showStripe) displayOptions.push('斑马纹')
			if (pageStyles.value.showHover) displayOptions.push('悬停高亮')

			personalSettingsForm.value.数据展示方式 = displayOptions
		} else {
			console.log('没有找到保存的设置，使用默认值')
		}

		// 使用nextTick确保DOM更新
		nextTick(() => {
			console.log('个性化设置加载完成，DOM已更新')
		})
	} catch (error) {
		console.error('加载个性化设置失败:', error)
		ElMessage.error('加载个性化设置失败，将使用默认设置')
	}
}

// 数据信息管理相关方法
const onDataInfoDialogClosed = () => {
	dataSearchKeyword.value = ''
	dataInfoPagination.value.page = 1
}

// 数据刷新
const onDataRefresh = async () => {
	try {
		dataInfoLoading.value = true

		// 模拟刷新延迟
		await new Promise((resolve) => setTimeout(resolve, 800))

		// 重新计算统计信息
		const totalCount = dataInfoAllList.value.length
		const validCount = dataInfoAllList.value.filter((item) => item.status === '正常').length
		const invalidCount = totalCount - validCount

		dataInfoStats.value = {
			total: totalCount,
			valid: validCount,
			invalid: invalidCount,
			lastUpdate: new Date().toLocaleString('zh-CN'),
		}

		ElMessage.success('数据刷新完成')
	} catch (error) {
		console.error('刷新数据失败:', error)
		ElMessage.error('刷新数据失败，请重试')
	} finally {
		dataInfoLoading.value = false
	}
}

// 数据导出
const onDataExport = async () => {
	try {
		dataExportLoading.value = true

		// 模拟导出延迟
		await new Promise((resolve) => setTimeout(resolve, 1500))

		ElMessage.success('数据导出完成，请查看下载文件')
	} catch (error) {
		console.error('导出数据失败:', error)
		ElMessage.error('导出数据失败，请重试')
	} finally {
		dataExportLoading.value = false
	}
}

// 数据校验
const onDataValidate = async () => {
	try {
		dataValidateLoading.value = true

		// 模拟校验延迟
		await new Promise((resolve) => setTimeout(resolve, 1200))

		const invalidCount = dataInfoAllList.value.filter((item) => item.status === '异常').length
		if (invalidCount > 0) {
			ElMessage.warning(`发现 ${invalidCount} 条异常数据，请及时处理`)
		} else {
			ElMessage.success('数据校验通过，所有数据正常')
		}
	} catch (error) {
		console.error('数据校验失败:', error)
		ElMessage.error('数据校验失败，请重试')
	} finally {
		dataValidateLoading.value = false
	}
}

// 数据搜索
const onDataSearch = () => {
	// 搜索时重置到第一页
	dataInfoPagination.value.page = 1

	// 更新分页总数
	let filteredData = dataInfoAllList.value
	if (dataSearchKeyword.value) {
		filteredData = filteredData.filter(
			(item) =>
				item.name.toLowerCase().includes(dataSearchKeyword.value.toLowerCase()) ||
				item.type.toLowerCase().includes(dataSearchKeyword.value.toLowerCase())
		)
	}
	dataInfoPagination.value.total = filteredData.length
}

// 数据信息分页变化
const onDataInfoPaginationChange = async (val: any, type: any) => {
	try {
		dataInfoTableLoading.value = true

		// 模拟分页加载延迟
		await new Promise((resolve) => setTimeout(resolve, 300))

		if (type === 'page') {
			dataInfoPagination.value.page = val
		} else {
			dataInfoPagination.value.size = val
			dataInfoPagination.value.page = 1 // 改变每页大小时重置到第一页
		}
	} catch (error) {
		console.error('分页操作失败:', error)
		ElMessage.error('分页操作失败，请重试')
	} finally {
		dataInfoTableLoading.value = false
	}
}

// 数据类型标签类型
const getDataTypeTagType = (type: string) => {
	const typeMap: Record<string, string> = {
		基础数据: 'primary',
		业务数据: 'success',
		库存数据: 'warning',
		财务数据: 'danger',
		服务数据: 'info',
		日志数据: '',
		营销数据: 'success',
	}
	return typeMap[type] || ''
}

// 数据状态标签类型
const getDataStatusTagType = (status: string) => {
	return status === '正常' ? 'success' : 'danger'
}

// 数据操作方法
const onDataView = (row: any) => {
	ElMessage.info(`查看数据: ${row.name}`)
}

const onDataEdit = (row: any) => {
	ElMessage.info(`编辑数据: ${row.name}`)
}

const onDataDelete = async (row: any) => {
	try {
		await ElMessageBox.confirm(`确定要删除"${row.name}"吗？`, '删除确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})

		// 模拟删除延迟
		await new Promise((resolve) => setTimeout(resolve, 500))

		// 从数据中删除
		const index = dataInfoAllList.value.findIndex((item) => item.id === row.id)
		if (index !== -1) {
			dataInfoAllList.value.splice(index, 1)

			// 更新统计信息
			const totalCount = dataInfoAllList.value.length
			const validCount = dataInfoAllList.value.filter((item) => item.status === '正常').length
			const invalidCount = totalCount - validCount

			dataInfoStats.value = {
				total: totalCount,
				valid: validCount,
				invalid: invalidCount,
				lastUpdate: new Date().toLocaleString('zh-CN'),
			}

			// 更新分页总数
			dataInfoPagination.value.total = totalCount

			// 如果当前页没有数据了，回到上一页
			const totalPages = Math.ceil(
				dataInfoPagination.value.total / dataInfoPagination.value.size
			)
			if (dataInfoPagination.value.page > totalPages && totalPages > 0) {
				dataInfoPagination.value.page = totalPages
			}

			ElMessage.success('删除成功')
		}
	} catch {
		// 用户取消删除
	}
}
</script>

<route>
{
  meta: {
    title: '业务数据描述',
    ignoreLabel: false
  }
}
</route>

<style scoped lang="scss">
.business-table-data {
	.top-actions {
		display: flex;
		gap: 8px;
	}

	.search {
		margin-bottom: 16px;
	}

	.action-buttons {
		display: flex;
		gap: 8px;
		margin-bottom: 16px;
		flex-wrap: wrap;
	}

	// 新增/编辑弹窗样式优化
	:deep(.el-dialog) {
		.el-dialog__body {
			padding: 20px 24px;
		}
	}

	// 数据信息输入框样式
	.data-info-section {
		margin: 20px 0;
	}

	// 移除富文本编辑器样式

	// 审核流程区域样式
	.workflow-section {
		margin: 20px 0;
		padding: 16px;
		background: #fafafa;
		border-radius: 4px;
		border: 1px solid #f0f0f0;

		.workflow-row {
			display: flex;
			align-items: center;
			gap: 12px;

			.workflow-label {
				font-size: 14px;
				font-weight: 500;
				color: #606266;
				min-width: 80px;
				flex-shrink: 0;
			}

			.el-select {
				width: 200px;
			}

			.el-button {
				margin-left: 8px;
			}
		}
	}

	// 对话框底部样式
	.dialog-footer {
		display: flex;
		justify-content: flex-end;
		gap: 12px;
	}

	// 批量导入弹窗样式 - 简化设计
	.batch-import-content {
		padding: 16px 0;

		.import-header {
			margin-bottom: 20px;

			.header-title {
				display: flex;
				align-items: center;
				margin-bottom: 8px;

				.el-icon {
					font-size: 16px;
					color: #409eff;
					margin-right: 8px;
				}

				span {
					font-size: 16px;
					font-weight: 500;
					color: #303133;
					flex: 1;
				}

				.el-button {
					color: #409eff;
					font-size: 12px;
					padding: 0;
				}
			}

			.import-description {
				color: #606266;
				font-size: 14px;
				line-height: 1.5;
			}
		}

		.import-steps {
			margin-bottom: 24px;

			.step-list {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 16px;
				background: #f8f9fa;
				border-radius: 6px;

				.step-item {
					display: flex;
					flex-direction: column;
					align-items: center;

					.step-number {
						width: 28px;
						height: 28px;
						border-radius: 50%;
						background: #409eff;
						color: white;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 14px;
						font-weight: 500;
						margin-bottom: 6px;
					}

					.step-text {
						font-size: 12px;
						color: #606266;
						text-align: center;
					}
				}

				.step-arrow {
					margin: 0 20px;
					color: #c0c4cc;
					font-size: 16px;
					margin-bottom: 20px;
				}
			}
		}

		.template-download {
			text-align: center;
			margin-bottom: 24px;

			.el-button {
				.el-icon {
					margin-right: 6px;
				}
			}

			.download-tip {
				display: block;
				margin-top: 8px;
				font-size: 12px;
				color: #909399;
			}
		}

		.file-upload {
			margin-bottom: 20px;

			.upload-demo {
				width: 100%;

				:deep(.el-upload) {
					width: 100%;
				}

				:deep(.el-upload-dragger) {
					width: 100%;
					height: 120px;
					border: 2px dashed #d9d9d9;
					border-radius: 6px;
					cursor: pointer;
					position: relative;
					overflow: hidden;
					transition: border-color 0.2s;

					&:hover {
						border-color: #409eff;
					}
				}

				.el-icon--upload {
					font-size: 28px;
					color: #c0c4cc;
					margin-bottom: 16px;
				}

				.el-upload__text {
					color: #606266;
					font-size: 14px;
					text-align: center;
				}

				:deep(.el-upload__tip) {
					font-size: 12px;
					color: #909399;
					margin-top: 8px;
					text-align: center;
				}
			}

			.selected-file {
				margin-top: 12px;

				.file-info {
					display: flex;
					align-items: center;
					padding: 8px 12px;
					background: #f0f9ff;
					border-radius: 4px;
					border: 1px solid #b3d8ff;

					.el-icon {
						font-size: 16px;
						color: #409eff;
						margin-right: 8px;
					}

					.file-name {
						flex: 1;
						font-size: 14px;
						color: #303133;
						word-break: break-all;
					}

					.el-button {
						color: #909399;
						padding: 0;
						margin-left: 8px;

						&:hover {
							color: #f56c6c;
						}
					}
				}
			}
		}

		.import-tips {
			:deep(.el-alert__content) {
				p {
					margin: 4px 0;
					font-size: 13px;
					line-height: 1.4;
				}
			}
		}
	}

	// 批量修改弹窗样式
	.batch-edit-content {
		.batch-edit-header {
			display: flex;
			align-items: center;
			margin-bottom: 20px;
			font-size: 16px;
			font-weight: 500;
			color: #409eff;

			.el-icon {
				margin-right: 8px;
				font-size: 18px;
			}
		}

		.batch-edit-tip {
			margin-bottom: 20px;
		}

		.batch-edit-form {
			.form-item {
				display: flex;
				align-items: center;
				margin-bottom: 20px;

				.field-checkbox {
					min-width: 120px;
					margin-right: 16px;
				}

				.field-input {
					flex: 1;
				}
			}
		}
	}
}

// 多维度分类弹窗样式
.multi-dimension-content {
	.multi-dimension-header {
		margin-bottom: 16px;
		display: flex;
		justify-content: flex-start;
	}

	.multi-dimension-table {
		margin-bottom: 16px;

		.el-table {
			.el-table__header {
				th {
					background-color: #f5f7fa;
					color: #606266;
					font-weight: 500;
				}
			}

			.el-table__body {
				tr:hover {
					background-color: #f5f7fa;
				}
			}
		}
	}

	.multi-dimension-pagination {
		display: flex;
		justify-content: flex-end;
		padding: 16px 0;
		border-top: 1px solid #e4e7ed;
		margin-top: 16px;
	}

	// 数据信息管理弹窗样式
	.data-info-content {
		.data-overview {
			margin-bottom: 24px;

			.overview-cards {
				display: grid;
				grid-template-columns: repeat(4, 1fr);
				gap: 16px;

				.overview-card {
					background: #f8f9fa;
					border: 1px solid #e9ecef;
					border-radius: 8px;
					padding: 20px;
					display: flex;
					align-items: center;
					transition: all 0.3s ease;

					&:hover {
						box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
						transform: translateY(-2px);
					}

					.card-icon {
						margin-right: 16px;
						font-size: 32px;
						color: #409eff;
					}

					.card-content {
						.card-title {
							font-size: 14px;
							color: #666;
							margin-bottom: 8px;
						}

						.card-value {
							font-size: 24px;
							font-weight: 600;
							color: #333;
						}
					}
				}
			}
		}

		.data-toolbar {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16px;
			padding: 16px;
			background: #f8f9fa;
			border-radius: 8px;

			.toolbar-left {
				display: flex;
				gap: 12px;
			}

			.toolbar-right {
				display: flex;
				align-items: center;
			}
		}

		.data-table {
			margin-bottom: 16px;

			.el-table {
				.el-table__header {
					th {
						background-color: #f5f7fa;
						color: #606266;
						font-weight: 500;
					}
				}

				.el-table__body {
					tr:hover {
						background-color: #f5f7fa;
					}
				}
			}
		}

		.data-pagination {
			display: flex;
			justify-content: flex-end;
			padding: 16px 0;
			border-top: 1px solid #e4e7ed;
		}
	}
}

// 导入须知弹窗样式
.import-rules-dialog {
	:deep(.el-dialog__header) {
		background: #f5f7fa;
		padding: 16px 20px;
		border-bottom: 1px solid #e4e7ed;

		.el-dialog__title {
			font-size: 16px;
			font-weight: 600;
			color: #303133;
		}
	}

	.import-rules-content {
		padding: 20px 0;

		.rule-item {
			display: flex;
			align-items: flex-start;
			margin-bottom: 16px;
			line-height: 1.6;

			&:last-child {
				margin-bottom: 0;
			}

			.rule-number {
				color: #409eff;
				font-weight: 600;
				margin-right: 8px;
				min-width: 20px;
			}

			.rule-text {
				color: #606266;
				font-size: 14px;
				flex: 1;
			}
		}
	}
}

// 业务数据规则弹窗样式
.business-rules-content {
	.rule-section {
		margin-bottom: 20px;

		.rule-item {
			display: flex;
			align-items: center;
			margin-bottom: 12px;

			.rule-label {
				width: 140px;
				font-size: 14px;
				color: #606266;
				text-align: right;
				margin-right: 12px;
				flex-shrink: 0;
			}

			.checkbox-group {
				display: flex;
				gap: 16px;
				flex-wrap: wrap;
			}

			.radio-group {
				display: flex;
				gap: 16px;
			}

			.format-setting {
				display: flex;
				align-items: center;
			}
		}
	}

	.rule-tip {
		margin-top: 20px;
		padding: 12px;
		background-color: #fdf6ec;
		border-radius: 4px;
	}
}

// 权限继承规则弹窗样式
.permission-rules-content {
	.rule-section {
		margin-bottom: 24px;

		.section-title {
			margin: 0 0 16px 0;
			color: #303133;
			font-size: 16px;
			font-weight: 600;
		}

		.radio-group {
			:deep(.el-radio) {
				display: block;
				margin-bottom: 12px;
				margin-right: 0;

				.el-radio__label {
					font-size: 14px;
					color: #606266;
				}
			}
		}
	}
}

// 动态更新提醒弹窗样式
.dynamic-update-content {
	.el-table {
		border: 1px solid #ebeef5;
		border-radius: 4px;

		:deep(.el-table__header) {
			th {
				background-color: #f5f7fa !important;
				color: #606266 !important;
				font-weight: 600;
			}
		}

		:deep(.el-table__body) {
			tr:hover {
				background-color: #f5f7fa;
			}
		}
	}
}

// 角色自定义弹窗样式
.role-custom-content {
	.role-custom-header {
		margin-bottom: 20px;
		display: flex;
		justify-content: flex-start;
	}

	.role-custom-table {
		:deep(.el-table) {
			border: 1px solid #dcdfe6;

			th {
				background-color: #f5f7fa;
				color: #606266;
				font-weight: 600;
			}

			td {
				padding: 12px 0;
			}
		}
	}
}

// 角色新增弹窗样式
.role-add-content {
	:deep(.el-form-item) {
		margin-bottom: 20px;

		.el-form-item__label {
			color: #606266;
			font-weight: 500;
		}

		.el-input__wrapper {
			border-radius: 4px;
		}

		.el-select {
			width: 100%;
		}
	}
}

// 角色编辑弹窗样式
.role-edit-content {
	:deep(.el-form-item) {
		margin-bottom: 20px;

		.el-form-item__label {
			color: #606266;
			font-weight: 500;
		}

		.el-input__wrapper {
			border-radius: 4px;
		}

		.el-select {
			width: 100%;
		}
	}
}

// 信息定时任务样式
.info-schedule-content {
	.end-time-section {
		.radio-group {
			margin-bottom: 12px;

			.el-radio {
				margin-right: 24px;
			}
		}

		.end-time-input {
			margin-bottom: 8px;
		}

		.warning-tip {
			font-size: 12px;
			line-height: 1.4;
		}
	}

	.el-form-item {
		margin-bottom: 20px;
	}

	.el-form-item__label {
		color: #606266;
		font-weight: 500;
	}
}

// 信息个性化设置样式
.personal-settings-content {
	.setting-section {
		margin-bottom: 24px;

		.setting-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 12px 0;

			.setting-label {
				font-weight: 500;
				color: #606266;
				min-width: 120px;
			}

			.setting-control {
				display: flex;
				align-items: center;
				flex: 1;

				.control-label {
					color: #606266;
					font-size: 14px;
				}

				.switch-label {
					margin-left: 8px;
					color: #409eff;
					font-size: 12px;
				}
			}
		}
	}
}

// 翻译设置样式
.translation-settings-content {
	.setting-section {
		margin-bottom: 24px;

		.setting-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 12px 0;

			.setting-label {
				font-weight: 500;
				color: #606266;
				min-width: 120px;
			}

			.setting-control {
				display: flex;
				align-items: center;
				flex: 1;
			}
		}
	}
}

// 表格个性化样式
.business-table-data {
	:deep(.el-table) {
		.el-table__header-wrapper {
			.el-table__header {
				th {
					height: auto !important;
					min-height: 40px;

					.cell {
						height: auto !important;
						line-height: 1.5;
						overflow: visible !important;
						white-space: nowrap;
					}
				}
			}
		}

		.el-table__body-wrapper {
			.el-table__body {
				td {
					.cell {
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						max-width: 100%;
					}
				}
			}
		}
	}
}

// 显示网格线
.custom-table-border {
	:deep(.el-table) {
		border: 1px solid #ebeef5 !important;

		.el-table__cell {
			// border-right: 1px solid #ebeef5 !important;
			border-bottom: 1px solid #ebeef5 !important;
		}

		th.el-table__cell {
			// border-right: 1px solid #ebeef5 !important;
			border-bottom: 1px solid #ebeef5 !important;
		}
	}
}

// 禁用网格线
.business-table-data:not(.custom-table-border) {
	:deep(.el-table) {
		border: none !important;

		.el-table__cell {
			border-right: none !important;
			border-bottom: none !important;
		}

		th.el-table__cell {
			border-right: none !important;
			border-bottom: 1px solid #ebeef5 !important; // 保留表头底部边框
		}
	}
}

// 禁用斑马纹
.business-table-data:not(.custom-table-stripe) {
	:deep(.el-table--striped) {
		.el-table__body {
			tr.el-table__row--striped {
				td.el-table__cell {
					background-color: transparent !important;
				}
			}
		}
	}
}

// 禁用悬停高亮
.business-table-data:not(.custom-table-hover) {
	:deep(.el-table) {
		.el-table__body {
			tr.hover-row > td.el-table__cell,
			tr:hover > td.el-table__cell {
				background-color: inherit !important;
			}
		}
	}
}

// 隐藏表头
.custom-table-no-header {
	:deep(.el-table) {
		.el-table__header-wrapper {
			display: none !important;
		}
	}
}

// 斑马纹样式控制 - 使用更强的选择器和明显的背景色
.business-table-data.custom-table-stripe {
	:deep(.el-table) {
		tbody tr:nth-child(even) td,
		.el-table__body tr:nth-child(even) td {
			background-color: #e8f4fd !important;
		}
	}

	// 针对TableV2组件的特殊处理
	:deep(table) {
		tbody tr:nth-child(even) td {
			background-color: #e8f4fd !important;
		}
	}

	// 更通用的选择器
	:deep(tr:nth-child(even)) {
		td {
			background-color: #e8f4fd !important;
		}
	}
}

// 禁用斑马纹时隐藏斑马纹效果
.business-table-data:not(.custom-table-stripe) {
	:deep(.el-table) {
		tbody tr:nth-child(even) td,
		.el-table__body tr:nth-child(even) td {
			background-color: transparent !important;
		}
	}

	// 针对TableV2组件的特殊处理
	:deep(table) {
		tbody tr:nth-child(even) td {
			background-color: transparent !important;
		}
	}
}

// 悬停高亮样式控制
.custom-table-hover {
	:deep(.el-table) {
		.el-table__body {
			tr.hover-row > td.el-table__cell,
			tr:hover > td.el-table__cell {
				background-color: #f5f7fa !important;
			}
		}
	}
}

// 禁用悬停高亮
.business-table-data:not(.custom-table-hover) {
	:deep(.el-table) {
		.el-table__body {
			tr.hover-row > td.el-table__cell,
			tr:hover > td.el-table__cell {
				background-color: inherit !important;
			}
		}
	}
}

// 启用表格列宽调整
.custom-table-resizable {
	:deep(.el-table) {
		.el-table__header-wrapper {
			.el-table__header {
				th {
					position: relative;
					user-select: none;

					&:hover {
						.el-table__column-resize-proxy {
							display: block;
						}
					}
				}

				// 列宽调整手柄
				th .cell {
					position: relative;

					&::after {
						content: '';
						position: absolute;
						top: 0;
						right: 0;
						width: 4px;
						height: 100%;
						cursor: col-resize;
						background: transparent;
						z-index: 1;
					}

					&:hover::after {
						background: #409eff;
					}
				}
			}
		}

		// 列宽调整时的视觉反馈
		.el-table__column-resize-proxy {
			position: absolute;
			left: 200px;
			top: 0;
			bottom: 0;
			width: 0;
			border-left: 1px solid #409eff;
			z-index: 10;
			display: none;
		}
	}
}

// 禁用表格列宽调整
.business-table-data:not(.custom-table-resizable) {
	:deep(.el-table) {
		.el-table__header th {
			resize: none !important;

			.cell::after {
				display: none !important;
			}
		}

		.el-table__column-resize-proxy {
			display: none !important;
		}

		// 禁用所有可能的列宽调整相关事件
		.el-table__header-wrapper {
			pointer-events: auto;

			th {
				cursor: default !important;

				&:hover {
					cursor: default !important;
				}
			}
		}
	}
}

// 权限日志记录对话框样式
.permission-record-content {
	.record-table {
		margin-bottom: 16px;

		:deep(.el-table) {
			.el-table__header {
				th {
					background-color: #f5f7fa !important;
					color: #606266;
					font-weight: 600;
				}
			}

			.el-table__body {
				tr:hover > td {
					background-color: #f5f7fa;
				}
			}
		}
	}

	.record-pagination {
		display: flex;
		justify-content: center;
		margin-top: 16px;
	}
}

// 备份与恢复对话框样式
.backup-restore-content {
	.backup-section,
	.restore-section {
		margin-bottom: 24px;

		.section-title {
			font-size: 16px;
			font-weight: 600;
			color: #303133;
			margin-bottom: 16px;
			padding-bottom: 8px;
			border-bottom: 1px solid #e4e7ed;
		}

		.form-item {
			margin-bottom: 16px;

			.form-label {
				display: block;
				font-size: 14px;
				color: #606266;
				margin-bottom: 8px;
				font-weight: 500;
			}

			:deep(.el-date-editor) {
				width: 100%;
			}

			:deep(.el-select) {
				width: 100%;
			}
		}
	}

	.restore-section {
		margin-bottom: 0;
	}
}

/* 权限管理样式 */
.permission-manage-content {
	padding: 20px 0;

	.role-section {
		margin-bottom: 30px;

		.role-item {
			display: flex;
			align-items: center;

			.role-label {
				font-size: 14px;
				color: #606266;
				font-weight: 500;
				min-width: 80px;
			}
		}
	}

	.personnel-section {
		margin-bottom: 30px;

		.personnel-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 15px;

			.personnel-label {
				font-size: 14px;
				color: #606266;
				font-weight: 500;
			}

			.personnel-search {
				display: flex;
				align-items: center;
			}
		}

		.personnel-table {
			margin-bottom: 15px;
			max-height: 300px;
			overflow-y: auto;
		}

		.personnel-pagination {
			display: flex;
			justify-content: flex-end;
		}
	}

	.operations-section {
		.operations-label {
			font-size: 14px;
			color: #606266;
			font-weight: 500;
			margin-bottom: 15px;
		}

		.operations-list {
			display: flex;
			gap: 20px;
			flex-wrap: wrap;

			.operation-checkbox {
				margin-right: 0;

				:deep(.el-checkbox__label) {
					font-size: 14px;
					color: #606266;
				}
			}
		}
	}
}

/* 信息关联推荐样式 */
.info-recommend-content {
	padding: 0;

	.recommend-header {
		margin-bottom: 20px;
		display: flex;
		justify-content: flex-start;
	}

	.recommend-table {
		width: 100%;
	}
}

.recommend-detail-content {
	padding: 20px 0;

	.el-form-item {
		margin-bottom: 20px;
	}
}

/* 历史操作记录样式 */
.history-record-content {
	padding: 0;

	.history-tabs {
		width: 100%;
	}

	.operation-history-table,
	.authorization-history-table {
		margin-top: 10px;
	}

	.operation-history-pagination,
	.authorization-history-pagination {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;
	}
}

/* 表格列宽调整样式 */
.custom-table-resizable :deep(.el-table th) {
	position: relative;
	user-select: none;
	border-right: 1px solid #ebeef5;
}

.custom-table-resizable :deep(.el-table th::after) {
	content: '⋮⋮';
	position: absolute;
	top: 0;
	right: 0;
	width: 30px;
	height: 100%;
	cursor: col-resize;
	background: rgba(0, 0, 0, 0.02);
	z-index: 100;
	border-right: 2px solid transparent;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	color: #ddd;
	letter-spacing: -1px;
	border-left: 1px solid transparent;
	transition: all 0.2s ease;
}

.custom-table-resizable :deep(.el-table th:hover::after) {
	background: rgba(64, 158, 255, 0.1);
	border-left: 2px solid #409eff;
	color: #409eff;
	font-weight: bold;
}

.custom-table-resizable :deep(.el-table th.is-resizing::after) {
	background: rgba(64, 158, 255, 0.2);
	border-left: 3px solid #409eff;
	color: #409eff;
	font-weight: bold;
}

/* 调试样式 - 让拖拽区域更明显 */
.custom-table-resizable :deep(.el-table th) {
	border-right: 1px solid #ddd;
}

.custom-table-resizable :deep(.el-table th:last-child::after) {
	display: none;
}

.business-table-data:not(.custom-table-resizable) :deep(.el-table th::after) {
	display: none;
}

.business-table-data:not(.custom-table-resizable) :deep(.el-table th) {
	//cursor: default;
}

// 文件上传拖拽区域样式
.upload-dragger {
	:deep(.el-upload-dragger) {
		width: 100%;
		height: 120px;
		border: 2px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
		transition: border-color 0.3s;

		&:hover {
			border-color: #409eff;
		}

		.upload-dragger-content {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			height: 100%;
			padding: 20px;

			.el-icon--upload {
				font-size: 28px;
				color: #c0c4cc;
				margin-bottom: 8px;
			}

			.el-upload__text {
				color: #606266;
				font-size: 14px;
				margin-bottom: 4px;

				em {
					color: #409eff;
					font-style: normal;
				}
			}

			.el-upload__tip {
				color: #909399;
				font-size: 12px;
				line-height: 1.4;
				text-align: center;
			}
		}
	}

	:deep(.el-upload-list) {
		margin-top: 10px;
	}
}

// 信息化模板管理对话框样式
.template-management {
	.template-table {
		.el-table {
			border: 1px solid #ebeef5;
			border-radius: 4px;

			:deep(.el-table__header) {
				background-color: #f5f7fa;

				th {
					background-color: #f5f7fa;
					color: #606266;
					font-weight: 600;
					border-bottom: 1px solid #ebeef5;
				}
			}

			:deep(.el-table__body) {
				tr {
					&:hover {
						background-color: #f5f7fa;
					}

					td {
						border-bottom: 1px solid #ebeef5;
						padding: 12px 0;

						.el-button {
							margin-right: 8px;

							&:last-child {
								margin-right: 0;
							}
						}
					}
				}
			}
		}
	}
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 12px;
	padding: 16px 0 0 0;
}

.template-table {
	margin: 20px 0;
}

.template-pagination {
	margin-top: 20px;
	display: flex;
	justify-content: center;
}

/* 审核管理弹窗样式 */
.audit-dialog-content {
	.audit-tip {
		background: #e7f3ff;
		padding: 12px 16px;
		border-radius: 4px;
		margin-bottom: 20px;
		border-left: 4px solid #409eff;

		.tip-text {
			color: #666;
			font-size: 14px;
			line-height: 1.5;
		}
	}

	.audit-form {
		.el-form-item {
			margin-bottom: 20px;

			.el-form-item__label {
				color: #333;
				font-weight: 500;

				&::before {
					content: '*';
					color: #f56c6c;
					margin-right: 4px;
				}
			}

			.el-radio-group {
				.el-radio {
					margin-right: 30px;

					.el-radio__label {
						font-size: 14px;
						color: #333;
					}
				}
			}

			.el-textarea {
				.el-textarea__inner {
					border-radius: 4px;
					border: 1px solid #dcdfe6;

					&:focus {
						border-color: #409eff;
					}
				}
			}
		}
	}
}

.dialog-footer {
	text-align: right;

	.el-button {
		margin-left: 10px;
	}
}

// 信息锁定/解锁弹窗样式
.info-lock-dialog,
.info-unlock-dialog {
	.info-lock-content,
	.info-unlock-content {
		.lock-message,
		.unlock-message {
			text-align: center;
			padding: 20px 0;

			p {
				font-size: 16px;
				color: #606266;
				margin: 0;
				line-height: 1.5;
			}
		}
	}
}

// 版本注释弹窗样式
.version-notes-dialog {
	.version-notes-content {
		.version-form {
			.version-table {
				.el-table {
					.el-table__header {
						th {
							background-color: #f5f7fa;
							color: #606266;
							font-weight: 500;
						}
					}

					.el-table__body {
						.el-table__row {
							&:hover {
								background-color: #f5f7fa;
							}
						}
					}

					// 表格内表单项样式
					.table-form-item {
						margin-bottom: 0;

						.el-form-item__content {
							margin-left: 0 !important;
						}

						.el-form-item__error {
							position: static;
							padding-top: 2px;
							font-size: 12px;
						}
					}

					.el-input {
						.el-input__inner {
							border: 1px solid #dcdfe6;
							border-radius: 4px;

							&:focus {
								border-color: #409eff;
							}
						}
					}

					.el-textarea {
						.el-textarea__inner {
							border: 1px solid #dcdfe6;
							border-radius: 4px;
							resize: none;

							&:focus {
								border-color: #409eff;
							}
						}
					}
				}
			}
		}
	}
}

// 批注弹窗样式
.comments-dialog {
	.comments-content {
		.add-comment-section {
			text-align: center;
			margin-bottom: 20px;
		}

		.comments-table {
			.el-table {
				.el-table__header {
					th {
						background-color: #f5f7fa;
						color: #606266;
						font-weight: 500;
					}
				}

				.el-table__body {
					.el-table__row {
						&:hover {
							background-color: #f5f7fa;
						}
					}
				}

				.el-input {
					.el-input__inner {
						border: 1px solid #dcdfe6;
						border-radius: 4px;

						&:focus {
							border-color: #409eff;
						}
					}
				}

				.el-textarea {
					.el-textarea__inner {
						border: 1px solid #dcdfe6;
						border-radius: 4px;
						resize: none;

						&:focus {
							border-color: #409eff;
						}
					}
				}

				.el-select {
					width: 100%;

					.el-input {
						.el-input__inner {
							border: 1px solid #dcdfe6;
							border-radius: 4px;

							&:focus {
								border-color: #409eff;
							}
						}
					}
				}
			}
		}
	}
}

// 突出显示设置弹窗样式
.highlight-settings-dialog {
	.highlight-settings-content {
		.add-rule-section {
			text-align: center;
			margin-bottom: 20px;
		}

		.highlight-rules-section {
			.rule-item {
				margin-bottom: 20px;
				padding: 16px;
				border: 1px solid #e8e8e8;
				border-radius: 6px;
				background: #fafafa;

				.rule-row {
					.field-section {
						display: flex;
						align-items: center;
						gap: 12px;

						.field-group {
							display: flex;
							align-items: center;
							gap: 12px;

							.field-label {
								font-weight: 500;
								color: #606266;
								min-width: 40px;
								font-size: 14px;
							}

							.el-select {
								width: 200px;
							}
						}

						.color-group {
							display: flex;
							align-items: center;
							gap: 12px;

							.color-label {
								font-weight: 500;
								color: #606266;
								min-width: 60px;
								font-size: 14px;
							}

							.el-color-picker {
								.el-color-picker__trigger {
									width: 40px;
									height: 32px;
									border-radius: 4px;
									border: 1px solid #dcdfe6;
								}
							}
						}

						.action-group {
							display: flex;
							align-items: center;
							gap: 8px;

							.el-button {
								width: 32px;
								height: 32px;
								padding: 0;
								display: flex;
								align-items: center;
								justify-content: center;

								.el-icon {
									font-size: 16px;
								}
							}
						}
					}
				}
			}
		}
	}
}

// 所属分类列默认样式
.category-cell {
	color: #409eff;
}

// 流程配置弹窗样式
.workflow-dialog-content {
	.section-title {
		font-size: 16px;
		font-weight: 600;
		color: #303133;
		margin-bottom: 16px;
		padding-bottom: 8px;
		border-bottom: 1px solid #e4e7ed;
	}

	.workflow-basic-info {
		margin-bottom: 20px;
	}

	.audit-mode-config {
		margin-bottom: 20px;

		.mode-description {
			margin-bottom: 16px;
		}
	}

	.el-form-item {
		margin-bottom: 18px;
	}

	.el-input,
	.el-select,
	.el-textarea {
		width: 100%;
	}

	.workflow-nodes-config {
		margin-top: 20px;

		.nodes-list {
			.node-item {
				border: 1px solid #e4e7ed;
				border-radius: 6px;
				padding: 16px;
				margin-bottom: 16px;
				background-color: #fafafa;

				.node-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 12px;

					.node-number {
						font-weight: 600;
						color: #409eff;
						font-size: 14px;
					}

					.node-actions {
						.el-button {
							margin-left: 8px;
						}
					}
				}
			}
		}

		.empty-nodes {
			text-align: center;
			padding: 40px 0;
		}
	}

	.auditor-config {
		.auditor-header {
			display: flex;
			align-items: center;
			gap: 12px;
			margin-bottom: 12px;

			.auditor-count {
				color: #409eff;
				font-size: 12px;
			}
		}

		.selected-auditors {
			margin-top: 8px;
		}

		.no-auditors {
			padding: 8px 0;
		}
	}
}

// 审核人选择弹窗样式
.auditor-selector-content {
	.auditor-search {
		margin-bottom: 16px;
	}

	.auditor-table {
		margin-bottom: 16px;
	}

	.auditor-pagination {
		display: flex;
		justify-content: center;
	}
}

// 快捷链接设置弹窗样式
.quick-link-dialog {
	.quick-link-content {
		.quick-link-header {
			text-align: center;
			margin-bottom: 24px;
		}

		.quick-link-categories {
			.category-row {
				display: flex;
				gap: 16px;
				margin-bottom: 16px;
				justify-content: center;

				.category-item {
					flex: 1;
					max-width: 150px;
					cursor: pointer;

					.category-card {
						background: #f8f9fa;
						border: 1px solid #e9ecef;
						border-radius: 8px;
						padding: 20px 16px;
						text-align: center;
						transition: all 0.3s ease;
						box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

						&:hover {
							background: #e3f2fd;
							border-color: #2196f3;
							transform: translateY(-2px);
							box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
						}

						.category-title {
							font-size: 14px;
							font-weight: 500;
							color: #333;
						}
					}
				}
			}
		}
	}
}

// 快捷链接设置详情弹窗样式
.quick-link-detail-dialog {
	.quick-link-detail-content {
		padding: 16px 0;

		:deep(.el-form-item) {
			margin-bottom: 20px;
		}

		:deep(.el-form-item__label) {
			font-weight: 500;
			color: #333;
		}
	}
}
</style>

<script setup lang="ts" name="typicalscenario">
import { onActivated, ref, inject, onMounted, reactive } from "vue";
import { Upload } from '@element-plus/icons-vue'
import { useRouter, useRoute } from "vue-router";
import { myOnlineLedgers } from "@/api/LedgerApi";
import { getLedgerTableFieldsApi } from "@/api/taskLinkApi";
import { ElMessage, ElMessageBox } from "element-plus";
import { useUserStore } from "@/stores/useUserStore";
import staticOptions from './staticOptions.js'
import detailOptions from './detailOptions.js'
import tableDetail from "./components/tableDetail.vue";
import * as ExcelJs from 'exceljs'
import { dayjs } from "element-plus";
const axios = inject("#axios") as any;
const route = useRoute();
const router = useRouter();

const ledgerOptions = staticOptions.staticLedgerOptions
const dataItemList = ref();
const importData = ref();
const trueDataItemList = ref([]);
const dataItemVisible = ref(false);
const dataItemRef = ref();
const analysisDataList = ref([]);
const importDialogVisible = ref(false);
const importForm = ref({})
const commitList = ref([])
const importDialog = () => {
  importDialogVisible.value = true
}

async function readExcelFromStream(file: File) {
  const workbook = new ExcelJs.Workbook()

  await workbook.xlsx.load(await file.arrayBuffer())
  // 处理工作簿数据
  const result: any[] = []

  // 遍历所有工作表
  workbook.eachSheet((worksheet, sheetId) => {
    const sheetData: any = {
      sheetName: worksheet.name,
      data: [],
    }
    // 遍历工作表中的所有行
    worksheet.eachRow((row, rowNumber) => {
      const rowData: any[] = []

      // 遍历行中的所有单元格
      row.eachCell((cell, colNumber) => {
        rowData.push({
          column: colNumber,
          value: cell.value,
          type: cell.type,
        })
      })

      sheetData.data.push({
        row: rowNumber,
        values: rowData,
      })
    })

    result.push(sheetData)
  })

  return result
}

const handleBeforeUpload = async (file: any) => {
  commitList.value = []
  // importForm.value.file = file
  importData.value = await readExcelFromStream(file)
  let result = importFormProps.value[2].options?.find((item2: any) => item2.value == importForm.value.dataItem) || {}
  importData.value[0].data.forEach((item: any) => {
    if (item.row != 1) {
      let data = {
        analysisCount: 0,
        dimensionChildType: importForm.value.dimensionChildType,
        dimensionType: importForm.value.dimensionType,
        name: item.values[0].value,
        description: item.values[1].value,
        dataItem: (result?.label + ';') || '',
        saveDataList: [result],
        ledgerId: importForm.value.ledgerId,
      }
      commitList.value.push(data)
    }
  })

  return false
}

const importDialogConfirm = () => {
  commitList.value.forEach((item: any) => {
    let id = Math.random().toString(36).substr(2) + new Date().getTime();
    localStorage.setItem("runwayForm", JSON.stringify(item));
    saveDataByIdToDataList(id, "runwayForm", "analysisTableDataList", 'add')
  })
  ElMessage.success('导入成功')
  importDialogVisible.value = false
}

const initTableData = () => {
  analysisDataList.value = []
  let data = JSON.parse(localStorage.getItem("analysisTableDataList") || '[]')
  pagination.value.total = data.length
  // 根据pagination.size进行分页
  data = data.slice(pagination.value.page * pagination.value.size - pagination.value.size, pagination.value.page * pagination.value.size)
  analysisDataList.value = data
}

const deleteDataByIdToDataList = (id: string, saveDataListStr: string, showMessage: boolean = true) => {
  let saveDataList = JSON.parse(localStorage.getItem(saveDataListStr) || '[]')
  saveDataList = saveDataList.filter((item: any) => item.id !== id)
  localStorage.setItem(saveDataListStr, JSON.stringify(saveDataList))
  if (
    showMessage
  ) {
    ElMessage.success('数据删除成功')
  }
  initTableData()
}

/**
 * 
 * @param id 匹配id
 * @param saveDataStr 保存在本地的数据标识
 * @param saveDataListStr 保存在本地的数据列表标识
 */
const saveDataByIdToDataList = (id: string, saveDataStr: string, saveDataListStr: string, type: string) => {
  // 临时保存的数据
  let data = JSON.parse(localStorage.getItem(saveDataStr) || '{}')
  // 已经储存的列表
  let saveDataList = JSON.parse(localStorage.getItem(saveDataListStr) || '[]')
  let resultList: any[] = []
  if (type == 'edit') {
    saveDataList = saveDataList.map((item: any) => item.id === data.id ? data : item);
    resultList = saveDataList
  } else {
    resultList = saveDataList
    data.id = id
    resultList.push(data)
  }
  localStorage.setItem(saveDataListStr, JSON.stringify(resultList))
  localStorage.removeItem(saveDataStr)
}


const dataItemChange = (val: any) => {
  console.log(val);
  dataItemList.value = []
  dataItemList.value = val
}
const initDialog = () => {

  nextTick(() => {
    dataItemRef.value.clearSelection()
    if (runwayForm.value.ledgerId) {
      console.log(runwayForm.value.ledgerId);
      dataItemData.value = staticOptions[
        (runwayForm.value.ledgerId + 'Options') as keyof typeof staticOptions
      ] || [];
    }
    if (dataItemList.value.length) {
      dataItemList.value.forEach((item: any) => {
        if (item.statisticalDimension) {
          dataItemRef.value.setRowValue(item.id, "statisticalDimension", item.statisticalDimension)
        }
        dataItemRef.value.toggleRowSelection(item, true);
        console.log(dataItemRef.value.getSelectionRows());
      })
    }
  })
}
const dataItemConfirm = () => {
  dataItemVisible.value = false;
  trueDataItemList.value = dataItemList.value;
  runwayForm.value.dataItem = ""
  dataItemList.value.map(item => {
    runwayForm.value.dataItem += item.label + ";"
  })
}
const openLedgerDialog = () => {
  dataItemVisible.value = true;
}

const dataItemColumns = [
  // 编号、数据项、类型、统计维度
  {
    prop: "index",
    label: "编号",
  },
  {
    prop: "label",
    label: "数据项",
  },
  {
    prop: "type",
    label: "类型",
  },
  {
    prop: "statisticalDimension",
    label: "统计维度",
  },
]
const dataItemData = ref()


// 表格中的操作列
const buttons: any = [
  {
    code: "view",
    label: "查看",
    type: "primary",
    icon: "i-majesticons-pencil-alt-line",
    verify: "true",
  },
  {
    code: "edit",
    label: "编辑",
    type: "primary",
    icon: "i-majesticons-pencil-alt-line",
    verify: "true",
  },
  {
    code: "export",
    label: "导出",
    type: "primary",
    icon: "i-majesticons-pencil-alt-line",
    verify: "true",
  },
  // {
  // 	code: 'download',
  // 	label: '下载',
  // 	type: 'info',
  // 	icon: 'i-ic-baseline-download',
  // 	verify: 'true',
  // },
  {
    code: "delete",
    label: "删除",
    type: "danger",
    icon: "i-ic-round-dangerous",
    verify: "true",
  },
];
const dataItemOptions = ref([]);
const dimensionTypeOptions = ref([
  {
    value: 1,
    label: "时间维度",
  },
  {
    value: 2,
    label: "空间维度",
  },
  {
    value: 3,
    label: "业务维度",
  },
]);
const ChildOptions = ref();
const options = ref([
  {
    value: 0,
    label: "表格",
  },
  {
    value: 1,
    label: "柱图",
  },
  {
    value: 2,
    label: "饼图",
  },
  {
    value: 3,
    label: "折线图",
  },
  {
    value: 3,
    label: "环形图",
  },
]);
// 查询条件
const runwayForm: any = ref({
  name: "",
});
// 设置表格高度计算
const tableHeight = ref(0);
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 80;
};
const tableData = ref([]);
const handleLeder = (value: any) => {
  // fieldList(value);
  dataItemData.value = staticOptions[
    (value + 'Options') as keyof typeof staticOptions
  ] || [];
};
// 数据项列表
const fieldList = async (value: any) => {
  const res = await axios?.get(`/api/ledger-service/ledger/${value}/table-field-names`);
  dataItemOptions.value = res.data;
};
// 表格内删除按钮
const onTableClickButton = ({ btn, row, index }: any) => {
  if (btn.code === "view") {
    dialogVisible.value = true;
    // router.push({ path: "/ledger/viewAnalysis", query: { id: row.id } });
  }
  if (btn.code === "delete") {
    ElMessageBox.confirm(`是否要删除 ${row.name} 多维分析?`, "消息确认", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        // axios
        //   ?.delete(`/api/newFeature/multidimensional-analysis/${row.id}`)
        //   .then((res: any) => {
        //     ElMessage.success("删除成功！");
        //     tableRef.value.reload();
        //   });
        deleteDataByIdToDataList(row.id, 'analysisTableDataList')
      })
      .catch(() => { });
  }
  if (btn.code === "edit") {
    title.value = "编辑分析";
    console.log(row);
    handleDimension(row.dimensionType);
    runwayForm.value = JSON.parse(JSON.stringify(row));
    dataItemList.value = runwayForm.value?.saveDataList || []
    fieldList(runwayForm.value.ledgerId);
    showTodoRemind.value = true;
  }
};

const statisticsItemList = ref<any>([])

const TypeClick = (row: any) => {
  runwayForm.value.dimensionChildType = row
  if (runwayForm.value.dimensionType == 3) {
    statisticsItemList.value = []
    statisticsItemList.value = detailOptions[
      (runwayForm.value.dimensionChildType + 'StatisticsOptions') as keyof typeof detailOptions
    ] || [];
  }
}
// const LedgersList = async () => {
//   const res = await myOnlineLedgers();
//   res.data.forEach((item: any) => {
//     item.label = item.name;
//     item.value = item.id;
//   });
//   ledgerOptions.value = res.data;
// };
const allSelectPageData = ref([]);

const rowDetailData = ref({})

const tableDetailVisible = ref<boolean>(false)

// 分析结果弹窗
const dialogVisible = ref();
const handleCount = (row: any) => {
  if (Object.keys(row?.statisticsItem ? row.statisticsItem : {}).length && row.dimensionType == 3) {
    switch (row.statisticsItem.type) {
      case 'table': {
        console.log('这是table===========================');
        rowDetailData.value = row || {}
        tableDetailVisible.value = true
        break;
      }
      case 'form': {
        console.log('这是form=================================');
        break;
      }
    }
  } else {
    dialogVisible.value = true;
  }
};
// 多选表格
const handleSelectionChange = (val: any) => {
  let ids = val.map((item: any) => item.id);
  allSelectPageData.value = ids; // 点击的时候保存数据
};
// 批量删除
const handleSeleCloes = () => {
  if (allSelectPageData.value.length <= 0)
    return ElMessage.warning("请勾选需要删除的多维分析");

  ElMessageBox.confirm(
    `是否要删除 ${allSelectPageData.value.length}条 多维分析?`,
    "消息确认",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      let ids = allSelectPageData.value;
      ids.forEach((id: string) => {
        deleteDataByIdToDataList(id, 'analysisTableDataList', false)
        ElMessage.success('数据删除成功')
      })
      // axios
      //   ?.post(`/api/newFeature/multidimensional-analysis/batch-delete`, ids)
      //   .then((res: any) => {
      //     ElMessage.success("删除成功！");
      //     tableRef.value.clearSelection();
      //     tableRef.value.reload();
      //   });
    })
    .catch(() => { });
};

// 表中的内容
const analysisCountData: any = ref([
  {
    village: "中山路社区",
    name: "李海",
    sex: "男",
    birthday: "1989-03-02",
    id: "500*************19",
    education: "初中",
    address: "重庆市-渝北区",
    phone: "150****4744",
    train: "是",
    recruit: "是",
    status: "已就业",
    statusCount: "10",
    Informant: "张三丰",
    dataSource: "用户新增",
    updataTime: "2024-01-10 12:00:21",
  },
  {
    village: "中山路社区",
    name: "张峰",
    sex: "男",
    birthday: "1991-12-21",
    id: "500*************1X",
    education: "本科",
    address: "重庆市-渝北区",
    phone: "182****7127",
    train: "是",
    recruit: "是",
    status: "已就业",
    statusCount: "10",
    Informant: "张三丰",
    dataSource: "用户编辑",
    updataTime: "2024-01-10 12:00:21",
  },
  {
    village: "中山路社区",
    name: "刘树林",
    sex: "男",
    birthday: "1992-06-04",
    id: "500*************31",
    education: "本科",
    address: "重庆市-渝北区",
    phone: "157****3386",
    train: "是",
    recruit: "是",
    status: "已就业",
    statusCount: "10",
    Informant: "张三丰",
    dataSource: "用户新增",
    updataTime: "2024-01-10 12:00:21",
  },
  {
    village: "中山路社区",
    name: "刘柯",
    sex: "男",
    birthday: "1994-04-12",
    id: "500*************36",
    education: "初中",
    address: "重庆市-巴南区",
    phone: "156****2903",
    train: "是",
    recruit: "是",
    status: "已就业",
    statusCount: "10",
    Informant: "张三丰",
    dataSource: "用户编辑",
    updataTime: "2024-01-10 12:00:21",
  },
  {
    village: "中山路社区",
    name: "李俊峰",
    sex: "男",
    birthday: "1993-03-24",
    id: "500*************11",
    education: "专科",
    address: "重庆市-巴南区",
    phone: "138****2006",
    train: "是",
    recruit: "是",
    status: "待就业",
    statusCount: "10",
    Informant: "张三丰",
    dataSource: "用户新增",
    updataTime: "2024-01-10 12:00:21",
  },
  {
    village: "中山路社区",
    name: "张海燕",
    sex: "女",
    birthday: "1997-11-23",
    id: "500*************17",
    education: "本科",
    address: "重庆市-巴南区",
    phone: "135****7857",
    train: "是",
    recruit: "是",
    status: "待就业",
    statusCount: "10",
    Informant: "张三丰",
    dataSource: "用户编辑",
    updataTime: "2024-01-10 12:00:21",
  },
  {
    village: "中山路社区",
    name: "刘艳楠",
    sex: "女",
    birthday: "1986-08-12",
    id: "500*************30",
    education: "初中",
    address: "重庆市-九龙坡区",
    phone: "158****9233",
    train: "是",
    recruit: "是",
    status: "待就业",
    statusCount: "10",
    Informant: "张三丰",
    dataSource: "用户新增",
    updataTime: "2024-01-10 12:00:21",
  },
  {
    village: "中山路社区",
    name: "谢苏飞",
    sex: "男",
    birthday: "1997-07-03",
    id: "500*************3X",
    education: "高中",
    address: "重庆市-渝北区",
    phone: "139****5131",
    train: "否",
    recruit: "是",
    status: "待就业",
    statusCount: "10",
    Informant: "张三丰",
    dataSource: "用户编辑",
    updataTime: "2024-01-10 12:00:21",
  },
  {
    village: "中山路社区",
    name: "张苗",
    sex: "男",
    birthday: "1997-04-18",
    id: "500*************18",
    education: "高中",
    address: "重庆市-九龙坡区",
    phone: "159****6566",
    train: "是",
    recruit: "是",
    status: "待就业",
    statusCount: "10",
    Informant: "张三丰",
    dataSource: "用户新增",
    updataTime: "2024-01-10 12:00:21",
  },
  {
    village: "中山路社区",
    name: "方彬彬",
    sex: "男",
    birthday: "1990-10-03",
    id: "500*************17",
    education: "本科",
    address: "重庆市-大渡口区",
    phone: "173****3156",
    train: "是",
    recruit: "是",
    status: "待就业",
    statusCount: "10",
    Informant: "张三丰",
    dataSource: "用户编辑",
    updataTime: "2024-01-10 12:00:21",
  },
]);
const analysisCountColumns: any = ref([
  {
    prop: "village",
    label: "所属村社",
  },
  {
    prop: "name",
    label: "姓名",
  },
  {
    prop: "sex",
    label: "性别",
  },
  {
    prop: "birthday",
    label: "出生日期",
  },
  {
    prop: "id",
    label: "身份证号",
  },
  {
    prop: "education",
    label: "学历",
  },
  {
    prop: "address",
    label: "现在家庭住址",
  },
  {
    prop: "phone",
    label: "联系电话",
  },
  {
    prop: "train",
    label: "是否提供免费就业培训",
    width: 180,
  },
  {
    prop: "recruit",
    label: "是否提供招聘信息",
    width: 180,
  },
  {
    prop: "status",
    label: "就业状态",
  },
  {
    prop: "statusCount",
    label: "历史就业次数",
  },
  {
    prop: "Informant",
    label: "填报人",
  },
  {
    prop: "dataSource",
    label: "数据来源",
  },
  {
    prop: "updataTime",
    label: "更新时间",
  },
]);
// 表头
const colData: any = ref([
  {
    prop: "name",
    label: "分析名称",
  },
  {
    prop: "description",
    label: "分析描述",
  },
  {
    prop: "dimensionType",
    label: "分析维度",
  },
  {
    prop: "ledgerName",
    label: "分析业务表",
  },
  {
    prop: "analysisCount",
    label: "分析结果",
  },
  {
    prop: "lastModificationTime",
    label: "更新时间",
  },
]);
const rules = reactive({
  name: [{ required: true, message: "请输入分析名称", trigger: "blur" }],
  dimensionType: [{ required: true, message: "请选择分析维度", trigger: "change" }],
  ledgerId: [{ required: true, message: "请选择分析业务表", trigger: "change" }],
  dataItem: [{ required: true, message: "请选择分析数据项", trigger: "change" }],
  dimensionChildType: [{ required: true, message: "请选择维度子级", trigger: "change" }],
  statisticsItem: [{ required: true, message: "请选择统计分析项", trigger: "change" }],
});
const formProps = ref([{ label: "多维分析名称", prop: "Name", type: "text" }]);
const importFormProps = ref([
  {
    label: "导入维度", prop: "dimensionType", type: "select", options: dimensionTypeOptions.value, attrs: {
      onChange: (val: number | string) => {
        switch (val) {
          case 1: {
            importFormProps.value[3].options = staticOptions.timeDimensionOptions
            importForm.value.dimensionChildType = null
            break;
          }
          case 2:
            {
              importFormProps.value[3].options = staticOptions.spaceDimensionOptions
              importForm.value.dimensionChildType = null
              break;
            }
          case 3: {
            importFormProps.value[3].options = staticOptions.businessDimensionOptions
            importForm.value.dimensionChildType = null
            break;
          }
        }
      }
    }
  },
  {
    label: "分析业务表", prop: "ledgerId", type: "select", options: ledgerOptions, attrs: {
      onChange: (val: string) => {
        importFormProps.value[2].options = staticOptions[
          (importForm.value?.ledgerId + 'Options') as keyof typeof staticOptions
        ] || [];
      }
    }
  },
  { label: "分析数据项", prop: "dataItem", type: "select", options: [] },
  { label: "维度子级", prop: "dimensionChildType", type: "select", options: [] },
  { label: '文件上传', prop: "file", type: 'text' }
]);
const form = ref({});
//表格与分页关联
const reqParams = reactive({
  Name: null,
  skipCount: 0,
  maxResultCount: 10,
});
const formSearch = ref({ Name: null });
const onSearch = () => {
  pagination.value.page = 1;
  // reqParams.skipCount = 0;
  // reqParams.maxResultCount = pagination.value.size;
  // reqParams.Name = formSearch.value.Name;
  initTableData()
};
// 分页相关参数
const pagination = ref({
  total: 0,
  page: 1,
  size: 10,
});
const showTodoRemind = ref();
const title = ref();
const hanleAddclick = () => {
  title.value = "新增分析";
  dataItemList.value = []
  showTodoRemind.value = true;
};
const ruleFormRef = ref();
const handleClose = () => {
  showTodoRemind.value = false;
  runwayForm.value = {};
  dataItemOptions.value = [];
};
const handleDimension = (row: any) => {
  runwayForm.value.dimensionChildType = "";
  if (row == 1) {
    ChildOptions.value = [
      {
        value: 1,
        label: "按日",
      },
      {
        value: 2,
        label: "按周",
      },
      {
        value: 3,
        label: "按月",
      },
      {
        value: 4,
        label: "按季度",
      },
      {
        value: 5,
        label: "按年度",
      },
    ];
  }
  if (row == 2) {
    ChildOptions.value = [
      {
        value: 6,
        label: "按市级",
      },
      {
        value: 7,
        label: "按区级",
      },
      {
        value: 8,
        label: "按街镇",
      },

      {
        value: 9,
        label: "按村社",
      },
    ];
  }
  if (row == 3) {
    ChildOptions.value = [
      { label: '残联业务', value: 'clyw' },
      { label: '妇联业务', value: 'flyw' },
      { label: '十字会业务', value: 'shxhyw' },
      { label: '林业局业务', value: 'lyjyw' },
      { label: '民政局业务', value: 'mzjyw' },
      { label: '农业农村委业务', value: 'nyw' },
      { label: '气象局业务', value: 'qxjyw' },
      { label: '侨联业务', value: 'qljyw' },
      { label: '人力资源和社会保障局业务', value: 'rlzyywr' },
      { label: '市场监管局业务', value: 'scjgyw' },
      { label: '水利局业务', value: 'sljyw' },
      { label: '司法局业务', value: 'sfjyw' },
      { label: '体育局业务', value: 'tyjyw' },
      { label: '社会工作部', value: 'shgzb' },
      { label: '统战部业务', value: 'tzbwy' },
      { label: '政法委', value: 'zfw' },
      { label: '组织部业务', value: 'zzbwy' },
      { label: '药监局', value: 'yjj' },
      { label: '住房城乡建设委业务', value: 'zfcjjswy' },
      { label: '总工会', value: 'zgh' },
      { label: '团委业务', value: 'twnyw' },
      { label: '应急管理局业务', value: 'yjjgljyw' }
    ];
  }
};
const submit = async (formEl: any) => {
  let data = runwayForm.value;
  console.log(data);
  if (!formEl) return;
  await formEl.validate((valid: any, fields: any) => {
    if (valid) {
      data.analysisCount = 0;
      data.saveDataList = dataItemList.value
      localStorage.setItem("runwayForm", JSON.stringify(data));
      console.log("data=====================================================", data);
      if (title.value == "编辑分析") {
        // 年月日 时分秒格式
        data.lastModificationTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
        localStorage.setItem("runwayForm", JSON.stringify(data));
        saveDataByIdToDataList(runwayForm.value.id, "runwayForm", "analysisTableDataList", 'edit')
        // let id = runwayForm.value.id;
        // axios
        //   ?.put(`/api/newFeature/multidimensional-analysis/${id}`, data)
        //   .then((res: any) => {
        //     showTodoRemind.value = false;
        //     tableRef.value.reload();
        //   });
      }
      if (title.value == "新增分析") {
        // id 随机数+时间戳
        let id = Math.random().toString(36).substr(2) + new Date().getTime();
        saveDataByIdToDataList(id, "runwayForm", "analysisTableDataList", 'add')
        // axios
        //   ?.post("/api/newFeature/multidimensional-analysis", data)
        //   .then((res: any) => {
        //     showTodoRemind.value = false;
        //     tableRef.value.reload();
        //   });
      }
      showTodoRemind.value = false;
      initTableData()
    } else {
    }
  });
};
const getDimension = (value: any) => {
  switch (value) {
    case 1:
      return "时间维度";
    case 2:
      return "空间维度";
    case 3:
      return "业务维度";
  }
};
// 高级查询
const seniorList = () => {
  pagination.value.page = 1;
  pagination.value.size = 10;
};
// 清空
const empty = () => {
  // manyName.value = "";
};
// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type == 'page') {
    pagination.value.page = val
    initTableData()
  } else {
    pagination.value.size = val
    initTableData()
  }
};
const tableRef = ref();
onMounted(() => {
  // LedgersList();
  initTableData()
});
// onActivated(() => {
//   console.log(222222);
// });
</script>
<template>
  <div class="dataAnalysis">
    <Block :enable-fixed-height="true" :enable-expand-content="true" :enableBackButton="false" :enable-close-button="false" :title="'多维分析'" @heightChanged="onBlockHeightChanged">
      <template #expand>
        <div class="expandBox">
          <div class="search">
            <Form :props="formProps" v-model="formSearch" :column-count="2" :label-width="96" :enable-reset="false" confirm-text="查询" button-vertical="flowing" @submit="onSearch">
            </Form>
          </div>
          <div class="buttonBox">
            <el-button type="primary" @click="importDialog" :icon="Upload">导入</el-button>
          </div>
        </div>

      </template>
      <template #topRight>
        <div class="df aic">
          <el-button type="danger" size="small" class="mg-left-10" @click="handleSeleCloes">批量删除</el-button>
          <el-button type="primary" size="small" class="mg-left-10" @click="hanleAddclick">新建分析</el-button>
        </div>
      </template>
      <!-- url="/api/newFeature/multidimensional-analysis" -->
      <TableV2 ref="tableRef" v-model="tableData" :defaultTableData="analysisDataList" :height="tableHeight" :columns="colData" :headers="{ Urlkey: 'ledger' }" :enableToolbar="false" :enable-create="false" :enable-edit="false" :enable-delete="false" :enableSelection="true" :enableIndex="true" :req-params="reqParams" :buttons="buttons" @clickButton="onTableClickButton" @selection-change="handleSelectionChange" @completed="
        () => {
          pagination.total = tableRef.getTotal();
        }
      ">
        <template #dimensionType="{ row }">
          <div>
            <span>{{ getDimension(row.dimensionType) }}</span>
          </div>
        </template>
        <template #analysisCount="{ row }">
          <div @click="handleCount(row)" class="analysisCount">
            {{ row.analysisCount }}
          </div>
        </template>
      </TableV2>
      <Pagination :total="pagination.total" :page-size="pagination.size" :current-page="pagination.page" @current-change="onPaginationChange($event, 'page')" @size-change="onPaginationChange($event, 'size')">
      </Pagination>
      <Dialog v-model="showTodoRemind" :title="title" width="600" @close="handleClose" :confirmText="'确定'" :destroy-on-close="true" @clickConfirm="submit(ruleFormRef)" @clickCancel="handleClose">
        <el-form ref="ruleFormRef" :model="runwayForm" :rules="rules" label-width="auto">
          <el-form-item label="分析名称:" prop="name">
            <el-input placeholder="请输入分析名称" v-model="runwayForm.name" style="width: 100%; margin-right: 10px"></el-input>
          </el-form-item>
          <el-form-item label="分析描述:" prop="description">
            <el-input style="width: 100%; margin-right: 10px" placeholder="请输入分析描述" v-model="runwayForm.description" type="textarea" />
          </el-form-item>
          <el-form-item label="分析维度" prop="dimensionType">
            <el-select @change="handleDimension" v-model="runwayForm.dimensionType" placeholder="请选择分析维度" style="width: 100%; margin-right: 10px">
              <el-option v-for="item in dimensionTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <div v-if="runwayForm.dimensionType == 1">
            <el-form-item label="选择日期">
              <el-date-picker v-model="runwayForm.timeRange" type="datetimerange" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
            </el-form-item>
            <el-form-item label="选择周期">
              <div style="display: flex;">
                <el-input v-model="runwayForm.dateCycle"></el-input>&nbsp;&nbsp;天
              </div>
            </el-form-item>
          </div>
          <el-form-item label="维度子级" prop="dimensionChildType">
            <el-select @change="TypeClick" v-model="runwayForm.dimensionChildType" placeholder="请选择维度子级" style="width: 100%; margin-right: 10px">
              <el-option v-for="item in ChildOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="runwayForm.dimensionType != 3" label="分析业务表" prop="ledgerId">
            <el-select v-model="runwayForm.ledgerId" placeholder="请选择分析业务表" style="width: 100%; margin-right: 10px" @change="handleLeder">
              <el-option v-for="item in ledgerOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="runwayForm.dimensionType != 3" label="分析数据项" prop="dataItem">
            <el-input v-model="runwayForm.dataItem" placeholder="请选择分析数据项" readonly style="width: 100%; margin-right: 10px" @click="openLedgerDialog" />
            <!-- <el-select v-model="runwayForm.dataItem" placeholder="请选择分析数据项" style="width: 100%; margin-right: 10px">
              <el-option v-for="item in dataItemOptions" :key="item.id" :label="item.name" :value="item.id" />
            </el-select> -->
          </el-form-item>
          <el-form-item v-if="runwayForm.dimensionType != 3" label="分组数据项">
            <el-select v-model="runwayForm.dataItemGroupBy" placeholder="请选择分析数据项" style="width: 100%; margin-right: 10px">
              <el-option v-for="item in trueDataItemList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="runwayForm.dimensionType == 3" prop="statisticsItem" label="统计分析项">
            <el-select v-model="runwayForm.statisticsItem" placeholder="请选择统计分析项" style="width: 100%; margin-right: 10px">
              <el-option v-for="item in statisticsItemList" value-key="label" :key="item.value" :label="item.label" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item label="展示类型">
            <el-select v-model="runwayForm.interval" placeholder="请选择展示类型" style="width: 100%; margin-right: 10px">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
      </Dialog>
      <Dialog v-model="dialogVisible" title="分析结果" width="1800" :enable-close="false" :enable-confirm="false">
        <TableV2 :columns="analysisCountColumns" :defaultTableData="analysisCountData" :height="500" :headers="{ Urlkey: 'ledger' }" :enableToolbar="false" :enable-create="false" :enable-edit="false" :enable-delete="false" :enableSelection="false" :enableIndex="true" :req-params="reqParams">
          <template #dimensionType="{ row }">
            <div>
              <span>{{ getDimension(row.dimensionType) }}</span>
            </div>
          </template>
          <template #analysisCount="{ row }">
            <div @click="handleCount(row)" class="analysisCount">
              {{ row.analysisCount }}
            </div>
          </template>
        </TableV2>
        <Pagination :total="10" :page-size="10" :current-page="1" @current-change="onPaginationChange($event, 'page')" @size-change="onPaginationChange($event, 'size')">
        </Pagination>
      </Dialog>
      <Dialog v-model="dataItemVisible" @open="initDialog" @clickConfirm="dataItemConfirm" @close="dataItemVisible = false" title="分析数据项选择">
        <TableV2 ref="dataItemRef" :enable-selection="true" @selectionChange="dataItemChange" :columns="dataItemColumns" :defaultTableData="dataItemData">
          <template #statisticalDimension="{ row }">
            <el-select v-model="row.statisticalDimension">
              <!-- 最大值、最小值、求和、同比、环比、增长率、排名分析、评估值 -->
              <el-option value="最大值">最大值</el-option>
              <el-option value="最小值">最小值</el-option>
              <el-option value="求和">求和</el-option>
              <el-option value="同比">同比</el-option>
              <el-option value="环比">环比</el-option>
              <el-option value="增长率">增长率</el-option>
              <el-option value="排名分析">排名分析</el-option>
              <el-option value="评估值">评估值</el-option>
            </el-select>
          </template>
        </TableV2>
      </Dialog>
      <Dialog v-model="importDialogVisible" @clickConfirm="importDialogConfirm" @close="importDialogVisible = false" title="导入">
        <Form v-model="importForm" :props="importFormProps" :enable-button="false">
          <template #form-file>
            <el-upload action="#" :before-upload="handleBeforeUpload" :show-file-list="false">
              <el-button type="primary">上传附件</el-button>
            </el-upload>
          </template>
        </Form>
      </Dialog>
      <tableDetail v-model="tableDetailVisible" title="分析结果" :rowData="rowDetailData" width="1800" height="500" :enable-close="false" :enable-confirm="false"></tableDetail>
    </Block>
  </div>
</template>
<route>
	{
		meta:{
			title:'多维分析'
		}
	}
</route>
<style scoped lang="scss">
::v-deep(.analysisCount) {
  cursor: pointer;
  text-decoration: underline;
  color: #1764ce;
}

.expandBox {
  position: relative;
}

.buttonBox {
  position: absolute;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
}

.search-box {
  align-items: start;
  display: flex;
  padding: 10px 15px 10px 10px;
}
</style>

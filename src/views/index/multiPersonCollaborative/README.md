# 多人协同功能模块

## 功能概述

多人协同功能模块是一个完整的团队协作管理系统，提供项目管理、团队协作、实时通信等核心功能。

## 目录结构

```
multiPersonCollaborative/
├── index.vue                          # 主页面组件
├── components/                        # 子组件目录
│   ├── StatusBar.vue                  # 顶部状态栏
│   ├── NavigationMenu.vue             # 左侧导航菜单
│   ├── ProjectManagement.vue          # 项目管理页面
│   ├── CreateProjectDialog.vue        # 创建项目对话框
│   ├── TeamManagement.vue             # 团队管理（占位）
│   ├── TemplateManagement.vue         # 模板管理（占位）
│   ├── CollaborativeWorkflow.vue      # 协同工作流程（占位）
│   ├── RegionManagement.vue           # 区域管理（占位）
│   ├── InstantMessaging.vue           # 即时通信（占位）
│   ├── BackupCycle.vue                # 备份周期（占位）
│   ├── OperationHistory.vue           # 操作历史记录（占位）
│   └── SharedAccessRecord.vue         # 共享访问记录（占位）
└── README.md                          # 说明文档
```

## 主要功能

### 1. 顶部状态栏 (StatusBar.vue)
- 显示实时在线用户状态
- 用户头像和状态指示器
- 在线人数统计
- 响应式设计，适配不同屏幕尺寸

### 2. 左侧导航菜单 (NavigationMenu.vue)
- 固定的9个功能模块导航
- 美观的渐变样式设计
- 支持菜单项切换
- 响应式布局

### 3. 项目管理 (ProjectManagement.vue)
- 项目列表展示（使用BaseTableComp组件）
- 项目搜索功能（项目名称、业务类型、分类）
- 创建新项目
- 项目状态管理
- 项目操作（查看、编辑、删除、更多）
- 分页功能
- 本地数据存储

### 4. 创建项目对话框 (CreateProjectDialog.vue)
- 完整的项目信息表单
- 表单验证
- 业务类型选择
- 通知设置
- 数据本地存储

## 技术特性

### 响应式设计
- 支持桌面端、平板端、移动端
- 断点设计：1200px、768px、480px
- 移动端优化布局

### 数据存储
- 使用localStorage进行本地数据持久化
- 项目数据自动保存和加载
- 支持数据导入导出

### 组件化架构
- 高度模块化的组件设计
- 组件间通过事件通信
- 易于扩展和维护

### 样式设计
- 使用Element Plus组件库
- 自定义渐变色彩方案
- 统一的设计语言
- 美观的视觉效果

## 使用方法

### 1. 基本使用
```vue
<template>
  <div>
    <!-- 直接使用主组件 -->
    <MultiPersonCollaborative />
  </div>
</template>

<script setup>
import MultiPersonCollaborative from '@/views/index/multiPersonCollaborative/index.vue'
</script>
```

### 2. 路由配置
```javascript
{
  path: '/collaborative',
  name: 'MultiPersonCollaborative',
  component: () => import('@/views/index/multiPersonCollaborative/index.vue')
}
```

### 3. 数据格式

#### 项目数据结构
```typescript
interface Project {
  id: string              // 项目ID
  sequence: number         // 序号
  projectName: string      // 项目名称
  businessType: string     // 关联业务类
  department: string       // 所属分类
  status: boolean          // 状态
  creator: string          // 创建人
  createTime: string       // 创建时间
}
```

#### 用户数据结构
```typescript
interface User {
  id: string               // 用户ID
  name: string             // 用户名称
  status: 'online' | 'busy' | 'away'  // 在线状态
  avatar?: string          // 头像URL
  isCurrent: boolean       // 是否当前用户
}
```

## 扩展开发

### 添加新的功能模块
1. 在`components`目录下创建新的Vue组件
2. 在`NavigationMenu.vue`中添加菜单项
3. 在主页面`index.vue`中注册组件
4. 更新组件映射关系

### 自定义样式
- 修改各组件的`<style>`部分
- 使用CSS变量进行主题定制
- 遵循Element Plus的设计规范

### 数据接口集成
- 替换本地存储为API调用
- 添加loading状态管理
- 实现错误处理机制

## 注意事项

1. **浏览器兼容性**: 支持现代浏览器，IE11+
2. **数据安全**: 本地存储数据，注意隐私保护
3. **性能优化**: 大量数据时建议实现虚拟滚动
4. **移动端**: 在小屏幕设备上测试交互体验

## 更新日志

### v1.0.0 (2025-07-30)
- 初始版本发布
- 完成基础架构和项目管理功能
- 实现响应式设计
- 添加本地数据存储

## 开发团队

- 前端开发：基于Vue 3 + TypeScript + Element Plus
- UI设计：现代化渐变色彩方案
- 交互设计：用户友好的操作体验

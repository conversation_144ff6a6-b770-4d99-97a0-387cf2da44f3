<template>
  <div class="multi-person-collaborative">
    <!-- 顶部状态栏 -->
    <StatusBar />

    <!-- 主体内容区域 -->
    <div class="main-content">
      <!-- 左侧导航菜单 -->
      <div class="sidebar">
        <NavigationMenu @menu-change="handleMenuChange" />
      </div>

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <component :is="currentComponent" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import StatusBar from './components/StatusBar.vue'
import NavigationMenu from './components/NavigationMenu.vue'
import ProjectManagement from './components/ProjectManagement.vue'
import TeamManagement from './components/TeamManagement.vue'
import TemplateManagement from './components/TemplateManagement.vue'
import CollaborativeWorkflow from './components/CollaborativeWorkflow.vue'
import RegionManagement from './components/RegionManagement.vue'
import InstantMessaging from './components/InstantMessaging.vue'
import BackupCycle from './components/BackupCycle.vue'
import OperationHistory from './components/OperationHistory.vue'
import SharedAccessRecord from './components/SharedAccessRecord.vue'

// 当前显示的组件
const currentComponent = ref('ProjectManagement')

// 组件映射
const componentMap = {
  'project-management': ProjectManagement,
  'team-management': TeamManagement,
  'template-management': TemplateManagement,
  'collaborative-workflow': CollaborativeWorkflow,
  'region-management': RegionManagement,
  'instant-messaging': InstantMessaging,
  'backup-cycle': BackupCycle,
  'operation-history': OperationHistory,
  'shared-access-record': SharedAccessRecord
}

// 处理菜单切换
const handleMenuChange = (menuKey: string) => {
  const component = componentMap[menuKey as keyof typeof componentMap]
  if (component) {
    currentComponent.value = component
  }
}

onMounted(() => {
  // 默认显示项目管理页面
  currentComponent.value = ProjectManagement
})
</script>

<style scoped lang="scss">
.multi-person-collaborative {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;

  .main-content {
    flex: 1;
    display: flex;
    gap: 16px;
    padding: 16px;
    overflow: hidden;

    .sidebar {
      width: 280px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
      overflow: hidden;
      border: 1px solid #e4e7ed;
    }

    .content-area {
      flex: 1;
      min-width: 0;
      overflow-y: auto;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .multi-person-collaborative {
    .main-content {
      .sidebar {
        width: 260px;
      }
    }
  }
}

@media (max-width: 768px) {
  .multi-person-collaborative {
    .main-content {
      .sidebar {
        width: 200px;
      }

      .content-area {
        padding: 15px;
      }
    }
  }
}

@media (max-width: 480px) {
  .multi-person-collaborative {
    .main-content {
      flex-direction: column;

      .sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid #e4e7ed;
      }

      .content-area {
        padding: 10px;
        height: calc(100vh - 120px);
      }
    }
  }
}
</style>
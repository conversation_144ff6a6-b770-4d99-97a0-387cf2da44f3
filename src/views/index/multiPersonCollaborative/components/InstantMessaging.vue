<template>
  <div class="instant-messaging">
    <!-- 即时通讯开关 -->
    <div class="messaging-header">
      <span class="header-title">即时通讯</span>
      <el-switch
        v-model="messagingEnabled"
        size="default"
        @change="handleMessagingToggle"
      />
    </div>

    <!-- 主要内容区域 -->
    <div class="messaging-content" v-if="messagingEnabled">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-container">
        <el-icon class="is-loading" size="32">
          <Loading />
        </el-icon>
        <p>正在加载聊天数据...</p>
      </div>

      <!-- 左侧联系人列表 -->
      <div v-else class="contacts-panel">
        <div class="contacts-header">
          <span class="contacts-title">我的通信录</span>
        </div>

        <div class="contacts-list">
          <div
            v-for="contact in contacts"
            :key="contact.id"
            class="contact-item"
            :class="{ active: selectedContactId === contact.id }"
            @click="selectContact(contact)"
          >
            <div class="contact-avatar">
              <el-avatar :size="40" :src="contact.avatar">
                {{ contact.name.charAt(0) }}
              </el-avatar>
              <span v-if="contact.unreadCount > 0" class="unread-badge">{{ contact.unreadCount }}</span>
            </div>
            <div class="contact-info">
              <div class="contact-name">{{ contact.name }}</div>
              <div class="contact-status">{{ contact.lastMessage || '暂无消息' }}</div>
            </div>
            <div class="contact-time">{{ contact.lastTime }}</div>
          </div>
        </div>

        <!-- 群组通知 -->
        <div class="group-notification">
          <div class="notification-item">
            <div class="notification-icon">📢</div>
            <div class="notification-text">请各部门及时提交月度工作总结</div>
          </div>
        </div>
      </div>

      <!-- 右侧聊天区域 -->
      <div class="chat-panel">
        <div v-if="selectedContact" class="chat-container">
          <!-- 聊天头部 -->
          <div class="chat-header">
            <el-avatar :size="32" :src="selectedContact.avatar">
              {{ selectedContact.name.charAt(0) }}
            </el-avatar>
            <span class="chat-title">{{ selectedContact.name }}</span>
          </div>

          <!-- 消息列表 -->
          <div class="messages-container" ref="messagesContainer">
            <div
              v-for="message in currentMessages"
              :key="message.id"
              class="message-item"
              :class="{ 'own-message': message.isOwn }"
            >
              <div class="message-avatar" v-if="!message.isOwn">
                <el-avatar :size="32" :src="selectedContact.avatar">
                  {{ selectedContact.name.charAt(0) }}
                </el-avatar>
              </div>
              <div class="message-content">
                <div class="message-bubble">
                  {{ message.content }}
                </div>
                <div class="message-time">{{ message.time }}</div>
              </div>
              <div class="message-avatar" v-if="message.isOwn">
                <el-avatar :size="32">我</el-avatar>
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="input-area">
            <el-input
              v-model="newMessage"
              placeholder="请输入内容"
              class="message-input"
              @keyup.enter="sendMessage"
            />
            <el-button type="primary" @click="sendMessage">发送</el-button>
          </div>
        </div>

        <!-- 未选择联系人时的提示 -->
        <div v-else class="no-contact-selected">
          <div class="empty-state">
            <el-icon size="64" color="#ccc">
              <ChatDotRound />
            </el-icon>
            <p>请选择一个联系人开始聊天</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 关闭状态提示 -->
    <div v-else class="messaging-disabled">
      <div class="disabled-state">
        <el-icon size="64" color="#ccc">
          <ChatDotRound />
        </el-icon>
        <p>即时通讯已关闭</p>
        <p class="disabled-tip">开启即时通讯功能以便与团队成员实时沟通</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { ChatDotRound, Loading } from '@element-plus/icons-vue'
import { MessagingDBManager } from '../../../../utils/indexedDB'

// 联系人接口定义
interface Contact {
  id: string
  name: string
  avatar?: string
  lastMessage?: string
  lastTime: string
  unreadCount: number
  isOnline: boolean
}

// 消息接口定义
interface Message {
  id: string
  contactId: string
  content: string
  time: string
  isOwn: boolean
}

// IndexedDB 管理器
const dbManager = new MessagingDBManager()

// 响应式数据
const messagingEnabled = ref(true)
const selectedContactId = ref('')
const newMessage = ref('')
const messagesContainer = ref()
const isLoading = ref(true)

// 联系人列表和消息数据
const contacts = ref<Contact[]>([])
const messages = ref<Message[]>([])

// 从数据库加载数据
const loadDataFromDB = async () => {
  try {
    isLoading.value = true
    console.log('开始加载聊天数据...')

    // 检查浏览器是否支持IndexedDB
    if (!window.indexedDB) {
      throw new Error('浏览器不支持IndexedDB')
    }

    console.log('初始化数据库...')
    // 初始化数据库
    await dbManager.init()
    console.log('数据库初始化成功')

    await dbManager.initDefaultData()
    console.log('默认数据初始化成功')

    // 加载联系人和消息
    console.log('加载联系人和消息...')
    const [loadedContacts, loadedMessages] = await Promise.all([
      dbManager.getContacts(),
      dbManager.getMessages()
    ])

    console.log('加载到的联系人:', loadedContacts)
    console.log('加载到的消息:', loadedMessages)

    contacts.value = loadedContacts
    messages.value = loadedMessages

    // 加载即时通讯开关状态
    const savedEnabled = await dbManager.getSetting('messagingEnabled')
    if (savedEnabled !== null) {
      messagingEnabled.value = savedEnabled
    }

    // 加载选中的联系人
    const savedContactId = await dbManager.getSetting('selectedContactId')
    if (savedContactId && contacts.value.find(c => c.id === savedContactId)) {
      selectedContactId.value = savedContactId
    } else if (contacts.value.length > 0) {
      selectedContactId.value = contacts.value[0].id
    }

    console.log('聊天数据加载完成')

  } catch (error) {
    console.error('Failed to load data from IndexedDB:', error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    ElMessage.error(`加载聊天数据失败: ${errorMessage}`)

    // 如果数据库加载失败，使用默认数据
    console.log('使用默认数据...')
    initDefaultData()
  } finally {
    isLoading.value = false
  }
}

// 初始化默认数据（作为备用方案）
const initDefaultData = () => {
  contacts.value = [
    {
      id: 'contact_1',
      name: '杨绍东',
      lastMessage: '我已经提交了业务数据表',
      lastTime: '10:23',
      unreadCount: 1,
      isOnline: true
    },
    {
      id: 'contact_2',
      name: '王明华',
      lastMessage: '明天的会议准备好了吗',
      lastTime: '10:23',
      unreadCount: 1,
      isOnline: true
    },
    {
      id: 'contact_3',
      name: '李华',
      lastMessage: '我已经提交了业务数据表',
      lastTime: '10:23',
      unreadCount: 0,
      isOnline: false
    },
    {
      id: 'contact_4',
      name: '张建国',
      lastMessage: '项目进度如何？',
      lastTime: '09:45',
      unreadCount: 0,
      isOnline: true
    },
    {
      id: 'contact_5',
      name: '陈美玲',
      lastMessage: '测试报告已完成',
      lastTime: '昨天',
      unreadCount: 2,
      isOnline: false
    }
  ]

  const today = new Date()
  const todayStr = formatLocalTime(today).substring(0, 10) // 获取今天的日期部分

  messages.value = [
    {
      id: 'msg_1',
      contactId: 'contact_1',
      content: '我已经提交了业务数据表',
      time: `${todayStr} 10:30:35`,
      isOwn: false
    },
    {
      id: 'msg_2',
      contactId: 'contact_1',
      content: '好的，我看一下',
      time: `${todayStr} 10:31:20`,
      isOwn: true
    },
    {
      id: 'msg_3',
      contactId: 'contact_2',
      content: '明天的会议准备好了吗',
      time: `${todayStr} 10:20:15`,
      isOwn: false
    },
    {
      id: 'msg_4',
      contactId: 'contact_2',
      content: '已经准备好了，会议材料都整理完毕',
      time: `${todayStr} 10:22:30`,
      isOwn: true
    },
    {
      id: 'msg_5',
      contactId: 'contact_4',
      content: '项目进度如何？',
      time: `${todayStr} 09:45:10`,
      isOwn: false
    },
    {
      id: 'msg_6',
      contactId: 'contact_4',
      content: '目前进度正常，预计下周完成',
      time: `${todayStr} 09:47:25`,
      isOwn: true
    }
  ]

  if (contacts.value.length > 0) {
    selectedContactId.value = contacts.value[0].id
  }
}

// 保存数据到数据库
const saveDataToDB = async () => {
  try {
    await Promise.all([
      dbManager.saveContacts(contacts.value),
      dbManager.saveMessages(messages.value),
      dbManager.saveSetting('messagingEnabled', messagingEnabled.value),
      dbManager.saveSetting('selectedContactId', selectedContactId.value)
    ])
  } catch (error) {
    console.error('Failed to save data to IndexedDB:', error)
  }
}

// 计算属性
const selectedContact = computed(() => {
  return contacts.value.find(contact => contact.id === selectedContactId.value)
})

const currentMessages = computed(() => {
  if (!selectedContactId.value) return []

  // 过滤当前联系人的消息并按时间排序
  const filteredMessages = messages.value.filter(message => message.contactId === selectedContactId.value)

  // 按时间排序（从早到晚）
  return filteredMessages.sort((a, b) => {
    const timeA = new Date(a.time).getTime()
    const timeB = new Date(b.time).getTime()
    return timeA - timeB
  })
})

// 工具函数
const formatLocalTime = (date: Date): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 方法
const handleMessagingToggle = async (value: string | number | boolean) => {
  const boolValue = Boolean(value)
  messagingEnabled.value = boolValue

  if (boolValue) {
    ElMessage.success('即时通讯已开启')
  } else {
    ElMessage.info('即时通讯已关闭')
    selectedContactId.value = ''
  }

  // 保存设置到数据库
  await dbManager.saveSetting('messagingEnabled', boolValue)
  await dbManager.saveSetting('selectedContactId', selectedContactId.value)
}

const selectContact = async (contact: Contact) => {
  selectedContactId.value = contact.id
  // 清除未读消息
  contact.unreadCount = 0

  // 更新联系人到数据库
  await dbManager.updateContact(contact)
  // 保存选中状态
  await dbManager.saveSetting('selectedContactId', contact.id)

  nextTick(() => {
    scrollToBottom()
  })
}

const sendMessage = async () => {
  if (!newMessage.value.trim() || !selectedContactId.value) {
    return
  }

  const message: Message = {
    id: `msg_${Date.now()}`,
    contactId: selectedContactId.value,
    content: newMessage.value.trim(),
    time: formatLocalTime(new Date()),
    isOwn: true
  }

  messages.value.push(message)

  // 更新联系人最后消息
  const contact = contacts.value.find(c => c.id === selectedContactId.value)
  if (contact) {
    contact.lastMessage = newMessage.value.trim()
    contact.lastTime = new Date().toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })

    // 更新联系人到数据库
    await dbManager.updateContact(contact)
  }

  // 保存消息到数据库
  await dbManager.addMessage(message)

  newMessage.value = ''

  nextTick(() => {
    scrollToBottom()
  })

  ElMessage.success('消息发送成功')
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 生命周期
onMounted(async () => {
  // 加载数据
  await loadDataFromDB()

  // 如果有联系人且没有选中任何联系人，默认选择第一个
  if (contacts.value.length > 0 && !selectedContactId.value) {
    await selectContact(contacts.value[0])
  }

  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
})

onUnmounted(() => {
  // 关闭数据库连接
  dbManager.close()
})

// 监听数据变化，自动保存
watch([contacts, messages], async () => {
  if (!isLoading.value) {
    await saveDataToDB()
  }
}, { deep: true })
</script>

<style lang="scss" scoped>
.instant-messaging {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;

  .messaging-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
    background: #f8f9fa;

    .header-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .messaging-content {
    flex: 1;
    display: flex;
    height: calc(100% - 65px);

    .loading-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #909399;

      p {
        margin-top: 16px;
        font-size: 14px;
      }
    }
  }

  .contacts-panel {
    width: 280px;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;

    .contacts-header {
      padding: 16px;
      border-bottom: 1px solid #e4e7ed;
      background: #f8f9fa;

      .contacts-title {
        font-size: 14px;
        font-weight: 600;
        color: #606266;
      }
    }

    .contacts-list {
      flex: 1;
      overflow-y: auto;

      .contact-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        cursor: pointer;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.2s;

        &:hover {
          background: #f5f7fa;
        }

        &.active {
          background: #e6f7ff;
          border-right: 3px solid #409eff;
        }

        .contact-avatar {
          position: relative;
          margin-right: 12px;

          .unread-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #f56c6c;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        .contact-info {
          flex: 1;
          min-width: 0;

          .contact-name {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
          }

          .contact-status {
            font-size: 12px;
            color: #909399;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .contact-time {
          font-size: 12px;
          color: #c0c4cc;
        }
      }
    }

    .group-notification {
      padding: 16px;
      border-top: 1px solid #e4e7ed;
      background: #fffbe6;

      .notification-item {
        display: flex;
        align-items: center;
        padding: 8px;
        background: #fff7e6;
        border-radius: 4px;

        .notification-icon {
          margin-right: 8px;
          font-size: 16px;
        }

        .notification-text {
          font-size: 12px;
          color: #e6a23c;
          flex: 1;
        }
      }
    }
  }

  .chat-panel {
    flex: 1;
    display: flex;
    flex-direction: column;

    .chat-container {
      height: 100%;
      display: flex;
      flex-direction: column;

      .chat-header {
        display: flex;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #e4e7ed;
        background: #f8f9fa;

        .chat-title {
          margin-left: 12px;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
      }

      .messages-container {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        background: #f5f7fa;

        .message-item {
          display: flex;
          margin-bottom: 16px;

          &.own-message {
            justify-content: flex-end;

            .message-content {
              .message-bubble {
                background: #409eff;
                color: white;
              }

              .message-time {
                text-align: right;
              }
            }
          }

          .message-avatar {
            margin: 0 8px;
          }

          .message-content {
            max-width: 60%;

            .message-bubble {
              background: white;
              padding: 12px 16px;
              border-radius: 12px;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
              word-wrap: break-word;
            }

            .message-time {
              font-size: 12px;
              color: #c0c4cc;
              margin-top: 4px;
              padding: 0 4px;
            }
          }
        }
      }

      .input-area {
        display: flex;
        padding: 16px 20px;
        border-top: 1px solid #e4e7ed;
        background: white;

        .message-input {
          flex: 1;
          margin-right: 12px;
        }
      }
    }

    .no-contact-selected,
    .messaging-disabled {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      .empty-state,
      .disabled-state {
        text-align: center;
        color: #909399;

        p {
          margin: 16px 0 8px;
          font-size: 16px;
        }

        .disabled-tip {
          font-size: 14px;
          color: #c0c4cc;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .instant-messaging {
    .messaging-content {
      .contacts-panel {
        width: 240px;
      }
    }
  }
}
</style>

<template>
  <DialogComp
    :visible="dialogVisible"
    title="查看项目详情"
    width="800px"
    :visibleFooterButton="false"
    @closed="handleClose"
  >
    <div class="view-project-dialog">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="项目名称">
          {{ props.projectData.projectName || '暂无数据' }}
        </el-descriptions-item>
        <el-descriptions-item label="关联业务表">
          {{ props.projectData.businessType || '暂无数据' }}
        </el-descriptions-item>
        <el-descriptions-item label="所属分类">
          {{ props.projectData.department || '暂无数据' }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="props.projectData.status ? 'success' : 'danger'">
            {{ props.projectData.status ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建人">
          {{ props.projectData.creator || '暂无数据' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ props.projectData.createTime || '暂无数据' }}
        </el-descriptions-item>
        <el-descriptions-item label="版本号" v-if="props.projectData.version">
          {{ props.projectData.version }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" span="2" v-if="props.projectData.remark">
          {{ props.projectData.remark }}
        </el-descriptions-item>
        <el-descriptions-item label="更新通知" v-if="props.projectData.updateNotification !== undefined">
          <el-tag :type="props.projectData.updateNotification ? 'success' : 'info'">
            {{ props.projectData.updateNotification ? '开启' : '关闭' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="通知方式" v-if="props.projectData.notificationMethods && props.projectData.notificationMethods.length > 0">
          <el-tag
            v-for="method in props.projectData.notificationMethods"
            :key="method"
            size="small"
            style="margin-right: 8px;"
          >
            {{ getNotificationMethodLabel(method) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleEdit">编辑</el-button>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import DialogComp from '@/components/common/dialog-comp.vue'

interface Project {
  id: string
  sequence: number
  projectName: string
  businessType: string
  department: string
  status: boolean
  creator: string
  createTime: string
  version?: string
  remark?: string
  updateNotification?: boolean
  notificationMethods?: string[]
}

interface Props {
  visible: boolean
  projectData: Project
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'edit', projectData: Project): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  projectData: () => ({
    id: '',
    sequence: 0,
    projectName: '',
    businessType: '',
    department: '',
    status: false,
    creator: '',
    createTime: ''
  })
})

const emit = defineEmits<Emits>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})



// 获取通知方式标签
const getNotificationMethodLabel = (method: string) => {
  const methodMap: Record<string, string> = {
    email: '邮件',
    sms: '短信',
    wechat: '微信',
    system: '系统通知'
  }
  return methodMap[method] || method
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}

// 编辑项目
const handleEdit = () => {
  emit('edit', props.projectData)
  handleClose()
}
</script>

<style lang="scss" scoped>
// 样式可以根据需要添加
</style>

<template>
  <div class="operation-history">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">操作历史记录</h2>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-left">
        <el-input
          v-model="searchForm.operator"
          placeholder="请输入操作人"
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <el-input
          v-model="searchForm.content"
          placeholder="请输入操作内容"
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
        >
        </el-input>

        <el-date-picker
          v-model="searchForm.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          class="search-input"
          @change="handleSearch"
        />
      </div>

      <div class="search-right">
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          查询
        </el-button>
        <el-button @click="handleReset">
          重置
        </el-button>
      </div>
    </div>

    <!-- 操作历史列表表格 -->
    <TableV2
      ref="tableRef"
      :defaultTableData="tableData"
      :columns="columns"
      :enable-toolbar="false"
      :enable-own-button="false"
      :enable-selection="false"
      :enable-index="true"
      :height="400"
      :loading="loading"
    />

    <!-- 分页组件 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onPageSizeChange"
        @current-change="onCurrentPageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'

// 操作历史记录接口定义
interface OperationRecord {
  id: string
  sequence: number
  operator: string
  content: string
  operationTime: string
}

// 搜索表单
const searchForm = reactive({
  operator: '',
  content: '',
  dateRange: [] as string[]
})

// 表格数据
const tableRef = ref()
const tableData = ref<OperationRecord[]>([])

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 加载状态
const loading = ref(false)

// TableV2 列配置
const columns = ref([
  {
    prop: 'operator',
    label: '操作人',
    width: 120
  },
  {
    prop: 'content',
    label: '操作内容',
    minWidth: 300
  },
  {
    prop: 'operationTime',
    label: '操作时间',
    width: 180
  }
])

// 本地存储键名
const STORAGE_KEY = 'collaborative_operation_history'

// 初始化模拟数据
const initMockData = () => {
  const mockData: OperationRecord[] = []
  const operators = [
    '张建国', '李明华', '王秀英', '刘志强', '陈美玲', '杨国庆', '赵小红', '黄建军',
    '周丽娟', '吴永强', '徐海燕', '孙志华', '胡晓明', '朱建平', '高秀芳', '林志远',
    '何丽华', '郭建设', '马小军', '罗美英', '梁志刚', '宋晓丽', '郑国强', '谢秀珍'
  ]
  const operations = [
    '创建了项目"基础信息收集"',
    '编辑了项目"老年人信息采集"',
    '删除了项目"测试项目"',
    '新增了模板"项目计划模板"',
    '编辑了模板"会议纪要模板"',
    '删除了模板"废弃模板"',
    '添加了团队成员"新员工"',
    '修改了团队成员角色',
    '移除了团队成员"离职员工"',
    '创建了协作工作流',
    '修改了工作流配置',
    '启用了项目状态',
    '禁用了模板状态',
    '导出了项目数据',
    '导入了模板数据',
    '备份了系统数据',
    '恢复了历史数据',
    '修改了用户权限',
    '更新了系统配置',
    '查看了操作历史',
    '生成了统计报告',
    '发送了系统通知',
    '同步了数据状态',
    '清理了缓存数据',
    '优化了系统性能'
  ]

  // 生成最近30天的操作记录
  const now = new Date()
  for (let i = 1; i <= 50; i++) {
    // 随机生成过去30天内的时间
    const daysAgo = Math.floor(Math.random() * 30)
    const hoursAgo = Math.floor(Math.random() * 24)
    const minutesAgo = Math.floor(Math.random() * 60)

    const operationDate = new Date(now)
    operationDate.setDate(operationDate.getDate() - daysAgo)
    operationDate.setHours(operationDate.getHours() - hoursAgo)
    operationDate.setMinutes(operationDate.getMinutes() - minutesAgo)

    mockData.push({
      id: `operation_${i}`,
      sequence: i,
      operator: operators[Math.floor(Math.random() * operators.length)],
      content: operations[Math.floor(Math.random() * operations.length)],
      operationTime: operationDate.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    })
  }

  // 按时间倒序排列（最新的在前面）
  return mockData.sort((a, b) => new Date(b.operationTime).getTime() - new Date(a.operationTime).getTime())
}

// 从本地存储加载数据
const loadOperationHistoryFromStorage = () => {
  const stored = localStorage.getItem(STORAGE_KEY)
  if (stored) {
    try {
      return JSON.parse(stored)
    } catch (e) {
      console.error('解析本地存储数据失败:', e)
    }
  }

  // 如果没有数据，初始化模拟数据并保存到localStorage
  const mockData = initMockData()
  saveOperationHistoryToStorage(mockData)
  return mockData
}

// 保存数据到本地存储
const saveOperationHistoryToStorage = (records: OperationRecord[]) => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(records))
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true

  try {
    // 模拟异步加载
    await new Promise(resolve => setTimeout(resolve, 300))

    const allRecords = loadOperationHistoryFromStorage()

    // 应用搜索过滤
    let filteredData = allRecords

    // 按操作人过滤
    if (searchForm.operator) {
      filteredData = filteredData.filter((item: OperationRecord) =>
        item.operator.includes(searchForm.operator)
      )
    }

    // 按操作内容过滤
    if (searchForm.content) {
      filteredData = filteredData.filter((item: OperationRecord) =>
        item.content.includes(searchForm.content)
      )
    }

    // 按日期范围过滤
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      const startDate = new Date(searchForm.dateRange[0])
      const endDate = new Date(searchForm.dateRange[1])
      endDate.setHours(23, 59, 59, 999) // 设置为当天结束时间

      filteredData = filteredData.filter((item: OperationRecord) => {
        const operationDate = new Date(item.operationTime)
        return operationDate >= startDate && operationDate <= endDate
      })
    }

    // 更新分页信息
    pagination.total = filteredData.length

    // 分页处理
    const start = (pagination.currentPage - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    tableData.value = filteredData.slice(start, end)
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.currentPage = 1
  loadTableData()
}

// 重置搜索
const handleReset = () => {
  searchForm.operator = ''
  searchForm.content = ''
  searchForm.dateRange = []
  pagination.currentPage = 1
  loadTableData()
}

// 分页大小改变
const onPageSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadTableData()
}

// 当前页改变
const onCurrentPageChange = (page: number) => {
  pagination.currentPage = page
  loadTableData()
}

// 组件挂载
onMounted(() => {
  loadTableData()
})
</script>

<style scoped lang="scss">
.operation-history {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;

  .page-header {
    margin-bottom: 20px;

    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin: revert;
    }
  }

  .search-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .search-left {
      display: flex;
      gap: 15px;
      flex: 1;

      .search-input {
        width: 200px;
      }
    }

    .search-right {
      display: flex;
      gap: 10px;
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
</style>

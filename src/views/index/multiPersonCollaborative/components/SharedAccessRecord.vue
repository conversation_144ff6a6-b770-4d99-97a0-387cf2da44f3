<template>
  <div class="shared-access-record">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">共享访问记录</h2>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-left">
        <el-input
          v-model="searchForm.visitor"
          placeholder="请输入访问人"
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <el-input
          v-model="searchForm.content"
          placeholder="请输入访问内容"
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
        >
        </el-input>

        <el-select
          v-model="searchForm.status"
          placeholder="请选择访问状态"
          class="search-input"
          clearable
          @change="handleSearch"
        >
          <el-option label="成功" value="success" />
          <el-option label="失败" value="failed" />
          <el-option label="拒绝" value="denied" />
        </el-select>

        <el-date-picker
          v-model="searchForm.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          class="search-input"
          @change="handleSearch"
        />
      </div>

      <div class="search-right">
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          查询
        </el-button>
        <el-button @click="handleReset">
          重置
        </el-button>
      </div>
    </div>

    <!-- 通知设置区域 -->
    <div class="notification-section">
      <div class="notification-left">
        <span class="notification-label">共享数据更新通知：</span>
        <el-radio-group v-model="notificationSettings.enabled" @change="handleNotificationChange">
          <el-radio :value="true">开启</el-radio>
          <el-radio :value="false">关闭</el-radio>
        </el-radio-group>
      </div>

      <div class="notification-right" v-if="notificationSettings.enabled">
        <span class="notification-label">通知方式：</span>
        <el-checkbox-group v-model="notificationSettings.methods" @change="handleNotificationMethodChange">
          <el-checkbox value="站内信">站内信</el-checkbox>
          <el-checkbox value="愉快证">愉快证</el-checkbox>
          <el-checkbox value="愉快DING">愉快DING</el-checkbox>
        </el-checkbox-group>
      </div>
    </div>

    <!-- 共享访问记录列表表格 -->
    <TableV2
      ref="tableRef"
      :defaultTableData="tableData"
      :columns="columns"
      :enable-toolbar="false"
      :enable-own-button="false"
      :enable-selection="false"
      :enable-index="true"
      :height="400"
      :loading="loading"
    >
      <template #accessStatus="{ row }">
        <el-tag
          :type="getStatusType(row.accessStatus)"
          size="small"
        >
          {{ getStatusText(row.accessStatus) }}
        </el-tag>
      </template>
    </TableV2>

    <!-- 分页组件 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onPageSizeChange"
        @current-change="onCurrentPageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 共享访问记录接口定义
interface AccessRecord {
  id: string
  sequence: number
  accessTime: string
  visitor: string
  content: string
  accessStatus: 'success' | 'failed' | 'denied'
  ipAddress: string
}

// 通知设置接口
interface NotificationSettings {
  enabled: boolean
  methods: string[]
}

// 搜索表单
const searchForm = reactive({
  visitor: '',
  content: '',
  status: '',
  dateRange: [] as string[]
})

// 通知设置
const notificationSettings = reactive<NotificationSettings>({
  enabled: true,
  methods: ['站内信', '愉快证']
})

// 表格数据
const tableRef = ref()
const tableData = ref<AccessRecord[]>([])

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 加载状态
const loading = ref(false)

// TableV2 列配置
const columns = ref([
  {
    prop: 'accessTime',
    label: '访问时间',
    width: 180
  },
  {
    prop: 'visitor',
    label: '访问人',
    width: 120
  },
  {
    prop: 'content',
    label: '访问内容',
    minWidth: 250
  },
  {
    prop: 'accessStatus',
    label: '访问状态',
    width: 100,
    slot: 'accessStatus'
  },
  {
    prop: 'ipAddress',
    label: 'IP地址',
    width: 140
  }
])

// 状态类型映射
const getStatusType = (status: string) => {
  const typeMap = {
    success: 'success',
    failed: 'danger',
    denied: 'warning'
  }
  return typeMap[status as keyof typeof typeMap] || 'info'
}

// 状态文本映射
const getStatusText = (status: string) => {
  const textMap = {
    success: '成功',
    failed: '失败',
    denied: '拒绝'
  }
  return textMap[status as keyof typeof textMap] || '未知'
}

// 本地存储键名
const STORAGE_KEY = 'collaborative_access_records'
const NOTIFICATION_STORAGE_KEY = 'collaborative_notification_settings'

// 初始化模拟数据
const initMockData = () => {
  const mockData: AccessRecord[] = []
  const visitors = [
    '张建国', '李明华', '王秀英', '刘志强', '陈美玲', '杨国庆', '赵小红', '黄建军',
    '周丽娟', '吴永强', '徐海燕', '孙志华', '胡晓明', '朱建平', '高秀芳', '林志远',
    '何丽华', '郭建设', '马小军', '罗美英', '梁志刚', '宋晓丽', '郑国强', '谢秀珍',
    '韩建华', '冯小刚', '邓丽君', '田志国', '曾小华', '彭建军'
  ]
  const contents = [
    '查看项目"基础信息收集"详情',
    '下载模板"项目计划模板"',
    '访问团队成员列表',
    '查看操作历史记录',
    '导出项目数据',
    '查看共享文档',
    '访问工作流配置',
    '下载备份文件',
    '查看统计报告',
    '访问系统设置',
    '查看用户权限',
    '下载日志文件',
    '访问数据源管理',
    '查看任务分解',
    '访问区域管理',
    '查看即时通讯记录'
  ]
  const statuses: ('success' | 'failed' | 'denied')[] = ['success', 'failed', 'denied']

  // 生成IP地址
  const generateIP = () => {
    return `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`
  }

  // 生成最近30天的访问记录
  const now = new Date()
  for (let i = 1; i <= 60; i++) {
    // 随机生成过去30天内的时间
    const daysAgo = Math.floor(Math.random() * 30)
    const hoursAgo = Math.floor(Math.random() * 24)
    const minutesAgo = Math.floor(Math.random() * 60)

    const accessDate = new Date(now)
    accessDate.setDate(accessDate.getDate() - daysAgo)
    accessDate.setHours(accessDate.getHours() - hoursAgo)
    accessDate.setMinutes(accessDate.getMinutes() - minutesAgo)

    // 成功率较高，失败和拒绝较少
    let status: 'success' | 'failed' | 'denied'
    const rand = Math.random()
    if (rand < 0.7) {
      status = 'success'
    } else if (rand < 0.85) {
      status = 'failed'
    } else {
      status = 'denied'
    }

    mockData.push({
      id: `access_${i}`,
      sequence: i,
      accessTime: accessDate.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      visitor: visitors[Math.floor(Math.random() * visitors.length)],
      content: contents[Math.floor(Math.random() * contents.length)],
      accessStatus: status,
      ipAddress: generateIP()
    })
  }

  // 按时间倒序排列（最新的在前面）
  return mockData.sort((a, b) => new Date(b.accessTime).getTime() - new Date(a.accessTime).getTime())
}

// 从本地存储加载访问记录数据
const loadAccessRecordsFromStorage = () => {
  const stored = localStorage.getItem(STORAGE_KEY)
  if (stored) {
    try {
      return JSON.parse(stored)
    } catch (e) {
      console.error('解析本地存储数据失败:', e)
    }
  }

  // 如果没有数据，初始化模拟数据并保存到localStorage
  const mockData = initMockData()
  saveAccessRecordsToStorage(mockData)
  return mockData
}

// 保存访问记录数据到本地存储
const saveAccessRecordsToStorage = (records: AccessRecord[]) => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(records))
}

// 从本地存储加载通知设置
const loadNotificationSettingsFromStorage = () => {
  const stored = localStorage.getItem(NOTIFICATION_STORAGE_KEY)
  if (stored) {
    try {
      const settings = JSON.parse(stored)
      notificationSettings.enabled = settings.enabled
      notificationSettings.methods = settings.methods
    } catch (e) {
      console.error('解析通知设置失败:', e)
    }
  }
}

// 保存通知设置到本地存储
const saveNotificationSettingsToStorage = () => {
  localStorage.setItem(NOTIFICATION_STORAGE_KEY, JSON.stringify(notificationSettings))
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true

  try {
    // 模拟异步加载
    await new Promise(resolve => setTimeout(resolve, 300))

    const allRecords = loadAccessRecordsFromStorage()

    // 应用搜索过滤
    let filteredData = allRecords

    // 按访问人过滤
    if (searchForm.visitor) {
      filteredData = filteredData.filter((item: AccessRecord) =>
        item.visitor.includes(searchForm.visitor)
      )
    }

    // 按访问内容过滤
    if (searchForm.content) {
      filteredData = filteredData.filter((item: AccessRecord) =>
        item.content.includes(searchForm.content)
      )
    }

    // 按访问状态过滤
    if (searchForm.status) {
      filteredData = filteredData.filter((item: AccessRecord) =>
        item.accessStatus === searchForm.status
      )
    }

    // 按日期范围过滤
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      const startDate = new Date(searchForm.dateRange[0])
      const endDate = new Date(searchForm.dateRange[1])
      endDate.setHours(23, 59, 59, 999) // 设置为当天结束时间

      filteredData = filteredData.filter((item: AccessRecord) => {
        const accessDate = new Date(item.accessTime)
        return accessDate >= startDate && accessDate <= endDate
      })
    }

    // 更新分页信息
    pagination.total = filteredData.length

    // 分页处理
    const start = (pagination.currentPage - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    tableData.value = filteredData.slice(start, end)
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.currentPage = 1
  loadTableData()
}

// 重置搜索
const handleReset = () => {
  searchForm.visitor = ''
  searchForm.content = ''
  searchForm.status = ''
  searchForm.dateRange = []
  pagination.currentPage = 1
  loadTableData()
}

// 通知开关变化处理
const handleNotificationChange = (value: string | number | boolean | undefined) => {
  const enabled = Boolean(value)
  if (!enabled) {
    notificationSettings.methods = []
  } else {
    notificationSettings.methods = ['站内信']
  }
  saveNotificationSettingsToStorage()
  ElMessage.success(`共享数据更新通知已${enabled ? '开启' : '关闭'}`)
}

// 通知方式变化处理
const handleNotificationMethodChange = () => {
  saveNotificationSettingsToStorage()
  ElMessage.success('通知方式设置已更新')
}

// 分页大小改变
const onPageSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadTableData()
}

// 当前页改变
const onCurrentPageChange = (page: number) => {
  pagination.currentPage = page
  loadTableData()
}

// 组件挂载
onMounted(() => {
  loadNotificationSettingsFromStorage()
  loadTableData()
})
</script>

<style scoped lang="scss">
.shared-access-record {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;

  .page-header {
    margin-bottom: 20px;

    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin: revert;
    }
  }

  .search-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .search-left {
      display: flex;
      gap: 15px;
      flex: 1;

      .search-input {
        width: 200px;
      }
    }

    .search-right {
      display: flex;
      gap: 10px;
    }
  }

  .notification-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .notification-left {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .notification-right {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .notification-label {
      font-weight: 500;
      color: #303133;
      white-space: nowrap;
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
</style>

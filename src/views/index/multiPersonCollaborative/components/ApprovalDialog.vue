<template>
  <DialogComp
    v-model:visible="dialogVisible"
    title="成员审批"
    width="600px"
    :visibleFooterButton="false"
    @closed="handleClose"
  >
    <div class="approval-dialog">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="姓名">
          {{ props.memberData?.name || '暂无数据' }}
        </el-descriptions-item>
        <el-descriptions-item label="所属部门">
          {{ props.memberData?.department || '暂无数据' }}
        </el-descriptions-item>
        <el-descriptions-item label="申请角色">
          {{ props.memberData?.teamRole || '暂无数据' }}
        </el-descriptions-item>
        <el-descriptions-item label="申请时间">
          {{ props.memberData?.applyTime || '暂无数据' }}
        </el-descriptions-item>
      </el-descriptions>

      <div class="approval-section">
        <h4>审批意见</h4>
        <el-form :model="approvalForm" :rules="formRules" ref="formRef" label-width="100px">
          <el-form-item label="审批结果" prop="approved">
            <el-radio-group v-model="approvalForm.approved">
              <el-radio :value="true">通过</el-radio>
              <el-radio :value="false">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审批意见" prop="comment">
            <el-input
              v-model="approvalForm.comment"
              type="textarea"
              :rows="4"
              placeholder="请输入审批意见"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'

interface TeamMember {
  id: string
  sequence: number
  name: string
  department: string
  teamRole: string
  status: 'pending' | 'approved' | 'rejected'
  applyTime: string
  approveTime?: string
  approver?: string
}

interface Props {
  visible: boolean
  memberData: TeamMember | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirmed', result: { approved: boolean, member: TeamMember }): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref()

// 审批表单
const approvalForm = reactive({
  approved: true,
  comment: ''
})

// 表单验证规则
const formRules = {
  comment: [
    { required: true, message: '请输入审批意见', trigger: 'blur' },
    { min: 5, message: '审批意见至少5个字符', trigger: 'blur' }
  ]
}

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听对话框打开，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    approvalForm.approved = true
    approvalForm.comment = ''
    formRef.value?.clearValidate()
  }
})

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 处理关闭
const handleClose = () => {
  emit('update:visible', false)
}

// 处理确认
const handleConfirm = async () => {
  if (!props.memberData) {
    ElMessage.error('成员数据无效')
    return
  }

  try {
    await formRef.value?.validate()
    
    const result = {
      approved: approvalForm.approved,
      member: {
        ...props.memberData,
        comment: approvalForm.comment
      }
    }
    
    emit('confirmed', result)
    dialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}
</script>

<style scoped lang="scss">
.approval-dialog {
  .approval-section {
    margin-top: 24px;
    
    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>

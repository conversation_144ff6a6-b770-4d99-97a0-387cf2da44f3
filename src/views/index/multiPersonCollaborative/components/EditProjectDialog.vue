<template>
  <DialogComp
    :visible="dialogVisible"
    title="编辑项目"
    width="600px"
    :visibleFooterButton="false"
    @closed="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="loading"
    >
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="formData.projectName"
          placeholder="请输入项目名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="关联业务表" prop="businessType">
        <el-select
          v-model="formData.businessType"
          placeholder="请选择关联业务表"
          style="width: 100%"
        >
          <el-option
            v-for="type in businessTypeOptions"
            :key="type"
            :label="type"
            :value="type"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="所属分类" prop="category">
        <el-select
          v-model="formData.category"
          placeholder="请选择所属分类"
          style="width: 100%"
        >
          <el-option
            v-for="category in categoryOptions"
            :key="category"
            :label="category"
            :value="category"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-switch
          v-model="formData.status"
          :active-value="true"
          :inactive-value="false"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>
      
      <el-form-item label="版本号" prop="version">
        <el-input
          v-model="formData.version"
          placeholder="请输入版本号，如：v1.0.0"
          maxlength="20"
        />
      </el-form-item>
      
      <el-form-item label="更新通知" prop="updateNotification">
        <el-switch
          v-model="formData.updateNotification"
          :active-value="true"
          :inactive-value="false"
          active-text="开启"
          inactive-text="关闭"
        />
      </el-form-item>
      
      <el-form-item label="通知方式" prop="notificationMethods" v-if="formData.updateNotification">
        <el-checkbox-group v-model="formData.notificationMethods">
          <el-checkbox value="email">邮件</el-checkbox>
          <el-checkbox value="sms">短信</el-checkbox>
          <el-checkbox value="wechat">微信</el-checkbox>
          <el-checkbox value="system">系统通知</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        保存
      </el-button>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'

interface Project {
  id: string
  sequence: number
  projectName: string
  businessType: string
  department: string
  status: boolean
  creator: string
  createTime: string
  version?: string
  remark?: string
  updateNotification?: boolean
  notificationMethods?: string[]
}

interface Props {
  visible: boolean
  projectData: Project
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  projectData: () => ({
    id: '',
    sequence: 0,
    projectName: '',
    businessType: '',
    department: '',
    status: false,
    creator: '',
    createTime: ''
  })
})

const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单数据
const formData = reactive({
  projectName: '',
  businessType: '',
  category: '',
  status: true,
  version: '',
  updateNotification: false,
  notificationMethods: [] as string[],
  remark: ''
})

// 业务表类型选项
const businessTypeOptions = [
  '基础信息收集',
  '老年人信息采集',
  '辖区居民业务数据',
  '辖区教育活动记录',
  '辖区地质风险区域排查',
  '新生儿信息登记',
  '辖区社会救助花名册',
  '辖区高龄老人花名册',
  '辖区大病救助记录',
  '孕产妇信息采集'
]

// 分类选项
const categoryOptions = ['分类1', '分类2', '分类3', '分类4']

// 表单验证规则
const formRules: FormRules = {
  projectName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 50, message: '项目名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  businessType: [
    { required: true, message: '请选择关联业务表', trigger: 'change' }
  ],
  category: [
    { required: true, message: '请选择所属分类', trigger: 'change' }
  ]
}

// 监听项目数据变化，初始化表单
watch(() => props.projectData, (newData) => {
  if (newData && props.visible) {
    Object.assign(formData, {
      projectName: newData.projectName,
      businessType: newData.businessType,
      category: newData.department,
      status: newData.status,
      version: newData.version || '',
      updateNotification: newData.updateNotification || false,
      notificationMethods: newData.notificationMethods || [],
      remark: newData.remark || ''
    })
  }
}, { immediate: true })

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}

// 取消
const handleCancel = () => {
  handleClose()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新项目数据到本地存储
    updateProjectToStorage()
    
    ElMessage.success('项目更新成功')
    emit('success')
    handleClose()
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 更新项目到本地存储
const updateProjectToStorage = () => {
  const stored = localStorage.getItem('collaborative_projects')
  let projects = []
  
  if (stored) {
    try {
      projects = JSON.parse(stored)
    } catch (e) {
      console.error('解析本地存储数据失败:', e)
    }
  }
  
  // 查找并更新项目
  const index = projects.findIndex((item: Project) => item.id === props.projectData.id)
  if (index !== -1) {
    projects[index] = {
      ...projects[index],
      projectName: formData.projectName,
      businessType: formData.businessType,
      department: formData.category,
      status: formData.status,
      version: formData.version,
      updateNotification: formData.updateNotification,
      notificationMethods: formData.notificationMethods,
      remark: formData.remark
    }
    
    localStorage.setItem('collaborative_projects', JSON.stringify(projects))
  }
}
</script>

<style lang="scss" scoped>
// 样式可以根据需要添加
</style>

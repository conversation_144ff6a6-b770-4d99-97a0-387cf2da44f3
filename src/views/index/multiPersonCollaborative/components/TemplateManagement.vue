<template>
  <div class="template-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">模板管理</h2>
    </div>

    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <div class="search-left">
        <el-input
          v-model="searchForm.templateName"
          placeholder="请输入模板名称"
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <div class="search-right">
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          查询
        </el-button>
        <el-button @click="handleReset">
          重置
        </el-button>
        <el-button type="primary" @click="handleCreateTemplate">
          新增模板
        </el-button>
      </div>
    </div>

    <!-- 模板列表表格 -->
    <TableV2
      ref="tableRef"
      :defaultTableData="tableData"
      :columns="columns"
      :enable-toolbar="false"
      :enable-own-button="true"
      :enable-selection="false"
      :enable-index="true"
      :height="400"
      :buttons="buttons"
      :loading="loading"
      @click-button="onTableClickButton"
    />

    <!-- 分页组件 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onPageSizeChange"
        @current-change="onCurrentPageChange"
      />
    </div>

    <!-- 新增/编辑模板对话框 -->
    <DialogComp
      v-model:visible="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :visibleFooterButton="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="template-form"
      >
        <el-form-item label="模板名称" prop="templateName">
          <el-input
            v-model="formData.templateName"
            placeholder="请输入模板名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="模板描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入模板描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="模板内容" prop="content">
          <el-input
            v-model="formData.content"
            type="textarea"
            :rows="6"
            placeholder="请输入模板内容"
            maxlength="2000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-select
            v-model="formData.status"
            placeholder="请选择状态"
            style="width: 100%"
          >
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleConfirm" :loading="submitLoading">
            确定
          </el-button>
        </div>
      </template>
    </DialogComp>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import DialogComp from '@/components/common/dialog-comp.vue'

// 模板接口定义
interface Template {
  id: string
  sequence: number
  templateName: string
  description: string
  content: string
  creator: string
  createTime: string
  status: boolean
}

// 搜索表单
const searchForm = reactive({
  templateName: ''
})

// 表格数据
const tableRef = ref()
const tableData = ref<Template[]>([])

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 对话框状态
const dialogVisible = ref(false)
const dialogTitle = ref('新增模板')
const isEdit = ref(false)
const currentTemplateId = ref('')

// 表单数据
const formRef = ref()
const formData = reactive<Omit<Template, 'id' | 'sequence' | 'creator' | 'createTime'>>({
  templateName: '',
  description: '',
  content: '',
  status: true
})

// 加载状态
const loading = ref(false)
const submitLoading = ref(false)

// TableV2 列配置
const columns = ref([
  {
    prop: 'templateName',
    label: '模板名称',
    minWidth: 150
  },
  {
    prop: 'description',
    label: '模板描述',
    minWidth: 200
  },
  {
    prop: 'creator',
    label: '创建人',
    width: 100
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 120
  }
])

// TableV2 操作按钮配置
const buttons = ref([
  {
    label: '编辑',
    type: 'warning',
    code: 'edit'
  },
  {
    label: '删除',
    type: 'danger',
    code: 'delete',
    popconfirm: '确认删除该模板吗?'
  }
])

// 表单验证规则
const formRules = {
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '模板名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入模板描述', trigger: 'blur' },
    { min: 5, max: 200, message: '模板描述长度在 5 到 200 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入模板内容', trigger: 'blur' },
    { min: 10, max: 2000, message: '模板内容长度在 10 到 2000 个字符', trigger: 'blur' }
  ]
}

// 本地存储键名
const STORAGE_KEY = 'collaborative_templates'

// 初始化模拟数据
const initMockData = () => {
  const mockData: Template[] = []
  const creators = [
    '张建国', '李明华', '王秀英', '刘志强', '陈美玲', '杨国庆', '赵小红', '黄建军',
    '周丽娟', '吴永强', '徐海燕', '孙志华', '胡晓明', '朱建平', '高秀芳', '林志远'
  ]

  // 真实的政府模板数据
  const templateData = [
    {
      name: '社区居民基础信息采集表模板',
      description: '用于收集社区居民基本信息，包括姓名、身份证号、联系方式、家庭成员等基础数据的标准表格模板',
      content: '# 社区居民基础信息采集表\n\n## 1. 个人基本信息\n- 姓名：\n- 身份证号：\n- 联系电话：\n- 现住址：\n\n## 2. 家庭成员信息\n- 家庭人口数：\n- 家庭成员详情：\n\n## 3. 其他信息\n- 特殊情况说明：\n- 备注：'
    },
    {
      name: '老年人健康状况调查表模板',
      description: '针对65岁以上老年人健康状况进行全面调查的专用表格，涵盖身体状况、慢性病史、生活自理能力等内容',
      content: '# 老年人健康状况调查表\n\n## 1. 基本信息\n- 姓名：\n- 年龄：\n- 性别：\n- 联系方式：\n\n## 2. 健康状况\n- 慢性病史：\n- 用药情况：\n- 生活自理能力：\n\n## 3. 医疗需求\n- 医疗服务需求：\n- 康复需求：'
    },
    {
      name: '困难家庭救助申请表模板',
      description: '用于困难家庭申请社会救助的标准表格，包含家庭经济状况、困难原因、救助需求等详细信息',
      content: '# 困难家庭救助申请表\n\n## 1. 申请人信息\n- 申请人姓名：\n- 身份证号：\n- 联系方式：\n- 家庭住址：\n\n## 2. 家庭经济状况\n- 家庭月收入：\n- 主要支出：\n- 困难原因：\n\n## 3. 救助申请\n- 申请救助类型：\n- 申请理由：'
    },
    {
      name: '新生儿出生登记表模板',
      description: '新生儿出生信息登记的官方表格模板，包含新生儿基本信息、父母信息、出生医院等必要数据',
      content: '# 新生儿出生登记表\n\n## 1. 新生儿信息\n- 姓名：\n- 性别：\n- 出生日期：\n- 出生时间：\n- 出生体重：\n\n## 2. 父母信息\n- 父亲姓名：\n- 母亲姓名：\n- 父母身份证号：\n\n## 3. 出生医院信息\n- 医院名称：\n- 接生医生：'
    },
    {
      name: '地质灾害隐患点排查记录表模板',
      description: '用于记录地质灾害隐患点排查情况的专业表格，包含隐患点位置、风险等级、防护措施等关键信息',
      content: '# 地质灾害隐患点排查记录表\n\n## 1. 隐患点基本信息\n- 隐患点编号：\n- 隐患点位置：\n- 坐标信息：\n- 隐患类型：\n\n## 2. 风险评估\n- 风险等级：\n- 威胁对象：\n- 威胁人数：\n\n## 3. 防护措施\n- 现有防护措施：\n- 建议措施：'
    },
    {
      name: '青少年教育活动记录表模板',
      description: '记录社区青少年教育活动开展情况的标准表格，包含活动内容、参与人数、效果评估等信息',
      content: '# 青少年教育活动记录表\n\n## 1. 活动基本信息\n- 活动名称：\n- 活动时间：\n- 活动地点：\n- 组织单位：\n\n## 2. 参与情况\n- 参与人数：\n- 年龄分布：\n- 活动内容：\n\n## 3. 效果评估\n- 活动效果：\n- 参与者反馈：'
    },
    {
      name: '孕产妇健康管理档案模板',
      description: '建立孕产妇健康管理档案的标准模板，涵盖孕期检查、分娩情况、产后随访等全程管理内容',
      content: '# 孕产妇健康管理档案\n\n## 1. 基本信息\n- 姓名：\n- 年龄：\n- 联系方式：\n- 预产期：\n\n## 2. 孕期管理\n- 建档时间：\n- 孕期检查记录：\n- 高危因素：\n\n## 3. 分娩及产后\n- 分娩方式：\n- 产后随访：'
    },
    {
      name: '残疾人康复服务需求调查表模板',
      description: '调查残疾人康复服务需求的专用表格，包含残疾类型、康复需求、服务意愿等详细信息',
      content: '# 残疾人康复服务需求调查表\n\n## 1. 基本信息\n- 姓名：\n- 残疾类型：\n- 残疾等级：\n- 联系方式：\n\n## 2. 康复需求\n- 康复项目需求：\n- 辅助器具需求：\n- 康复训练需求：\n\n## 3. 服务意愿\n- 接受服务意愿：\n- 服务时间偏好：'
    },
    {
      name: '流动人口信息登记表模板',
      description: '外来务工人员及流动人口信息登记的标准表格，包含个人信息、居住信息、就业情况等内容',
      content: '# 流动人口信息登记表\n\n## 1. 个人基本信息\n- 姓名：\n- 身份证号：\n- 户籍地址：\n- 现居住地址：\n\n## 2. 就业情况\n- 工作单位：\n- 职业：\n- 月收入：\n\n## 3. 居住情况\n- 居住类型：\n- 居住时间：'
    },
    {
      name: '社区志愿者服务登记表模板',
      description: '社区志愿者参与服务活动的登记表格，记录志愿者信息、服务内容、服务时长等数据',
      content: '# 社区志愿者服务登记表\n\n## 1. 志愿者信息\n- 姓名：\n- 联系方式：\n- 专业特长：\n- 可服务时间：\n\n## 2. 服务记录\n- 服务日期：\n- 服务内容：\n- 服务时长：\n\n## 3. 服务评价\n- 服务质量：\n- 受益人反馈：'
    },
    {
      name: '环境卫生整治检查表模板',
      description: '社区环境卫生整治工作检查的标准表格，包含检查区域、存在问题、整改措施等内容',
      content: '# 环境卫生整治检查表\n\n## 1. 检查基本信息\n- 检查日期：\n- 检查区域：\n- 检查人员：\n- 天气情况：\n\n## 2. 检查内容\n- 道路清洁情况：\n- 垃圾处理情况：\n- 绿化维护情况：\n\n## 3. 问题及整改\n- 发现问题：\n- 整改措施：'
    },
    {
      name: '食品安全监管记录表模板',
      description: '食品安全监管检查的记录表格，涵盖检查对象、检查内容、问题发现、处理措施等信息',
      content: '# 食品安全监管记录表\n\n## 1. 检查对象信息\n- 经营单位名称：\n- 经营地址：\n- 负责人：\n- 联系方式：\n\n## 2. 检查内容\n- 证照情况：\n- 卫生状况：\n- 食品质量：\n\n## 3. 检查结果\n- 发现问题：\n- 处理措施：'
    },
    {
      name: '公共租赁住房申请表模板',
      description: '公共租赁住房申请的标准表格，包含申请人信息、家庭状况、住房需求等详细内容',
      content: '# 公共租赁住房申请表\n\n## 1. 申请人信息\n- 申请人姓名：\n- 身份证号：\n- 工作单位：\n- 月收入：\n\n## 2. 家庭状况\n- 家庭人口：\n- 家庭月收入：\n- 现住房情况：\n\n## 3. 申请理由\n- 申请原因：\n- 住房需求：'
    },
    {
      name: '特困人员供养服务记录表模板',
      description: '特困人员供养服务的记录表格，包含服务对象信息、服务内容、服务效果等管理数据',
      content: '# 特困人员供养服务记录表\n\n## 1. 服务对象信息\n- 姓名：\n- 身份证号：\n- 特困类型：\n- 联系方式：\n\n## 2. 服务内容\n- 生活照料：\n- 医疗护理：\n- 精神慰藉：\n\n## 3. 服务效果\n- 服务质量评价：\n- 满意度调查：'
    },
    {
      name: '退役军人服务保障登记表模板',
      description: '退役军人服务保障工作的登记表格，记录退役军人基本信息、服务需求、保障措施等内容',
      content: '# 退役军人服务保障登记表\n\n## 1. 基本信息\n- 姓名：\n- 身份证号：\n- 退役时间：\n- 服役经历：\n\n## 2. 服务需求\n- 就业需求：\n- 医疗需求：\n- 其他需求：\n\n## 3. 保障措施\n- 已享受政策：\n- 拟提供服务：'
    },
    {
      name: '社区文化活动组织记录表模板',
      description: '社区文化活动组织开展的记录表格，包含活动策划、实施过程、参与情况、效果评估等信息',
      content: '# 社区文化活动组织记录表\n\n## 1. 活动策划\n- 活动主题：\n- 活动目标：\n- 预期参与人数：\n- 活动预算：\n\n## 2. 实施过程\n- 活动时间：\n- 活动地点：\n- 实际参与人数：\n\n## 3. 效果评估\n- 活动效果：\n- 居民反馈：'
    },
    {
      name: '防汛抗旱应急预案执行记录表模板',
      description: '防汛抗旱应急预案执行情况的记录表格，包含预警信息、应急响应、处置措施、效果评估等内容',
      content: '# 防汛抗旱应急预案执行记录表\n\n## 1. 预警信息\n- 预警时间：\n- 预警等级：\n- 预警内容：\n- 发布渠道：\n\n## 2. 应急响应\n- 响应时间：\n- 响应级别：\n- 参与人员：\n\n## 3. 处置措施\n- 采取措施：\n- 处置效果：'
    },
    {
      name: '城乡低保对象动态管理表模板',
      description: '城乡低保对象动态管理的标准表格，包含低保对象信息变更、收入核查、保障标准调整等管理内容',
      content: '# 城乡低保对象动态管理表\n\n## 1. 低保对象信息\n- 姓名：\n- 身份证号：\n- 家庭人口：\n- 保障类型：\n\n## 2. 收入核查\n- 家庭月收入：\n- 收入来源：\n- 核查时间：\n\n## 3. 保障调整\n- 调整原因：\n- 调整金额：'
    },
    {
      name: '社区卫生服务站建设评估表模板',
      description: '社区卫生服务站建设情况评估的专用表格，涵盖硬件设施、人员配置、服务能力等评估指标',
      content: '# 社区卫生服务站建设评估表\n\n## 1. 基本情况\n- 服务站名称：\n- 建设地址：\n- 服务人口：\n- 建设投资：\n\n## 2. 硬件设施\n- 建筑面积：\n- 设备配置：\n- 信息化水平：\n\n## 3. 服务能力\n- 人员配置：\n- 服务项目：'
    },
    {
      name: '妇女儿童权益保护工作记录表模板',
      description: '妇女儿童权益保护工作的记录表格，包含案件信息、处理过程、保护措施、跟踪回访等内容',
      content: '# 妇女儿童权益保护工作记录表\n\n## 1. 案件基本信息\n- 案件编号：\n- 当事人信息：\n- 案件类型：\n- 接案时间：\n\n## 2. 处理过程\n- 调查情况：\n- 处理措施：\n- 协调部门：\n\n## 3. 保护效果\n- 保护措施：\n- 跟踪回访：'
    },
    {
      name: '义务教育阶段学生资助申请表模板',
      description: '义务教育阶段困难学生资助申请的标准表格，包含学生信息、家庭经济状况、资助需求等内容',
      content: '# 义务教育阶段学生资助申请表\n\n## 1. 学生基本信息\n- 学生姓名：\n- 学籍号：\n- 就读学校：\n- 年级班级：\n\n## 2. 家庭经济状况\n- 家庭月收入：\n- 主要困难：\n- 致贫原因：\n\n## 3. 资助申请\n- 申请资助类型：\n- 申请理由：'
    },
    {
      name: '社区养老服务需求评估表模板',
      description: '社区老年人养老服务需求评估的专业表格，包含身体状况、生活能力、服务需求等评估内容',
      content: '# 社区养老服务需求评估表\n\n## 1. 老人基本信息\n- 姓名：\n- 年龄：\n- 健康状况：\n- 家庭状况：\n\n## 2. 生活能力评估\n- 日常生活自理能力：\n- 认知能力：\n- 社交能力：\n\n## 3. 服务需求\n- 生活照料需求：\n- 医疗护理需求：\n- 精神慰藉需求：'
    },
    {
      name: '高龄老人补贴发放记录表模板',
      description: '80岁以上高龄老人补贴发放管理的标准表格，记录补贴对象信息、发放标准、发放时间、资金使用等详细数据',
      content: '# 高龄老人补贴发放记录表\n\n## 1. 补贴对象信息\n- 姓名：\n- 身份证号：\n- 年龄：\n- 联系方式：\n\n## 2. 补贴标准\n- 补贴类型：\n- 补贴金额：\n- 发放周期：\n\n## 3. 发放记录\n- 发放时间：\n- 发放方式：\n- 签收确认：'
    }
  ]

  // 生成随机日期（最近6个月内）
  const generateRandomDate = () => {
    const now = new Date()
    const sixMonthsAgo = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000)
    const randomTime = sixMonthsAgo.getTime() + Math.random() * (now.getTime() - sixMonthsAgo.getTime())
    const randomDate = new Date(randomTime)

    return `${randomDate.getFullYear()}.${(randomDate.getMonth() + 1).toString().padStart(2, '0')}.${randomDate.getDate().toString().padStart(2, '0')}`
  }

  // 生成23个模板
  for (let i = 0; i < 23; i++) {
    const template = templateData[i % templateData.length]
    mockData.push({
      id: `template_${i + 1}`,
      sequence: i + 1,
      templateName: template.name,
      description: template.description,
      content: template.content,
      creator: creators[Math.floor(Math.random() * creators.length)],
      createTime: generateRandomDate(),
      status: Math.random() > 0.15 // 85%的模板是启用状态
    })
  }

  // 按创建时间倒序排列（最新的在前面）
  return mockData.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
}

// 从本地存储加载数据
const loadTemplatesFromStorage = () => {
  const stored = localStorage.getItem(STORAGE_KEY)
  if (stored) {
    try {
      return JSON.parse(stored)
    } catch (e) {
      console.error('解析本地存储数据失败:', e)
    }
  }

  // 如果没有数据，初始化模拟数据并保存到localStorage
  const mockData = initMockData()
  saveTemplatesToStorage(mockData)
  return mockData
}

// 保存数据到本地存储
const saveTemplatesToStorage = (templates: Template[]) => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(templates))
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true

  try {
    // 模拟异步加载
    await new Promise(resolve => setTimeout(resolve, 300))

    const allTemplates = loadTemplatesFromStorage()

    // 应用搜索过滤
    let filteredData = allTemplates
    if (searchForm.templateName) {
      filteredData = filteredData.filter((item: Template) =>
        item.templateName.includes(searchForm.templateName)
      )
    }

    // 更新分页信息
    pagination.total = filteredData.length

    // 分页处理
    const start = (pagination.currentPage - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    tableData.value = filteredData.slice(start, end)
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.currentPage = 1
  loadTableData()
}

// 重置搜索
const handleReset = () => {
  searchForm.templateName = ''
  pagination.currentPage = 1
  loadTableData()
}

// 创建模板
const handleCreateTemplate = () => {
  dialogTitle.value = '新增模板'
  isEdit.value = false
  resetFormData()
  dialogVisible.value = true
}

// 重置表单数据
const resetFormData = () => {
  formData.templateName = ''
  formData.description = ''
  formData.content = ''
  formData.status = true
  currentTemplateId.value = ''
}

// 分页大小改变
const onPageSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadTableData()
}

// 当前页改变
const onCurrentPageChange = (page: number) => {
  pagination.currentPage = page
  loadTableData()
}

// 表格按钮点击处理
const onTableClickButton = (data: any) => {
  let btn, row

  if (data && typeof data === 'object') {
    if (data.row && data.btn) {
      row = data.row
      btn = data.btn
    } else if (data.btn && data.scope) {
      btn = data.btn
      row = data.scope
    } else {
      return
    }
  } else {
    return
  }

  if (!btn || !row) {
    return
  }

  const action = btn.code
  switch (action) {
    case 'edit':
      handleEditTemplate(row)
      break
    case 'delete':
      handleDeleteTemplate(row)
      break
  }
}

// 编辑模板
const handleEditTemplate = (row: Template) => {
  if (!row || !row.templateName) {
    ElMessage.error('编辑失败：模板数据无效')
    return
  }

  dialogTitle.value = '编辑模板'
  isEdit.value = true
  currentTemplateId.value = row.id

  // 填充表单数据
  formData.templateName = row.templateName
  formData.description = row.description
  formData.content = row.content
  formData.status = row.status

  dialogVisible.value = true
}

// 删除模板
const handleDeleteTemplate = (row: Template) => {
  if (!row || !row.templateName) {
    ElMessage.error('删除失败：模板数据无效')
    return
  }

  ElMessageBox.confirm(
    `确认删除模板"${row.templateName}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const allTemplates = loadTemplatesFromStorage()
    const filteredTemplates = allTemplates.filter((item: Template) => item.id !== row.id)
    saveTemplatesToStorage(filteredTemplates)
    loadTableData()
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

// 取消对话框
const handleCancel = () => {
  dialogVisible.value = false
  resetFormData()
}

// 确认提交
const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    const allTemplates = loadTemplatesFromStorage()

    if (isEdit.value) {
      // 编辑模式
      const index = allTemplates.findIndex((item: Template) => item.id === currentTemplateId.value)
      if (index !== -1) {
        allTemplates[index] = {
          ...allTemplates[index],
          templateName: formData.templateName,
          description: formData.description,
          content: formData.content,
          status: formData.status
        }
        ElMessage.success('编辑成功')
      }
    } else {
      // 新增模式
      const newTemplate: Template = {
        id: `template_${Date.now()}`,
        sequence: allTemplates.length + 1,
        templateName: formData.templateName,
        description: formData.description,
        content: formData.content,
        creator: '当前用户',
        createTime: new Date().toLocaleDateString('zh-CN').replace(/\//g, '.'),
        status: formData.status
      }
      allTemplates.push(newTemplate)
      ElMessage.success('新增成功')
    }

    saveTemplatesToStorage(allTemplates)
    loadTableData()
    dialogVisible.value = false
    resetFormData()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 组件挂载
onMounted(() => {
  loadTableData()
})
</script>

<style scoped lang="scss">
.template-management {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;

  .page-header {
    margin-bottom: 20px;

    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin: revert;
    }
  }

  .search-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .search-left {
      display: flex;
      gap: 15px;
      flex: 1;

      .search-input {
        width: 200px;
      }
    }

    .search-right {
      display: flex;
      gap: 10px;
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .template-form {
    .el-form-item {
      margin-bottom: 20px;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}
</style>

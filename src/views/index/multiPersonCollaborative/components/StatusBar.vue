<template>
  <div class="status-bar">
    <div class="status-container">
      <div class="status-title">实时在线状态</div>
      <div class="user-status-list">
        <div 
          v-for="user in onlineUsers" 
          :key="user.id"
          class="user-status-item"
          :class="{ 'current-user': user.isCurrent }"
        >
          <div class="user-avatar">
            <el-avatar :size="32" :src="user.avatar">
              {{ user.name.charAt(0) }}
            </el-avatar>
            <div class="status-indicator" :class="user.status"></div>
          </div>
          <div class="user-info">
            <span class="user-name">{{ user.name }}</span>
            <span v-if="user.isCurrent" class="current-label">(你)</span>
          </div>
        </div>
      </div>
      <div class="online-count">
        在线人数: {{ onlineUsers.length }}
      </div>

      <!-- 用户活动提示区域 -->
      <div class="activity-notifications">
        <!-- 用户活动提示 -->
        <transition name="slide-fade">
          <div
            v-if="currentActivity"
            class="activity-notification"
            :class="{ 'conflict': currentActivity.type === 'conflict' }"
          >
            <el-icon class="activity-icon">
              <EditPen v-if="currentActivity.type === 'editing'" />
              <Plus v-if="currentActivity.type === 'adding'" />
              <Warning v-if="currentActivity.type === 'conflict'" />
            </el-icon>
            <span class="activity-text">{{ currentActivity.message }}</span>
            <el-button
              type="text"
              size="small"
              class="close-btn"
              @click="dismissActivity"
            >
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { EditPen, Plus, Warning, Close } from '@element-plus/icons-vue'

interface User {
  id: string
  name: string
  status: 'online' | 'busy' | 'away'
  avatar?: string
  isCurrent: boolean
}

interface ActivityNotification {
  id: string
  type: 'editing' | 'adding' | 'conflict'
  message: string
  timestamp: number
}

// 在线用户列表
const onlineUsers = ref<User[]>([
  {
    id: '1',
    name: '张东',
    status: 'online',
    isCurrent: true
  },
  {
    id: '2',
    name: '杨绍东',
    status: 'online',
    isCurrent: false
  },
  {
    id: '3',
    name: '王明芳',
    status: 'online',
    isCurrent: false
  }
])

// 当前活动通知
const currentActivity = ref<ActivityNotification | null>(null)

// 活动通知队列
const activityQueue = ref<ActivityNotification[]>([])

// 定时器引用
let activityTimer: NodeJS.Timeout | null = null
let conflictTimer: NodeJS.Timeout | null = null

// 预设的活动消息
const activityMessages = [
  { type: 'editing' as const, message: '林峰 正在编辑"基础信息收集表"' },
  { type: 'adding' as const, message: '王明芳 正在新增"老年人信息采集表"' },
  { type: 'editing' as const, message: '杨绍东 正在编辑"辖区居民业务数据表"' },
  { type: 'adding' as const, message: '钱飒 正在新增"教育活动记录表"' },
  { type: 'editing' as const, message: '孙晓 正在编辑"地质风险区域排查表"' }
]

const conflictMessages = [
  { type: 'conflict' as const, message: '检测到与林峰同时编辑"基础信息收集表"的字段冲突' },
  { type: 'conflict' as const, message: '检测到与王明芳同时编辑"老年人信息采集表"的字段冲突' },
  { type: 'conflict' as const, message: '检测到与杨绍东同时编辑"辖区居民业务数据表"的字段冲突' }
]

// 显示活动通知
const showActivityNotification = (activity: Omit<ActivityNotification, 'id' | 'timestamp'>) => {
  const notification: ActivityNotification = {
    id: `activity_${Date.now()}`,
    timestamp: Date.now(),
    ...activity
  }

  currentActivity.value = notification

  // 5秒后自动消失
  setTimeout(() => {
    if (currentActivity.value?.id === notification.id) {
      currentActivity.value = null
    }
  }, 5000)
}

// 手动关闭活动通知
const dismissActivity = () => {
  currentActivity.value = null
}

// 生成随机活动通知
const generateRandomActivity = () => {
  const messages = Math.random() > 0.7 ? conflictMessages : activityMessages
  const randomMessage = messages[Math.floor(Math.random() * messages.length)]
  showActivityNotification(randomMessage)
}

// 启动活动通知定时器
const startActivityTimer = () => {
  // 每8-15秒显示一个用户活动提示
  const scheduleNext = () => {
    const delay = Math.random() * 7000 + 8000 // 8-15秒
    activityTimer = setTimeout(() => {
      if (!currentActivity.value) { // 只有在没有当前活动时才显示新的
        generateRandomActivity()
      }
      scheduleNext()
    }, delay)
  }
  scheduleNext()
}

// 启动冲突检测定时器
const startConflictTimer = () => {
  // 每20-40秒可能显示一个冲突提示
  const scheduleNext = () => {
    const delay = Math.random() * 20000 + 20000 // 20-40秒
    conflictTimer = setTimeout(() => {
      if (!currentActivity.value && Math.random() > 0.6) { // 40%概率显示冲突
        const conflictMessage = conflictMessages[Math.floor(Math.random() * conflictMessages.length)]
        showActivityNotification(conflictMessage)
      }
      scheduleNext()
    }, delay)
  }
  scheduleNext()
}

// 模拟实时更新用户状态
const updateUserStatus = () => {
  // 这里可以添加WebSocket或其他实时通信逻辑
  console.log('更新用户状态')
}

// 清理定时器
const clearTimers = () => {
  if (activityTimer) {
    clearTimeout(activityTimer)
    activityTimer = null
  }
  if (conflictTimer) {
    clearTimeout(conflictTimer)
    conflictTimer = null
  }
}

onMounted(() => {
  // 初始化状态更新
  updateUserStatus()

  // 启动活动通知定时器
  startActivityTimer()
  startConflictTimer()

  // 3秒后显示第一个活动通知
  setTimeout(() => {
    generateRandomActivity()
  }, 3000)
})

onUnmounted(() => {
  clearTimers()
})
</script>

<style scoped lang="scss">
.status-bar {
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .status-container {
    display: flex;
    align-items: center;
    width: 100%;
    
    .status-title {
      color: #ffffff;
      font-size: 16px;
      font-weight: 600;
      margin-right: 30px;
      white-space: nowrap;
    }
    
    .user-status-list {
      display: flex;
      align-items: center;
      gap: 20px;
      flex: 1;
      
      .user-status-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        border-radius: 20px;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        
        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-1px);
        }
        
        &.current-user {
          background: rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .user-avatar {
          position: relative;
          
          .status-indicator {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            border: 2px solid #ffffff;
            
            &.online {
              background-color: #67c23a;
            }
            
            &.busy {
              background-color: #f56c6c;
            }
            
            &.away {
              background-color: #e6a23c;
            }
          }
        }
        
        .user-info {
          display: flex;
          align-items: center;
          gap: 4px;
          
          .user-name {
            color: #ffffff;
            font-size: 14px;
            font-weight: 500;
          }
          
          .current-label {
            color: #ffffff;
            font-size: 12px;
            opacity: 0.8;
          }
        }
      }
    }
    
    .online-count {
      color: #ffffff;
      font-size: 14px;
      font-weight: 500;
      padding: 6px 12px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      backdrop-filter: blur(10px);
      margin-right: 20px;
    }

    .activity-notifications {
      margin-left: auto;

      .activity-notification {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        min-width: 280px;
        max-width: 400px;

        &.conflict {
          background: rgba(245, 108, 108, 0.95);
          border-color: rgba(245, 108, 108, 0.3);

          .activity-text {
            color: #ffffff;
          }

          .activity-icon {
            color: #ffffff;
          }

          .close-btn {
            color: #ffffff;

            &:hover {
              background: rgba(255, 255, 255, 0.2);
            }
          }
        }

        .activity-icon {
          color: #409eff;
          font-size: 16px;
          flex-shrink: 0;
        }

        .activity-text {
          color: #303133;
          font-size: 13px;
          font-weight: 500;
          flex: 1;
          line-height: 1.4;
        }

        .close-btn {
          color: #909399;
          padding: 2px;
          margin: 0;
          min-height: auto;

          &:hover {
            background: rgba(0, 0, 0, 0.1);
            color: #606266;
          }

          .el-icon {
            font-size: 12px;
          }
        }
      }
    }
  }
}

// 动画效果
.slide-fade-enter-active {
  transition: all 0.4s ease;
}

.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

// 响应式设计
@media (max-width: 768px) {
  .status-bar {
    height: 50px;
    padding: 0 15px;
    
    .status-container {
      .status-title {
        font-size: 14px;
        margin-right: 20px;
      }
      
      .user-status-list {
        gap: 15px;
        
        .user-status-item {
          padding: 6px 10px;
          
          .user-info {
            .user-name {
              font-size: 13px;
            }
          }
        }
      }
      
      .online-count {
        font-size: 13px;
        padding: 4px 8px;
        margin-right: 15px;
      }

      .activity-notifications {
        .activity-notification {
          min-width: 240px;
          max-width: 300px;
          padding: 6px 12px;

          .activity-text {
            font-size: 12px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .status-bar {
    .status-container {
      .status-title {
        display: none;
      }
      
      .user-status-list {
        gap: 10px;
        
        .user-status-item {
          .user-info {
            .current-label {
              display: none;
            }
          }
        }
      }

      .activity-notifications {
        .activity-notification {
          min-width: 200px;
          max-width: 250px;
          padding: 4px 8px;

          .activity-text {
            font-size: 11px;
          }

          .activity-icon {
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>

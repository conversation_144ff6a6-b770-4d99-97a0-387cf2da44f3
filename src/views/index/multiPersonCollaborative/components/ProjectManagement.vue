<template>
  <div class="project-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">项目管理</h2>
    </div>
    
    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <div class="search-left">
        <el-input
          v-model="searchForm.projectName"
          placeholder="请输入项目名称"
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-input
          v-model="searchForm.businessType"
          placeholder="关联业务类"
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
        >
        </el-input>
        
        <el-input
          v-model="searchForm.department"
          placeholder="所属分类"
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
        >
        </el-input>
      </div>
      
      <div class="search-right">
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          查询
        </el-button>
        <el-button @click="handleReset">
          重置
        </el-button>
        <el-button type="primary" @click="handleCreateProject">
          创建项目
        </el-button>
      </div>
    </div>
    
    <!-- 项目列表表格 -->
    <TableV2
      ref="tableRef"
      :defaultTableData="tableData"
      :columns="columns"
      :enable-toolbar="false"
      :enable-own-button="true"
      :enable-selection="false"
      :enable-index="true"
      :height="400"
      :buttons="buttons"
      :loading="loading"
      @click-button="onTableClickButton"
    >
      <template #status="{ row }">
        <el-switch
          v-model="row.status"
          :active-value="true"
          :inactive-value="false"
          @change="handleStatusChange(row)"
        />
      </template>
    </TableV2>

    <!-- 分页组件 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onPageSizeChange"
        @current-change="onCurrentPageChange"
      />
    </div>
    
    <!-- 创建项目对话框 -->
    <CreateProjectDialog
      v-model:visible="createDialogVisible"
      @success="handleCreateSuccess"
    />

    <!-- 查看项目对话框 -->
    <ViewProjectDialog
      :visible="viewDialogVisible"
      :project-data="currentProject"
      @update:visible="viewDialogVisible = $event"
      @edit="handleEditFromView"
    />

    <!-- 编辑项目对话框 -->
    <EditProjectDialog
      :visible="editDialogVisible"
      :project-data="currentProject"
      @update:visible="editDialogVisible = $event"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import CreateProjectDialog from './CreateProjectDialog.vue'
import ViewProjectDialog from './ViewProjectDialog.vue'
import EditProjectDialog from './EditProjectDialog.vue'

interface Project {
  id: string
  sequence: number
  projectName: string
  businessType: string
  department: string
  status: boolean
  creator: string
  createTime: string
}

interface SearchForm {
  projectName: string
  businessType: string
  department: string
}

// 搜索表单
const searchForm = reactive<SearchForm>({
  projectName: '',
  businessType: '',
  department: ''
})

// 表格数据
const tableData = ref<Project[]>([])

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 对话框显示状态
const createDialogVisible = ref(false)
const viewDialogVisible = ref(false)
const editDialogVisible = ref(false)

// 当前操作的项目数据
const currentProject = ref<Project>({
  id: '',
  sequence: 0,
  projectName: '',
  businessType: '',
  department: '',
  status: false,
  creator: '',
  createTime: ''
})

// 加载状态
const loading = ref(false)

// TableV2 列配置
const columns = ref([
  {
    prop: 'projectName',
    label: '项目名称',
    minWidth: 150
  },
  {
    prop: 'businessType',
    label: '关联业务表',
    minWidth: 120
  },
  {
    prop: 'department',
    label: '所属分类',
    minWidth: 100
  },
  {
    prop: 'status',
    label: '状态',
    width: 80,
    slot: 'status'
  },
  {
    prop: 'creator',
    label: '创建人',
    width: 100
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 120
  }
])

// TableV2 操作按钮配置
const buttons = ref([
  {
    label: '查看',
    type: 'primary',
    code: 'view'
  },
  {
    label: '编辑',
    type: 'warning',
    code: 'edit'
  },
  {
    label: '删除',
    type: 'danger',
    code: 'delete',
    popconfirm: '确认删除吗?'
  }
])

// 初始化模拟数据
const initMockData = () => {
  const mockData: Project[] = []

  // 真实的部门名称
  const departments = [
    '社区服务中心', '民政事务部', '卫生健康局', '教育局',
    '应急管理局', '人力资源部', '社会保障局', '统计局'
  ]

  // 真实的创建人员
  const creators = [
    '张建国', '李明华', '王秀英', '刘志强', '陈美玲', '杨国庆', '赵小红', '黄建军',
    '周丽娟', '吴永强', '徐海燕', '孙志华', '胡晓明', '朱建平', '高秀芳', '林志远',
    '何丽华', '郭建设', '马小军', '罗美英', '梁志刚', '宋晓丽', '郑国强', '谢秀珍'
  ]

  // 真实的项目名称模板
  const projectTemplates = [
    { name: '2024年度社区居民基础信息普查项目', type: '基础信息收集' },
    { name: '辖区老年人健康状况调研项目', type: '老年人信息采集' },
    { name: '社区居民就业情况统计项目', type: '辖区居民业务数据' },
    { name: '青少年素质教育活动记录项目', type: '辖区教育活动记录' },
    { name: '地质灾害隐患点排查项目', type: '辖区地质风险区域排查' },
    { name: '2024年新生儿出生登记项目', type: '新生儿信息登记' },
    { name: '困难家庭社会救助统计项目', type: '辖区社会救助花名册' },
    { name: '80岁以上高龄老人补贴项目', type: '辖区高龄老人花名册' },
    { name: '重大疾病医疗救助项目', type: '辖区大病救助记录' },
    { name: '孕产妇健康管理项目', type: '孕产妇信息采集' },
    { name: '残疾人康复服务需求调查', type: '残疾人信息统计' },
    { name: '城乡低保对象动态管理', type: '低保户信息管理' },
    { name: '外来务工人员信息登记', type: '流动人口登记' },
    { name: '社区养老服务需求调研', type: '老年人信息采集' },
    { name: '义务教育阶段学生资助', type: '辖区教育活动记录' },
    { name: '防汛抗旱应急预案更新', type: '辖区地质风险区域排查' },
    { name: '社区卫生服务站建设', type: '基础信息收集' },
    { name: '妇女儿童权益保护项目', type: '孕产妇信息采集' },
    { name: '退役军人服务保障项目', type: '辖区居民业务数据' },
    { name: '社区文化活动中心建设', type: '辖区教育活动记录' },
    { name: '环境卫生整治专项行动', type: '基础信息收集' },
    { name: '食品安全监管信息系统', type: '辖区居民业务数据' },
    { name: '公共租赁住房申请管理', type: '辖区社会救助花名册' },
    { name: '社区志愿者服务队建设', type: '基础信息收集' },
    { name: '特困人员供养服务项目', type: '辖区社会救助花名册' }
  ]

  // 生成随机日期（最近3个月内）
  const generateRandomDate = () => {
    const now = new Date()
    const threeMonthsAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
    const randomTime = threeMonthsAgo.getTime() + Math.random() * (now.getTime() - threeMonthsAgo.getTime())
    const randomDate = new Date(randomTime)

    return `${randomDate.getFullYear()}.${(randomDate.getMonth() + 1).toString().padStart(2, '0')}.${randomDate.getDate().toString().padStart(2, '0')}`
  }

  // 生成25个真实项目
  for (let i = 1; i <= 25; i++) {
    const template = projectTemplates[Math.floor(Math.random() * projectTemplates.length)]

    mockData.push({
      id: `project_${i}`,
      sequence: i,
      projectName: template.name,
      businessType: template.type,
      department: departments[Math.floor(Math.random() * departments.length)],
      status: Math.random() > 0.2, // 80%的项目是启用状态
      creator: creators[Math.floor(Math.random() * creators.length)],
      createTime: generateRandomDate()
    })
  }

  // 按创建时间倒序排列（最新的在前面）
  return mockData.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
}

// 从本地存储加载数据
const loadProjectsFromStorage = () => {
  const stored = localStorage.getItem('collaborative_projects')
  if (stored) {
    try {
      return JSON.parse(stored)
    } catch (e) {
      console.error('解析本地存储数据失败:', e)
    }
  }

  // 如果没有数据，初始化模拟数据并保存到localStorage
  const mockData = initMockData()
  saveProjectsToStorage(mockData)
  return mockData
}

// 保存数据到本地存储
const saveProjectsToStorage = (projects: Project[]) => {
  localStorage.setItem('collaborative_projects', JSON.stringify(projects))
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true

  try {
    // 模拟异步加载
    await new Promise(resolve => setTimeout(resolve, 300))

    const allProjects = loadProjectsFromStorage()

    // 应用搜索过滤
    let filteredData = allProjects
    if (searchForm.projectName) {
      filteredData = filteredData.filter((item: Project) =>
        item.projectName.includes(searchForm.projectName)
      )
    }
    if (searchForm.businessType) {
      filteredData = filteredData.filter((item: Project) =>
        item.businessType.includes(searchForm.businessType)
      )
    }
    if (searchForm.department) {
      filteredData = filteredData.filter((item: Project) =>
        item.department.includes(searchForm.department)
      )
    }

    // 更新分页信息
    pagination.total = filteredData.length

    // 分页处理
    const start = (pagination.currentPage - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    tableData.value = filteredData.slice(start, end)
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.currentPage = 1
  loadTableData()
}

// 重置搜索
const handleReset = () => {
  searchForm.projectName = ''
  searchForm.businessType = ''
  searchForm.department = ''
  pagination.currentPage = 1
  loadTableData()
}

// 创建项目
const handleCreateProject = () => {
  createDialogVisible.value = true
}

// 创建成功回调
const handleCreateSuccess = () => {
  loadTableData()
}

// 查看项目
const handleViewProject = (row: Project) => {
  // 数据验证
  if (!row || !row.projectName) {
    console.error('查看项目失败：行数据无效', row)
    ElMessage.error('查看失败：项目数据无效')
    return
  }

  currentProject.value = { ...row }
  viewDialogVisible.value = true
}

// 编辑项目
const handleEditProject = (row: Project) => {
  // 数据验证
  if (!row || !row.projectName) {
    console.error('编辑项目失败：行数据无效', row)
    ElMessage.error('编辑失败：项目数据无效')
    return
  }

  currentProject.value = { ...row }
  editDialogVisible.value = true
}

// 从查看对话框跳转到编辑
const handleEditFromView = (projectData: Project) => {
  currentProject.value = { ...projectData }
  viewDialogVisible.value = false
  editDialogVisible.value = true
}

// 编辑成功回调
const handleEditSuccess = () => {
  loadTableData()
}

// 状态切换处理
const handleStatusChange = (row: Project) => {
  const allProjects = loadProjectsFromStorage()
  const index = allProjects.findIndex((item: Project) => item.id === row.id)
  if (index !== -1) {
    allProjects[index].status = row.status
    saveProjectsToStorage(allProjects)
  }
}

// TableV2 按钮点击处理
const onTableClickButton = (data: any) => {
  // 根据实际的TableV2实现，数据结构应该是 {row, btn}
  let btn, row

  if (data && typeof data === 'object') {
    if (data.row && data.btn) {
      // 标准格式: {row, btn}
      row = data.row
      btn = data.btn
    } else if (data.btn && data.scope) {
      // 备用格式: {btn, scope}
      btn = data.btn
      row = data.scope
    } else {
      return
    }
  } else {
    return
  }

  if (!btn || !row) {
    return
  }

  const action = btn.code

  switch (action) {
    case 'view':
      handleViewProject(row)
      break
    case 'edit':
      handleEditProject(row)
      break
    case 'delete':
      handleDeleteProject(row)
      break
  }
}

// 删除项目
const handleDeleteProject = (row: Project) => {
  // 数据验证
  if (!row || !row.projectName) {
    console.error('删除项目失败：行数据无效', row)
    ElMessage.error('删除失败：项目数据无效')
    return
  }

  ElMessageBox.confirm(
    `确定要删除项目"${row.projectName}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const allProjects = loadProjectsFromStorage()
    const filteredProjects = allProjects.filter((item: Project) => item.id !== row.id)

    // 重新排序序号
    filteredProjects.forEach((project: Project, index: number) => {
      project.sequence = index + 1
    })

    saveProjectsToStorage(filteredProjects)
    loadTableData()
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

// 分页大小改变
const onPageSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadTableData()
}

// 当前页改变
const onCurrentPageChange = (page: number) => {
  pagination.currentPage = page
  loadTableData()
}

onMounted(() => {
  loadTableData()
})
</script>

<style scoped lang="scss">
.project-management {
  .page-header {
    margin-bottom: 20px;
    
    .page-title {
      margin: revert;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .search-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    .search-left {
      display: flex;
      gap: 15px;
      flex: 1;
      
      .search-input {
        width: 200px;
      }
    }
    
    .search-right {
      display: flex;
      gap: 10px;
      margin-left: 20px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .project-management {
    .search-section {
      .search-left {
        .search-input {
          width: 180px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .project-management {
    .page-header {
      .page-title {
        font-size: 20px;
      }
    }

    .search-section {
      flex-direction: column;
      gap: 15px;
      padding: 15px;

      .search-left {
        width: 100%;
        flex-wrap: wrap;
        gap: 10px;

        .search-input {
          width: 100%;
          min-width: 150px;
        }
      }

      .search-right {
        width: 100%;
        justify-content: flex-start;
        margin-left: 0;
        flex-wrap: wrap;
        gap: 8px;
      }
    }
  }
}

@media (max-width: 480px) {
  .project-management {
    .page-header {
      margin-bottom: 15px;

      .page-title {
        font-size: 18px;
      }
    }

    .search-section {
      padding: 12px;

      .search-right {
        :deep(.el-button) {
          flex: 1;
          min-width: 80px;
        }
      }
    }
  }
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 0 20px;
}
</style>

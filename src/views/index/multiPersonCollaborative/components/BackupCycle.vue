<template>
  <div class="backup-cycle">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">备份周期</h2>
      <p class="page-description">配置数据备份的周期和策略</p>
    </div>

    <!-- 配置表单 -->
    <div class="backup-config-form">
      <el-card class="config-card">
        <template #header>
          <div class="card-header">
            <span>备份配置</span>
            <el-button type="primary" @click="showConfigDialog = true">
              <el-icon><Plus /></el-icon>
              新建配置
            </el-button>
          </div>
        </template>

        <!-- 配置列表 -->
        <div v-if="isLoading" class="loading-container">
          <el-icon class="is-loading" size="32">
            <Loading />
          </el-icon>
          <p>正在加载配置...</p>
        </div>

        <div v-else-if="backupConfigs.length === 0" class="empty-state">
          <el-empty description="暂无备份配置">
            <el-button type="primary" @click="showConfigDialog = true">创建第一个配置</el-button>
          </el-empty>
        </div>

        <div v-else class="config-list">
          <div
            v-for="config in backupConfigs"
            :key="config.id"
            class="config-item"
          >
            <div class="config-info">
              <div class="config-title">备份配置 #{{ config.id.slice(-6) }}</div>
              <div class="config-details">
                <div class="detail-item">
                  <span class="label">开始时间：</span>
                  <span class="value">{{ config.startDate }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">全量备份：</span>
                  <span class="value">{{ getFrequencyText(config.fullBackupFrequency) }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">增量备份：</span>
                  <span class="value">{{ getFrequencyText(config.incrementalBackupFrequency) }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">清理策略：</span>
                  <span class="value">{{ getRetentionText(config.dataRetentionPolicy) }}</span>
                </div>
              </div>
            </div>
            <div class="config-actions">
              <el-button size="small" @click="editConfig(config)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteConfig(config.id)">删除</el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 配置对话框 -->
    <el-dialog
      v-model="showConfigDialog"
      :title="isEditing ? '编辑备份配置' : '新建备份配置'"
      width="500px"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="140px"
        label-position="left"
      >
        <el-form-item label="数据备份开始时间" prop="startDate">
          <el-date-picker
            v-model="formData.startDate"
            type="date"
            placeholder="请选择日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="全量备份频率" prop="fullBackupFrequency">
          <el-select v-model="formData.fullBackupFrequency" placeholder="下拉选择" style="width: 100%">
            <el-option label="每小时" value="hourly" />
            <el-option label="每日" value="daily" />
            <el-option label="每周" value="weekly" />
            <el-option label="每月" value="monthly" />
          </el-select>
        </el-form-item>

        <el-form-item label="增量备份频率" prop="incrementalBackupFrequency">
          <el-select v-model="formData.incrementalBackupFrequency" placeholder="下拉选择" style="width: 100%">
            <el-option label="每小时" value="hourly" />
            <el-option label="每日" value="daily" />
            <el-option label="每周" value="weekly" />
            <el-option label="每月" value="monthly" />
          </el-select>
        </el-form-item>

        <el-form-item label="备份数据清理策略" prop="dataRetentionPolicy">
          <el-select v-model="formData.dataRetentionPolicy" placeholder="下拉选择" style="width: 100%">
            <el-option label="保留30天" value="30days" />
            <el-option label="保留60天" value="60days" />
            <el-option label="保留120天" value="120days" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleConfirm" :loading="saving">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Loading } from '@element-plus/icons-vue'
import { IndexedDBManager } from '../../../../utils/indexedDB'

// 备份配置接口定义
interface BackupConfig {
  id: string
  startDate: string
  fullBackupFrequency: string
  incrementalBackupFrequency: string
  dataRetentionPolicy: string
  createdAt: string
  updatedAt: string
}

// IndexedDB 管理器
const dbManager = new IndexedDBManager('BackupConfigDB', 1)

// 响应式数据
const showConfigDialog = ref(false)
const isLoading = ref(true)
const saving = ref(false)
const isEditing = ref(false)
const editingId = ref('')
const backupConfigs = ref<BackupConfig[]>([])
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  startDate: '',
  fullBackupFrequency: '',
  incrementalBackupFrequency: '',
  dataRetentionPolicy: ''
})

// 表单验证规则
const formRules: FormRules = {
  startDate: [
    { required: true, message: '请选择备份开始时间', trigger: 'change' }
  ],
  fullBackupFrequency: [
    { required: true, message: '请选择全量备份频率', trigger: 'change' }
  ],
  incrementalBackupFrequency: [
    { required: true, message: '请选择增量备份频率', trigger: 'change' }
  ],
  dataRetentionPolicy: [
    { required: true, message: '请选择数据清理策略', trigger: 'change' }
  ]
}

// 从数据库加载配置
const loadConfigs = async () => {
  try {
    isLoading.value = true
    await dbManager.init()
    const configs = await dbManager.getAllBackupConfigs()
    backupConfigs.value = configs.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  } catch (error) {
    console.error('Failed to load backup configs:', error)
    ElMessage.error('加载备份配置失败')
  } finally {
    isLoading.value = false
  }
}

// 获取频率文本
const getFrequencyText = (frequency: string): string => {
  const frequencyMap: Record<string, string> = {
    hourly: '每小时',
    daily: '每日',
    weekly: '每周',
    monthly: '每月'
  }
  return frequencyMap[frequency] || frequency
}

// 获取保留策略文本
const getRetentionText = (policy: string): string => {
  const policyMap: Record<string, string> = {
    '30days': '保留30天',
    '60days': '保留60天',
    '120days': '保留120天'
  }
  return policyMap[policy] || policy
}

// 编辑配置
const editConfig = (config: BackupConfig) => {
  isEditing.value = true
  editingId.value = config.id
  formData.startDate = config.startDate
  formData.fullBackupFrequency = config.fullBackupFrequency
  formData.incrementalBackupFrequency = config.incrementalBackupFrequency
  formData.dataRetentionPolicy = config.dataRetentionPolicy
  showConfigDialog.value = true
}

// 删除配置
const deleteConfig = async (id: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个备份配置吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await dbManager.deleteBackupConfig(id)
    await loadConfigs()
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete backup config:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 处理对话框关闭
const handleDialogClose = () => {
  showConfigDialog.value = false
  isEditing.value = false
  editingId.value = ''
  formRef.value?.resetFields()
}

// 处理确认
const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    const now = new Date().toISOString()
    const config: BackupConfig = {
      id: isEditing.value ? editingId.value : `backup_${Date.now()}`,
      startDate: formData.startDate,
      fullBackupFrequency: formData.fullBackupFrequency,
      incrementalBackupFrequency: formData.incrementalBackupFrequency,
      dataRetentionPolicy: formData.dataRetentionPolicy,
      createdAt: isEditing.value ? backupConfigs.value.find(c => c.id === editingId.value)?.createdAt || now : now,
      updatedAt: now
    }

    await dbManager.saveBackupConfig(config)
    await loadConfigs()

    ElMessage.success(isEditing.value ? '更新成功' : '创建成功')
    handleDialogClose()
  } catch (error) {
    console.error('Failed to save backup config:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 生命周期
onMounted(() => {
  loadConfigs()
})
</script>

<style scoped lang="scss">
.backup-cycle {
  .page-header {
    margin-bottom: 30px;

    .page-title {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .page-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .backup-config-form {
    .config-card {
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;
        color: #303133;
      }

      .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 20px;
        color: #909399;

        p {
          margin-top: 16px;
          font-size: 14px;
        }
      }

      .empty-state {
        padding: 60px 20px;
        text-align: center;
      }

      .config-list {
        .config-item {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          padding: 20px;
          border: 1px solid #ebeef5;
          border-radius: 8px;
          margin-bottom: 16px;
          background: #fafafa;
          transition: all 0.3s ease;

          &:hover {
            background: #f5f7fa;
            border-color: #c6e2ff;
          }

          &:last-child {
            margin-bottom: 0;
          }

          .config-info {
            flex: 1;

            .config-title {
              font-size: 16px;
              font-weight: 600;
              color: #303133;
              margin-bottom: 12px;
            }

            .config-details {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 8px;

              .detail-item {
                display: flex;
                align-items: center;
                font-size: 14px;

                .label {
                  color: #606266;
                  margin-right: 8px;
                  min-width: 80px;
                }

                .value {
                  color: #303133;
                  font-weight: 500;
                }
              }
            }
          }

          .config-actions {
            display: flex;
            gap: 8px;
            margin-left: 20px;
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .backup-cycle {
    .backup-config-form {
      .config-card {
        .config-list {
          .config-item {
            flex-direction: column;
            align-items: stretch;

            .config-info {
              margin-bottom: 16px;

              .config-details {
                grid-template-columns: 1fr;
              }
            }

            .config-actions {
              margin-left: 0;
              justify-content: flex-end;
            }
          }
        }
      }
    }
  }
}
</style>

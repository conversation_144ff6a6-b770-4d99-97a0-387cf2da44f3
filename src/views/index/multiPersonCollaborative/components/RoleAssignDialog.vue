<template>
  <DialogComp
    v-model:visible="dialogVisible"
    title="角色分配"
    width="600px"
    :visibleFooterButton="false"
    @closed="handleClose"
  >
    <div class="role-assign-dialog">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="姓名">
          {{ props.memberData?.name || '暂无数据' }}
        </el-descriptions-item>
        <el-descriptions-item label="所属部门">
          {{ props.memberData?.department || '暂无数据' }}
        </el-descriptions-item>
        <el-descriptions-item label="当前角色">
          {{ props.memberData?.teamRole || '暂无数据' }}
        </el-descriptions-item>
        <el-descriptions-item label="审批时间">
          {{ props.memberData?.approveTime || '暂无数据' }}
        </el-descriptions-item>
      </el-descriptions>

      <div class="role-section">
        <h4>角色分配</h4>
        <el-form :model="roleForm" :rules="formRules" ref="formRef" label-width="100px">
          <el-form-item label="团队角色" prop="teamRole">
            <el-select
              v-model="roleForm.teamRole"
              placeholder="请选择团队角色"
              style="width: 100%"
            >
              <el-option label="项目经理" value="项目经理" />
              <el-option label="技术负责人" value="技术负责人" />
              <el-option label="前端工程师" value="前端工程师" />
              <el-option label="后端工程师" value="后端工程师" />
              <el-option label="全栈工程师" value="全栈工程师" />
              <el-option label="UI设计师" value="UI设计师" />
              <el-option label="UX设计师" value="UX设计师" />
              <el-option label="产品经理" value="产品经理" />
              <el-option label="测试工程师" value="测试工程师" />
              <el-option label="运维工程师" value="运维工程师" />
              <el-option label="数据分析师" value="数据分析师" />
              <el-option label="运营专员" value="运营专员" />
            </el-select>
          </el-form-item>
          <el-form-item label="权限级别" prop="permissionLevel">
            <el-select
              v-model="roleForm.permissionLevel"
              placeholder="请选择权限级别"
              style="width: 100%"
            >
              <el-option label="管理员" value="admin" />
              <el-option label="高级用户" value="senior" />
              <el-option label="普通用户" value="normal" />
              <el-option label="只读用户" value="readonly" />
            </el-select>
          </el-form-item>
          <el-form-item label="备注说明" prop="remark">
            <el-input
              v-model="roleForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注说明（可选）"
              maxlength="150"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'

interface TeamMember {
  id: string
  sequence: number
  name: string
  department: string
  teamRole: string
  status: 'pending' | 'approved' | 'rejected'
  applyTime: string
  approveTime?: string
  approver?: string
  permissionLevel?: string
  remark?: string
}

interface Props {
  visible: boolean
  memberData: TeamMember | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirmed', member: TeamMember): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref()

// 角色表单
const roleForm = reactive({
  teamRole: '',
  permissionLevel: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  teamRole: [
    { required: true, message: '请选择团队角色', trigger: 'change' }
  ],
  permissionLevel: [
    { required: true, message: '请选择权限级别', trigger: 'change' }
  ]
}

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听对话框打开，初始化表单
watch(() => props.visible, (newVal) => {
  if (newVal && props.memberData) {
    roleForm.teamRole = props.memberData.teamRole || ''
    roleForm.permissionLevel = props.memberData.permissionLevel || 'normal'
    roleForm.remark = props.memberData.remark || ''
    formRef.value?.clearValidate()
  }
})

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 处理关闭
const handleClose = () => {
  emit('update:visible', false)
}

// 处理确认
const handleConfirm = async () => {
  if (!props.memberData) {
    ElMessage.error('成员数据无效')
    return
  }

  try {
    await formRef.value?.validate()
    
    const updatedMember: TeamMember = {
      ...props.memberData,
      teamRole: roleForm.teamRole,
      permissionLevel: roleForm.permissionLevel,
      remark: roleForm.remark
    }
    
    emit('confirmed', updatedMember)
    dialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}
</script>

<style scoped lang="scss">
.role-assign-dialog {
  .role-section {
    margin-top: 24px;
    
    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>

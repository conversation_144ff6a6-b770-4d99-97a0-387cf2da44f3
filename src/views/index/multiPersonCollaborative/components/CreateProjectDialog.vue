<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建项目"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
    class="create-project-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="project-form"
    >
      <el-form-item label="项目名称:" prop="projectName" required>
        <el-input
          v-model="formData.projectName"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="项目所属分类:" prop="category">
        <el-input
          v-model="formData.category"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="关联业务表:" prop="businessType" required>
        <el-select
          v-model="formData.businessType"
          placeholder="请选择"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="item in businessTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="版本:" prop="version" required>
        <el-input
          v-model="formData.version"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="备注:" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="4"
          placeholder="请输入"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="状态:" prop="status">
        <el-switch
          v-model="formData.status"
          :active-value="true"
          :inactive-value="false"
        />
      </el-form-item>
      
      <el-form-item label="项目信息更新通知:" prop="updateNotification">
        <el-switch
          v-model="formData.updateNotification"
          :active-value="true"
          :inactive-value="false"
        />
      </el-form-item>
      
      <el-form-item label="通知方式:" prop="notificationMethods">
        <el-checkbox-group v-model="formData.notificationMethods">
          <el-checkbox label="站内信" value="internal" />
          <el-checkbox label="短信提醒" value="sms" />
          <el-checkbox label="邮件DING" value="email" />
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'

interface ProjectFormData {
  projectName: string
  category: string
  businessType: string
  version: string
  remark: string
  status: boolean
  updateNotification: boolean
  notificationMethods: string[]
}

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 对话框显示状态
const dialogVisible = ref(false)
const loading = ref(false)

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive<ProjectFormData>({
  projectName: '',
  category: '',
  businessType: '',
  version: '',
  remark: '',
  status: true,
  updateNotification: true,
  notificationMethods: ['internal']
})

// 业务类型选项
const businessTypeOptions = ref([
  { label: '基础信息收集', value: '基础信息收集' },
  { label: '老年人信息采集', value: '老年人信息采集' },
  { label: '辖区居民业务数据', value: '辖区居民业务数据' },
  { label: '辖区教育活动记录', value: '辖区教育活动记录' },
  { label: '辖区地质风险区域排查', value: '辖区地质风险区域排查' },
  { label: '新生儿信息登记', value: '新生儿信息登记' },
  { label: '辖区社会救助花名册', value: '辖区社会救助花名册' },
  { label: '辖区高龄老人花名册', value: '辖区高龄老人花名册' },
  { label: '辖区大病救助记录', value: '辖区大病救助记录' },
  { label: '孕产妇信息采集', value: '孕产妇信息采集' }
])

// 表单验证规则
const formRules: FormRules = {
  projectName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 50, message: '项目名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  businessType: [
    { required: true, message: '请选择关联业务类', trigger: 'change' }
  ],
  version: [
    { required: true, message: '请输入版本', trigger: 'blur' },
    { pattern: /^[0-9.]+$/, message: '版本格式不正确', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 重置表单
const resetForm = () => {
  formData.projectName = ''
  formData.category = ''
  formData.businessType = ''
  formData.version = ''
  formData.remark = ''
  formData.status = true
  formData.updateNotification = true
  formData.notificationMethods = ['internal']
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 生成项目ID
const generateProjectId = () => {
  return 'project_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
}

// 获取当前时间字符串
const getCurrentTimeString = () => {
  const now = new Date()
  return `${now.getFullYear()}.${(now.getMonth() + 1).toString().padStart(2, '0')}.${now.getDate().toString().padStart(2, '0')}`
}

// 保存项目到本地存储
const saveProjectToStorage = (projectData: any) => {
  const stored = localStorage.getItem('collaborative_projects')
  let projects = []
  
  if (stored) {
    try {
      projects = JSON.parse(stored)
    } catch (e) {
      console.error('解析本地存储数据失败:', e)
    }
  }
  
  // 添加新项目
  const newProject = {
    id: generateProjectId(),
    sequence: projects.length + 1,
    projectName: projectData.projectName,
    businessType: projectData.businessType,
    department: projectData.category || '默认分类',
    status: projectData.status,
    creator: '张三', // 当前用户
    createTime: getCurrentTimeString(),
    version: projectData.version,
    remark: projectData.remark,
    updateNotification: projectData.updateNotification,
    notificationMethods: projectData.notificationMethods
  }
  
  projects.push(newProject)
  localStorage.setItem('collaborative_projects', JSON.stringify(projects))
  
  return newProject
}

// 确认创建
const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 保存到本地存储
    saveProjectToStorage(formData)

    ElMessage.success('项目创建成功')
    emit('success')
    handleClose()
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 取消
const handleCancel = () => {
  handleClose()
}

// 关闭对话框
const handleClose = () => {
  resetForm()
  dialogVisible.value = false
}
</script>

<style scoped lang="scss">
.create-project-dialog {
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    padding: 20px 24px;
    
    .el-dialog__title {
      color: #ffffff;
      font-weight: 600;
    }
    
    .el-dialog__headerbtn {
      .el-dialog__close {
        color: #ffffff;
        font-size: 18px;
        
        &:hover {
          color: #f0f0f0;
        }
      }
    }
  }
  
  .project-form {
    padding: 20px 0;
    
    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #303133;
    }
    
    :deep(.el-form-item) {
      margin-bottom: 20px;
    }
    
    :deep(.el-input__wrapper) {
      border-radius: 6px;
    }
    
    :deep(.el-select) {
      width: 100%;
    }
    
    :deep(.el-textarea__inner) {
      border-radius: 6px;
    }
    
    :deep(.el-checkbox-group) {
      display: flex;
      gap: 20px;
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 0 0;
    border-top: 1px solid #e4e7ed;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .create-project-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 5vh auto;
    }
    
    .project-form {
      :deep(.el-form-item__label) {
        width: 100px !important;
        font-size: 14px;
      }
      
      :deep(.el-checkbox-group) {
        flex-direction: column;
        gap: 10px;
      }
    }
  }
}
</style>

<template>
  <div class="navigation-menu">
    <div class="menu-header">
      <h3 class="menu-title">多人协同</h3>
    </div>
    
    <el-menu
      :default-active="activeMenu"
      class="navigation-menu-list"
      @select="handleMenuSelect"
    >
      <el-menu-item
        v-for="item in menuItems"
        :key="item.key"
        :index="item.key"
        class="menu-item"
      >
        <el-icon class="menu-icon">
          <component :is="item.icon" />
        </el-icon>
        <span class="menu-text">{{ item.label }}</span>
      </el-menu-item>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  FolderOpened,
  UserFilled,
  Document,
  Connection,
  Location,
  ChatDotRound,
  Clock,
  Operation,
  Share
} from '@element-plus/icons-vue'

interface MenuItem {
  key: string
  label: string
  icon: any
}

// 定义emit
const emit = defineEmits<{
  menuChange: [menuKey: string]
}>()

// 当前激活的菜单项
const activeMenu = ref('project-management')

// 菜单项列表（按照用户要求的固定顺序）
const menuItems = ref<MenuItem[]>([
  {
    key: 'project-management',
    label: '项目管理',
    icon: FolderOpened
  },
  {
    key: 'team-management',
    label: '团队管理',
    icon: UserFilled
  },
  {
    key: 'template-management',
    label: '模板管理',
    icon: Document
  },
  {
    key: 'collaborative-workflow',
    label: '协同工作流程',
    icon: Connection
  },
  {
    key: 'region-management',
    label: '区域管理',
    icon: Location
  },
  {
    key: 'instant-messaging',
    label: '即时通信',
    icon: ChatDotRound
  },
  {
    key: 'backup-cycle',
    label: '备份周期',
    icon: Clock
  },
  {
    key: 'operation-history',
    label: '操作历史记录',
    icon: Operation
  },
  {
    key: 'shared-access-record',
    label: '共享访问记录',
    icon: Share
  }
])

// 处理菜单选择
const handleMenuSelect = (key: string) => {
  activeMenu.value = key
  emit('menuChange', key)
}
</script>

<style scoped lang="scss">
.navigation-menu {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .menu-header {
    padding: 16px;
    border-bottom: 1px solid #e9ecef;
    background: #409eff;

    .menu-title {
      margin: 0;
      color: #ffffff;
      font-size: 16px;
      font-weight: 600;
      text-align: center;
    }
  }
  
  .navigation-menu-list {
    flex: 1;
    border: none;
    background-color: #ffffff;

    :deep(.el-menu-item) {
      height: 48px;
      line-height: 48px;
      padding-left: 20px !important;
      margin: 0;
      border-radius: 0;
      border-bottom: 1px solid #f5f7fa;
      color: #303133;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        background-color: #ecf5ff;
        color: #409eff;
      }

      &.is-active {
        background-color: #409eff;
        color: #ffffff;
        font-weight: 600;

        .menu-icon {
          color: #ffffff;
        }
      }
      
      .menu-icon {
        margin-right: 12px;
        font-size: 16px;
        color: #333;
        transition: color 0.3s ease;
      }

      .menu-text {
        font-size: 14px;
        color: #333;
        transition: all 0.3s ease;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .navigation-menu {
    .menu-header {
      padding: 15px 12px 12px;
      
      .menu-title {
        font-size: 16px;
      }
    }
    
    .navigation-menu-list {
      :deep(.el-menu-item) {
        height: 48px;
        line-height: 48px;
        padding: 0 16px;
        margin: 3px 6px;
        
        .menu-icon {
          font-size: 16px;
          margin-right: 10px;
        }
        
        .menu-text {
          font-size: 13px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .navigation-menu {
    .menu-header {
      padding: 12px 10px 10px;
      
      .menu-title {
        font-size: 15px;
      }
    }
    
    .navigation-menu-list {
      :deep(.el-menu-item) {
        height: 44px;
        line-height: 44px;
        padding: 0 12px;
        margin: 2px 4px;
        
        .menu-icon {
          font-size: 15px;
          margin-right: 8px;
        }
        
        .menu-text {
          font-size: 12px;
        }
      }
    }
  }
}
</style>

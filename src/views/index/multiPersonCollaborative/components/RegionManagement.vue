<template>
  <div class="region-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">区域管理</h2>
      <p class="page-description">管理协同工作的区域范围和权限</p>
    </div>

    <!-- 查询栏 -->
    <div class="search-bar">
      <el-card class="search-card">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          inline
          label-width="80px"
          class="search-form"
        >
          <el-form-item label="区域名称">
            <el-input
              v-model="searchForm.regionName"
              placeholder="请输入区域名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>

          <el-form-item label="区域类型">
            <el-select
              v-model="searchForm.regionType"
              placeholder="请选择区域类型"
              clearable
              style="width: 150px"
            >
              <el-option label="专属区域" value="exclusive" />
              <el-option label="共享区域" value="shared" />
              <el-option label="临时区域" value="temporary" />
            </el-select>
          </el-form-item>

          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="启用" value="true" />
              <el-option label="禁用" value="false" />
            </el-select>
          </el-form-item>

          <el-form-item label="创建人">
            <el-input
              v-model="searchForm.creator"
              placeholder="请输入创建人"
              clearable
              style="width: 150px"
            />
          </el-form-item>

          <el-form-item label="创建时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 240px"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 操作栏 -->
    <div class="operation-bar">
      <div class="operation-left">
        <!-- 移除记录数显示 -->
      </div>
      <div class="operation-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建区域
        </el-button>
      </div>
    </div>

    <!-- 区域列表 -->
    <div class="region-table-container">
      <el-card class="table-card">
        <div v-if="isLoading" class="loading-container">
          <el-icon class="is-loading" size="32">
            <Loading />
          </el-icon>
          <p>正在加载区域数据...</p>
        </div>

        <div v-else>
          <TableV2
            :defaultTableData="paginatedRegions"
            :columns="tableColumns"
            :enable-toolbar="false"
            :enable-own-button="true"
            :enable-selection="false"
            :enable-index="true"
            :height="400"
            :buttons="tableButtons"
            :loading="isLoading"
            @click-button="onTableClickButton"
          >
            <!-- 区域类型列自定义显示 -->
            <template #regionType="{ row }">
              <el-tag :type="getRegionTypeTagType(row.regionType)" size="small">
                {{ getRegionTypeText(row.regionType) }}
              </el-tag>
            </template>

            <!-- 创建时间列自定义显示 -->
            <template #createdAt="{ row }">
              {{ formatTime(row.createdAt) }}
            </template>
          </TableV2>

          <!-- 分页组件 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 创建/编辑区域对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="isEditing ? '编辑区域' : '创建区域'"
      width="600px"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="区域名称" prop="regionName">
          <el-input
            v-model="formData.regionName"
            placeholder="请输入区域名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="区域描述" prop="regionDescription">
          <el-input
            v-model="formData.regionDescription"
            type="textarea"
            placeholder="请输入区域描述"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="区域类型" prop="regionType">
          <el-select v-model="formData.regionType" placeholder="请选择区域类型" style="width: 100%">
            <el-option label="专属区域" value="exclusive" />
            <el-option label="共享区域" value="shared" />
            <el-option label="临时区域" value="temporary" />
          </el-select>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :rows="2"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleConfirm" :loading="saving">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看区域详情对话框 -->
    <el-dialog
      v-model="showViewDialog"
      title="区域详情"
      width="600px"
    >
      <el-form
        v-if="viewData"
        :model="viewData"
        label-width="100px"
        class="view-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="区域名称">
              <el-input
                :value="viewData.regionName"
                readonly
                class="readonly-input"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区域类型">
              <el-input
                :value="getRegionTypeText(viewData.regionType)"
                readonly
                class="readonly-input"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="区域描述">
              <el-input
                :value="viewData.regionDescription"
                type="textarea"
                :rows="3"
                readonly
                class="readonly-input"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                :value="viewData.remark"
                type="textarea"
                :rows="2"
                readonly
                class="readonly-input"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态">
              <el-input
                :value="viewData.status ? '启用' : '禁用'"
                readonly
                class="readonly-input"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建人">
              <el-input
                :value="viewData.creator"
                readonly
                class="readonly-input"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="创建时间">
              <el-input
                :value="formatTime(viewData.createdAt)"
                readonly
                class="readonly-input"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showViewDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>



    <!-- 更多操作菜单 -->
    <el-dialog
      v-model="showMoreDialog"
      title="更多操作"
      width="400px"
    >
      <div class="more-actions">
        <el-button class="action-btn" @click="handlePermissionSetting">
          <el-icon><Lock /></el-icon>
          权限设置
        </el-button>
        <el-button class="action-btn" @click="handleShareSetting">
          <el-icon><Share /></el-icon>
          共享设置
        </el-button>
        <el-button class="action-btn" @click="handleLock">
          <el-icon><Lock /></el-icon>
          锁定
        </el-button>
        <el-button class="action-btn" @click="handleTempPermission">
          <el-icon><Clock /></el-icon>
          临时权限
        </el-button>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showMoreDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限管理对话框 -->
    <el-dialog
      v-model="showPermissionDialog"
      title="权限管理"
      width="600px"
    >
      <el-form
        ref="permissionFormRef"
        :model="permissionFormData"
        :rules="permissionFormRules"
        label-width="100px"
        class="permission-form"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="区域名称">
              <el-input
                :value="currentPermissionRegion?.regionName"
                readonly
                class="readonly-input"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="授权人" prop="authorizedUsers">
              <el-select
                v-model="permissionFormData.authorizedUsers"
                multiple
                placeholder="请选择授权人"
                style="width: 100%"
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option label="张伟 - 系统管理员" value="zhangwei_admin" />
                <el-option label="李娜 - 项目经理" value="lina_pm" />
                <el-option label="王强 - 技术主管" value="wangqiang_tech" />
                <el-option label="刘敏 - 业务分析师" value="liumin_ba" />
                <el-option label="陈杰 - 开发工程师" value="chenjie_dev" />
                <el-option label="赵雪 - 测试工程师" value="zhaoxue_qa" />
                <el-option label="孙涛 - 运维工程师" value="suntao_ops" />
                <el-option label="周丽 - 产品经理" value="zhouli_product" />
                <el-option label="吴斌 - 架构师" value="wubin_arch" />
                <el-option label="郑红 - 数据分析师" value="zhenghong_data" />
                <el-option label="马超 - 安全专家" value="machao_security" />
                <el-option label="朱琳 - UI设计师" value="zhulin_ui" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="权限" prop="permissions">
              <el-checkbox-group v-model="permissionFormData.permissions">
                <el-checkbox value="view">查看</el-checkbox>
                <el-checkbox value="create">新增</el-checkbox>
                <el-checkbox value="edit">编辑</el-checkbox>
                <el-checkbox value="delete">删除</el-checkbox>
                <el-checkbox value="lock">锁定</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                v-model="permissionFormData.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="状态">
              <el-select
                v-model="permissionFormData.status"
                placeholder="请选择状态"
                style="width: 100%"
              >
                <el-option label="开启" value="enabled" />
                <el-option label="关闭" value="disabled" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handlePermissionDialogClose">取消</el-button>
          <el-button type="primary" @click="handlePermissionSave" :loading="saving">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 共享设置对话框 -->
    <el-dialog
      v-model="showShareDialog"
      title="共享设置"
      width="600px"
    >
      <el-form
        ref="shareFormRef"
        :model="shareFormData"
        :rules="shareFormRules"
        label-width="100px"
        class="share-form"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="区域名称">
              <el-input
                :value="currentShareRegion?.regionName"
                readonly
                class="readonly-input"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="共享人" prop="sharedUsers">
              <el-select
                v-model="shareFormData.sharedUsers"
                multiple
                placeholder="请选择共享人"
                style="width: 100%"
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option label="张伟 - 系统管理员" value="zhangwei_admin" />
                <el-option label="李娜 - 项目经理" value="lina_pm" />
                <el-option label="王强 - 技术主管" value="wangqiang_tech" />
                <el-option label="刘敏 - 业务分析师" value="liumin_ba" />
                <el-option label="陈杰 - 开发工程师" value="chenjie_dev" />
                <el-option label="赵雪 - 测试工程师" value="zhaoxue_qa" />
                <el-option label="孙涛 - 运维工程师" value="suntao_ops" />
                <el-option label="周丽 - 产品经理" value="zhouli_product" />
                <el-option label="吴斌 - 架构师" value="wubin_arch" />
                <el-option label="郑红 - 数据分析师" value="zhenghong_data" />
                <el-option label="马超 - 安全专家" value="machao_security" />
                <el-option label="朱琳 - UI设计师" value="zhulin_ui" />
                <el-option label="黄磊 - 前端工程师" value="huanglei_fe" />
                <el-option label="林芳 - 后端工程师" value="linfang_be" />
                <el-option label="徐刚 - 数据库管理员" value="xugang_dba" />
                <el-option label="何静 - 质量保证" value="hejing_qa" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="共享有效期" prop="shareExpiry">
              <el-date-picker
                v-model="shareFormData.shareExpiry"
                type="datetime"
                placeholder="请选择共享有效期"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="(time: Date) => time.getTime() < Date.now()"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleShareDialogClose">取消</el-button>
          <el-button type="primary" @click="handleShareSave" :loading="saving">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 锁定设置对话框 -->
    <el-dialog
      v-model="showLockDialog"
      title="锁定设置"
      width="500px"
    >
      <el-form
        ref="lockFormRef"
        :model="lockFormData"
        :rules="lockFormRules"
        label-width="100px"
        class="lock-form"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="区域名称">
              <el-input
                :value="currentLockRegion?.regionName"
                readonly
                class="readonly-input"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="启动锁定">
              <el-switch
                v-model="lockFormData.isLocked"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="lockFormData.isLocked">
          <el-col :span="24">
            <el-form-item label="解锁密码" prop="unlockPassword">
              <el-input
                v-model="lockFormData.unlockPassword"
                type="password"
                placeholder="请输入解锁密码"
                show-password
                readonly
              />
              <div class="form-tip">默认解锁密码：lc24</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleLockDialogClose">取消</el-button>
          <el-button type="primary" @click="handleLockSave" :loading="saving">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 临时权限对话框 -->
    <el-dialog
      v-model="showTempPermissionDialog"
      title="临时权限"
      width="600px"
    >
      <el-form
        ref="tempPermissionFormRef"
        :model="tempPermissionFormData"
        :rules="tempPermissionFormRules"
        label-width="100px"
        class="temp-permission-form"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="区域名称">
              <el-input
                :value="currentTempPermissionRegion?.regionName"
                readonly
                class="readonly-input"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="授权人" prop="authorizedUsers">
              <el-select
                v-model="tempPermissionFormData.authorizedUsers"
                multiple
                placeholder="请选择授权人"
                style="width: 100%"
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option label="张伟 - 系统管理员" value="zhangwei_admin" />
                <el-option label="李娜 - 项目经理" value="lina_pm" />
                <el-option label="王强 - 技术主管" value="wangqiang_tech" />
                <el-option label="刘敏 - 业务分析师" value="liumin_ba" />
                <el-option label="陈杰 - 开发工程师" value="chenjie_dev" />
                <el-option label="赵雪 - 测试工程师" value="zhaoxue_qa" />
                <el-option label="孙涛 - 运维工程师" value="suntao_ops" />
                <el-option label="周丽 - 产品经理" value="zhouli_product" />
                <el-option label="吴斌 - 架构师" value="wubin_arch" />
                <el-option label="郑红 - 数据分析师" value="zhenghong_data" />
                <el-option label="马超 - 安全专家" value="machao_security" />
                <el-option label="朱琳 - UI设计师" value="zhulin_ui" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="权限" prop="permissions">
              <el-checkbox-group v-model="tempPermissionFormData.permissions">
                <el-checkbox value="view">查看</el-checkbox>
                <el-checkbox value="create">新增</el-checkbox>
                <el-checkbox value="edit">编辑</el-checkbox>
                <el-checkbox value="delete">删除</el-checkbox>
                <el-checkbox value="lock">锁定</el-checkbox>
                <el-checkbox value="share">共享</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="权限有效期" prop="permissionExpiry">
              <el-date-picker
                v-model="tempPermissionFormData.permissionExpiry"
                type="datetime"
                placeholder="请选择权限有效期"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="(time: Date) => time.getTime() < Date.now()"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="状态">
              <el-select
                v-model="tempPermissionFormData.status"
                placeholder="请选择状态"
                style="width: 100%"
              >
                <el-option label="开启" value="enabled" />
                <el-option label="关闭" value="disabled" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleTempPermissionDialogClose">取消</el-button>
          <el-button type="primary" @click="handleTempPermissionSave" :loading="saving">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Loading, Lock, Share, Clock, Search } from '@element-plus/icons-vue'
import { IndexedDBManager } from '../../../../utils/indexedDB'

// 区域接口定义
interface Region {
  id: string
  regionName: string
  regionType: string
  regionDescription: string
  remark: string
  status: boolean
  creator: string
  createdAt: string
  updatedAt: string
}

// IndexedDB 管理器
const dbManager = new IndexedDBManager('RegionManagementDB', 4)

// 响应式数据
const isLoading = ref(true)
const saving = ref(false)
const showCreateDialog = ref(false)
const showViewDialog = ref(false)
const showMoreDialog = ref(false)
const showPermissionDialog = ref(false)
const showShareDialog = ref(false)
const showLockDialog = ref(false)
const showTempPermissionDialog = ref(false)

const isEditing = ref(false)
const editingId = ref('')
const regions = ref<Region[]>([])
const viewData = ref<Region | null>(null)
const currentMoreData = ref<Region | null>(null)
const currentPermissionRegion = ref<Region | null>(null)
const currentShareRegion = ref<Region | null>(null)
const currentLockRegion = ref<Region | null>(null)
const currentTempPermissionRegion = ref<Region | null>(null)
const formRef = ref<FormInstance>()
const searchFormRef = ref<FormInstance>()
const permissionFormRef = ref<FormInstance>()
const shareFormRef = ref<FormInstance>()
const lockFormRef = ref<FormInstance>()
const tempPermissionFormRef = ref<FormInstance>()

// 搜索表单数据
const searchForm = reactive({
  regionName: '',
  regionType: '',
  status: '' as string,
  creator: '',
  dateRange: [] as string[]
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表单数据
const formData = reactive({
  regionName: '',
  regionDescription: '',
  regionType: '',
  remark: '',
  status: true
})

// 权限表单数据
const permissionFormData = reactive({
  authorizedUsers: [] as string[],
  permissions: [] as string[],
  remark: '',
  status: 'enabled'
})

// 共享表单数据
const shareFormData = reactive({
  sharedUsers: [] as string[],
  shareExpiry: ''
})

// 锁定表单数据
const lockFormData = reactive({
  isLocked: false,
  unlockPassword: 'lc24'
})

// 临时权限表单数据
const tempPermissionFormData = reactive({
  authorizedUsers: [] as string[],
  permissions: [] as string[],
  permissionExpiry: '',
  status: 'enabled'
})

// 表单验证规则
const formRules: FormRules = {
  regionName: [
    { required: true, message: '请输入区域名称', trigger: 'blur' },
    { min: 2, max: 50, message: '区域名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  regionDescription: [
    { required: true, message: '请输入区域描述', trigger: 'blur' },
    { max: 200, message: '区域描述不能超过 200 个字符', trigger: 'blur' }
  ],
  regionType: [
    { required: true, message: '请选择区域类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 权限表单验证规则
const permissionFormRules: FormRules = {
  authorizedUsers: [
    { required: true, message: '请选择授权人', trigger: 'change' }
  ],
  permissions: [
    { required: true, message: '请选择权限', trigger: 'change' }
  ]
}

// 共享表单验证规则
const shareFormRules: FormRules = {
  sharedUsers: [
    { required: true, message: '请选择共享人', trigger: 'change' }
  ],
  shareExpiry: [
    { required: true, message: '请选择共享有效期', trigger: 'change' }
  ]
}

// 锁定表单验证规则
const lockFormRules: FormRules = {
  unlockPassword: [
    { required: true, message: '请输入解锁密码', trigger: 'blur' }
  ]
}

// 临时权限表单验证规则
const tempPermissionFormRules: FormRules = {
  authorizedUsers: [
    { required: true, message: '请选择授权人', trigger: 'change' }
  ],
  permissions: [
    { required: true, message: '请选择权限', trigger: 'change' }
  ],
  permissionExpiry: [
    { required: true, message: '请选择权限有效期', trigger: 'change' }
  ]
}

// 表格列配置
const tableColumns = ref([
  {
    prop: 'regionName',
    label: '区域名称',
    width: 150,
    showOverflowTooltip: true
  },
  {
    prop: 'regionType',
    label: '区域类型',
    width: 120,
    slot: 'regionType'
  },
  {
    prop: 'regionDescription',
    label: '区域描述',
    minWidth: 200,
    showOverflowTooltip: true
  },
  {
    prop: 'creator',
    label: '创建人',
    width: 120
  },
  {
    prop: 'createdAt',
    label: '创建时间',
    width: 160,
    slot: 'createdAt'
  }
])

// 表格按钮配置
const tableButtons = ref([
  {
    label: '查看',
    type: 'primary',
    code: 'view'
  },
  {
    label: '编辑',
    type: 'success',
    code: 'edit'
  },
  {
    label: '删除',
    type: 'danger',
    code: 'delete'
  },
  {
    label: '更多',
    type: 'info',
    code: 'more'
  }
])

// 过滤后的区域数据（不分页）
const filteredRegions = computed(() => {
  let filtered = regions.value

  // 按区域名称过滤
  if (searchForm.regionName.trim()) {
    filtered = filtered.filter(region =>
      region.regionName.toLowerCase().includes(searchForm.regionName.toLowerCase())
    )
  }

  // 按区域类型过滤
  if (searchForm.regionType) {
    filtered = filtered.filter(region => region.regionType === searchForm.regionType)
  }

  // 按状态过滤
  if (searchForm.status !== '') {
    const statusValue = searchForm.status === 'true'
    filtered = filtered.filter(region => region.status === statusValue)
  }

  // 按创建人过滤
  if (searchForm.creator.trim()) {
    filtered = filtered.filter(region =>
      region.creator.toLowerCase().includes(searchForm.creator.toLowerCase())
    )
  }

  // 按创建时间范围过滤
  if (searchForm.dateRange && searchForm.dateRange.length === 2) {
    const [startDate, endDate] = searchForm.dateRange
    filtered = filtered.filter(region => {
      const createdDate = new Date(region.createdAt).toISOString().split('T')[0]
      return createdDate >= startDate && createdDate <= endDate
    })
  }

  // 更新总数
  pagination.total = filtered.length

  return filtered
})

// 分页后的区域数据
const paginatedRegions = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredRegions.value.slice(start, end)
})

// 从数据库加载区域数据
const loadRegions = async () => {
  try {
    isLoading.value = true
    await dbManager.init()

    // 检查是否有数据，如果没有则创建默认数据
    let regionData = await dbManager.getAllRegions()

    if (regionData.length === 0) {
      // 创建默认区域数据
      const defaultRegions = generateDefaultRegions()
      for (const region of defaultRegions) {
        await dbManager.saveRegion(region)
      }
      regionData = await dbManager.getAllRegions()
    }

    regions.value = regionData.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  } catch (error) {
    console.error('Failed to load regions:', error)
    ElMessage.error('加载区域数据失败')
    // 使用默认数据作为后备
    regions.value = generateDefaultRegions()
  } finally {
    isLoading.value = false
  }
}

// 生成默认区域数据
const generateDefaultRegions = (): Region[] => {
  const now = new Date().toISOString()
  const creators = ['张建国', '李明华', '王秀英', '刘德华', '陈小明', '赵丽娜', '孙志强', '周美丽']
  const regionTypes = ['exclusive', 'shared', 'temporary']
  const regionTypeNames = ['专属区域', '共享区域', '临时区域']

  return Array.from({ length: 15 }, (_, index) => {
    const regionType = regionTypes[index % 3]
    const regionTypeName = regionTypeNames[index % 3]
    const creator = creators[index % creators.length]

    return {
      id: `region_${Date.now()}_${index}`,
      regionName: `${regionTypeName}${String(index + 1).padStart(2, '0')}`,
      regionType,
      regionDescription: `这是一个${regionTypeName}，用于${getRegionPurpose(regionType)}`,
      remark: index % 3 === 0 ? `重要${regionTypeName}，需要特别关注` : '',
      status: index % 4 !== 0, // 大部分启用，少数禁用
      creator,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: now
    }
  })
}

// 获取区域用途描述
const getRegionPurpose = (type: string): string => {
  const purposes: Record<string, string> = {
    exclusive: '特定团队或部门的独立工作',
    shared: '多团队协作和资源共享',
    temporary: '临时项目或短期任务'
  }
  return purposes[type] || '协同工作'
}

// 获取区域类型文本
const getRegionTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    exclusive: '专属区域',
    shared: '共享区域',
    temporary: '临时区域'
  }
  return typeMap[type] || type
}

// 获取区域类型标签类型
const getRegionTypeTagType = (type: string): 'primary' | 'success' | 'danger' | 'info' | 'warning' => {
  const tagTypeMap: Record<string, 'primary' | 'success' | 'danger' | 'info' | 'warning'> = {
    exclusive: 'danger',
    shared: 'success',
    temporary: 'warning'
  }
  return tagTypeMap[type] || 'info'
}

// 格式化时间
const formatTime = (timeStr: string): string => {
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 表格按钮点击处理
const onTableClickButton = (data: any) => {
  console.log('按钮点击事件:', data)

  // 正确的数据结构: {row: ..., btn: ..., index: ...}
  const { btn, row } = data

  console.log('按钮信息:', btn)
  console.log('行数据:', row)

  if (!btn || !row) {
    console.error('按钮或行数据缺失:', { btn, row })
    return
  }

  const buttonCode = btn.code
  console.log('按钮代码:', buttonCode)

  switch (buttonCode) {
    case 'view':
      handleView(row)
      break
    case 'edit':
      handleEdit(row)
      break
    case 'delete':
      handleDelete(row)
      break
    case 'more':
      handleMore(row)
      break
    default:
      console.warn('Unknown button code:', buttonCode)
  }
}

// 搜索相关方法
const handleSearch = () => {
  console.log('执行搜索:', searchForm)
  // 搜索逻辑已通过 filteredRegions 计算属性实现
  ElMessage.success('搜索完成')
}

const handleReset = () => {
  // 重置搜索表单
  searchForm.regionName = ''
  searchForm.regionType = ''
  searchForm.status = ''
  searchForm.creator = ''
  searchForm.dateRange = []

  // 重置分页到第一页
  pagination.currentPage = 1

  // 重置表单验证状态
  searchFormRef.value?.resetFields()

  ElMessage.success('重置完成')
}

// 分页处理方法
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  pagination.currentPage = 1 // 改变页面大小时回到第一页
}

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
}

// 表格操作方法
const handleView = (row: Region) => {
  console.log('查看区域详情:', row)
  console.log('设置viewData前:', viewData.value)
  viewData.value = { ...row } // 创建副本避免引用问题
  console.log('设置viewData后:', viewData.value)
  showViewDialog.value = true
}

const handleEdit = (row: Region) => {
  isEditing.value = true
  editingId.value = row.id
  formData.regionName = row.regionName
  formData.regionDescription = row.regionDescription
  formData.regionType = row.regionType
  formData.remark = row.remark
  formData.status = row.status
  showCreateDialog.value = true
}

const handleDelete = async (row: Region) => {
  try {
    await ElMessageBox.confirm(`确定要删除区域"${row.regionName}"吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await dbManager.deleteRegion(row.id)
    await loadRegions()
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete region:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleMore = (row: Region) => {
  currentMoreData.value = row
  showMoreDialog.value = true
}

// 对话框操作方法
const handleDialogClose = () => {
  showCreateDialog.value = false
  isEditing.value = false
  editingId.value = ''
  formRef.value?.resetFields()
}

const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    const now = new Date().toISOString()
    const region: Region = {
      id: isEditing.value ? editingId.value : `region_${Date.now()}`,
      regionName: formData.regionName,
      regionDescription: formData.regionDescription,
      regionType: formData.regionType,
      remark: formData.remark,
      status: formData.status,
      creator: '当前用户', // 实际项目中应该从用户信息获取
      createdAt: isEditing.value ? regions.value.find(r => r.id === editingId.value)?.createdAt || now : now,
      updatedAt: now
    }

    await dbManager.saveRegion(region)
    await loadRegions()

    ElMessage.success(isEditing.value ? '更新成功' : '创建成功')
    handleDialogClose()
  } catch (error) {
    console.error('Failed to save region:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 更多操作方法
const handlePermissionSetting = async () => {
  console.log('打开权限设置对话框，当前区域:', currentMoreData.value)
  if (currentMoreData.value) {
    currentPermissionRegion.value = currentMoreData.value

    try {
      // 加载该区域的现有权限数据
      const existingPermissions = await dbManager.getPermissionsByRegionId(currentMoreData.value.id)
      console.log('现有权限数据:', existingPermissions)

      if (existingPermissions.length > 0) {
        // 如果有现有权限，加载第一个权限配置
        const permission = existingPermissions[0]
        permissionFormData.authorizedUsers = [...permission.authorizedUsers]
        permissionFormData.permissions = [...permission.permissions]
        permissionFormData.remark = permission.remark || ''
        permissionFormData.status = permission.status || 'enabled'
      } else {
        // 重置权限表单
        permissionFormData.authorizedUsers = []
        permissionFormData.permissions = []
        permissionFormData.remark = ''
        permissionFormData.status = 'enabled'
      }
    } catch (error) {
      console.error('加载权限数据失败:', error)
      // 重置权限表单
      permissionFormData.authorizedUsers = []
      permissionFormData.permissions = []
      permissionFormData.remark = ''
      permissionFormData.status = 'enabled'
    }

    showMoreDialog.value = false
    showPermissionDialog.value = true
  }
}

const handleShareSetting = async () => {
  console.log('打开共享设置对话框，当前区域:', currentMoreData.value)
  if (currentMoreData.value) {
    currentShareRegion.value = currentMoreData.value

    try {
      // 加载该区域的现有共享设置
      const existingShares = await dbManager.getShareSettingsByRegionId(currentMoreData.value.id)
      console.log('现有共享设置:', existingShares)

      if (existingShares.length > 0) {
        // 如果有现有共享设置，加载第一个配置
        const shareSetting = existingShares[0]
        shareFormData.sharedUsers = [...shareSetting.sharedUsers]
        shareFormData.shareExpiry = shareSetting.shareExpiry || ''
      } else {
        // 重置共享表单
        shareFormData.sharedUsers = []
        shareFormData.shareExpiry = ''
      }
    } catch (error) {
      console.error('加载共享设置失败:', error)
      // 重置共享表单
      shareFormData.sharedUsers = []
      shareFormData.shareExpiry = ''
    }

    showMoreDialog.value = false
    showShareDialog.value = true
  }
}

const handleLock = async () => {
  console.log('打开锁定设置对话框，当前区域:', currentMoreData.value)
  if (currentMoreData.value) {
    currentLockRegion.value = currentMoreData.value

    try {
      // 加载该区域的现有锁定设置
      const existingLocks = await dbManager.getLockSettingsByRegionId(currentMoreData.value.id)
      console.log('现有锁定设置:', existingLocks)

      if (existingLocks.length > 0) {
        // 如果有现有锁定设置，加载配置
        const lockSetting = existingLocks[0]
        lockFormData.isLocked = lockSetting.isLocked || false
        lockFormData.unlockPassword = lockSetting.unlockPassword || 'lc24'
      } else {
        // 重置锁定表单
        lockFormData.isLocked = false
        lockFormData.unlockPassword = 'lc24'
      }
    } catch (error) {
      console.error('加载锁定设置失败:', error)
      // 重置锁定表单
      lockFormData.isLocked = false
      lockFormData.unlockPassword = 'lc24'
    }

    showMoreDialog.value = false
    showLockDialog.value = true
  }
}

const handleTempPermission = async () => {
  console.log('打开临时权限对话框，当前区域:', currentMoreData.value)
  if (currentMoreData.value) {
    currentTempPermissionRegion.value = currentMoreData.value

    try {
      // 加载该区域的现有临时权限
      const existingTempPerms = await dbManager.getTempPermissionsByRegionId(currentMoreData.value.id)
      console.log('现有临时权限:', existingTempPerms)

      if (existingTempPerms.length > 0) {
        // 如果有现有临时权限，加载第一个配置
        const tempPerm = existingTempPerms[0]
        tempPermissionFormData.authorizedUsers = [...tempPerm.authorizedUsers]
        tempPermissionFormData.permissions = [...tempPerm.permissions]
        tempPermissionFormData.permissionExpiry = tempPerm.permissionExpiry || ''
        tempPermissionFormData.status = tempPerm.status || 'enabled'
      } else {
        // 重置临时权限表单
        tempPermissionFormData.authorizedUsers = []
        tempPermissionFormData.permissions = []
        tempPermissionFormData.permissionExpiry = ''
        tempPermissionFormData.status = 'enabled'
      }
    } catch (error) {
      console.error('加载临时权限失败:', error)
      // 重置临时权限表单
      tempPermissionFormData.authorizedUsers = []
      tempPermissionFormData.permissions = []
      tempPermissionFormData.permissionExpiry = ''
      tempPermissionFormData.status = 'enabled'
    }

    showMoreDialog.value = false
    showTempPermissionDialog.value = true
  }
}

// 权限管理相关方法
const handlePermissionDialogClose = () => {
  showPermissionDialog.value = false
  currentPermissionRegion.value = null
  // 重置表单
  permissionFormData.authorizedUsers = []
  permissionFormData.permissions = []
  permissionFormData.remark = ''
  permissionFormData.status = 'enabled'
}

const handlePermissionSave = async () => {
  if (!permissionFormRef.value || !currentPermissionRegion.value) return

  try {
    // 验证表单
    await permissionFormRef.value.validate()

    saving.value = true

    // 检查是否已有权限配置
    const existingPermissions = await dbManager.getPermissionsByRegionId(currentPermissionRegion.value.id)

    let permissionData
    if (existingPermissions.length > 0) {
      // 更新现有权限
      permissionData = {
        ...existingPermissions[0],
        authorizedUsers: [...permissionFormData.authorizedUsers],
        permissions: [...permissionFormData.permissions],
        remark: permissionFormData.remark,
        status: permissionFormData.status,
        updatedAt: new Date().toISOString()
      }
    } else {
      // 创建新权限
      permissionData = {
        id: `permission_${currentPermissionRegion.value.id}_${Date.now()}`,
        regionId: currentPermissionRegion.value.id,
        regionName: currentPermissionRegion.value.regionName,
        authorizedUsers: [...permissionFormData.authorizedUsers],
        permissions: [...permissionFormData.permissions],
        remark: permissionFormData.remark,
        status: permissionFormData.status,
        creator: '当前用户', // 实际项目中应该从用户信息获取
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    }

    // 保存到IndexedDB
    await dbManager.savePermission(permissionData)

    ElMessage.success('权限设置保存成功')
    handlePermissionDialogClose()
  } catch (error) {
    console.error('Failed to save permission:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 共享设置相关方法
const handleShareDialogClose = () => {
  showShareDialog.value = false
  currentShareRegion.value = null
  // 重置表单
  shareFormData.sharedUsers = []
  shareFormData.shareExpiry = ''
}

const handleShareSave = async () => {
  if (!shareFormRef.value || !currentShareRegion.value) return

  try {
    // 验证表单
    await shareFormRef.value.validate()

    saving.value = true

    // 检查是否已有共享设置
    const existingShares = await dbManager.getShareSettingsByRegionId(currentShareRegion.value.id)

    let shareData
    if (existingShares.length > 0) {
      // 更新现有共享设置
      shareData = {
        ...existingShares[0],
        sharedUsers: [...shareFormData.sharedUsers],
        shareExpiry: shareFormData.shareExpiry,
        updatedAt: new Date().toISOString()
      }
    } else {
      // 创建新共享设置
      shareData = {
        id: `share_${currentShareRegion.value.id}_${Date.now()}`,
        regionId: currentShareRegion.value.id,
        regionName: currentShareRegion.value.regionName,
        sharedUsers: [...shareFormData.sharedUsers],
        shareExpiry: shareFormData.shareExpiry,
        creator: '当前用户', // 实际项目中应该从用户信息获取
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    }

    // 保存到IndexedDB
    await dbManager.saveShareSetting(shareData)

    ElMessage.success('共享设置保存成功')
    handleShareDialogClose()
  } catch (error) {
    console.error('Failed to save share setting:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 锁定设置相关方法
const handleLockDialogClose = () => {
  showLockDialog.value = false
  currentLockRegion.value = null
  // 重置表单
  lockFormData.isLocked = false
  lockFormData.unlockPassword = 'lc24'
}

const handleLockSave = async () => {
  if (!lockFormRef.value || !currentLockRegion.value) return

  try {
    // 如果启用锁定，验证表单
    if (lockFormData.isLocked) {
      await lockFormRef.value.validate()
    }

    saving.value = true

    // 检查是否已有锁定设置
    const existingLocks = await dbManager.getLockSettingsByRegionId(currentLockRegion.value.id)

    let lockData
    if (existingLocks.length > 0) {
      // 更新现有锁定设置
      lockData = {
        ...existingLocks[0],
        isLocked: lockFormData.isLocked,
        unlockPassword: lockFormData.unlockPassword,
        updatedAt: new Date().toISOString()
      }
    } else {
      // 创建新锁定设置
      lockData = {
        id: `lock_${currentLockRegion.value.id}_${Date.now()}`,
        regionId: currentLockRegion.value.id,
        regionName: currentLockRegion.value.regionName,
        isLocked: lockFormData.isLocked,
        unlockPassword: lockFormData.unlockPassword,
        creator: '当前用户', // 实际项目中应该从用户信息获取
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    }

    // 保存到IndexedDB
    await dbManager.saveLockSetting(lockData)

    ElMessage.success('锁定设置保存成功')
    handleLockDialogClose()
  } catch (error) {
    console.error('Failed to save lock setting:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 临时权限相关方法
const handleTempPermissionDialogClose = () => {
  showTempPermissionDialog.value = false
  currentTempPermissionRegion.value = null
  // 重置表单
  tempPermissionFormData.authorizedUsers = []
  tempPermissionFormData.permissions = []
  tempPermissionFormData.permissionExpiry = ''
  tempPermissionFormData.status = 'enabled'
}

const handleTempPermissionSave = async () => {
  if (!tempPermissionFormRef.value || !currentTempPermissionRegion.value) return

  try {
    // 验证表单
    await tempPermissionFormRef.value.validate()

    saving.value = true

    // 检查是否已有临时权限
    const existingTempPerms = await dbManager.getTempPermissionsByRegionId(currentTempPermissionRegion.value.id)

    let tempPermData
    if (existingTempPerms.length > 0) {
      // 更新现有临时权限
      tempPermData = {
        ...existingTempPerms[0],
        authorizedUsers: [...tempPermissionFormData.authorizedUsers],
        permissions: [...tempPermissionFormData.permissions],
        permissionExpiry: tempPermissionFormData.permissionExpiry,
        status: tempPermissionFormData.status,
        updatedAt: new Date().toISOString()
      }
    } else {
      // 创建新临时权限
      tempPermData = {
        id: `temp_perm_${currentTempPermissionRegion.value.id}_${Date.now()}`,
        regionId: currentTempPermissionRegion.value.id,
        regionName: currentTempPermissionRegion.value.regionName,
        authorizedUsers: [...tempPermissionFormData.authorizedUsers],
        permissions: [...tempPermissionFormData.permissions],
        permissionExpiry: tempPermissionFormData.permissionExpiry,
        status: tempPermissionFormData.status,
        creator: '当前用户', // 实际项目中应该从用户信息获取
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    }

    // 保存到IndexedDB
    await dbManager.saveTempPermission(tempPermData)

    ElMessage.success('临时权限保存成功')
    handleTempPermissionDialogClose()
  } catch (error) {
    console.error('Failed to save temp permission:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 生命周期
onMounted(() => {
  loadRegions()
})
</script>

<style scoped lang="scss">
.region-management {
  .page-header {
    margin-bottom: 30px;

    .page-title {
      margin: revert;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .page-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .search-bar {
    margin-bottom: 20px;

    .search-card {
      background: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .search-form {
        .el-form-item {
          margin-bottom: 16px;
          margin-right: 20px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }

  .operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .operation-left {
      flex: 1;

      .result-count {
        color: #409eff;
        font-weight: 500;
        font-size: 14px;
      }
    }

    .operation-right {
      display: flex;
      gap: 12px;
    }
  }

  .region-table-container {
    .table-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 20px;
        color: #909399;

        p {
          margin-top: 16px;
          font-size: 14px;
        }
      }
    }

    .pagination-wrapper {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
      padding: 20px 0;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  .view-form {
    .readonly-input {
      .el-input__inner,
      .el-textarea__inner {
        background-color: #f5f7fa;
        border-color: #e4e7ed;
        color: #606266;
        cursor: not-allowed;
      }
    }

    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }
  }

  .view-content {
    .detail-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16px;
      font-size: 14px;

      .label {
        color: #606266;
        margin-right: 12px;
        min-width: 80px;
        font-weight: 500;
      }

      .value {
        color: #303133;
        flex: 1;
        word-break: break-all;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .more-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;

    .action-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      height: 48px;
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      background: #ffffff;
      color: #606266;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        color: #409eff;
        background: #ecf5ff;
      }

      .el-icon {
        font-size: 16px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .region-management {
    .operation-bar {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .operation-left,
      .operation-right {
        flex: none;
      }

      .operation-right {
        justify-content: center;
      }
    }

    .more-actions {
      grid-template-columns: 1fr;
    }
  }
}

// 锁定表单样式
.lock-form {
  .readonly-input {
    background-color: #f5f7fa;

    :deep(.el-input__inner) {
      background-color: #f5f7fa;
      color: #909399;
    }
  }

  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }
}

// 临时权限表单样式
.temp-permission-form {
  .readonly-input {
    background-color: #f5f7fa;

    :deep(.el-input__inner) {
      background-color: #f5f7fa;
      color: #909399;
    }
  }

  :deep(.el-checkbox-group) {
    .el-checkbox {
      margin-right: 20px;
      margin-bottom: 10px;
    }
  }
}

// 共享表单样式
.share-form {
  .readonly-input {
    background-color: #f5f7fa;

    :deep(.el-input__inner) {
      background-color: #f5f7fa;
      color: #909399;
    }
  }
}
</style>

<template>
  <DialogComp
    v-model:visible="dialogVisible"
    title="普通成员加入申请"
    width="600px"
    :visibleFooterButton="false"
    @closed="handleClose"
  >
    <div class="member-apply-dialog">
      <el-form :model="applyForm" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item label="姓名" prop="name">
          <el-input
            v-model="applyForm.name"
            placeholder="请输入姓名"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item label="所属部门" prop="department">
          <el-select
            v-model="applyForm.department"
            placeholder="请选择所属部门"
            style="width: 100%"
          >
            <el-option label="技术部" value="技术部" />
            <el-option label="产品部" value="产品部" />
            <el-option label="设计部" value="设计部" />
            <el-option label="测试部" value="测试部" />
            <el-option label="运营部" value="运营部" />
            <el-option label="市场部" value="市场部" />
            <el-option label="人事部" value="人事部" />
            <el-option label="财务部" value="财务部" />
          </el-select>
        </el-form-item>
        <el-form-item label="申请角色" prop="teamRole">
          <el-select
            v-model="applyForm.teamRole"
            placeholder="请选择申请的团队角色"
            style="width: 100%"
          >
            <el-option label="项目经理" value="项目经理" />
            <el-option label="技术负责人" value="技术负责人" />
            <el-option label="前端工程师" value="前端工程师" />
            <el-option label="后端工程师" value="后端工程师" />
            <el-option label="全栈工程师" value="全栈工程师" />
            <el-option label="UI设计师" value="UI设计师" />
            <el-option label="UX设计师" value="UX设计师" />
            <el-option label="产品经理" value="产品经理" />
            <el-option label="测试工程师" value="测试工程师" />
            <el-option label="运维工程师" value="运维工程师" />
            <el-option label="数据分析师" value="数据分析师" />
            <el-option label="运营专员" value="运营专员" />
          </el-select>
        </el-form-item>
        <el-form-item label="联系方式" prop="contact">
          <el-input
            v-model="applyForm.contact"
            placeholder="请输入联系方式（手机号或邮箱）"
            maxlength="50"
          />
        </el-form-item>
        <el-form-item label="工作经验" prop="experience">
          <el-select
            v-model="applyForm.experience"
            placeholder="请选择工作经验"
            style="width: 100%"
          >
            <el-option label="1年以下" value="1年以下" />
            <el-option label="1-3年" value="1-3年" />
            <el-option label="3-5年" value="3-5年" />
            <el-option label="5-10年" value="5-10年" />
            <el-option label="10年以上" value="10年以上" />
          </el-select>
        </el-form-item>
        <el-form-item label="申请理由" prop="reason">
          <el-input
            v-model="applyForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请简述申请加入团队的理由和个人优势"
            maxlength="300"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">提交申请</el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'

interface TeamMember {
  name: string
  department: string
  teamRole: string
  applyTime: string
  contact?: string
  experience?: string
  reason?: string
}

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirmed', memberData: TeamMember): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref()

// 申请表单
const applyForm = reactive({
  name: '',
  department: '',
  teamRole: '',
  contact: '',
  experience: '',
  reason: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在2到20个字符', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请选择所属部门', trigger: 'change' }
  ],
  teamRole: [
    { required: true, message: '请选择申请角色', trigger: 'change' }
  ],
  contact: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { 
      pattern: /^(1[3-9]\d{9}|[\w\.-]+@[\w\.-]+\.\w+)$/, 
      message: '请输入有效的手机号或邮箱', 
      trigger: 'blur' 
    }
  ],
  experience: [
    { required: true, message: '请选择工作经验', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入申请理由', trigger: 'blur' },
    { min: 10, message: '申请理由至少10个字符', trigger: 'blur' }
  ]
}

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听对话框打开，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  applyForm.name = ''
  applyForm.department = ''
  applyForm.teamRole = ''
  applyForm.contact = ''
  applyForm.experience = ''
  applyForm.reason = ''
  formRef.value?.clearValidate()
}

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 处理关闭
const handleClose = () => {
  emit('update:visible', false)
}

// 处理确认
const handleConfirm = async () => {
  try {
    await formRef.value?.validate()
    
    const memberData: TeamMember = {
      name: applyForm.name,
      department: applyForm.department,
      teamRole: applyForm.teamRole,
      applyTime: new Date().toLocaleDateString('zh-CN').replace(/\//g, '.'),
      contact: applyForm.contact,
      experience: applyForm.experience,
      reason: applyForm.reason
    }
    
    emit('confirmed', memberData)
    dialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}
</script>

<style scoped lang="scss">
.member-apply-dialog {
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>

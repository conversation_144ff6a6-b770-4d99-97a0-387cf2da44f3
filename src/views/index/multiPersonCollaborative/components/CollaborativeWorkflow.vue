<template>
	<div class="collaborative-workflow">
		<div class="page-header">
			<h2 class="page-title">协同工作流程</h2>
			<el-button
				type="primary"
				size="default"
				@click="handleMergeWorkflow"
			>
				合并协同流程
			</el-button>
		</div>

		<div class="content-placeholder">
			<ProcessEngine
				ref="processEngineRef"
				style="width: 100%; height: 100%"
				@selection-change="handleSelectionChange"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import {ref} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import ProcessEngine from '../../processEngine/index.vue'

// 选中的行数据
const selectedRows = ref<any[]>([])
// ProcessEngine组件引用
const processEngineRef = ref()

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
	selectedRows.value = selection
}

// 处理合并协同流程
const handleMergeWorkflow = () => {
	// 这里可以添加实际的合并逻辑
	// 使用弹窗显示成功提示
	ElMessageBox.alert('合并成功！！！', '提示', {
		confirmButtonText: '确定',
		type: 'success'
	}).then(() => {
		// 清空选择
		selectedRows.value = []
		// 如果ProcessEngine组件有clearSelection方法，可以调用
		if (processEngineRef.value && processEngineRef.value.clearSelection) {
			processEngineRef.value.clearSelection()
		}
	})
}
</script>

<style scoped lang="scss">
.collaborative-workflow {
	.page-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 30px;

		.page-title {
			margin: 0;
			font-size: 24px;
			font-weight: 600;
			color: #303133;
		}

		.page-description {
			margin: 0;
			color: #606266;
			font-size: 14px;
		}
	}

	.content-placeholder {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		min-height: 400px;
		background: #ffffff;
		border-radius: 12px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		padding: 40px;
		text-align: center;

		.placeholder-icon {
			margin-bottom: 20px;
			color: #e6a23c;
		}

		h3 {
			margin: 0 0 12px 0;
			font-size: 20px;
			color: #303133;
		}

		p {
			margin: 0 0 24px 0;
			color: #909399;
			font-size: 14px;
		}

		.feature-list {
			display: flex;
			flex-wrap: wrap;
			gap: 8px;
			justify-content: center;

			.feature-tag {
				margin: 0;
			}
		}
	}
}
</style>

<template>
  <div class="team-management">
    <Block title="团队管理" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
      <template #topRight>
        <div class="top-buttons">
          <el-button type="primary" @click="showMemberApplyDialog">
            <el-icon><UserFilled /></el-icon>
            普通成员加入申请
          </el-button>
          <el-button type="success" @click="showRoleManagementDialog">
            <el-icon><Setting /></el-icon>
            角色管理
          </el-button>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="姓名">
            <el-input
              v-model="searchForm.name"
              placeholder="请输入姓名"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="部门">
            <el-select
              v-model="searchForm.department"
              placeholder="请选择部门"
              clearable
              style="width: 150px"
            >
              <el-option label="全部" value="" />
              <el-option label="技术部" value="技术部" />
              <el-option label="产品部" value="产品部" />
              <el-option label="设计部" value="设计部" />
              <el-option label="测试部" value="测试部" />
              <el-option label="运营部" value="运营部" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="全部" value="" />
              <el-option label="待审核" value="pending" />
              <el-option label="审核成功" value="approved" />
              <el-option label="审核失败" value="rejected" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <div class="table-container">
        <TableV2
          ref="tableRef"
          :defaultTableData="tableData"
          :columns="tableColumns"
          :enable-toolbar="false"
          :enable-own-button="true"
          :enable-selection="false"
          :enable-index="false"
          :height="tableHeight"
          :buttons="tableButtons"
          :loading="loading"
          @click-button="onTableClickButton"
        >
          <template #status="{ row }">
            <el-tag
              :type="getStatusTagType(row.status)"
              size="small"
            >
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </TableV2>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onPageSizeChange"
          @current-change="onCurrentPageChange"
        />
      </div>
    </Block>

    <!-- 审批对话框 -->
    <ApprovalDialog
      v-model:visible="approvalDialogVisible"
      :member-data="currentMember"
      @confirmed="handleApprovalConfirmed"
    />

    <!-- 角色分配对话框 -->
    <RoleAssignDialog
      v-model:visible="roleAssignDialogVisible"
      :member-data="currentMember"
      @confirmed="handleRoleAssignConfirmed"
    />

    <!-- 成员申请对话框 -->
    <MemberApplyDialog
      v-model:visible="memberApplyDialogVisible"
      @confirmed="handleMemberApplyConfirmed"
    />

    <!-- 角色管理对话框 -->
    <RoleManagementDialog
      v-model:visible="roleManagementDialogVisible"
      @confirmed="handleRoleManagementConfirmed"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UserFilled, Setting, Search, Refresh } from '@element-plus/icons-vue'

import ApprovalDialog from './ApprovalDialog.vue'
import RoleAssignDialog from './RoleAssignDialog.vue'
import MemberApplyDialog from './MemberApplyDialog.vue'
import RoleManagementDialog from './RoleManagementDialog.vue'

// 团队成员接口
interface TeamMember {
  id: string
  sequence: number
  name: string
  department: string
  teamRole: string
  status: 'pending' | 'approved' | 'rejected'
  applyTime: string
  approveTime?: string
  approver?: string
}

// 搜索表单
const searchForm = reactive({
  name: '',
  department: '',
  status: ''
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 表格相关
const tableRef = ref()
const tableHeight = ref(400)
const tableData = ref<TeamMember[]>([])
const allMembers = ref<TeamMember[]>([])
const loading = ref(false)

// 对话框状态
const approvalDialogVisible = ref(false)
const roleAssignDialogVisible = ref(false)
const memberApplyDialogVisible = ref(false)
const roleManagementDialogVisible = ref(false)
const currentMember = ref<TeamMember | null>(null)

// 表格列配置
const tableColumns = [
  { prop: 'sequence', label: '序号' },
  { prop: 'name', label: '姓名' },
  { prop: 'department', label: '所属部门' },
  { prop: 'teamRole', label: '团队角色' },
  { prop: 'status', label: '状态', slot: 'status' }
]

// TableV2 操作按钮配置
const tableButtons = ref([
  {
    label: '审批',
    type: 'primary',
    code: 'approve'
  },
  {
    label: '角色分配',
    type: 'success',
    code: 'assign_role'
  }
])

// 根据状态获取操作按钮
const getOperationButtons = (row: TeamMember) => {
  switch (row.status) {
    case 'pending':
      return [{ code: 'approve', label: '审批', type: 'primary' }]
    case 'approved':
      return [{ code: 'assign_role', label: '角色分配', type: 'success' }]
    case 'rejected':
      return []
    default:
      return []
  }
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'pending':
      return 'warning'
    case 'approved':
      return 'success'
    case 'rejected':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态标签文本
const getStatusLabel = (status: string) => {
  switch (status) {
    case 'pending':
      return '待审核'
    case 'approved':
      return '审核成功'
    case 'rejected':
      return '审核失败'
    default:
      return '未知'
  }
}

// 本地存储键名
const STORAGE_KEY = 'team_members_data'

// 从本地存储加载数据
const loadMembersFromStorage = (): TeamMember[] => {
  const stored = localStorage.getItem(STORAGE_KEY)
  if (stored) {
    return JSON.parse(stored)
  }

  // 返回模拟数据
  return generateMockData()
}

// 保存数据到本地存储
const saveMembersToStorage = (members: TeamMember[]) => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(members))
}

// 生成模拟数据
const generateMockData = (): TeamMember[] => {
  const mockData: TeamMember[] = [
    // 待审核成员
    {
      id: 'member_1',
      sequence: 1,
      name: '张建国',
      department: '技术部',
      teamRole: '开发工程师',
      status: 'pending',
      applyTime: '2025.01.15'
    },
    {
      id: 'member_2',
      sequence: 2,
      name: '李明华',
      department: '产品部',
      teamRole: '产品经理',
      status: 'pending',
      applyTime: '2025.01.16'
    },
    {
      id: 'member_3',
      sequence: 3,
      name: '王秀英',
      department: '设计部',
      teamRole: 'UI设计师',
      status: 'pending',
      applyTime: '2025.01.17'
    },
    // 审核成功成员
    {
      id: 'member_4',
      sequence: 4,
      name: '刘志强',
      department: '技术部',
      teamRole: '前端工程师',
      status: 'approved',
      applyTime: '2025.01.10',
      approveTime: '2025.01.12',
      approver: '管理员'
    },
    {
      id: 'member_5',
      sequence: 5,
      name: '陈美玲',
      department: '测试部',
      teamRole: '测试工程师',
      status: 'approved',
      applyTime: '2025.01.08',
      approveTime: '2025.01.10',
      approver: '管理员'
    },
    // 审核失败成员
    {
      id: 'member_6',
      sequence: 6,
      name: '杨国庆',
      department: '运营部',
      teamRole: '运营专员',
      status: 'rejected',
      applyTime: '2025.01.05',
      approveTime: '2025.01.07',
      approver: '管理员'
    },
    // 更多待审核成员
    {
      id: 'member_7',
      sequence: 7,
      name: '赵小红',
      department: '技术部',
      teamRole: '后端工程师',
      status: 'pending',
      applyTime: '2025.01.18'
    },
    {
      id: 'member_8',
      sequence: 8,
      name: '黄建军',
      department: '产品部',
      teamRole: '产品助理',
      status: 'pending',
      applyTime: '2025.01.19'
    },
    {
      id: 'member_9',
      sequence: 9,
      name: '周丽娟',
      department: '设计部',
      teamRole: 'UX设计师',
      status: 'pending',
      applyTime: '2025.01.20'
    },
    {
      id: 'member_10',
      sequence: 10,
      name: '吴永强',
      department: '市场部',
      teamRole: '市场专员',
      status: 'pending',
      applyTime: '2025.01.21'
    },
    // 更多审核成功成员
    {
      id: 'member_11',
      sequence: 11,
      name: '徐海燕',
      department: '技术部',
      teamRole: '全栈工程师',
      status: 'approved',
      applyTime: '2025.01.01',
      approveTime: '2025.01.03',
      approver: '管理员'
    },
    {
      id: 'member_12',
      sequence: 12,
      name: '孙志华',
      department: '运营部',
      teamRole: '数据分析师',
      status: 'approved',
      applyTime: '2024.12.28',
      approveTime: '2024.12.30',
      approver: '管理员'
    },
    {
      id: 'member_13',
      sequence: 13,
      name: '胡晓明',
      department: '人事部',
      teamRole: '人事专员',
      status: 'approved',
      applyTime: '2024.12.25',
      approveTime: '2024.12.27',
      approver: '管理员'
    },
    {
      id: 'member_14',
      sequence: 14,
      name: '朱建平',
      department: '财务部',
      teamRole: '财务分析师',
      status: 'approved',
      applyTime: '2024.12.20',
      approveTime: '2024.12.22',
      approver: '管理员'
    },
    // 更多审核失败成员
    {
      id: 'member_15',
      sequence: 15,
      name: '高秀芳',
      department: '技术部',
      teamRole: '运维工程师',
      status: 'rejected',
      applyTime: '2025.01.02',
      approveTime: '2025.01.04',
      approver: '管理员'
    },
    {
      id: 'member_16',
      sequence: 16,
      name: '林志远',
      department: '设计部',
      teamRole: '平面设计师',
      status: 'rejected',
      applyTime: '2024.12.30',
      approveTime: '2025.01.01',
      approver: '管理员'
    },
    {
      id: 'member_17',
      sequence: 17,
      name: '何丽华',
      department: '产品部',
      teamRole: '产品运营',
      status: 'pending',
      applyTime: '2025.01.22'
    },
    {
      id: 'member_18',
      sequence: 18,
      name: '郭建设',
      department: '技术部',
      teamRole: '架构师',
      status: 'approved',
      applyTime: '2024.12.15',
      approveTime: '2024.12.17',
      approver: '管理员'
    },
    {
      id: 'member_19',
      sequence: 19,
      name: '马小军',
      department: '测试部',
      teamRole: '自动化测试工程师',
      status: 'approved',
      applyTime: '2024.12.10',
      approveTime: '2024.12.12',
      approver: '管理员'
    },
    {
      id: 'member_20',
      sequence: 20,
      name: '罗美英',
      department: '运营部',
      teamRole: '内容运营',
      status: 'pending',
      applyTime: '2025.01.23'
    },
    {
      id: 'member_21',
      sequence: 21,
      name: '梁志刚',
      department: '市场部',
      teamRole: '品牌经理',
      status: 'approved',
      applyTime: '2024.12.05',
      approveTime: '2024.12.07',
      approver: '管理员'
    },
    {
      id: 'member_22',
      sequence: 22,
      name: '宋晓丽',
      department: '技术部',
      teamRole: '移动端工程师',
      status: 'rejected',
      applyTime: '2024.12.28',
      approveTime: '2024.12.30',
      approver: '管理员'
    },
    {
      id: 'member_23',
      sequence: 23,
      name: '郑国强',
      department: '设计部',
      teamRole: '交互设计师',
      status: 'pending',
      applyTime: '2025.01.24'
    },
    {
      id: 'member_24',
      sequence: 24,
      name: '谢秀珍',
      department: '产品部',
      teamRole: '项目经理',
      status: 'approved',
      applyTime: '2024.11.30',
      approveTime: '2024.12.02',
      approver: '管理员'
    },
    {
      id: 'member_25',
      sequence: 25,
      name: '韩建华',
      department: '技术部',
      teamRole: '算法工程师',
      status: 'approved',
      applyTime: '2024.11.25',
      approveTime: '2024.11.27',
      approver: '管理员'
    }
  ]

  return mockData
}

// 加载表格数据
const loadTableData = () => {
  allMembers.value = loadMembersFromStorage()

  // 确保数据已保存到localStorage
  if (!localStorage.getItem(STORAGE_KEY)) {
    saveMembersToStorage(allMembers.value)
  }

  filterAndPaginateData()
}

// 筛选和分页数据
const filterAndPaginateData = () => {
  let filteredData = [...allMembers.value]

  // 应用搜索筛选
  if (searchForm.name) {
    filteredData = filteredData.filter(item =>
      item.name.includes(searchForm.name)
    )
  }

  if (searchForm.department) {
    filteredData = filteredData.filter(item =>
      item.department === searchForm.department
    )
  }

  if (searchForm.status) {
    filteredData = filteredData.filter(item =>
      item.status === searchForm.status
    )
  }

  // 更新总数
  pagination.total = filteredData.length

  // 分页
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  tableData.value = filteredData.slice(start, end)
}

// 搜索处理
const handleSearch = () => {
  pagination.currentPage = 1
  filterAndPaginateData()
}

// 重置搜索
const handleReset = () => {
  searchForm.name = ''
  searchForm.department = ''
  searchForm.status = ''
  pagination.currentPage = 1
  filterAndPaginateData()
}

// 分页大小改变
const onPageSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  filterAndPaginateData()
}

// 当前页改变
const onCurrentPageChange = (page: number) => {
  pagination.currentPage = page
  filterAndPaginateData()
}

// 块高度改变
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 200
}

// 表格按钮点击处理
const onTableClickButton = (data: any) => {
  let btn, row

  if (data && typeof data === 'object') {
    if (data.row && data.btn) {
      row = data.row
      btn = data.btn
    } else if (data.btn && data.scope) {
      btn = data.btn
      row = data.scope
    } else {
      return
    }
  } else {
    return
  }

  if (!btn || !row) {
    return
  }

  // 根据行状态判断是否允许操作
  const allowedActions = getOperationButtons(row).map(b => b.code)
  if (!allowedActions.includes(btn.code)) {
    ElMessage.warning('当前状态不允许此操作')
    return
  }

  const action = btn.code

  switch (action) {
    case 'approve':
      handleApprove(row)
      break
    case 'assign_role':
      handleAssignRole(row)
      break
  }
}

// 处理审批
const handleApprove = (row: TeamMember) => {
  if (!row || !row.name) {
    ElMessage.error('审批失败：成员数据无效')
    return
  }

  currentMember.value = { ...row }
  approvalDialogVisible.value = true
}

// 处理角色分配
const handleAssignRole = (row: TeamMember) => {
  if (!row || !row.name) {
    ElMessage.error('角色分配失败：成员数据无效')
    return
  }

  currentMember.value = { ...row }
  roleAssignDialogVisible.value = true
}

// 显示成员申请对话框
const showMemberApplyDialog = () => {
  memberApplyDialogVisible.value = true
}

// 显示角色管理对话框
const showRoleManagementDialog = () => {
  roleManagementDialogVisible.value = true
}

// 审批确认处理
const handleApprovalConfirmed = (result: { approved: boolean, member: TeamMember }) => {
  const { approved, member } = result

  // 更新成员状态
  const memberIndex = allMembers.value.findIndex(m => m.id === member.id)
  if (memberIndex !== -1) {
    allMembers.value[memberIndex] = {
      ...member,
      status: approved ? 'approved' : 'rejected',
      approveTime: new Date().toLocaleDateString('zh-CN').replace(/\//g, '.'),
      approver: '管理员'
    }

    saveMembersToStorage(allMembers.value)
    loadTableData()

    ElMessage.success(approved ? '审批通过' : '审批拒绝')
  }
}

// 角色分配确认处理
const handleRoleAssignConfirmed = (member: TeamMember) => {
  const memberIndex = allMembers.value.findIndex(m => m.id === member.id)
  if (memberIndex !== -1) {
    allMembers.value[memberIndex] = member
    saveMembersToStorage(allMembers.value)
    loadTableData()
    ElMessage.success('角色分配成功')
  }
}

// 成员申请确认处理
const handleMemberApplyConfirmed = (memberData: Omit<TeamMember, 'id' | 'sequence'>) => {
  const newMember: TeamMember = {
    ...memberData,
    id: `member_${Date.now()}`,
    sequence: allMembers.value.length + 1,
    status: 'pending'
  }

  allMembers.value.push(newMember)
  saveMembersToStorage(allMembers.value)
  loadTableData()
  ElMessage.success('申请提交成功')
}

// 角色管理确认处理
const handleRoleManagementConfirmed = () => {
  ElMessage.success('角色管理设置已保存')
}

// 组件挂载
onMounted(() => {
  loadTableData()
})
</script>

<style scoped lang="scss">
.team-management {
  .top-buttons {
    display: flex;
    gap: 12px;
  }

  .search-area {
    margin-bottom: 16px;
    padding: 16px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .table-container {
    margin-bottom: 16px;
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    padding: 16px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
</style>

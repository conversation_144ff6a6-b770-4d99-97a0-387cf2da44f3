<script setup lang="ts" name=sourcemodal>
import {onMounted, ref, reactive} from 'vue'
import {getPlanTask, getIgnoreRecord, getStatisticsStatementList} from '@/api/LedgerApi'
import on from 'gdt-jsapi/on'

interface Props {
	visible: any
	checkNames: string
}

const pageation = ref({
	currentPage: 1,
	pageSize: 10,
	total: 0,
})
const emits = defineEmits(['onCloseds'])

const props: Props = withDefaults(defineProps<Props>(), {
	visible: false,
	checkNames: '',
})

const dialogColData = [{field: 'name', title: `${props.checkNames}`}]
const dialogTableData = ref([])
const dialogForm: any = ref({})
const isLoading = ref(true)

const searchForm = reactive({
	Name: '',
})

const getDialogTableList = () => {
	isLoading.value = true
	const params = {
		SkipCount: (pageation.value.currentPage - 1) * pageation.value.pageSize,
		MaxResultCount: pageation.value.pageSize,
		regionId: JSON.parse(localStorage.getItem('currentDepartmentInfo') as string).regionId,
	}
	getStatisticsStatementList({...searchForm, ...params})
		.then((res: any) => {
			dialogTableData.value = res.data.items
			pageation.value.total = res.data.totalCount
		})
		.finally(() => {
			isLoading.value = false
		})
}
const onPageationChange = (val: any, type: any) => {
	if (type === 'size') {
		pageation.value.pageSize = val
	} else {
		pageation.value.currentPage = val
	}
	getDialogTableList()
}

const onClosed = () => {
	emits('onCloseds', false)
}
const onConfirm = () => {
	emits('onCloseds', false)
}

onMounted(() => {
	getDialogTableList()
})
const restData = () => {
	searchForm.Name = ''
	getDialogTableList()
}
const onClickTableButton = (btn, row) => {
	// const btns = JSON.parse(JSON.stringify(btn))
	// const rows = JSON.parse(JSON.stringify(row))
	console.log(btn.scope)

	if (btn.btn.code === 'view') {
		getIgnoreRecord({id: btn.scope.id, ignoreRecord: false}).then((res) => {
			getDialogTableList()
		})
	}

	if (btn.btn.code === 'delete') {
		getIgnoreRecord({id: btn.scope.id, ignoreRecord: true}).then((res) => {
			getDialogTableList()
		})
	}
}
</script>

<template>
	<Dialog
		width="900"
		title="统计配置"
		cancelText="关闭"
		v-model="props.visible"
		:enableButton="false"
		@close="onClosed"
		@clickCancel="onClosed"
		@clickConfirm="onConfirm"
	>
		<div class="search-items" style="margin-bottom: 10px;" mb-10px flex items-center>
			<!-- <h3 class="title">查询条件</h3> -->
			<!-- <el-form label-width="10" label-position="right" class="form">
				<el-form-item> -->
			<el-input v-model="searchForm.Name" placeholder="输入名称搜索" @keyup.enter="getDialogTableList" clearable style="width: 300px;"></el-input>
			<!-- </el-form-item>
			</el-form> -->
			<el-button size="default" type="default" @click="restData" class="mg-left-10" >
				<i class="i-ic-outline-cleaning-services" mg-right-5px></i>
				清空
			</el-button>
			<el-button size="default" type="primary" @click="getDialogTableList">
				<i class="i-ic-baseline-send"  mg-right-5px></i>
				查询</el-button
			>
		</div>
		<div class="reslut">
			<BaseTableComp
				ref="tableRef"
				:auto-height="true"
				:offsetHeight="0"
				:colData="dialogColData"
				:checkbox="false"
				:data="dialogTableData"
				@click-button="onClickTableButton"
				:currentPage="pageation.currentPage"
				:pageSize="pageation.pageSize"
				:total="pageation.total"
				@size-change="onPageationChange($event, 'size')"
				@current-change="onPageationChange($event, 'current')"
				:buttons="[
					{
						code: 'view',
						title: '纳入统计',
						icon: '<i i-majesticons-eye-line></i>',
						verify: 'row.ignoreRecord',
					},
					{
						code: 'delete',
						title: '移除',
						type: 'danger',
						icon: '<i i-ic-round-dangerous></i>',
						verify: '!row.ignoreRecord',
					},
				]"
				:visibleSetting="false"
				:visiblePage="true"
				:hideHeader="true"
			>
				<template #apiQueryParameters="scope">
					{{ scope.rowData.apiQueryParameters.map((m: any) => m.displayName).join(',') }}
				</template>
			</BaseTableComp>
		</div>
		<template #footer>
			<el-button @click="restData">清空</el-button>
			<el-button type="primary" :loading="isLoading" @click="getDialogTableList">查询</el-button>
		</template>
	</Dialog>
</template>
<style scoped lang="scss">
.top-comp {
	justify-content: flex-end;
}
.search {
	display: flex;
	justify-items: flex-end;
}

.title {
	border-left: 3px solid var(--z-main);

	font-size: 14px;
	font-weight: 500;
	line-height: 1;
	padding-left: 5px;
	margin: 10px 0;
}
.search-items {
	border-radius: 5px;
	// border: var(--z-border);
	background-color: #fafafa;

	.form {
		width: 98%;
	}
}

.info-items {
	box-shadow: 0 1px 3px #0003, 0 1px 1px #00000024, 0 2px 1px -1px #0000001f;
	border-radius: 5px;
	background: rgb(255, 255, 255);
	display: flex;
	flex-wrap: wrap;
	max-width: 100%;
	margin: 0 auto;
	padding: 5px;

	.item {
		background-color: #fff;
		border: var(--z-border);
		display: flex;
		height: 40px;
		line-height: 40px;
		margin-bottom: -1px;
		margin-left: -1px;
		overflow: hidden;
		width: 50%;

		&:first-child {
			border-radius: 5px 0 0 0;
		}

		&:nth-child(2) {
			border-radius: 0 5px 0 0;
		}

		&:nth-last-child(2) {
			border-radius: 0 0 0 5px;
		}

		&:last-child {
			border-radius: 0 0 5px 0;
		}

		&:nth-child(2n) {
			margin-right: 0;
		}
	}

	.label {
		background-color: #fafafa;
		font-size: 13px;
		font-weight: 500;
		padding: 0 10px;
		text-align: left;
		width: 120px;
	}

	.value {
		color: #666;
		flex: 1;
		font-size: 13px;
		text-align: left;
		padding: 0 10px;
	}
}
</style>

<script setup lang="ts">
import {reactive, ref} from 'vue'
import {useRoute} from 'vue-router'
import {AuthorizationMode, AuthorizationTypeMap} from '@/define/Authorization'
import {
	CreateAuthorization,
	UpdateAuthorization,
	UpdateBatchAuthorization,
	DeleteAuthorization,
	DeleteBatchAuthorization,
} from '@/api/PlatformApi'
import Authorization from './Authorization.vue'
import {ElMessage} from 'element-plus'

const emits = defineEmits(['update:modelValue'])
const props = defineProps({
	modelValue: {
		type: Boolean,
		default: false,
	},
	height: {
		type: Number,
		default: 0,
	},
})

const route = useRoute()
const loading = ref(false)
const showAuthorization = ref(false)

const tableRef = ref()
const tableTotal = ref(0)
const currentRows: any = ref([])
const reqParams: any = reactive({
	ledgerId: route.query.id,
	skipCount: 0,
	maxResultCount: 10,
})

const columns = [
	{
		prop: 'authorizationMode',
		label: '授权模式',
	},
	{
		prop: '_authorization',
		label: '授权内容',
	},
	{
		prop: 'permissions',
		label: '授予权限',
	},
	{
		prop: 'creationTime',
		label: '授权时间',
		sortable: true,
	},
]
const tableButtons = [
	{
		prop: 'auth',
		label: '权限管理',
		type: 'primary',
	},
	{
		prop: 'delete',
		label: '取消授权',
		type: 'danger',
		popconfirm: '确定取消吗？',
	},
]
const searchFormItems = [
	{
		prop: 'userName',
		label: '',
		type: 'text',
		placeholder: '请输入名称',
	},
	{
		prop: 'permissions',
		label: '',
		type: 'select',
		attrs: {
			multiple: true,
		},
		placeholder: '请选择授予权限',
		options: Object.entries(AuthorizationTypeMap).map(([key, value]) => ({
			value: key,
			label: value,
		})),
	},
]

const onTableLoading = (val: boolean) => {
	loading.value = val
	emits('update:modelValue', val)
}

const onTableCompleted = () => {
	tableTotal.value = tableRef.value?.getTotal()
}

const onTableButtonClick = ({row, btn, index}: any) => {
	console.log(row, btn, index)
	if (btn.prop === 'auth') {
		currentRows.value = [row]
		showAuthorization.value = true
	} else if (btn.prop === 'delete') {
		DeleteAuthorization(row.id)
			.then(() => ElMessage.success('取消授权成功'))
			.catch((err) => window.errMsg(err, '取消授权'))
			.finally(() => {
				tableRef.value?.reload()
			})
	}
}

const onBatchAuth = () => {
	currentRows.value = tableRef.value?.getSelectionRows()
	if (currentRows.value.length === 0) {
		ElMessage.warning('请选择要授权的数据')
	} else {
		showAuthorization.value = true
	}
}

const onBatchDeleteAuth = () => {
	const rows = tableRef.value?.getSelectionRows()
	if (rows.length === 0) {
		ElMessage.warning('请选择要取消授权的数据')
	} else {
		const ids = rows.map((item: any) => item.id)
		DeleteBatchAuthorization(ids)
			.then(() => ElMessage.success('批量取消授权成功'))
			.catch((err) => window.errMsg(err, '批量取消授权'))
			.finally(() => {
				tableRef.value?.reload()
			})
	}
}

const onPaginationChange = (val: number, key: string) => {
	if (key === 'page') {
		reqParams.skipCount = (val - 1) * reqParams.maxResultCount
	} else {
		reqParams.maxResultCount = val
	}
}

const onAuthorizationConfirm = (val: any) => {
	console.log(val)

	const data: any = {
		ledgerId: reqParams.ledgerId,
		authorizationMode: val.mode,
		// departmentIds: null,
		// userIds: null,
		// roleIds: null,
		permissions: val.authorizationType,
	}

	const ids = val._authorization.map((item: any) => item.at(-1))

	if (val.mode === AuthorizationMode.Personnel) {
		data['userIds'] = ids
	} else if (val.mode === AuthorizationMode.Role) {
		data['roleIds'] = ids
	} else if (val.mode === AuthorizationMode.Department) {
		data['departmentIds'] = ids
	}

	if (currentRows.value.length === 0) {
		CreateAuthorization(data)
			.then(() => {})
			.catch((err) => window.errMsg(err, '添加授权'))
			.finally(() => {
				showAuthorization.value = false
				tableRef.value?.reload()
			})
	} else {
		const promise: any = []

		if (currentRows.value === 1) {
			const row = currentRows.value[0]
			promise.push(UpdateAuthorization({id: row.id}, val.authorizationType))
		} else {
			promise.push(
				UpdateBatchAuthorization({
					ids: currentRows.value.map((item: any) => item.id),
					permissions: val.authorizationType,
				})
			)
		}

		Promise.all(promise)
			.then((res) => {
				ElMessage.success('修改授权成功')
			})
			.catch((err) => window.errMsg(err, '修改授权'))
			.finally(() => {
				showAuthorization.value = false
				currentRows.value.length = 0
				tableRef.value?.reload()
			})
	}
}

defineExpose({
	getSearchFormItems: () => searchFormItems,
	setReqParams: (params: any) => {
		if (!params.permissions) delete reqParams.permissions
		Object.assign(reqParams, params)
		console.log('reqParams', reqParams)
	},
})
</script>
<template>
	<div class="department-authorization">
		<TableV2
			ref="tableRef"
			url="/api/ledger/ledgerPermissionsAuthorizationMode/all-authorization-modes"
			:columns="columns"
			:height="height"
			:reqParams="reqParams"
			:buttons="tableButtons"
			:enable-toolbar="true"
			:enable-create="false"
			:enable-edit="false"
			:enable-delete="false"
			:enable-selection="true"
			@click-button="onTableButtonClick"
			@completed="onTableCompleted"
			@loading="onTableLoading"
		>
			<template #toolbarLeft>
				<el-button size="small" type="primary" @click="showAuthorization = true">
					添加授权
				</el-button>
				<el-button size="small" type="primary" @click="onBatchAuth">批量编辑权限</el-button>
				<el-popconfirm title="确定取消吗?" @confirm="onBatchDeleteAuth">
					<template #reference>
						<el-button size="small" type="danger"> 批量取消授权 </el-button>
					</template>
				</el-popconfirm>
			</template>

			<template #authorizationMode="scoped">
				{{
					scoped.row.authorizationMode === AuthorizationMode.Personnel
						? '人员'
						: scoped.row.authorizationMode === AuthorizationMode.Role
						? '角色'
						: scoped.row.authorizationMode === AuthorizationMode.Department
						? '科室'
						: '-'
				}}
			</template>

			<template #_authorization="scoped">
				{{
					scoped.row.authorizationMode === AuthorizationMode.Personnel
						? scoped.row.user?.name
						: scoped.row.authorizationMode === AuthorizationMode.Role
						? scoped.row?.roleName
						: scoped.row.authorizationMode === AuthorizationMode.Department
						? scoped.row.department?.name
						: '-'
				}}
			</template>

			<template #permissions="scoped">
				{{
					scoped.row.permissions.map((item: any) => AuthorizationTypeMap[item]).join('、')
				}}
			</template>
		</TableV2>

		<Pagination
			:total="tableTotal"
			:current-page="reqParams.skipCount / reqParams.maxResultCount + 1"
			@current-change="onPaginationChange($event, 'page')"
			@size-change="onPaginationChange($event, 'size')"
		></Pagination>

		<Authorization
			v-model="showAuthorization"
			:rows="currentRows"
			@click-confirm="onAuthorizationConfirm"
			@closed="currentRows.length = 0"
		></Authorization>
	</div>
</template>

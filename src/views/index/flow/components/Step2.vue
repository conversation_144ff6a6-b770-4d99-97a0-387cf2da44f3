<script setup lang="ts">
import {nextTick, inject, onMounted, ref, watch} from 'vue'
import {FlowNodeTypes, DefalutWorkflow} from '@/define/Workflow'
import ReviewNode from './ReviewNode.vue'
import ReportNode from './ReportNode.vue'
import ConditionNode from './ConditionNode.vue'
import {useArrayToTree} from '@/hooks/useConvertHook'
import {useUserStore} from '@/stores/useUserStore'
import axios from 'axios'

enum DepartmentTypes {
	Same = 0, //填报人同部门,
	Upper = 1, //填报人上一级部门,
	UpperTwo = 2, //填报人上二级部门,
	UpperThree = 3, //填报人上三级部门,
	UpperFour = 4, //填报人上四级部门,
	UpperFive = 5, //填报人上五级部门,
}

const emits = defineEmits(['update:modelValue'])
const props = defineProps({
	modelValue: {type: Object, default: () => ({})}, // v-model
	templateIndex: {type: Number, default: 0},
	showButton: {type: String, default: ''},
	formData: {type: Object, default: {}},
})

const user = useUserStore()
const superAdmin = user.getUserInfo?.baseRoles.indexOf('ledger-super-admin') > -1

const flowRef = ref()
const flowConfig = ref(props.modelValue)
const flowHeight = ref(0)

const curClickNode: any = ref(null)
const curClickNodeParent: any = ref(null)
const curClickNodeParents: any = ref([])
const curEnterNode: any = ref(null)

const showReview = ref(false)
const showReport = ref(false)
const showCondition = ref(false)

const curInfoNode: any = ref(null)

const onFlowNodeMouseEnter = (_flowNodeInfo: any, currentNode: any) => {
	console.log(currentNode)
	curInfoNode.value = currentNode
	curEnterNode.value = currentNode
}

let isfull: boolean = false
const departmentOptions = [
	{label: '填报人同部门', value: DepartmentTypes.Same},
	{label: '填报人上一级部门', value: DepartmentTypes.Upper},
	{label: '填报人上二级部门', value: DepartmentTypes.UpperTwo},
	{label: '填报人上三级部门', value: DepartmentTypes.UpperThree},
	{label: '填报人上四级部门', value: DepartmentTypes.UpperFour},
	{label: '填报人上五级部门', value: DepartmentTypes.UpperFive},
]
const onFlowNodeClick = (flowNodeInfo: any) => {
	const {node} = flowNodeInfo // flow 组件对象
	// curClickNode.value = node

	console.log('Flow Emit Node Click: ', node)

	flowRef.value
		.getNodeBeforeById(node.id)
		.then((node: any, parentNode: any, parentNodes: any) => {
			// flow nodeConfig 对象
			curClickNode.value = null
			nextTick(() => {
				curClickNode.value = node
				curClickNodeParent.value = parentNode
				curClickNodeParents.value = parentNodes

				if (node.type === FlowNodeTypes.Review) {
					showReview.value = true
				} else if (node.type === FlowNodeTypes.Condition) {
					showCondition.value = true
				}
			})
		})
}

const onFlowResize = () => {
	const h = document.body.offsetHeight - (isfull ? 75 : 242)
	flowHeight.value = h
}

const output = () => {
	emits('update:modelValue', flowConfig.value)
}

watch(
	() => props.modelValue,
	(val) => {
		flowConfig.value = val
		getDepartmentChildren()
	},
	{deep: true}
)

watch(
	() => flowConfig.value,
	() => output(),
	{deep: true}
)

watch(
	() => props.templateIndex,
	(val: number) => {
		console.log('Template Index Change: ', val)

		if (DefalutWorkflow[val]) {
			console.log('Template Index Change: ', val)
			flowConfig.value = JSON.parse(JSON.stringify(DefalutWorkflow[val]))
			output()
		}
	},
	{immediate: true}
)

const reviewObj: any = ref({
	selectDepartmentGroup: [],
	originDepartMentData: [],
})

const getDepartmentChildren = async () => {
	axios
		.request({
			method: 'get',
			url: '/api/platform/departmentInternal/get-department-children',
			headers: {
				Urlkey: 'iframeCode',
			},
		})
		.then((res: any) => {
			const {data} = res
			const arr = data.map((v: any) => ({
				...v,
				label: v.name,
				value: v.id,
				// disabled: v?.department?.departmentId ? false : true,
				children: v.children === null ? [] : v.children,
				isLeaf: true,
				disabled: false,
			}))
			reviewObj.value.selectDepartmentGroup = useArrayToTree(
				arr,
				'id',
				'parentId',
				'name',
				true,
				'children'
			)
			reviewObj.value.originDepartMentData = [...departmentOptions, ...arr]
			console.log(reviewObj.value)
		})
}

onMounted(() => {
	onFlowResize()
	output()
	if (!superAdmin) {
		getDepartmentChildren()
	}
})

defineExpose({
	flowRef,
	size: (isFull: boolean) => {
		isfull = isFull
		onFlowResize()
	},
})
</script>
<template>
	<div class="flow">
		<Flow
			ref="flowRef"
			v-model="flowConfig"
			v-resize="onFlowResize"
			:height="flowHeight"
			:showButton="showButton"
			:showNodeInfo="true"
			@node-click="onFlowNodeClick"
			@node-mouse-enter="onFlowNodeMouseEnter"
			@node-mouse-leave="() => (curInfoNode = null)"
			width="100%"
		>
			<template #node-info>
				<div
					class="node-info-item"
					v-if="curInfoNode?.type === FlowNodeTypes.Review && curInfoNode"
				>
					<span class="title">审核人</span>

					<el-tag
						type="primary"
						v-for="item of curInfoNode.auditUsers"
						class="mg-right-10 mg-top-10"
					>
						{{
							departmentOptions.find(
								(f: any) => f.value === item.additionalConditionValue
							)?.label ||
							item.additionalConditionValueLabel ||
							item.additionalConditionLabel
						}}
						- {{ item.type == 3 ? item.valueLabel : item.value }}
					</el-tag>

					<span class="title mg-top-20">审核方式</span>

					<el-tag type="primary" class="mg-right-10 mg-top-10">
						{{
							curInfoNode.isCountersign ? '全部审核人都需审核' : '任一审核人审核即可'
						}}
					</el-tag>
				</div>

				<div
					class="node-info-item"
					v-else-if="curInfoNode?.type === FlowNodeTypes.Condition && curInfoNode"
				>
					<span class="title">是否默认分支</span>
					<el-tag type="primary" class="mg-right-10 mg-top-10">{{
						curInfoNode.config?.isDefault ? '是' : '否'
					}}</el-tag>
					<span class="title mg-top-20">对应值</span>
					<el-tag v-if="curInfoNode.config" type="primary" class="mg-right-10 mg-top-10">
						{{ Object.values(curInfoNode.config?.value).join() }}
					</el-tag>
				</div>
			</template>
		</Flow>

		<!-- <ReportNode
			v-model="showReport"
			:title="'节点配置 - ' + curClickNode?.label"
			:node="curClickNode"
			@click-close="showReport = false"
			@click-confirm="showReport = false"
		>
		</ReportNode> -->
		<ReviewNode
			v-model="showReview"
			:title="'节点配置 - ' + curClickNode?.label"
			:node="curClickNode"
			:parent-node="curClickNodeParent"
			:parent-nodes="curClickNodeParents"
			:formData="props.formData"
			:initData="reviewObj"
			@click-close="showReview = false"
			@click-confirm="(val) => (showReview = !val)"
		></ReviewNode>

		<ConditionNode
			v-model="showCondition"
			:title="'节点配置 - ' + curClickNode?.label"
			:node="curClickNode"
			:parent-node="curClickNodeParent"
			:parent-nodes="curClickNodeParents"
			@click-close="showCondition = false"
			@click-confirm="(val) => (showCondition = !val)"
		></ConditionNode>
	</div>
</template>
<style scoped lang="scss">
.node-info-item {
	.title {
		border-radius: 5px;
		background-color: var(--z-bg-secondary);
		font-weight: 500;
		font-size: 14px;
		padding: 10px;
		width: 100%;
	}
}
</style>

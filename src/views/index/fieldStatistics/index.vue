<script setup lang="ts" name="fieldStatistics">
import { ref, onMounted, nextTick } from 'vue'
import { router } from '@/router'
import * as echarts from 'echarts'

// 统计数据接口
interface FieldStatistic {
  id: string
  fieldName: string
  dataType: string
  usageFrequency: string
  lastUsedTime: string
}

// 响应式数据
const loading = ref(false)
const tableData = ref<FieldStatistic[]>([])
const tableHeight = ref(400)

// 图表引用
const pieChartRef = ref<HTMLDivElement>()
const barChartRef = ref<HTMLDivElement>()
let pieChart: echarts.ECharts | null = null
let barChart: echarts.ECharts | null = null

// 图表数据 - 根据模拟数据动态计算
const pieChartData = ref([
  { name: '邮箱地址', value: 1, color: '#5470c6' },
  { name: '年龄', value: 1, color: '#91cc75' },
  { name: '性别', value: 1, color: '#fac858' },
  { name: '出生年月', value: 1, color: '#ee6666' },
  { name: '残疾证号', value: 1, color: '#73c0de' }
])

const barChartData = ref([
  { name: '高', value: 1, color: '#5470c6' },
  { name: '中', value: 1, color: '#91cc75' },
  { name: '低', value: 1, color: '#fac858' },
  { name: '无', value: 2, color: '#ee6666' }
])

// 表格列定义
const columns = [
  { field: 'fieldName', title: '字段名称', minWidth: 200 },
  { field: 'dataType', title: '数据类型', minWidth: 150 },
  { field: 'usageFrequency', title: '使用频率', minWidth: 120 },
  { field: 'lastUsedTime', title: '最后使用时间', minWidth: 180 }
]

// 模拟数据
const mockData: FieldStatistic[] = [
  {
    id: '1',
    fieldName: '邮箱地址',
    dataType: '邮箱地址',
    usageFrequency: '高',
    lastUsedTime: '2025.7.4'
  },
  {
    id: '2',
    fieldName: '年龄',
    dataType: '年龄',
    usageFrequency: '中',
    lastUsedTime: ''
  },
  {
    id: '3',
    fieldName: '性别',
    dataType: '性别',
    usageFrequency: '低',
    lastUsedTime: ''
  },
  {
    id: '4',
    fieldName: '出生年月',
    dataType: '出生年月',
    usageFrequency: '无',
    lastUsedTime: ''
  },
  {
    id: '5',
    fieldName: '残疾证号',
    dataType: '残疾证号',
    usageFrequency: '无',
    lastUsedTime: ''
  }
]

// 返回
const onBack = () => {
  router.push('/fieldType')
}

// Block高度变化
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 300
}

// 初始化饼图
const initPieChart = () => {
  if (!pieChartRef.value) {
    console.log('pieChartRef.value is null')
    return
  }

  console.log('Initializing pie chart...')
  console.log('pieChartRef dimensions:', pieChartRef.value.offsetWidth, 'x', pieChartRef.value.offsetHeight)

  pieChart = echarts.init(pieChartRef.value)

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: pieChartData.value.map(item => item.name)
    },
    series: [
      {
        name: '字段类型分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: pieChartData.value.map(item => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ]
  }

  pieChart.setOption(option)

  // 强制重新渲染
  setTimeout(() => {
    if (pieChart) {
      pieChart.resize()
    }
  }, 200)

  console.log('Pie chart initialized')
}

// 初始化柱状图
const initBarChart = () => {
  if (!barChartRef.value) {
    console.log('barChartRef.value is null')
    return
  }

  console.log('Initializing bar chart...')
  console.log('barChartRef dimensions:', barChartRef.value.offsetWidth, 'x', barChartRef.value.offsetHeight)

  barChart = echarts.init(barChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: barChartData.value.map(item => item.name),
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '使用频率',
        type: 'bar',
        barWidth: '60%',
        data: barChartData.value.map(item => ({
          value: item.value,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ]
  }

  barChart.setOption(option)

  // 强制重新渲染
  setTimeout(() => {
    if (barChart) {
      barChart.resize()
    }
  }, 200)

  console.log('Bar chart initialized')
}

// 窗口大小变化时重新调整图表大小
const resizeCharts = () => {
  if (pieChart) {
    pieChart.resize()
  }
  if (barChart) {
    barChart.resize()
  }
}

// 初始化
onMounted(() => {
  console.log('Component mounted')
  tableData.value = mockData

  nextTick(() => {
    console.log('nextTick called')
    console.log('pieChartRef.value:', pieChartRef.value)
    console.log('barChartRef.value:', barChartRef.value)

    // 延迟一下确保DOM完全渲染
    setTimeout(() => {
      initPieChart()
      initBarChart()

      // 添加窗口大小变化监听
      window.addEventListener('resize', resizeCharts)
    }, 100)
  })
})
</script>
<route>
{
meta: {
title:'字段信息使用统计',
ignoreLabel:false
}
}
</route>
<template>
  <div class="field-statistics">
    <Block title="字段信息使用统计" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
      <template #topRight>
        <el-button size="small" @click="onBack">返回</el-button>
      </template>

      <!-- 统计图表区域 -->
      <div class="statistics-charts">
        <div class="chart-row">
          <!-- 饼图 -->
          <div class="chart-container">
            <h3>字段类型数据分布图</h3>
            <div ref="pieChartRef" class="echarts-container" style="width: 100%; height: 300px; background: #f5f5f5;"></div>
          </div>

          <!-- 柱状图 -->
          <div class="chart-container">
            <h3>字段使用频率</h3>
            <div ref="barChartRef" class="echarts-container" style="width: 100%; height: 300px; background: #f5f5f5;"></div>
          </div>
        </div>
      </div>

      <!-- 字段列表 -->
      <div class="field-list">
        <h3>字段列表</h3>
        <el-table
          ref="tableRef"
          :data="tableData"
          :height="tableHeight"
          v-loading="loading"
          border
          stripe
        >
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column
            v-for="column in columns"
            :key="column.field"
            :prop="column.field"
            :label="column.title"
            :min-width="column.minWidth"
            show-overflow-tooltip
          />
        </el-table>
      </div>
    </Block>
  </div>
</template>

<style scoped>
.field-statistics {
  height: 100%;
}

.statistics-charts {
  margin-bottom: 24px;
}

.chart-row {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.chart-container {
  flex: 1;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  min-height: 350px;
}

.chart-container h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.echarts-container {
  width: 100%;
  height: 300px;
  min-height: 300px;
}

.field-list h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}
</style>

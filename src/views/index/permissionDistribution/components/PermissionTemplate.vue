<script lang="ts" setup>
import {ref, reactive, computed, nextTick, watch} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {ArrowDown} from '@element-plus/icons-vue'
import {ACTION_KEY, useLocalStorage} from '@/hooks/useLocalStorage'
import {useSleep} from '@/hooks/useSleep'
import {useUserStore} from '@/stores/useUserStore'

const userInfo = useUserStore()

// 权限分配模板数据接口
interface PermissionTemplateItem {
	id: number
	templateName: string
	assignedPermissions: string
	callCount: number
	status: string
	commentCount: number
	replyCount: number
	favoriteCount: number
	isTop?: boolean
}

// 评论数据接口
interface CommentItem {
	id: number
	templateId: number
	commenterName: string
	commentTime: string
	commentContent: string
	replyTime?: string
	replyContent?: string
}

// 评论表单接口
interface CommentForm {
	commenterName: string
	commentContent: string
}

// 回复表单接口
interface ReplyForm {
	replyContent: string
}

// 表格配置
const tableConfig = reactive({
	loading: false,
	data: [],
	total: 0,
	pageSize: 10,
	currentPage: 1,
})

// 搜索参数
const searchParams = reactive<{[key: string]: string}>({
	templateName: '',
	assignedPermissions: '',
	status: '',
})

// 表单配置
const formProps = [
	{
		label: '模板名称',
		field: 'templateName',
		type: 'text',
		placeholder: '请输入模板名称',
	},
	{
		label: '分配权限',
		field: 'assignedPermissions',
		type: 'text',
		placeholder: '请输入分配权限',
	},
	{
		label: '状态',
		field: 'status',
		type: 'select',
		placeholder: '请选择状态',
		options: [
			{label: '启用', value: '启用'},
			{label: '禁用', value: '禁用'},
		],
	},
]

// 表格列配置
const columns = [
	{prop: 'templateName', label: '模板名称'},
	{prop: 'assignedPermissions', label: '分配权限'},
	{prop: 'callCount', label: '调用次数'},
	{prop: 'status', label: '状态'},
	{prop: 'isTop', label: '是否置顶', slot: 'isTop'},
	{prop: 'commentCount', label: '评论数'},
	{prop: 'replyCount', label: '回复数'},
	{prop: 'favoriteCount', label: '收藏'},
	{prop: 'operation', label: '操作',width: '200px'},
]

// 置顶模板ID（只能有一个置顶模板）
const topTemplateId = ref<number | null>(null)

// 选中的行
const selectedRows = ref<PermissionTemplateItem[]>([])

// 新增模板对话框
const addDialogVisible = ref(false)
const addForm = ref({
	templateName: '',
	assignedPermissions: '',
	description: '',
	isTop: false
})

// 评论对话框
const commentDialogVisible = ref(false)
const commentForm = ref<CommentForm>({
	commenterName: '',
	commentContent: ''
})
const currentCommentTemplate = ref<PermissionTemplateItem | null>(null)

// 回复对话框
const replyDialogVisible = ref(false)
const replyForm = ref<ReplyForm>({
	replyContent: ''
})
const currentReplyComment = ref<CommentItem | null>(null)

// 评论列表对话框
const commentListDialogVisible = ref(false)
const currentTemplateComments = ref<CommentItem[]>([])
const currentTemplateForComments = ref<PermissionTemplateItem | null>(null)

// 评论数据存储
const commentsData = ref<CommentItem[]>([
	{
		id: 1,
		templateId: 1,
		commenterName: '张三',
		commentTime: '2024-01-15 10:30:00',
		commentContent: '这个权限模板配置很全面，适合数据管理员使用',
		replyTime: '2024-01-15 14:20:00',
		replyContent: '感谢您的反馈，我们会继续优化模板配置'
	},
	{
		id: 2,
		templateId: 1,
		commenterName: '李四',
		commentTime: '2024-01-16 09:15:00',
		commentContent: '建议增加数据导入权限',
	},
	{
		id: 3,
		templateId: 2,
		commenterName: '王五',
		commentTime: '2024-01-17 16:45:00',
		commentContent: '审核流程权限设置合理',
		replyTime: '2024-01-17 17:30:00',
		replyContent: '谢谢建议，我们会根据实际需求调整'
	}
])

// 模拟数据
const tableData = ref<PermissionTemplateItem[]>([
	{
		id: 1,
		templateName: '数据管理员权限模板',
		assignedPermissions: '数据查看、数据编辑、数据导出',
		callCount: 156,
		status: '启用',
		commentCount: 12,
		replyCount: 8,
		favoriteCount: 25,
		isTop: false,
	},
	{
		id: 2,
		templateName: '审核员权限模板',
		assignedPermissions: '审核权限、驳回权限、通过权限',
		callCount: 89,
		status: '启用',
		commentCount: 6,
		replyCount: 4,
		favoriteCount: 18,
		isTop: false,
	},
	{
		id: 3,
		templateName: '普通用户权限模板',
		assignedPermissions: '数据查看、基础操作',
		callCount: 234,
		status: '启用',
		commentCount: 15,
		replyCount: 12,
		favoriteCount: 42,
		isTop: false,
	},
	{
		id: 4,
		templateName: '系统管理员权限模板',
		assignedPermissions: '系统配置、用户管理、权限分配',
		callCount: 67,
		status: '禁用',
		commentCount: 3,
		replyCount: 2,
		favoriteCount: 8,
		isTop: false,
	},
	{
		id: 5,
		templateName: '财务专员权限模板',
		assignedPermissions: '财务数据查看、报表生成、审核权限',
		callCount: 123,
		status: '启用',
		commentCount: 9,
		replyCount: 7,
		favoriteCount: 31,
		isTop: false,
	},
	{
		id: 6,
		templateName: '人事管理权限模板',
		assignedPermissions: '员工信息管理、考勤管理、薪资查看',
		callCount: 78,
		status: '启用',
		commentCount: 5,
		replyCount: 3,
		favoriteCount: 16,
		isTop: false,
	},
	{
		id: 7,
		templateName: '项目经理权限模板',
		assignedPermissions: '项目管理、任务分配、进度查看',
		callCount: 145,
		status: '启用',
		commentCount: 11,
		replyCount: 9,
		favoriteCount: 28,
		isTop: false,
	},
	{
		id: 8,
		templateName: '客服专员权限模板',
		assignedPermissions: '客户信息查看、工单处理、回复权限',
		callCount: 201,
		status: '启用',
		commentCount: 18,
		replyCount: 15,
		favoriteCount: 35,
		isTop: false,
	},
	{
		id: 9,
		templateName: '销售代表权限模板',
		assignedPermissions: '客户管理、订单查看、销售报表',
		callCount: 167,
		status: '禁用',
		commentCount: 7,
		replyCount: 5,
		favoriteCount: 22,
		isTop: false,
	},
	{
		id: 10,
		templateName: '技术支持权限模板',
		assignedPermissions: '系统维护、故障处理、日志查看',
		callCount: 92,
		status: '启用',
		commentCount: 4,
		replyCount: 2,
		favoriteCount: 13,
		isTop: false,
	},
])

const deepData = ref<PermissionTemplateItem[]>([...tableData.value])

// 本地存储
const storage: any = useLocalStorage()

// 分页配置
const pagination = reactive({
	total: 0,
	size: 10,
	page: 1,
})

// 分页数据
const paginatedData = computed(() => {
	// 先按置顶状态排序，置顶的在前面
	const sortedData = [...tableData.value].sort((a, b) => {
		if (a.isTop && !b.isTop) return -1
		if (!a.isTop && b.isTop) return 1
		return 0
	})

	const start = (pagination.page - 1) * pagination.size
	const end = start + pagination.size
	return sortedData.slice(start, end)
})

// 搜索功能
const handlSearch = () => {
	let filteredData = [...deepData.value]
	
	if (searchParams.templateName) {
		filteredData = filteredData.filter(item => 
			item.templateName.includes(searchParams.templateName)
		)
	}
	
	if (searchParams.assignedPermissions) {
		filteredData = filteredData.filter(item => 
			item.assignedPermissions.includes(searchParams.assignedPermissions)
		)
	}
	
	if (searchParams.status) {
		filteredData = filteredData.filter(item => 
			item.status === searchParams.status
		)
	}
	
	tableData.value = filteredData
	pagination.total = filteredData.length
	pagination.page = 1
}

// 重置搜索
const resetSearch = () => {
	Object.keys(searchParams).forEach(key => {
		searchParams[key] = ''
	})
	tableData.value = [...deepData.value]
	pagination.total = deepData.value.length
	pagination.page = 1
}

// 获取当前时间戳
const getCurrentTimestamp = () => {
	const now = new Date()
	const year = now.getFullYear()
	const month = String(now.getMonth() + 1).padStart(2, '0')
	const day = String(now.getDate()).padStart(2, '0')
	const hours = String(now.getHours()).padStart(2, '0')
	const minutes = String(now.getMinutes()).padStart(2, '0')
	const seconds = String(now.getSeconds()).padStart(2, '0')
	return `${year}${month}${day}_${hours}${minutes}${seconds}`
}

// 导出模板功能
const exportTemplate = (row: PermissionTemplateItem) => {
	const timestamp = getCurrentTimestamp()
	const fileName = `${timestamp}-${row.templateName}`

	// 创建导出数据
	const exportData = {
		templateInfo: {
			templateName: row.templateName,
			assignedPermissions: row.assignedPermissions,
			status: row.status,
			callCount: row.callCount,
			commentCount: row.commentCount,
			replyCount: row.replyCount,
			favoriteCount: row.favoriteCount,
			exportTime: new Date().toLocaleString(),
		},
		permissions: row.assignedPermissions.split('、').map(permission => ({
			name: permission,
			type: '数据权限',
			description: `${permission}相关权限配置`
		}))
	}

	// 转换为JSON字符串
	const jsonString = JSON.stringify(exportData, null, 2)

	// 创建Blob对象
	const blob = new Blob([jsonString], { type: 'application/json' })

	// 创建下载链接
	const url = URL.createObjectURL(blob)
	const link = document.createElement('a')
	link.href = url
	link.download = `${fileName}.json`

	// 触发下载
	document.body.appendChild(link)
	link.click()
	document.body.removeChild(link)

	// 释放URL对象
	URL.revokeObjectURL(url)

	ElMessage.success(`导出成功：${fileName}.json`)
}

// 修改操作
const handleEdit = (row: PermissionTemplateItem) => {
	ElMessage.info(`编辑模板：${row.templateName}`)
}

// 删除操作
const handleDelete = (row: PermissionTemplateItem) => {
	ElMessageBox.confirm(`确定要删除模板"${row.templateName}"吗？`, '删除确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
	.then(() => {
		// 如果删除的是置顶模板，清除置顶状态
		if (row.isTop) {
			topTemplateId.value = null
		}

		const actualIndex = tableData.value.findIndex(item => item.id === row.id)
		if (actualIndex !== -1) {
			tableData.value.splice(actualIndex, 1)
			deepData.value.splice(actualIndex, 1)
			pagination.total = tableData.value.length
			saveToLocalStorage()
			ElMessage.success('删除成功')
		}
	})
	.catch(() => {})
}

// 查看操作
const handleView = (row: PermissionTemplateItem) => {
	ElMessage.info(`查看模板：${row.templateName}`)
}

// 更多操作处理
const handleMoreAction = (command: string, row: PermissionTemplateItem) => {
	switch (command) {
		case 'export':
			// 导出模板
			exportTemplate(row)
			break
		case 'favorite':
			// 收藏
			row.favoriteCount++
			saveToLocalStorage()
			ElMessage.success('收藏成功')
			break
		case 'top':
			// 置顶（只能有一个置顶模板）
			const currentTopTemplate = tableData.value.find(item => item.isTop)
			if (currentTopTemplate && currentTopTemplate.id !== row.id) {
				ElMessage.warning('已有置顶模板，请先取消当前置顶模板')
				return
			}

			// 设置当前模板为置顶
			row.isTop = true
			topTemplateId.value = row.id
			saveToLocalStorage()
			ElMessage.success('置顶成功')
			break
		case 'call':
			// 调用
			row.callCount++
			saveToLocalStorage()
			ElMessage.success('调用成功')
			break
		case 'comment':
			// 打开评论对话框
			currentCommentTemplate.value = row
			commentForm.value = {
				commenterName: '',
				commentContent: ''
			}
			commentDialogVisible.value = true
			break
		case 'reply':
			// 打开回复列表对话框
			currentTemplateForComments.value = row
			currentTemplateComments.value = commentsData.value.filter(comment => comment.templateId === row.id)
			commentListDialogVisible.value = true
			break
		default:
			break
	}
}

// 新增模板
const handleAdd = () => {
	addDialogVisible.value = true
	// 重置表单
	addForm.value = {
		templateName: '',
		assignedPermissions: '',
		description: '',
		isTop: false
	}
}

// 确认新增模板
const confirmAdd = () => {
	if (!addForm.value.templateName.trim()) {
		ElMessage.warning('请输入模板名称')
		return
	}
	if (!addForm.value.assignedPermissions.trim()) {
		ElMessage.warning('请输入权限配置')
		return
	}
	if (!addForm.value.description.trim()) {
		ElMessage.warning('请输入模板描述')
		return
	}

	// 检查置顶逻辑
	if (addForm.value.isTop) {
		const currentTopTemplate = tableData.value.find(item => item.isTop)
		if (currentTopTemplate) {
			ElMessage.warning('已有置顶模板，请先取消当前置顶模板')
			return
		}
	}

	// 生成新的ID
	const newId = Math.max(...tableData.value.map(item => item.id)) + 1

	// 创建新模板
	const newTemplate: PermissionTemplateItem = {
		id: newId,
		templateName: addForm.value.templateName,
		assignedPermissions: addForm.value.assignedPermissions,
		callCount: 0,
		status: '启用',
		commentCount: 0,
		replyCount: 0,
		favoriteCount: 0,
		isTop: addForm.value.isTop
	}

	// 添加到表格数据
	tableData.value.unshift(newTemplate)
	deepData.value.unshift(newTemplate)

	// 如果设置为置顶
	if (addForm.value.isTop) {
		topTemplateId.value = newId
		ElMessage.success('新增模板成功并已置顶')
	} else {
		ElMessage.success('新增模板成功')
	}

	// 更新分页和保存数据
	pagination.total = tableData.value.length
	saveToLocalStorage()

	// 关闭对话框
	addDialogVisible.value = false
}

// 取消新增
const cancelAdd = () => {
	addDialogVisible.value = false
}

// 提交评论
const submitComment = () => {
	if (!commentForm.value.commenterName.trim()) {
		ElMessage.warning('请输入评论人姓名')
		return
	}
	if (!commentForm.value.commentContent.trim()) {
		ElMessage.warning('请输入评论内容')
		return
	}

	// 生成新的评论ID
	const newCommentId = Math.max(...commentsData.value.map(item => item.id), 0) + 1

	// 创建新评论
	const newComment: CommentItem = {
		id: newCommentId,
		templateId: currentCommentTemplate.value!.id,
		commenterName: commentForm.value.commenterName,
		commentTime: new Date().toLocaleString(),
		commentContent: commentForm.value.commentContent
	}

	// 添加到评论数据
	commentsData.value.push(newComment)

	// 更新模板的评论数量
	const template = tableData.value.find(item => item.id === currentCommentTemplate.value!.id)
	if (template) {
		template.commentCount++
	}

	saveToLocalStorage()
	ElMessage.success('评论提交成功')
	commentDialogVisible.value = false
}

// 取消评论
const cancelComment = () => {
	commentDialogVisible.value = false
}

// 提交回复
const submitReply = () => {
	if (!replyForm.value.replyContent.trim()) {
		ElMessage.warning('请输入回复内容')
		return
	}

	// 更新评论的回复内容
	if (currentReplyComment.value) {
		const comment = commentsData.value.find(item => item.id === currentReplyComment.value!.id)
		if (comment) {
			comment.replyTime = new Date().toLocaleString()
			comment.replyContent = replyForm.value.replyContent

			// 更新模板的回复数量
			const template = tableData.value.find(item => item.id === comment.templateId)
			if (template) {
				template.replyCount++
			}
		}
	}

	saveToLocalStorage()
	ElMessage.success('回复提交成功')
	replyDialogVisible.value = false

	// 刷新评论列表
	if (currentTemplateForComments.value) {
		currentTemplateComments.value = commentsData.value.filter(comment => comment.templateId === currentTemplateForComments.value!.id)
	}
}

// 取消回复
const cancelReply = () => {
	replyDialogVisible.value = false
}

// 查看评论详情
const viewComment = (comment: CommentItem) => {
	ElMessage.info(`查看评论：${comment.commentContent}`)
}

// 删除评论
const deleteComment = (comment: CommentItem) => {
	ElMessageBox.confirm(`确定要删除这条评论吗？`, '删除确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
	.then(() => {
		const index = commentsData.value.findIndex(item => item.id === comment.id)
		if (index !== -1) {
			commentsData.value.splice(index, 1)

			// 更新模板的评论数量
			const template = tableData.value.find(item => item.id === comment.templateId)
			if (template) {
				template.commentCount--
				if (comment.replyContent) {
					template.replyCount--
				}
			}

			saveToLocalStorage()
			ElMessage.success('删除成功')

			// 刷新评论列表
			if (currentTemplateForComments.value) {
				currentTemplateComments.value = commentsData.value.filter(c => c.templateId === currentTemplateForComments.value!.id)
			}
		}
	})
	.catch(() => {})
}

// 回复评论
const replyToComment = (comment: CommentItem) => {
	currentReplyComment.value = comment
	replyForm.value = {
		replyContent: ''
	}
	replyDialogVisible.value = true
}

// 对评论进行评论（新增评论）
const commentOnTemplate = (templateId: number) => {
	const template = tableData.value.find(item => item.id === templateId)
	if (template) {
		currentCommentTemplate.value = template
		commentForm.value = {
			commenterName: '',
			commentContent: ''
		}
		commentDialogVisible.value = true
	}
}

// 批量删除
const handleBatchDelete = () => {
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请先选择要删除的模板')
		return
	}

	ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 个模板吗？`, '批量删除确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
	.then(() => {
		// 删除选中的模板
		selectedRows.value.forEach(selectedRow => {
			const index = tableData.value.findIndex(item => item.id === selectedRow.id)
			if (index > -1) {
				tableData.value.splice(index, 1)
			}
			const deepIndex = deepData.value.findIndex(item => item.id === selectedRow.id)
			if (deepIndex > -1) {
				deepData.value.splice(deepIndex, 1)
			}
		})

		selectedRows.value = []
		pagination.total = tableData.value.length
		saveToLocalStorage()
		ElMessage.success('批量删除成功')
	})
	.catch(() => {})
}

// 批量导出
const handleBatchExport = () => {
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请先选择要导出的模板')
		return
	}

	const timestamp = getCurrentTimestamp()
	const exportData = {
		exportInfo: {
			exportTime: new Date().toLocaleString(),
			totalCount: selectedRows.value.length,
			exportType: '批量导出'
		},
		templates: selectedRows.value.map(row => ({
			templateName: row.templateName,
			assignedPermissions: row.assignedPermissions,
			status: row.status,
			callCount: row.callCount,
			commentCount: row.commentCount,
			replyCount: row.replyCount,
			favoriteCount: row.favoriteCount,
		}))
	}

	const jsonString = JSON.stringify(exportData, null, 2)
	const blob = new Blob([jsonString], { type: 'application/json' })
	const url = URL.createObjectURL(blob)
	const link = document.createElement('a')
	link.href = url
	link.download = `${timestamp}-权限分配模板.json`

	document.body.appendChild(link)
	link.click()
	document.body.removeChild(link)
	URL.revokeObjectURL(url)

	ElMessage.success(`批量导出成功：${selectedRows.value.length} 个模板`)
}

// 批量收藏
const handleBatchFavorite = () => {
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请先选择要收藏的模板')
		return
	}

	selectedRows.value.forEach(selectedRow => {
		const row = tableData.value.find(item => item.id === selectedRow.id)
		if (row) {
			row.favoriteCount++
		}
	})

	saveToLocalStorage()
	ElMessage.success(`批量收藏成功：${selectedRows.value.length} 个模板`)
}

// 全部调用
const handleCallAll = () => {
	tableData.value.forEach(row => {
		row.callCount++
	})

	saveToLocalStorage()
	ElMessage.success(`全部调用成功：${tableData.value.length} 个模板`)
}

// 处理表格选择变化
const handleSelectionChange = (selection: PermissionTemplateItem[]) => {
	selectedRows.value = selection
}

// 表格操作按钮点击事件（保留用于兼容）
const onTableClickButton = ({btn, row, index}: any) => {
	if (btn.code === 'view') {
		ElMessage.info(`查看模板：${row.templateName}`)
	} else if (btn.code === 'edit') {
		ElMessage.info(`编辑模板：${row.templateName}`)
	} else if (btn.code === 'delete') {
		ElMessageBox.confirm(`确定要删除模板"${row.templateName}"吗？`, '删除确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
		.then(() => {
			const actualIndex = tableData.value.findIndex(item => item.id === row.id)
			if (actualIndex !== -1) {
				tableData.value.splice(actualIndex, 1)
				deepData.value.splice(actualIndex, 1)
				tableConfig.total = tableData.value.length
				saveToLocalStorage()
				ElMessage.success('删除成功')
			}
		})
		.catch(() => {})
	} else if (btn.code === 'favorite') {
		row.favoriteCount += 1
		saveToLocalStorage()
		ElMessage.success(`已收藏模板：${row.templateName}`)
	}
}

// 分页变化
const onPaginationChange = (value: number, type: string) => {
	if (type === 'page') {
		pagination.page = value
	} else if (type === 'size') {
		pagination.size = value
		pagination.page = 1
	}
}

// 保存数据到本地存储
const saveToLocalStorage = () => {
	storage.save(ACTION_KEY.PermissionTemplate, tableData.value)
}

// 从本地存储加载数据
const loadFromLocalStorage = () => {
	const savedData = storage.get(ACTION_KEY.PermissionTemplate)
	if (savedData && savedData.length > 0) {
		tableData.value = savedData
		deepData.value = [...savedData]
	}
}

// 初始化
const onOpen = () => {
	loadFromLocalStorage()
	pagination.total = tableData.value.length
}

const onClose = () => {
	resetSearch()
}

const onConfirm = () => {
	// 确认操作
}

// 更新总数
watch(() => tableData.value.length, (newLength) => {
	pagination.total = newLength
}, { immediate: true })
</script>

<template>
	<Dialog
		v-bind="$attrs"
		:enable-confirm="true"
		:destroy-on-close="true"
		:loading="tableConfig.loading"
		loading-text="正在获取数据"
		width="1200"
		@open="onOpen"
		@click-close="onClose"
		@close="onClose"
		@clickConfirm="onConfirm"
	>
		<el-form :model="searchParams" label-width="auto" :inline="true" ref="formRef">
			<el-form-item
				:label="item.label"
				v-for="(item, index) of formProps"
				:key="index"
				:prop="item.field"
			>
				<el-input
					v-if="item.type === 'text'"
					v-model="searchParams[item.field]"
					:placeholder="item.placeholder"
					clearable
					style="width: 200px"
				/>
				<el-select
					v-else-if="item.type === 'select'"
					v-model="searchParams[item.field]"
					:placeholder="item.placeholder"
					clearable
					style="width: 200px"
				>
					<el-option
						v-for="option in item.options"
						:key="option.value"
						:label="option.label"
						:value="option.value"
					/>
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="handlSearch">搜索</el-button>
				<el-button @click="resetSearch">重置</el-button>
			</el-form-item>
		</el-form>

		<!-- 批量操作按钮 -->
		<div class="batch-operations" style="margin: 15px 0;">
			<el-button type="primary" @click="handleAdd">新增</el-button>
			<el-button type="danger" @click="handleBatchDelete" :disabled="selectedRows.length === 0">
				批量删除 ({{ selectedRows.length }})
			</el-button>
			<el-button type="success" @click="handleBatchExport" :disabled="selectedRows.length === 0">
				批量导出 ({{ selectedRows.length }})
			</el-button>
			<el-button type="warning" @click="handleBatchFavorite" :disabled="selectedRows.length === 0">
				批量收藏 ({{ selectedRows.length }})
			</el-button>
			<el-button type="info" @click="handleCallAll">全部调用</el-button>
		</div>

		<TableV2
			:defaultTableData="paginatedData"
			:enable-selection="true"
			:auto-height="true"
			:max-height="400"
			:columns="columns"
			:enable-toolbar="false"
			:enable-create="false"
			:enable-edit="false"
			:enable-delete="false"
			:enable-index="true"
			@selection-change="handleSelectionChange"
			ref="templateTableRef"
			class="mg-top-5"
		>
			<template #isTop="{ row }">
				<el-tag v-if="row.isTop" type="danger" size="small">置顶</el-tag>
				<el-tag v-else type="info" size="small">非置顶</el-tag>
			</template>
			<template #operation="{ row }">
				<el-button size="small" type="primary" @click="handleEdit(row)">修改</el-button>
				<el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
				<el-button size="small" type="success" @click="handleView(row)">查看</el-button>
				<el-dropdown @command="(command) => handleMoreAction(command, row)">
					<el-button size="small" type="info">
						更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
					</el-button>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item command="export">导出</el-dropdown-item>
							<el-dropdown-item command="favorite">收藏</el-dropdown-item>
							<el-dropdown-item command="top">置顶</el-dropdown-item>
							<el-dropdown-item command="call">调用</el-dropdown-item>
							<el-dropdown-item command="comment">评论</el-dropdown-item>
							<el-dropdown-item command="reply">回复</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
			</template>
		</TableV2>

		<Pagination
			:total="pagination.total"
			:page-size="pagination.size"
			:current-page="pagination.page"
			@current-change="onPaginationChange($event, 'page')"
			@size-change="onPaginationChange($event, 'size')"
		/>
	</Dialog>

	<!-- 新增模板对话框 -->
	<el-dialog
		v-model="addDialogVisible"
		title="新增权限分配模板"
		width="600px"
		:close-on-click-modal="false"
	>
		<el-form :model="addForm" label-width="100px">
			<el-form-item label="模板名称" required>
				<el-input
					v-model="addForm.templateName"
					placeholder="请输入模板名称"
					clearable
				/>
			</el-form-item>
			<el-form-item label="权限配置" required>
				<el-input
					v-model="addForm.assignedPermissions"
					placeholder="请输入权限配置，多个权限用、分隔"
					clearable
				/>
			</el-form-item>
			<el-form-item label="模板描述" required>
				<el-input
					v-model="addForm.description"
					type="textarea"
					:rows="3"
					placeholder="请输入模板描述"
				/>
			</el-form-item>
			<el-form-item label="状态">
				<el-checkbox v-model="addForm.isTop">设为置顶模板</el-checkbox>
				<div style="color: #999; font-size: 12px; margin-top: 5px;">
					注意：置顶模板只能有一个，设置后会取消其他模板的置顶状态
				</div>
			</el-form-item>
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="cancelAdd">取消</el-button>
				<el-button type="primary" @click="confirmAdd">确定</el-button>
			</span>
		</template>
	</el-dialog>

	<!-- 评论对话框 -->
	<el-dialog
		v-model="commentDialogVisible"
		title="添加评论"
		width="500px"
		:close-on-click-modal="false"
	>
		<el-form :model="commentForm" label-width="80px">
			<el-form-item label="评论人" required>
				<el-input
					v-model="commentForm.commenterName"
					placeholder="请输入评论人姓名"
					clearable
				/>
			</el-form-item>
			<el-form-item label="评论内容" required>
				<el-input
					v-model="commentForm.commentContent"
					type="textarea"
					:rows="4"
					placeholder="请输入评论内容"
				/>
			</el-form-item>
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="cancelComment">取消</el-button>
				<el-button type="primary" @click="submitComment">提交评论</el-button>
			</span>
		</template>
	</el-dialog>

	<!-- 回复对话框 -->
	<el-dialog
		v-model="replyDialogVisible"
		title="回复评论"
		width="500px"
		:close-on-click-modal="false"
	>
		<div v-if="currentReplyComment" style="margin-bottom: 15px; padding: 10px; background: #f5f5f5; border-radius: 4px;">
			<div><strong>原评论：</strong></div>
			<div style="margin-top: 5px;">{{ currentReplyComment.commentContent }}</div>
			<div style="margin-top: 5px; font-size: 12px; color: #666;">
				{{ currentReplyComment.commenterName }} · {{ currentReplyComment.commentTime }}
			</div>
		</div>

		<el-form :model="replyForm" label-width="80px">
			<el-form-item label="回复内容" required>
				<el-input
					v-model="replyForm.replyContent"
					type="textarea"
					:rows="4"
					placeholder="请输入回复内容"
				/>
			</el-form-item>
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="cancelReply">取消</el-button>
				<el-button type="primary" @click="submitReply">提交回复</el-button>
			</span>
		</template>
	</el-dialog>

	<!-- 评论列表对话框 -->
	<el-dialog
		v-model="commentListDialogVisible"
		:title="`${currentTemplateForComments?.templateName} - 评论管理`"
		width="900px"
		:close-on-click-modal="false"
	>
		<div style="margin-bottom: 15px;">
			<el-button type="primary" @click="commentOnTemplate(currentTemplateForComments?.id || 0)">
				添加评论
			</el-button>
		</div>

		<el-table :data="currentTemplateComments" style="width: 100%">
			<el-table-column prop="commenterName" label="评论人" width="100" />
			<el-table-column prop="commentTime" label="评论时间" width="150" />
			<el-table-column prop="commentContent" label="评论内容" min-width="200" />
			<el-table-column prop="replyTime" label="回复时间" width="150">
				<template #default="{ row }">
					{{ row.replyTime || '-' }}
				</template>
			</el-table-column>
			<el-table-column prop="replyContent" label="回复内容" min-width="200">
				<template #default="{ row }">
					{{ row.replyContent || '-' }}
				</template>
			</el-table-column>
			<el-table-column label="操作" width="200">
				<template #default="{ row }">
					<el-button size="small" type="primary" @click="viewComment(row)">查看评论</el-button>
					<el-button size="small" type="danger" @click="deleteComment(row)">删除评论</el-button>
					<el-button size="small" type="success" @click="replyToComment(row)">回复</el-button>
				</template>
			</el-table-column>
		</el-table>

		<div v-if="currentTemplateComments.length === 0" style="text-align: center; padding: 40px; color: #999;">
			暂无评论数据
		</div>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="commentListDialogVisible = false">关闭</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<style scoped lang="scss">
// 样式可以根据需要添加
</style>

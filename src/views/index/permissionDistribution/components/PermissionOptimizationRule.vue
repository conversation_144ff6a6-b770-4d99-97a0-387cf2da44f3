<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, View, Delete, Plus } from '@element-plus/icons-vue'

// 权限优化规则数据接口
interface PermissionOptimizationRuleItem {
	id: number
	ruleName: string
	applicablePermission: string
	type: '资源访问控制' | '操作权限控制' | '数据权限控制' | '条件限制控制'
	permissionRule: string
	createTime: string
}

// 编辑表单接口
interface EditRuleForm {
	ruleName: string
	applicablePermission: string
	type: '资源访问控制' | '操作权限控制' | '数据权限控制' | '条件限制控制'
	permissionRule: string
}

// 查看规则接口
interface ViewRuleData {
	ruleName: string
	applicablePermission: string
	type: string
	permissionRule: string
	createTime: string
}

// Props
interface Props {
	modelValue: boolean
	title?: string
	width?: string
}

const props = withDefaults(defineProps<Props>(), {
	title: '权限优化规则',
	width: '1200px'
})

const emit = defineEmits<{
	'update:modelValue': [value: boolean]
}>()

// 对话框显示控制
const dialogVisible = computed({
	get: () => props.modelValue,
	set: (value) => emit('update:modelValue', value)
})

// 权限类型选项
const typeOptions = [
	{ label: '资源访问控制', value: '资源访问控制' },
	{ label: '操作权限控制', value: '操作权限控制' },
	{ label: '数据权限控制', value: '数据权限控制' },
	{ label: '条件限制控制', value: '条件限制控制' }
]

// 适用权限选项
const permissionOptions = [
	{ label: '数据查看权限', value: 'data_view' },
	{ label: '数据编辑权限', value: 'data_edit' },
	{ label: '数据删除权限', value: 'data_delete' },
	{ label: '用户管理权限', value: 'user_manage' },
	{ label: '系统配置权限', value: 'system_config' },
	{ label: '报表生成权限', value: 'report_generate' },
	{ label: '审核权限', value: 'audit_permission' },
	{ label: '财务权限', value: 'finance_permission' },
	{ label: '人事权限', value: 'hr_permission' },
	{ label: '项目管理权限', value: 'project_manage' }
]

// 规则数据
const ruleData = ref<PermissionOptimizationRuleItem[]>([
	{
		id: 1,
		ruleName: '数据查看权限时间限制',
		applicablePermission: '数据查看权限',
		type: '条件限制控制',
		permissionRule: '用户只能在工作时间（9:00-18:00）访问敏感数据，非工作时间自动禁用查看权限。',
		createTime: '2024-01-15 10:30:00'
	},
	{
		id: 2,
		ruleName: '数据编辑权限IP限制',
		applicablePermission: '数据编辑权限',
		type: '资源访问控制',
		permissionRule: '数据编辑操作仅允许从公司内网IP地址段（***********/24）进行，外网访问自动拒绝。',
		createTime: '2024-01-14 14:20:00'
	},
	{
		id: 3,
		ruleName: '财务数据分级访问',
		applicablePermission: '财务权限',
		type: '数据权限控制',
		permissionRule: '根据用户级别分级访问：普通用户只能查看部门财务数据，主管可查看全部门数据，总监可查看全公司数据。',
		createTime: '2024-01-13 09:15:00'
	},
	{
		id: 4,
		ruleName: '用户管理操作审批',
		applicablePermission: '用户管理权限',
		type: '操作权限控制',
		permissionRule: '用户创建、删除、权限变更操作需要上级主管审批，审批通过后才能执行。',
		createTime: '2024-01-12 16:45:00'
	},
	{
		id: 5,
		ruleName: '系统配置双重验证',
		applicablePermission: '系统配置权限',
		type: '操作权限控制',
		permissionRule: '系统关键配置修改需要双重验证：操作者验证+另一名管理员确认。',
		createTime: '2024-01-11 11:30:00'
	},
	{
		id: 6,
		ruleName: '报表生成频率限制',
		applicablePermission: '报表生成权限',
		type: '条件限制控制',
		permissionRule: '每个用户每天最多生成10个报表，超出限制需要申请特殊权限。',
		createTime: '2024-01-10 13:20:00'
	},
	{
		id: 7,
		ruleName: '审核权限地域限制',
		applicablePermission: '审核权限',
		type: '资源访问控制',
		permissionRule: '审核操作仅允许在指定办公地点进行，基于GPS定位验证用户位置。',
		createTime: '2024-01-09 15:10:00'
	},
	{
		id: 8,
		ruleName: '人事数据脱敏访问',
		applicablePermission: '人事权限',
		type: '数据权限控制',
		permissionRule: '非HR部门用户访问人事数据时，敏感信息（身份证号、薪资等）自动脱敏显示。',
		createTime: '2024-01-08 10:00:00'
	},
	{
		id: 9,
		ruleName: '项目管理权限继承',
		applicablePermission: '项目管理权限',
		type: '操作权限控制',
		permissionRule: '项目成员权限自动继承项目经理设定的基础权限，项目结束后权限自动回收。',
		createTime: '2024-01-07 14:30:00'
	},
	{
		id: 10,
		ruleName: '数据删除安全策略',
		applicablePermission: '数据删除权限',
		type: '操作权限控制',
		permissionRule: '数据删除操作需要三重确认：用户确认+系统警告+24小时冷却期，期间可撤销删除。',
		createTime: '2024-01-06 09:45:00'
	},
	{
		id: 11,
		ruleName: '跨部门数据访问',
		applicablePermission: '数据查看权限',
		type: '资源访问控制',
		permissionRule: '跨部门数据访问需要目标部门主管授权，访问记录自动通知相关部门。',
		createTime: '2024-01-05 16:20:00'
	},
	{
		id: 12,
		ruleName: '临时权限自动回收',
		applicablePermission: '用户管理权限',
		type: '条件限制控制',
		permissionRule: '临时授予的权限设置自动过期时间，到期后系统自动回收，避免权限滥用。',
		createTime: '2024-01-04 12:15:00'
	}
])

// 表格列配置
const columns = [
	{ prop: 'ruleName', label: '规则名称' },
	{ prop: 'applicablePermission', label: '适用权限' },
	{ prop: 'type', label: '类型' },
	{ prop: 'permissionRule', label: '权限规则', width: '300px' },
	{ prop: 'createTime', label: '创建时间' },
	{ prop: 'operation', label: '操作', width: '200px' }
]

// 分页配置
const pagination = reactive({
	total: 0,
	size: 10,
	page: 1,
})

// 分页数据
const paginatedData = computed(() => {
	const start = (pagination.page - 1) * pagination.size
	const end = start + pagination.size
	return filteredData.value.slice(start, end)
})

// 分页事件
const onPaginationChange = (value: number, type: string) => {
	if (type === 'page') {
		pagination.page = value
	} else if (type === 'size') {
		pagination.size = value
		pagination.page = 1
	}
}

// 筛选条件
const filterForm = ref({
	applicablePermission: ''
})

// 筛选后的数据
const filteredData = computed(() => {
	if (!filterForm.value.applicablePermission) {
		return ruleData.value
	}
	return ruleData.value.filter(item =>
		item.applicablePermission === filterForm.value.applicablePermission
	)
})

// 表格选择
const selectedRows = ref<PermissionOptimizationRuleItem[]>([])
const handleSelectionChange = (selection: PermissionOptimizationRuleItem[]) => {
	selectedRows.value = selection
}

// 新增对话框
const addDialogVisible = ref(false)
const addForm = ref<EditRuleForm>({
	ruleName: '',
	applicablePermission: '',
	type: '资源访问控制',
	permissionRule: ''
})

// 编辑对话框
const editDialogVisible = ref(false)
const editForm = ref<EditRuleForm>({
	ruleName: '',
	applicablePermission: '',
	type: '资源访问控制',
	permissionRule: ''
})
const currentEditRule = ref<PermissionOptimizationRuleItem | null>(null)

// 查看对话框
const viewDialogVisible = ref(false)
const viewData = ref<ViewRuleData>({
	ruleName: '',
	applicablePermission: '',
	type: '',
	permissionRule: '',
	createTime: ''
})

// 新增规则
const handleAdd = () => {
	addForm.value = {
		ruleName: '',
		applicablePermission: '',
		type: '资源访问控制',
		permissionRule: ''
	}
	addDialogVisible.value = true
}

// 确认新增
const confirmAdd = () => {
	if (!addForm.value.ruleName.trim()) {
		ElMessage.warning('请输入规则名称')
		return
	}
	if (!addForm.value.applicablePermission) {
		ElMessage.warning('请选择适用权限')
		return
	}
	if (!addForm.value.permissionRule.trim()) {
		ElMessage.warning('请输入权限规则')
		return
	}
	
	const newRule: PermissionOptimizationRuleItem = {
		id: Date.now(),
		ruleName: addForm.value.ruleName,
		applicablePermission: addForm.value.applicablePermission,
		type: addForm.value.type,
		permissionRule: addForm.value.permissionRule,
		createTime: new Date().toLocaleString('zh-CN', {
			year: 'numeric',
			month: '2-digit',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit'
		}).replace(/\//g, '-')
	}
	
	ruleData.value.unshift(newRule)
	pagination.total = ruleData.value.length
	ElMessage.success('新增成功')
	addDialogVisible.value = false
}

// 取消新增
const cancelAdd = () => {
	addDialogVisible.value = false
}

// 查看规则
const handleView = (row: PermissionOptimizationRuleItem) => {
	viewData.value = {
		ruleName: row.ruleName,
		applicablePermission: row.applicablePermission,
		type: row.type,
		permissionRule: row.permissionRule,
		createTime: row.createTime
	}
	viewDialogVisible.value = true
}

// 编辑规则
const handleEdit = (row: PermissionOptimizationRuleItem) => {
	currentEditRule.value = row
	editForm.value = {
		ruleName: row.ruleName,
		applicablePermission: row.applicablePermission,
		type: row.type,
		permissionRule: row.permissionRule
	}
	editDialogVisible.value = true
}

// 确认编辑
const confirmEdit = () => {
	if (!editForm.value.ruleName.trim()) {
		ElMessage.warning('请输入规则名称')
		return
	}
	if (!editForm.value.applicablePermission) {
		ElMessage.warning('请选择适用权限')
		return
	}
	if (!editForm.value.permissionRule.trim()) {
		ElMessage.warning('请输入权限规则')
		return
	}
	
	if (currentEditRule.value) {
		currentEditRule.value.ruleName = editForm.value.ruleName
		currentEditRule.value.applicablePermission = editForm.value.applicablePermission
		currentEditRule.value.type = editForm.value.type
		currentEditRule.value.permissionRule = editForm.value.permissionRule
		
		ElMessage.success('编辑成功')
		editDialogVisible.value = false
	}
}

// 取消编辑
const cancelEdit = () => {
	editDialogVisible.value = false
}

// 删除规则
const handleDelete = (row: PermissionOptimizationRuleItem) => {
	ElMessageBox.confirm(
		`确定要删除规则"${row.ruleName}"吗？`,
		'确认删除',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}
	)
		.then(() => {
			const index = ruleData.value.findIndex(item => item.id === row.id)
			if (index !== -1) {
				ruleData.value.splice(index, 1)
				pagination.total = ruleData.value.length
				ElMessage.success('删除成功')
			}
		})
		.catch(() => {})
}

// 删除选中
const handleDeleteSelected = () => {
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请选择要删除的规则')
		return
	}
	
	ElMessageBox.confirm(
		`确定要删除选中的${selectedRows.value.length}条规则吗？`,
		'确认删除',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}
	)
		.then(() => {
			const selectedIds = selectedRows.value.map(row => row.id)
			ruleData.value = ruleData.value.filter(item => !selectedIds.includes(item.id))
			pagination.total = ruleData.value.length
			selectedRows.value = []
			ElMessage.success('删除成功')
		})
		.catch(() => {})
}

// 删除全部
const handleDeleteAll = () => {
	if (ruleData.value.length === 0) {
		ElMessage.warning('没有可删除的规则')
		return
	}

	ElMessageBox.confirm(
		`确定要删除全部${ruleData.value.length}条规则吗？此操作不可恢复！`,
		'确认删除全部',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}
	)
		.then(() => {
			ruleData.value = []
			pagination.total = 0
			selectedRows.value = []
			ElMessage.success('删除成功')
		})
		.catch(() => {})
}

// 重置筛选
const resetFilter = () => {
	filterForm.value.applicablePermission = ''
	pagination.page = 1
}

// 监听对话框打开
watch(() => props.modelValue, (newValue) => {
	if (newValue) {
		// 对话框打开时初始化分页
		pagination.total = filteredData.value.length
		pagination.page = 1
	}
})

// 监听筛选条件变化
watch(() => filterForm.value.applicablePermission, () => {
	pagination.total = filteredData.value.length
	pagination.page = 1
})
</script>

<template>
	<Dialog
		v-model="dialogVisible"
		:title="title"
		:width="width"
		:destroy-on-close="true"
	>
		<!-- 筛选条件 -->
		<div style="margin-bottom: 16px; padding: 16px; background-color: #f5f7fa; border-radius: 4px;">
			<el-row :gutter="16" align="middle">
				<el-col :span="4">
					<span style="font-weight: 500;">适用权限：</span>
				</el-col>
				<el-col :span="8">
					<el-select
						v-model="filterForm.applicablePermission"
						placeholder="请选择适用权限"
						style="width: 100%;"
						clearable
					>
						<el-option
							v-for="option in permissionOptions"
							:key="option.value"
							:label="option.label"
							:value="option.label"
						/>
					</el-select>
				</el-col>
				<el-col :span="4">
					<el-button @click="resetFilter">重置</el-button>
				</el-col>
			</el-row>
		</div>

		<!-- 操作按钮 -->
		<div style="margin-bottom: 16px;">
			<el-button type="primary" :icon="Plus" @click="handleAdd">新增</el-button>
			<el-button type="danger" @click="handleDeleteSelected">删除选中</el-button>
			<el-button type="danger" @click="handleDeleteAll">删除全部</el-button>
		</div>
		
		<!-- 规则列表表格 -->
		<TableV2
			:defaultTableData="paginatedData"
			:auto-height="true"
			:max-height="400"
			:columns="columns"
			:enable-toolbar="false"
			:enable-create="false"
			:enable-edit="false"
			:enable-delete="false"
			:enable-index="true"
			:enable-selection="true"
			@selection-change="handleSelectionChange"
			class="mg-top-5"
		>
			<template #operation="{ row }">
				<el-button size="small" type="primary" @click="handleView(row)" :icon="View">查看</el-button>
				<el-button size="small" type="warning" @click="handleEdit(row)" :icon="Edit">编辑</el-button>
				<el-button size="small" type="danger" @click="handleDelete(row)" :icon="Delete">删除</el-button>
			</template>
		</TableV2>
		
		<!-- 分页 -->
		<Pagination
			:total="pagination.total"
			:page-size="pagination.size"
			:current-page="pagination.page"
			@current-change="onPaginationChange($event, 'page')"
			@size-change="onPaginationChange($event, 'size')"
		/>
		
		<!-- 新增规则对话框 -->
		<el-dialog
			v-model="addDialogVisible"
			title="新增权限优化规则"
			width="600px"
			:close-on-click-modal="false"
		>
			<el-form :model="addForm" label-width="100px">
				<el-form-item label="规则名称" required>
					<el-input
						v-model="addForm.ruleName"
						placeholder="请输入规则名称"
						clearable
					/>
				</el-form-item>
				<el-form-item label="适用权限" required>
					<el-select
						v-model="addForm.applicablePermission"
						placeholder="请选择适用权限"
						style="width: 100%;"
						clearable
					>
						<el-option
							v-for="option in permissionOptions"
							:key="option.value"
							:label="option.label"
							:value="option.label"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="类型" required>
					<el-select
						v-model="addForm.type"
						placeholder="请选择类型"
						style="width: 100%;"
					>
						<el-option
							v-for="option in typeOptions"
							:key="option.value"
							:label="option.label"
							:value="option.value"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="权限规则" required>
					<el-input
						v-model="addForm.permissionRule"
						type="textarea"
						:rows="4"
						placeholder="请输入权限规则描述"
						clearable
					/>
				</el-form-item>
			</el-form>
			
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancelAdd">取消</el-button>
					<el-button type="primary" @click="confirmAdd">确定</el-button>
				</span>
			</template>
		</el-dialog>
		
		<!-- 查看规则对话框 -->
		<el-dialog
			v-model="viewDialogVisible"
			title="查看权限优化规则"
			width="600px"
			:close-on-click-modal="false"
		>
			<el-descriptions :column="1" border>
				<el-descriptions-item label="规则名称">{{ viewData.ruleName }}</el-descriptions-item>
				<el-descriptions-item label="适用权限">{{ viewData.applicablePermission }}</el-descriptions-item>
				<el-descriptions-item label="类型">
					<el-tag>{{ viewData.type }}</el-tag>
				</el-descriptions-item>
				<el-descriptions-item label="创建时间">{{ viewData.createTime }}</el-descriptions-item>
				<el-descriptions-item label="权限规则">
					<p style="line-height: 1.6; margin: 0;">{{ viewData.permissionRule }}</p>
				</el-descriptions-item>
			</el-descriptions>
			
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="viewDialogVisible = false">关闭</el-button>
				</span>
			</template>
		</el-dialog>
		
		<!-- 编辑规则对话框 -->
		<el-dialog
			v-model="editDialogVisible"
			title="编辑权限优化规则"
			width="600px"
			:close-on-click-modal="false"
		>
			<el-form :model="editForm" label-width="100px">
				<el-form-item label="规则名称" required>
					<el-input
						v-model="editForm.ruleName"
						placeholder="请输入规则名称"
						clearable
					/>
				</el-form-item>
				<el-form-item label="适用权限" required>
					<el-select
						v-model="editForm.applicablePermission"
						placeholder="请选择适用权限"
						style="width: 100%;"
						clearable
					>
						<el-option
							v-for="option in permissionOptions"
							:key="option.value"
							:label="option.label"
							:value="option.label"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="类型" required>
					<el-select
						v-model="editForm.type"
						placeholder="请选择类型"
						style="width: 100%;"
					>
						<el-option
							v-for="option in typeOptions"
							:key="option.value"
							:label="option.label"
							:value="option.value"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="权限规则" required>
					<el-input
						v-model="editForm.permissionRule"
						type="textarea"
						:rows="4"
						placeholder="请输入权限规则描述"
						clearable
					/>
				</el-form-item>
			</el-form>
			
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancelEdit">取消</el-button>
					<el-button type="primary" @click="confirmEdit">确定</el-button>
				</span>
			</template>
		</el-dialog>
	</Dialog>
</template>

<style scoped lang="scss">
// 样式可以根据需要添加
</style>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, View, Delete, Plus } from '@element-plus/icons-vue'

// 权限变更通知数据接口
interface PermissionChangeNotificationItem {
	id: number
	notificationName: string
	notificationPermission: string
	notificationPersonnel: '权限创建人员' | '管理员' | '超级管理员'
	notificationMethod: '系统通知' | '渝快政工作通知' | '渝快政Ding通知'
	createTime: string
	notificationContent?: string
	applicablePermission?: string
}

// 新增表单接口
interface AddNotificationForm {
	notificationName: string
	notificationContent: string
	notificationPersonnel: '权限创建人员' | '管理员' | '超级管理员'
	applicablePermission: string
	notificationMethod: '系统通知' | '渝快政工作通知' | '渝快政Ding通知'
}

// 编辑表单接口
interface EditNotificationForm {
	notificationName: string
	notificationPermission: string
	notificationPersonnel: '权限创建人员' | '管理员' | '超级管理员'
	notificationMethod: '系统通知' | '渝快政工作通知' | '渝快政Ding通知'
	notificationContent: string
	applicablePermission: string
}

// 查看通知接口
interface ViewNotificationData {
	notificationName: string
	notificationPermission: string
	notificationPersonnel: string
	notificationMethod: string
	createTime: string
	notificationContent: string
	applicablePermission: string
}

// Props
interface Props {
	modelValue: boolean
	title?: string
	width?: string
}

const props = withDefaults(defineProps<Props>(), {
	title: '权限变更通知',
	width: '1200px'
})

const emit = defineEmits<{
	'update:modelValue': [value: boolean]
}>()

// 对话框显示控制
const dialogVisible = computed({
	get: () => props.modelValue,
	set: (value) => emit('update:modelValue', value)
})

// 通知人员选项
const personnelOptions = [
	{ label: '权限创建人员', value: '权限创建人员' },
	{ label: '管理员', value: '管理员' },
	{ label: '超级管理员', value: '超级管理员' }
]

// 通知方式选项
const methodOptions = [
	{ label: '系统通知', value: '系统通知' },
	{ label: '渝快政工作通知', value: '渝快政工作通知' },
	{ label: '渝快政Ding通知', value: '渝快政Ding通知' }
]

// 适用权限选项
const permissionOptions = [
	{ label: '数据查看权限', value: 'data_view' },
	{ label: '数据编辑权限', value: 'data_edit' },
	{ label: '数据删除权限', value: 'data_delete' },
	{ label: '用户管理权限', value: 'user_manage' },
	{ label: '系统配置权限', value: 'system_config' },
	{ label: '报表生成权限', value: 'report_generate' },
	{ label: '审核权限', value: 'audit_permission' },
	{ label: '财务权限', value: 'finance_permission' },
	{ label: '人事权限', value: 'hr_permission' },
	{ label: '项目管理权限', value: 'project_manage' }
]

// 通知数据
const notificationData = ref<PermissionChangeNotificationItem[]>([
	{
		id: 1,
		notificationName: '数据查看权限变更通知',
		notificationPermission: '数据查看权限',
		notificationPersonnel: '管理员',
		notificationMethod: '系统通知',
		createTime: '2024-01-15 10:30:00',
		notificationContent: '您的数据查看权限已发生变更，请及时查看最新的权限配置。如有疑问，请联系系统管理员。',
		applicablePermission: '数据查看权限'
	},
	{
		id: 2,
		notificationName: '用户管理权限升级通知',
		notificationPermission: '用户管理权限',
		notificationPersonnel: '超级管理员',
		notificationMethod: '渝快政工作通知',
		createTime: '2024-01-14 14:20:00',
		notificationContent: '您的用户管理权限已升级，现在可以执行更多的用户管理操作。请谨慎使用新增的权限功能。',
		applicablePermission: '用户管理权限'
	},
	{
		id: 3,
		notificationName: '财务权限临时授权通知',
		notificationPermission: '财务权限',
		notificationPersonnel: '权限创建人员',
		notificationMethod: '渝快政Ding通知',
		createTime: '2024-01-13 09:15:00',
		notificationContent: '您已获得临时财务权限，有效期至2024年2月13日。请在有效期内完成相关财务操作。',
		applicablePermission: '财务权限'
	},
	{
		id: 4,
		notificationName: '系统配置权限回收通知',
		notificationPermission: '系统配置权限',
		notificationPersonnel: '超级管理员',
		notificationMethod: '系统通知',
		createTime: '2024-01-12 16:45:00',
		notificationContent: '由于岗位调整，您的系统配置权限已被回收。如需重新申请，请联系人事部门。',
		applicablePermission: '系统配置权限'
	},
	{
		id: 5,
		notificationName: '报表生成权限新增通知',
		notificationPermission: '报表生成权限',
		notificationPersonnel: '管理员',
		notificationMethod: '渝快政工作通知',
		createTime: '2024-01-11 11:30:00',
		notificationContent: '您已获得报表生成权限，可以生成部门相关的统计报表。请按照规范使用此权限。',
		applicablePermission: '报表生成权限'
	},
	{
		id: 6,
		notificationName: '审核权限范围调整通知',
		notificationPermission: '审核权限',
		notificationPersonnel: '管理员',
		notificationMethod: '系统通知',
		createTime: '2024-01-10 13:20:00',
		notificationContent: '您的审核权限范围已调整，现在只能审核本部门的申请。跨部门审核需要额外申请。',
		applicablePermission: '审核权限'
	},
	{
		id: 7,
		notificationName: '数据编辑权限限制通知',
		notificationPermission: '数据编辑权限',
		notificationPersonnel: '超级管理员',
		notificationMethod: '渝快政Ding通知',
		createTime: '2024-01-09 15:10:00',
		notificationContent: '您的数据编辑权限已添加时间限制，仅在工作时间（9:00-18:00）可用。',
		applicablePermission: '数据编辑权限'
	},
	{
		id: 8,
		notificationName: '人事权限分级通知',
		notificationPermission: '人事权限',
		notificationPersonnel: '权限创建人员',
		notificationMethod: '系统通知',
		createTime: '2024-01-08 10:00:00',
		notificationContent: '人事权限已实行分级管理，您当前为二级权限，可查看和编辑部门人事信息。',
		applicablePermission: '人事权限'
	},
	{
		id: 9,
		notificationName: '项目管理权限继承通知',
		notificationPermission: '项目管理权限',
		notificationPersonnel: '管理员',
		notificationMethod: '渝快政工作通知',
		createTime: '2024-01-07 14:30:00',
		notificationContent: '您已被指定为项目经理，自动继承项目管理权限。项目结束后权限将自动回收。',
		applicablePermission: '项目管理权限'
	},
	{
		id: 10,
		notificationName: '数据删除权限安全提醒',
		notificationPermission: '数据删除权限',
		notificationPersonnel: '超级管理员',
		notificationMethod: '渝快政Ding通知',
		createTime: '2024-01-06 09:45:00',
		notificationContent: '您的数据删除权限已启用安全策略，删除操作需要三重确认和24小时冷却期。',
		applicablePermission: '数据删除权限'
	}
])

// 表格列配置
const columns = [
	{ prop: 'notificationName', label: '通知名称' },
	{ prop: 'notificationPermission', label: '通知权限' },
	{ prop: 'notificationPersonnel', label: '通知人员' },
	{ prop: 'notificationMethod', label: '通知方式' },
	{ prop: 'createTime', label: '创建时间' },
	{ prop: 'operation', label: '操作', width: '200px' }
]

// 分页配置
const pagination = reactive({
	total: 0,
	size: 10,
	page: 1,
})

// 分页数据
const paginatedData = computed(() => {
	const start = (pagination.page - 1) * pagination.size
	const end = start + pagination.size
	return filteredData.value.slice(start, end)
})

// 分页事件
const onPaginationChange = (value: number, type: string) => {
	if (type === 'page') {
		pagination.page = value
	} else if (type === 'size') {
		pagination.size = value
		pagination.page = 1
	}
}

// 筛选表单
const filterForm = ref({
	notificationName: ''
})

// 筛选后的数据
const filteredData = computed(() => {
	if (!filterForm.value.notificationName.trim()) {
		return notificationData.value
	}
	return notificationData.value.filter(item =>
		item.notificationName.includes(filterForm.value.notificationName.trim())
	)
})

// 重置筛选
const resetFilter = () => {
	filterForm.value.notificationName = ''
	pagination.page = 1
}

// 表格选择
const selectedRows = ref<PermissionChangeNotificationItem[]>([])
const handleSelectionChange = (selection: PermissionChangeNotificationItem[]) => {
	selectedRows.value = selection
}

// 新增对话框
const addDialogVisible = ref(false)
const addForm = ref<AddNotificationForm>({
	notificationName: '',
	notificationContent: '',
	notificationPersonnel: '管理员',
	applicablePermission: '',
	notificationMethod: '系统通知'
})

// 编辑对话框
const editDialogVisible = ref(false)
const editForm = ref<EditNotificationForm>({
	notificationName: '',
	notificationPermission: '',
	notificationPersonnel: '管理员',
	notificationMethod: '系统通知',
	notificationContent: '',
	applicablePermission: ''
})
const currentEditNotification = ref<PermissionChangeNotificationItem | null>(null)

// 查看对话框
const viewDialogVisible = ref(false)
const viewData = ref<ViewNotificationData>({
	notificationName: '',
	notificationPermission: '',
	notificationPersonnel: '',
	notificationMethod: '',
	createTime: '',
	notificationContent: '',
	applicablePermission: ''
})

// 新增通知
const handleAdd = () => {
	addForm.value = {
		notificationName: '',
		notificationContent: '',
		notificationPersonnel: '管理员',
		applicablePermission: '',
		notificationMethod: '系统通知'
	}
	addDialogVisible.value = true
}

// 确认新增
const confirmAdd = () => {
	if (!addForm.value.notificationName.trim()) {
		ElMessage.warning('请输入通知名称')
		return
	}
	if (!addForm.value.notificationContent.trim()) {
		ElMessage.warning('请输入通知内容')
		return
	}
	if (!addForm.value.applicablePermission) {
		ElMessage.warning('请选择适用权限')
		return
	}
	
	const newNotification: PermissionChangeNotificationItem = {
		id: Date.now(),
		notificationName: addForm.value.notificationName,
		notificationPermission: addForm.value.applicablePermission,
		notificationPersonnel: addForm.value.notificationPersonnel,
		notificationMethod: addForm.value.notificationMethod,
		createTime: new Date().toLocaleString('zh-CN', {
			year: 'numeric',
			month: '2-digit',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit'
		}).replace(/\//g, '-'),
		notificationContent: addForm.value.notificationContent,
		applicablePermission: addForm.value.applicablePermission
	}
	
	notificationData.value.unshift(newNotification)
	pagination.total = notificationData.value.length
	ElMessage.success('新增成功')
	addDialogVisible.value = false
}

// 取消新增
const cancelAdd = () => {
	addDialogVisible.value = false
}

// 查看通知
const handleView = (row: PermissionChangeNotificationItem) => {
	viewData.value = {
		notificationName: row.notificationName,
		notificationPermission: row.notificationPermission,
		notificationPersonnel: row.notificationPersonnel,
		notificationMethod: row.notificationMethod,
		createTime: row.createTime,
		notificationContent: row.notificationContent || '',
		applicablePermission: row.applicablePermission || ''
	}
	viewDialogVisible.value = true
}

// 编辑通知
const handleEdit = (row: PermissionChangeNotificationItem) => {
	currentEditNotification.value = row
	editForm.value = {
		notificationName: row.notificationName,
		notificationPermission: row.notificationPermission,
		notificationPersonnel: row.notificationPersonnel,
		notificationMethod: row.notificationMethod,
		notificationContent: row.notificationContent || '',
		applicablePermission: row.applicablePermission || ''
	}
	editDialogVisible.value = true
}

// 确认编辑
const confirmEdit = () => {
	if (!editForm.value.notificationName.trim()) {
		ElMessage.warning('请输入通知名称')
		return
	}
	if (!editForm.value.notificationContent.trim()) {
		ElMessage.warning('请输入通知内容')
		return
	}
	if (!editForm.value.applicablePermission) {
		ElMessage.warning('请选择适用权限')
		return
	}
	
	if (currentEditNotification.value) {
		currentEditNotification.value.notificationName = editForm.value.notificationName
		currentEditNotification.value.notificationPermission = editForm.value.applicablePermission
		currentEditNotification.value.notificationPersonnel = editForm.value.notificationPersonnel
		currentEditNotification.value.notificationMethod = editForm.value.notificationMethod
		currentEditNotification.value.notificationContent = editForm.value.notificationContent
		currentEditNotification.value.applicablePermission = editForm.value.applicablePermission
		
		ElMessage.success('编辑成功')
		editDialogVisible.value = false
	}
}

// 取消编辑
const cancelEdit = () => {
	editDialogVisible.value = false
}

// 删除通知
const handleDelete = (row: PermissionChangeNotificationItem) => {
	ElMessageBox.confirm(
		`确定要删除通知"${row.notificationName}"吗？`,
		'确认删除',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}
	)
		.then(() => {
			const index = notificationData.value.findIndex(item => item.id === row.id)
			if (index !== -1) {
				notificationData.value.splice(index, 1)
				pagination.total = notificationData.value.length
				ElMessage.success('删除成功')
			}
		})
		.catch(() => {})
}

// 删除选中
const handleDeleteSelected = () => {
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请选择要删除的通知')
		return
	}
	
	ElMessageBox.confirm(
		`确定要删除选中的${selectedRows.value.length}条通知吗？`,
		'确认删除',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}
	)
		.then(() => {
			const selectedIds = selectedRows.value.map(row => row.id)
			notificationData.value = notificationData.value.filter(item => !selectedIds.includes(item.id))
			pagination.total = notificationData.value.length
			selectedRows.value = []
			ElMessage.success('删除成功')
		})
		.catch(() => {})
}

// 删除全部
const handleDeleteAll = () => {
	if (notificationData.value.length === 0) {
		ElMessage.warning('没有可删除的通知')
		return
	}
	
	ElMessageBox.confirm(
		`确定要删除全部${notificationData.value.length}条通知吗？此操作不可恢复！`,
		'确认删除全部',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}
	)
		.then(() => {
			notificationData.value = []
			pagination.total = 0
			selectedRows.value = []
			ElMessage.success('删除成功')
		})
		.catch(() => {})
}

// 监听对话框打开
watch(() => props.modelValue, (newValue) => {
	if (newValue) {
		// 对话框打开时初始化分页
		pagination.total = filteredData.value.length
		pagination.page = 1
	}
})

// 监听筛选条件变化
watch(() => filterForm.value.notificationName, () => {
	pagination.total = filteredData.value.length
	pagination.page = 1
})
</script>

<template>
	<Dialog
		v-model="dialogVisible"
		:title="title"
		:width="width"
		:destroy-on-close="true"
	>
		<!-- 筛选条件 -->
		<div style="margin-bottom: 16px; padding: 16px; background-color: #f5f7fa; border-radius: 4px;">
			<el-row :gutter="16" align="middle">
				<el-col :span="4">
					<span style="font-weight: 500;">通知名称：</span>
				</el-col>
				<el-col :span="8">
					<el-input
						v-model="filterForm.notificationName"
						placeholder="请输入通知名称"
						clearable
						style="width: 100%;"
					/>
				</el-col>
				<el-col :span="4">
					<el-button @click="resetFilter">重置</el-button>
				</el-col>
			</el-row>
		</div>

		<!-- 操作按钮 -->
		<div style="margin-bottom: 16px;">
			<el-button type="primary" :icon="Plus" @click="handleAdd">新增</el-button>
			<el-button type="danger" @click="handleDeleteSelected">删除选中</el-button>
			<el-button type="danger" @click="handleDeleteAll">删除全部</el-button>
		</div>
		
		<!-- 通知列表表格 -->
		<TableV2
			:defaultTableData="paginatedData"
			:auto-height="true"
			:max-height="400"
			:columns="columns"
			:enable-toolbar="false"
			:enable-create="false"
			:enable-edit="false"
			:enable-delete="false"
			:enable-index="true"
			:enable-selection="true"
			@selection-change="handleSelectionChange"
			class="mg-top-5"
		>
			<template #operation="{ row }">
				<el-button size="small" type="primary" @click="handleView(row)" :icon="View">查看</el-button>
				<el-button size="small" type="warning" @click="handleEdit(row)" :icon="Edit">编辑</el-button>
				<el-button size="small" type="danger" @click="handleDelete(row)" :icon="Delete">删除</el-button>
			</template>
		</TableV2>
		
		<!-- 分页 -->
		<Pagination
			:total="pagination.total"
			:page-size="pagination.size"
			:current-page="pagination.page"
			@current-change="onPaginationChange($event, 'page')"
			@size-change="onPaginationChange($event, 'size')"
		/>

		<!-- 新增通知对话框 -->
		<el-dialog
			v-model="addDialogVisible"
			title="新增权限变更通知"
			width="600px"
			:close-on-click-modal="false"
		>
			<el-form :model="addForm" label-width="100px">
				<el-form-item label="通知名称" required>
					<el-input
						v-model="addForm.notificationName"
						placeholder="请输入通知名称"
						clearable
					/>
				</el-form-item>
				<el-form-item label="通知内容" required>
					<el-input
						v-model="addForm.notificationContent"
						type="textarea"
						:rows="4"
						placeholder="请输入通知内容"
						clearable
					/>
				</el-form-item>
				<el-form-item label="通知人员" required>
					<el-select
						v-model="addForm.notificationPersonnel"
						placeholder="请选择通知人员"
						style="width: 100%;"
					>
						<el-option
							v-for="option in personnelOptions"
							:key="option.value"
							:label="option.label"
							:value="option.value"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="适用权限" required>
					<el-select
						v-model="addForm.applicablePermission"
						placeholder="请选择适用权限"
						style="width: 100%;"
						clearable
					>
						<el-option
							v-for="option in permissionOptions"
							:key="option.value"
							:label="option.label"
							:value="option.label"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="通知方式" required>
					<el-select
						v-model="addForm.notificationMethod"
						placeholder="请选择通知方式"
						style="width: 100%;"
					>
						<el-option
							v-for="option in methodOptions"
							:key="option.value"
							:label="option.label"
							:value="option.value"
						/>
					</el-select>
				</el-form-item>
			</el-form>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancelAdd">取消</el-button>
					<el-button type="primary" @click="confirmAdd">确定</el-button>
				</span>
			</template>
		</el-dialog>

		<!-- 查看通知对话框 -->
		<el-dialog
			v-model="viewDialogVisible"
			title="查看权限变更通知"
			width="600px"
			:close-on-click-modal="false"
		>
			<el-descriptions :column="1" border>
				<el-descriptions-item label="通知名称">{{ viewData.notificationName }}</el-descriptions-item>
				<el-descriptions-item label="通知权限">{{ viewData.notificationPermission }}</el-descriptions-item>
				<el-descriptions-item label="通知人员">
					<el-tag>{{ viewData.notificationPersonnel }}</el-tag>
				</el-descriptions-item>
				<el-descriptions-item label="通知方式">
					<el-tag type="success">{{ viewData.notificationMethod }}</el-tag>
				</el-descriptions-item>
				<el-descriptions-item label="创建时间">{{ viewData.createTime }}</el-descriptions-item>
				<el-descriptions-item label="适用权限">{{ viewData.applicablePermission }}</el-descriptions-item>
				<el-descriptions-item label="通知内容">
					<p style="line-height: 1.6; margin: 0;">{{ viewData.notificationContent }}</p>
				</el-descriptions-item>
			</el-descriptions>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="viewDialogVisible = false">关闭</el-button>
				</span>
			</template>
		</el-dialog>

		<!-- 编辑通知对话框 -->
		<el-dialog
			v-model="editDialogVisible"
			title="编辑权限变更通知"
			width="600px"
			:close-on-click-modal="false"
		>
			<el-form :model="editForm" label-width="100px">
				<el-form-item label="通知名称" required>
					<el-input
						v-model="editForm.notificationName"
						placeholder="请输入通知名称"
						clearable
					/>
				</el-form-item>
				<el-form-item label="通知内容" required>
					<el-input
						v-model="editForm.notificationContent"
						type="textarea"
						:rows="4"
						placeholder="请输入通知内容"
						clearable
					/>
				</el-form-item>
				<el-form-item label="通知人员" required>
					<el-select
						v-model="editForm.notificationPersonnel"
						placeholder="请选择通知人员"
						style="width: 100%;"
					>
						<el-option
							v-for="option in personnelOptions"
							:key="option.value"
							:label="option.label"
							:value="option.value"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="适用权限" required>
					<el-select
						v-model="editForm.applicablePermission"
						placeholder="请选择适用权限"
						style="width: 100%;"
						clearable
					>
						<el-option
							v-for="option in permissionOptions"
							:key="option.value"
							:label="option.label"
							:value="option.label"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="通知方式" required>
					<el-select
						v-model="editForm.notificationMethod"
						placeholder="请选择通知方式"
						style="width: 100%;"
					>
						<el-option
							v-for="option in methodOptions"
							:key="option.value"
							:label="option.label"
							:value="option.value"
						/>
					</el-select>
				</el-form-item>
			</el-form>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancelEdit">取消</el-button>
					<el-button type="primary" @click="confirmEdit">确定</el-button>
				</span>
			</template>
		</el-dialog>
	</Dialog>
</template>

<style scoped lang="scss">
// 样式可以根据需要添加
</style>

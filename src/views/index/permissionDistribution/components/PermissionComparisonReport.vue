<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, Edit, View, Delete } from '@element-plus/icons-vue'

// 权限比对报告数据接口
interface PermissionComparisonReportItem {
	id: number
	reportName: string
	comparisonPermission: string
	targetPermission: string
	generateTime: string
	status: '生成中' | '已生成'
	comparisonReport: string
	reportResult: string
}

// 编辑表单接口
interface EditReportForm {
	reportName: string
	comparisonPermission: string
	targetPermission: string
	comparisonReport: string
	reportResult: string
}

// 查看报告接口
interface ViewReportData {
	reportName: string
	comparisonPermission: string
	targetPermission: string
	generateTime: string
	status: string
	comparisonReport: string
	reportResult: string
}

// Props
interface Props {
	modelValue: boolean
	title?: string
	width?: string
}

const props = withDefaults(defineProps<Props>(), {
	title: '权限比对报告',
	width: '1200px'
})

const emit = defineEmits<{
	'update:modelValue': [value: boolean]
}>()

// 对话框显示控制
const dialogVisible = computed({
	get: () => props.modelValue,
	set: (value) => emit('update:modelValue', value)
})

// 权限选项
const permissionOptions = [
	{ label: '数据查看权限', value: 'data_view' },
	{ label: '数据编辑权限', value: 'data_edit' },
	{ label: '数据删除权限', value: 'data_delete' },
	{ label: '用户管理权限', value: 'user_manage' },
	{ label: '系统配置权限', value: 'system_config' },
	{ label: '报表生成权限', value: 'report_generate' },
	{ label: '审核权限', value: 'audit_permission' },
	{ label: '财务权限', value: 'finance_permission' },
	{ label: '人事权限', value: 'hr_permission' },
	{ label: '项目管理权限', value: 'project_manage' }
]

// 报告数据
const reportData = ref<PermissionComparisonReportItem[]>([
	{
		id: 1,
		reportName: '数据权限对比分析报告',
		comparisonPermission: '数据查看权限',
		targetPermission: '数据编辑权限',
		generateTime: '2024-01-15 10:30:00',
		status: '已生成',
		comparisonReport: '本次对比分析了数据查看权限与数据编辑权限的差异。数据查看权限主要用于只读访问，而数据编辑权限允许用户修改数据内容。通过对比发现，数据编辑权限包含了数据查看权限的所有功能，并额外增加了创建、更新、删除等操作权限。',
		reportResult: '对比结果显示：1. 数据查看权限覆盖15个角色，128个用户；2. 数据编辑权限覆盖8个角色，45个用户；3. 权限重叠度为35%；4. 建议将部分只需查看权限的用户从编辑权限中移除，以提高安全性。'
	},
	{
		id: 2,
		reportName: '用户管理权限对比报告',
		comparisonPermission: '用户管理权限',
		targetPermission: '系统配置权限',
		generateTime: '2024-01-14 14:20:00',
		status: '已生成',
		comparisonReport: '用户管理权限与系统配置权限的对比分析。用户管理权限主要负责用户账户的创建、修改、删除和权限分配，而系统配置权限涉及系统参数设置、功能模块配置等。两者在管理范围上有明显区别，但在某些系统管理功能上存在交集。',
		reportResult: '对比结果：1. 用户管理权限涉及5个角色，23个用户；2. 系统配置权限涉及2个角色，8个用户；3. 功能重叠主要在系统用户配置方面；4. 建议明确权限边界，避免权限冲突。'
	},
	{
		id: 3,
		reportName: '财务权限对比分析',
		comparisonPermission: '财务权限',
		targetPermission: '审核权限',
		generateTime: '2024-01-13 09:15:00',
		status: '生成中',
		comparisonReport: '正在分析财务权限与审核权限的关联性和差异性...',
		reportResult: '报告正在生成中，请稍后查看完整结果。'
	},
	{
		id: 4,
		reportName: '项目管理权限对比',
		comparisonPermission: '项目管理权限',
		targetPermission: '人事权限',
		generateTime: '2024-01-12 16:45:00',
		status: '已生成',
		comparisonReport: '项目管理权限与人事权限的对比分析。项目管理权限主要涉及项目创建、任务分配、进度跟踪等功能，而人事权限涉及员工信息管理、考勤管理、薪资管理等。两者在人员管理方面存在一定的交集。',
		reportResult: '对比结果：1. 项目管理权限涉及7个角色，42个用户；2. 人事权限涉及3个角色，15个用户；3. 在项目人员分配方面存在权限重叠；4. 建议建立统一的人员管理接口。'
	},
	{
		id: 5,
		reportName: '报表权限对比分析',
		comparisonPermission: '报表生成权限',
		targetPermission: '数据查看权限',
		generateTime: '2024-01-11 11:30:00',
		status: '已生成',
		comparisonReport: '报表生成权限与数据查看权限的关联性分析。报表生成权限允许用户创建和导出各类报表，而数据查看权限提供基础的数据访问能力。报表生成通常需要基于数据查看权限。',
		reportResult: '对比结果：1. 报表生成权限涉及12个角色，89个用户；2. 数据查看权限涉及15个角色，128个用户；3. 所有报表生成用户都应具备数据查看权限；4. 发现3个用户只有报表权限但缺少查看权限，需要补充。'
	},
	{
		id: 6,
		reportName: '系统配置权限对比',
		comparisonPermission: '系统配置权限',
		targetPermission: '数据删除权限',
		generateTime: '2024-01-10 13:20:00',
		status: '已生成',
		comparisonReport: '系统配置权限与数据删除权限的安全性对比。系统配置权限涉及系统级别的参数设置，而数据删除权限涉及业务数据的删除操作。两者都属于高风险权限，需要严格控制。',
		reportResult: '对比结果：1. 系统配置权限涉及2个角色，8个用户；2. 数据删除权限涉及3个角色，12个用户；3. 两个权限的用户重叠度为25%；4. 建议加强这两类权限的审批流程和操作日志记录。'
	},
	{
		id: 7,
		reportName: '审核流程权限对比',
		comparisonPermission: '审核权限',
		targetPermission: '财务权限',
		generateTime: '2024-01-09 15:10:00',
		status: '已生成',
		comparisonReport: '审核权限与财务权限的流程对比分析。审核权限主要涉及业务流程的审批和确认，而财务权限涉及资金管理和财务报表。两者在财务审批流程中存在密切关联。',
		reportResult: '对比结果：1. 审核权限涉及6个角色，34个用户；2. 财务权限涉及4个角色，18个用户；3. 在财务审批环节存在权限交集；4. 建议优化审批流程，明确各环节的权限要求。'
	},
	{
		id: 8,
		reportName: '人事权限对比分析',
		comparisonPermission: '人事权限',
		targetPermission: '用户管理权限',
		generateTime: '2024-01-08 10:00:00',
		status: '已生成',
		comparisonReport: '人事权限与用户管理权限的功能边界分析。人事权限主要管理员工的基本信息、考勤、薪资等，而用户管理权限主要管理系统用户账户和权限分配。两者在员工账户管理方面存在重叠。',
		reportResult: '对比结果：1. 人事权限涉及3个角色，15个用户；2. 用户管理权限涉及5个角色，23个用户；3. 在员工账户创建和维护方面存在职责重叠；4. 建议建立统一的员工账户管理流程。'
	},
	{
		id: 9,
		reportName: '数据编辑权限对比',
		comparisonPermission: '数据编辑权限',
		targetPermission: '数据删除权限',
		generateTime: '2024-01-07 14:30:00',
		status: '已生成',
		comparisonReport: '数据编辑权限与数据删除权限的安全级别对比。数据编辑权限允许修改现有数据，而数据删除权限允许永久删除数据。删除权限的风险级别更高，需要更严格的控制。',
		reportResult: '对比结果：1. 数据编辑权限涉及8个角色，45个用户；2. 数据删除权限涉及3个角色，12个用户；3. 所有删除权限用户都具备编辑权限；4. 建议对删除操作增加二次确认和备份机制。'
	},
	{
		id: 10,
		reportName: '综合权限对比报告',
		comparisonPermission: '数据查看权限',
		targetPermission: '项目管理权限',
		generateTime: '2024-01-06 09:45:00',
		status: '已生成',
		comparisonReport: '数据查看权限与项目管理权限的综合对比分析。数据查看权限提供基础的数据访问能力，而项目管理权限涉及项目全生命周期管理。两者在项目数据查看方面存在关联。',
		reportResult: '对比结果：1. 数据查看权限涉及15个角色，128个用户；2. 项目管理权限涉及7个角色，42个用户；3. 项目管理用户中95%具备数据查看权限；4. 建议为项目管理角色统一配置数据查看权限。'
	},
	{
		id: 11,
		reportName: '权限使用情况对比',
		comparisonPermission: '报表生成权限',
		targetPermission: '审核权限',
		generateTime: '2024-01-05 16:20:00',
		status: '已生成',
		comparisonReport: '报表生成权限与审核权限的使用频率对比。报表生成权限主要用于数据统计和分析，而审核权限用于业务流程控制。两者在审核报表生成方面存在协作关系。',
		reportResult: '对比结果：1. 报表生成权限涉及12个角色，89个用户；2. 审核权限涉及6个角色，34个用户；3. 审核相关报表使用频率较高；4. 建议为审核角色增加专门的审核报表权限。'
	},
	{
		id: 12,
		reportName: '部门权限对比分析',
		comparisonPermission: '用户管理权限',
		targetPermission: '财务权限',
		generateTime: '2024-01-04 12:15:00',
		status: '已生成',
		comparisonReport: '用户管理权限与财务权限的部门分布对比。用户管理权限主要集中在IT部门和人事部门，而财务权限主要集中在财务部门。两者在跨部门协作中需要权限配合。',
		reportResult: '对比结果：1. 用户管理权限分布在3个部门，23个用户；2. 财务权限分布在2个部门，18个用户；3. 跨部门权限协作场景较少；4. 建议建立跨部门权限协调机制。'
	},
	{
		id: 13,
		reportName: '客户权限对比报告',
		comparisonPermission: '客户管理权限',
		targetPermission: '销售管理权限',
		generateTime: '2024-01-03 10:30:00',
		status: '已生成',
		comparisonReport: '客户管理权限与销售管理权限的业务关联对比。客户管理权限主要涉及客户信息维护和关系管理，而销售管理权限涉及销售流程和业绩管理。两者在客户销售环节密切配合。',
		reportResult: '对比结果：1. 客户管理权限涉及4个角色，28个用户；2. 销售管理权限涉及5个角色，35个用户；3. 权限重叠度为60%，协作频繁；4. 建议整合客户销售权限，提高业务效率。'
	},
	{
		id: 14,
		reportName: '订单权限对比分析',
		comparisonPermission: '订单管理权限',
		targetPermission: '库存管理权限',
		generateTime: '2024-01-02 14:20:00',
		status: '生成中',
		comparisonReport: '正在分析订单管理权限与库存管理权限的业务流程关联...',
		reportResult: '报告正在生成中，预计完成时间：2024-01-02 16:00:00'
	},
	{
		id: 15,
		reportName: '采购权限对比报告',
		comparisonPermission: '采购管理权限',
		targetPermission: '财务权限',
		generateTime: '2024-01-01 09:15:00',
		status: '已生成',
		comparisonReport: '采购管理权限与财务权限的流程衔接对比。采购管理权限涉及供应商管理、采购申请、合同签订等，而财务权限涉及付款审批、成本核算等。两者在采购付款流程中紧密配合。',
		reportResult: '对比结果：1. 采购管理权限涉及3个角色，16个用户；2. 财务权限涉及4个角色，18个用户；3. 在采购付款环节存在权限依赖；4. 建议优化采购财务一体化流程。'
	}
])

// 表格列配置
const columns = [
	{ prop: 'reportName', label: '报告名称' },
	{ prop: 'comparisonPermission', label: '对比权限' },
	{ prop: 'generateTime', label: '生成时间' },
	{ prop: 'status', label: '状态' },
	{ prop: 'operation', label: '操作', width: '300px' }
]

// 分页配置
const pagination = reactive({
	total: 0,
	size: 10,
	page: 1,
})

// 分页数据
const paginatedData = computed(() => {
	const start = (pagination.page - 1) * pagination.size
	const end = start + pagination.size
	return reportData.value.slice(start, end)
})

// 分页事件
const onPaginationChange = (value: number, type: string) => {
	if (type === 'page') {
		pagination.page = value
	} else if (type === 'size') {
		pagination.size = value
		pagination.page = 1
	}
}

// 编辑对话框
const editDialogVisible = ref(false)
const editForm = ref<EditReportForm>({
	reportName: '',
	comparisonPermission: '',
	targetPermission: '',
	comparisonReport: '',
	reportResult: ''
})
const currentEditReport = ref<PermissionComparisonReportItem | null>(null)

// 查看对话框
const viewDialogVisible = ref(false)
const viewData = ref<ViewReportData>({
	reportName: '',
	comparisonPermission: '',
	targetPermission: '',
	generateTime: '',
	status: '',
	comparisonReport: '',
	reportResult: ''
})



// 查看报告
const handleView = (row: PermissionComparisonReportItem) => {
	if (row.status === '生成中') {
		ElMessage.warning('报告正在生成中，请稍后查看')
		return
	}

	viewData.value = {
		reportName: row.reportName,
		comparisonPermission: row.comparisonPermission,
		targetPermission: row.targetPermission,
		generateTime: row.generateTime,
		status: row.status,
		comparisonReport: row.comparisonReport,
		reportResult: row.reportResult
	}
	viewDialogVisible.value = true
}

// 编辑报告
const handleEdit = (row: PermissionComparisonReportItem) => {
	currentEditReport.value = row
	editForm.value = {
		reportName: row.reportName,
		comparisonPermission: row.comparisonPermission,
		targetPermission: row.targetPermission,
		comparisonReport: row.comparisonReport,
		reportResult: row.reportResult
	}
	editDialogVisible.value = true
}

// 下载报告
const handleDownload = (row: PermissionComparisonReportItem) => {
	if (row.status === '生成中') {
		ElMessage.warning('报告正在生成中，请稍后下载')
		return
	}
	
	// 创建下载数据
	const downloadData = {
		reportName: row.reportName,
		comparisonPermission: row.comparisonPermission,
		targetPermission: row.targetPermission,
		generateTime: row.generateTime,
		status: row.status,
		reportContent: '这里是权限比对报告的详细内容...'
	}
	
	// 创建并下载文件
	const blob = new Blob([JSON.stringify(downloadData, null, 2)], { type: 'application/json' })
	const url = URL.createObjectURL(blob)
	const link = document.createElement('a')
	link.href = url
	link.download = `${row.reportName}_${new Date().getTime()}.json`
	document.body.appendChild(link)
	link.click()
	document.body.removeChild(link)
	URL.revokeObjectURL(url)
	
	ElMessage.success('报告下载成功')
}

// 删除报告
const handleDelete = (row: PermissionComparisonReportItem) => {
	ElMessageBox.confirm(
		`确定要删除报告"${row.reportName}"吗？`,
		'确认删除',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}
	)
		.then(() => {
			const index = reportData.value.findIndex(item => item.id === row.id)
			if (index !== -1) {
				reportData.value.splice(index, 1)
				pagination.total = reportData.value.length
				ElMessage.success('删除成功')
			}
		})
		.catch(() => {})
}

// 确认编辑
const confirmEdit = () => {
	if (!editForm.value.reportName.trim()) {
		ElMessage.warning('请输入报告名称')
		return
	}
	if (!editForm.value.comparisonPermission) {
		ElMessage.warning('请选择对比权限')
		return
	}
	if (!editForm.value.targetPermission) {
		ElMessage.warning('请选择目标权限')
		return
	}
	if (!editForm.value.comparisonReport.trim()) {
		ElMessage.warning('请输入对比报告内容')
		return
	}
	if (!editForm.value.reportResult.trim()) {
		ElMessage.warning('请输入报告结果')
		return
	}

	if (currentEditReport.value) {
		currentEditReport.value.reportName = editForm.value.reportName
		currentEditReport.value.comparisonPermission = editForm.value.comparisonPermission
		currentEditReport.value.targetPermission = editForm.value.targetPermission
		currentEditReport.value.comparisonReport = editForm.value.comparisonReport
		currentEditReport.value.reportResult = editForm.value.reportResult

		ElMessage.success('编辑成功')
		editDialogVisible.value = false
	}
}

// 取消编辑
const cancelEdit = () => {
	editDialogVisible.value = false
}

// 监听对话框打开
watch(() => props.modelValue, (newValue) => {
	if (newValue) {
		// 对话框打开时初始化分页
		pagination.total = reportData.value.length
		pagination.page = 1
	}
})
</script>

<template>
	<Dialog
		v-model="dialogVisible"
		:title="title"
		:width="width"
		:destroy-on-close="true"
	>
		<!-- 报告列表表格 -->
		<TableV2
			:defaultTableData="paginatedData"
			:auto-height="true"
			:max-height="400"
			:columns="columns"
			:enable-toolbar="false"
			:enable-create="false"
			:enable-edit="false"
			:enable-delete="false"
			:enable-index="true"
			class="mg-top-5"
		>
			<template #status="{ row }">
				<el-tag v-if="row.status === '已生成'" type="success">已生成</el-tag>
				<el-tag v-else type="warning">生成中</el-tag>
			</template>
			<template #operation="{ row }">
				<el-button size="small" type="primary" @click="handleView(row)" :icon="View">查看</el-button>
				<el-button size="small" type="warning" @click="handleEdit(row)" :icon="Edit">编辑</el-button>
				<el-button size="small" type="success" @click="handleDownload(row)" :icon="Download">下载</el-button>
				<el-button size="small" type="danger" @click="handleDelete(row)" :icon="Delete">删除</el-button>
			</template>
		</TableV2>
		
		<!-- 分页 -->
		<Pagination
			:total="pagination.total"
			:page-size="pagination.size"
			:current-page="pagination.page"
			@current-change="onPaginationChange($event, 'page')"
			@size-change="onPaginationChange($event, 'size')"
		/>
		
		<!-- 查看报告对话框 -->
		<el-dialog
			v-model="viewDialogVisible"
			title="查看权限比对报告"
			width="800px"
			:close-on-click-modal="false"
		>
			<el-descriptions :column="2" border>
				<el-descriptions-item label="报告名称">{{ viewData.reportName }}</el-descriptions-item>
				<el-descriptions-item label="生成时间">{{ viewData.generateTime }}</el-descriptions-item>
				<el-descriptions-item label="对比权限">{{ viewData.comparisonPermission }}</el-descriptions-item>
				<el-descriptions-item label="目标权限">{{ viewData.targetPermission }}</el-descriptions-item>
				<el-descriptions-item label="状态">
					<el-tag v-if="viewData.status === '已生成'" type="success">已生成</el-tag>
					<el-tag v-else type="warning">生成中</el-tag>
				</el-descriptions-item>
			</el-descriptions>

			<div style="margin-top: 20px;">
				<h4>对比报告</h4>
				<el-card shadow="never" style="margin-top: 10px;">
					<p style="line-height: 1.6; margin: 0;">{{ viewData.comparisonReport }}</p>
				</el-card>
			</div>

			<div style="margin-top: 20px;">
				<h4>报告结果</h4>
				<el-card shadow="never" style="margin-top: 10px;">
					<p style="line-height: 1.6; margin: 0;">{{ viewData.reportResult }}</p>
				</el-card>
			</div>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="viewDialogVisible = false">关闭</el-button>
				</span>
			</template>
		</el-dialog>

		<!-- 编辑报告对话框 -->
		<el-dialog
			v-model="editDialogVisible"
			title="编辑权限比对报告"
			width="800px"
			:close-on-click-modal="false"
		>
			<el-form :model="editForm" label-width="100px">
				<el-form-item label="报告名称" required>
					<el-input
						v-model="editForm.reportName"
						placeholder="请输入报告名称"
						clearable
					/>
				</el-form-item>
				<el-form-item label="对比权限" required>
					<el-select
						v-model="editForm.comparisonPermission"
						placeholder="请选择对比权限"
						style="width: 100%;"
						clearable
					>
						<el-option
							v-for="option in permissionOptions"
							:key="option.value"
							:label="option.label"
							:value="option.label"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="目标权限" required>
					<el-select
						v-model="editForm.targetPermission"
						placeholder="请选择目标权限"
						style="width: 100%;"
						clearable
					>
						<el-option
							v-for="option in permissionOptions"
							:key="option.value"
							:label="option.label"
							:value="option.label"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="对比报告" required>
					<el-input
						v-model="editForm.comparisonReport"
						type="textarea"
						:rows="4"
						placeholder="请输入对比报告内容"
						clearable
					/>
				</el-form-item>
				<el-form-item label="报告结果" required>
					<el-input
						v-model="editForm.reportResult"
						type="textarea"
						:rows="4"
						placeholder="请输入报告结果"
						clearable
					/>
				</el-form-item>
			</el-form>
			
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancelEdit">取消</el-button>
					<el-button type="primary" @click="confirmEdit">确定</el-button>
				</span>
			</template>
		</el-dialog>
	</Dialog>
</template>

<style scoped lang="scss">
// 样式可以根据需要添加
</style>

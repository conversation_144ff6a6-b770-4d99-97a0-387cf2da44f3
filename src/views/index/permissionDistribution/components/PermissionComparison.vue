<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 权限比对数据接口
interface PermissionComparisonItem {
	id: number
	permissionName: string
	roleCount: number
	userCount: number
	usageCount: number
	temporaryAuthCount: number
}

// 比对报告接口
interface ComparisonReport {
	id: number
	reportName: string
	reportDescription: string
	createTime: string
	comparisonPermission: string
	targetPermission: string
	reportData: PermissionComparisonItem[]
}

// 报告表单接口
interface ReportForm {
	reportName: string
	reportDescription: string
}

// Props
interface Props {
	modelValue: boolean
	title?: string
	width?: string
}

const props = withDefaults(defineProps<Props>(), {
	title: '权限比对',
	width: '1200px'
})

const emit = defineEmits<{
	'update:modelValue': [value: boolean]
}>()

// 对话框显示控制
const dialogVisible = computed({
	get: () => props.modelValue,
	set: (value) => emit('update:modelValue', value)
})

// 监听对话框打开
watch(() => props.modelValue, (newValue) => {
	if (newValue) {
		// 对话框打开时初始化分页
		pagination.total = comparisonData.value.length
		pagination.page = 1
	}
})

// 权限选项
const permissionOptions = [
	{ label: '数据查看权限', value: 'data_view' },
	{ label: '数据编辑权限', value: 'data_edit' },
	{ label: '数据删除权限', value: 'data_delete' },
	{ label: '用户管理权限', value: 'user_manage' },
	{ label: '系统配置权限', value: 'system_config' },
	{ label: '报表生成权限', value: 'report_generate' },
	{ label: '审核权限', value: 'audit_permission' },
	{ label: '财务权限', value: 'finance_permission' },
	{ label: '人事权限', value: 'hr_permission' },
	{ label: '项目管理权限', value: 'project_manage' }
]

// 比对配置
const comparisonConfig = reactive({
	comparisonPermission: '', // 比对权限
	targetPermission: ''      // 被比对权限
})

// 比对结果数据
const comparisonData = ref<PermissionComparisonItem[]>([
	{
		id: 1,
		permissionName: '数据查看权限',
		roleCount: 15,
		userCount: 128,
		usageCount: 2456,
		temporaryAuthCount: 8
	},
	{
		id: 2,
		permissionName: '数据编辑权限',
		roleCount: 8,
		userCount: 45,
		usageCount: 1234,
		temporaryAuthCount: 3
	},
	{
		id: 3,
		permissionName: '数据删除权限',
		roleCount: 3,
		userCount: 12,
		usageCount: 156,
		temporaryAuthCount: 1
	},
	{
		id: 4,
		permissionName: '用户管理权限',
		roleCount: 5,
		userCount: 23,
		usageCount: 789,
		temporaryAuthCount: 2
	},
	{
		id: 5,
		permissionName: '系统配置权限',
		roleCount: 2,
		userCount: 8,
		usageCount: 234,
		temporaryAuthCount: 0
	},
	{
		id: 6,
		permissionName: '报表生成权限',
		roleCount: 12,
		userCount: 89,
		usageCount: 1567,
		temporaryAuthCount: 5
	},
	{
		id: 7,
		permissionName: '审核权限',
		roleCount: 6,
		userCount: 34,
		usageCount: 892,
		temporaryAuthCount: 4
	},
	{
		id: 8,
		permissionName: '财务权限',
		roleCount: 4,
		userCount: 18,
		usageCount: 456,
		temporaryAuthCount: 1
	},
	{
		id: 9,
		permissionName: '人事权限',
		roleCount: 3,
		userCount: 15,
		usageCount: 345,
		temporaryAuthCount: 2
	},
	{
		id: 10,
		permissionName: '项目管理权限',
		roleCount: 7,
		userCount: 42,
		usageCount: 1123,
		temporaryAuthCount: 6
	},
	{
		id: 11,
		permissionName: '客户管理权限',
		roleCount: 9,
		userCount: 67,
		usageCount: 1890,
		temporaryAuthCount: 7
	},
	{
		id: 12,
		permissionName: '订单管理权限',
		roleCount: 11,
		userCount: 78,
		usageCount: 2134,
		temporaryAuthCount: 9
	},
	{
		id: 13,
		permissionName: '库存管理权限',
		roleCount: 6,
		userCount: 34,
		usageCount: 987,
		temporaryAuthCount: 4
	},
	{
		id: 14,
		permissionName: '采购管理权限',
		roleCount: 4,
		userCount: 21,
		usageCount: 567,
		temporaryAuthCount: 2
	},
	{
		id: 15,
		permissionName: '销售管理权限',
		roleCount: 8,
		userCount: 56,
		usageCount: 1456,
		temporaryAuthCount: 6
	},
	{
		id: 16,
		permissionName: '营销管理权限',
		roleCount: 5,
		userCount: 29,
		usageCount: 723,
		temporaryAuthCount: 3
	},
	{
		id: 17,
		permissionName: '合同管理权限',
		roleCount: 7,
		userCount: 41,
		usageCount: 1098,
		temporaryAuthCount: 5
	},
	{
		id: 18,
		permissionName: '文档管理权限',
		roleCount: 10,
		userCount: 73,
		usageCount: 1789,
		temporaryAuthCount: 8
	},
	{
		id: 19,
		permissionName: '流程管理权限',
		roleCount: 6,
		userCount: 38,
		usageCount: 934,
		temporaryAuthCount: 4
	},
	{
		id: 20,
		permissionName: '质量管理权限',
		roleCount: 4,
		userCount: 19,
		usageCount: 456,
		temporaryAuthCount: 2
	},
	{
		id: 21,
		permissionName: '安全管理权限',
		roleCount: 3,
		userCount: 14,
		usageCount: 289,
		temporaryAuthCount: 1
	},
	{
		id: 22,
		permissionName: '监控管理权限',
		roleCount: 5,
		userCount: 27,
		usageCount: 678,
		temporaryAuthCount: 3
	},
	{
		id: 23,
		permissionName: '日志管理权限',
		roleCount: 8,
		userCount: 52,
		usageCount: 1234,
		temporaryAuthCount: 6
	},
	{
		id: 24,
		permissionName: '备份管理权限',
		roleCount: 2,
		userCount: 9,
		usageCount: 178,
		temporaryAuthCount: 1
	},
	{
		id: 25,
		permissionName: '统计分析权限',
		roleCount: 9,
		userCount: 64,
		usageCount: 1567,
		temporaryAuthCount: 7
	}
])

// 表格列配置
const columns = [
	{ prop: 'permissionName', label: '权限名称' },
	{ prop: 'roleCount', label: '角色数量' },
	{ prop: 'userCount', label: '用户数量' },
	{ prop: 'usageCount', label: '使用次数' },
	{ prop: 'temporaryAuthCount', label: '临时授权数' }
]

// 分页配置
const pagination = reactive({
	total: 0,
	size: 10,
	page: 1,
})

// 分页数据
const paginatedData = computed(() => {
	const start = (pagination.page - 1) * pagination.size
	const end = start + pagination.size
	return comparisonData.value.slice(start, end)
})

// 分页事件
const onPaginationChange = (value: number, type: string) => {
	if (type === 'page') {
		pagination.page = value
	} else if (type === 'size') {
		pagination.size = value
		pagination.page = 1
	}
}

// 生成报告对话框
const reportDialogVisible = ref(false)
const reportForm = ref<ReportForm>({
	reportName: '',
	reportDescription: ''
})

// 全部比对
const handleFullComparison = () => {
	if (!comparisonConfig.comparisonPermission) {
		ElMessage.warning('请选择比对权限')
		return
	}
	if (!comparisonConfig.targetPermission) {
		ElMessage.warning('请选择被比对权限')
		return
	}
	
	ElMessage.success('全部比对完成')
	pagination.total = comparisonData.value.length
}

// 开始比对
const handleStartComparison = () => {
	if (!comparisonConfig.comparisonPermission) {
		ElMessage.warning('请选择比对权限')
		return
	}
	if (!comparisonConfig.targetPermission) {
		ElMessage.warning('请选择被比对权限')
		return
	}
	
	// 模拟比对过程
	ElMessage.info('开始比对...')
	setTimeout(() => {
		ElMessage.success('比对完成')
		pagination.total = comparisonData.value.length
	}, 1000)
}

// 生成对比报告
const handleGenerateReport = () => {
	if (!comparisonConfig.comparisonPermission) {
		ElMessage.warning('请先进行权限比对')
		return
	}
	if (!comparisonConfig.targetPermission) {
		ElMessage.warning('请先进行权限比对')
		return
	}
	
	// 重置表单
	reportForm.value = {
		reportName: '',
		reportDescription: ''
	}
	reportDialogVisible.value = true
}

// 确认生成报告
const confirmGenerateReport = () => {
	if (!reportForm.value.reportName.trim()) {
		ElMessage.warning('请输入报告名称')
		return
	}
	if (!reportForm.value.reportDescription.trim()) {
		ElMessage.warning('请输入报告描述')
		return
	}
	
	// 创建报告
	const newReport: ComparisonReport = {
		id: Date.now(),
		reportName: reportForm.value.reportName,
		reportDescription: reportForm.value.reportDescription,
		createTime: new Date().toLocaleString(),
		comparisonPermission: getPermissionLabel(comparisonConfig.comparisonPermission),
		targetPermission: getPermissionLabel(comparisonConfig.targetPermission),
		reportData: [...comparisonData.value]
	}
	
	ElMessage.success('对比报告生成成功')
	reportDialogVisible.value = false
}

// 取消生成报告
const cancelGenerateReport = () => {
	reportDialogVisible.value = false
}

// 获取权限标签
const getPermissionLabel = (value: string) => {
	const option = permissionOptions.find(item => item.value === value)
	return option ? option.label : value
}


</script>

<template>
	<Dialog
		v-model="dialogVisible"
		:title="title"
		:width="width"
		:destroy-on-close="true"
	>
		<!-- 比对配置 -->
		<div style="margin-bottom: 20px; padding: 15px; background: #f5f5f5; border-radius: 4px;">
			<div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
				<div style="display: flex; align-items: center; gap: 10px;">
					<label style="font-weight: bold;">比对权限：</label>
					<el-select
						v-model="comparisonConfig.comparisonPermission"
						placeholder="请选择比对权限"
						style="width: 200px;"
						clearable
					>
						<el-option
							v-for="option in permissionOptions"
							:key="option.value"
							:label="option.label"
							:value="option.value"
						/>
					</el-select>
				</div>
				
				<div style="display: flex; align-items: center; gap: 10px;">
					<label style="font-weight: bold;">被比对权限：</label>
					<el-select
						v-model="comparisonConfig.targetPermission"
						placeholder="请选择被比对权限"
						style="width: 200px;"
						clearable
					>
						<el-option
							v-for="option in permissionOptions"
							:key="option.value"
							:label="option.label"
							:value="option.value"
						/>
					</el-select>
				</div>
			</div>
			
			<div style="display: flex; gap: 10px;">
				<el-button type="primary" @click="handleFullComparison">全部比对</el-button>
				<el-button type="success" @click="handleStartComparison">开始比对</el-button>
				<el-button type="warning" @click="handleGenerateReport">生成对比报告</el-button>
			</div>
		</div>
		
		<!-- 比对结果表格 -->
		<TableV2
			:defaultTableData="paginatedData"
			:auto-height="true"
			:max-height="400"
			:columns="columns"
			:enable-toolbar="false"
			:enable-create="false"
			:enable-edit="false"
			:enable-delete="false"
			:enable-index="true"
			class="mg-top-5"
		/>
		
		<!-- 分页 -->
		<Pagination
			:total="pagination.total"
			:page-size="pagination.size"
			:current-page="pagination.page"
			@current-change="onPaginationChange($event, 'page')"
			@size-change="onPaginationChange($event, 'size')"
		/>
		
		<!-- 生成报告对话框 -->
		<el-dialog
			v-model="reportDialogVisible"
			title="生成对比报告"
			width="500px"
			:close-on-click-modal="false"
		>
			<el-form :model="reportForm" label-width="100px">
				<el-form-item label="报告名称" required>
					<el-input
						v-model="reportForm.reportName"
						placeholder="请输入报告名称"
						clearable
					/>
				</el-form-item>
				<el-form-item label="报告描述" required>
					<el-input
						v-model="reportForm.reportDescription"
						type="textarea"
						:rows="4"
						placeholder="请输入报告描述"
					/>
				</el-form-item>
			</el-form>
			
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancelGenerateReport">取消</el-button>
					<el-button type="primary" @click="confirmGenerateReport">确定</el-button>
				</span>
			</template>
		</el-dialog>
	</Dialog>
</template>

<style scoped lang="scss">
// 样式可以根据需要添加
</style>

<script setup lang="ts" name="PermissionUsageReport">
import {ACTION_KEY, useLocalStorage} from '@/hooks/useLocalStorage'
import {useSleep} from '@/hooks/useSleep'
import {useUserStore} from '@/stores/useUserStore'
import {exportExcel} from '@/views/index/statementTask/exportExcel'
import * as echarts from 'echarts'

const storage: any = useLocalStorage()
const emits = defineEmits(['update:modelValue'])
const userInfo = useUserStore().getUserInfo
const loading = ref(false)

const getCurrentTimeFormatted = () => {
	const now = new Date()
	const year = now.getFullYear()
	const month = String(now.getMonth() + 1).padStart(2, '0')
	const day = String(now.getDate()).padStart(2, '0')
	const hours = String(now.getHours()).padStart(2, '0')
	const minutes = String(now.getMinutes()).padStart(2, '0')
	return `${year}-${month}-${day} ${hours}:${minutes}`
}

const searchParams = ref<Form>({
	reportName: '',
	permissionName: '',
	status: '',
	skipCount: 0,
	maxResultCount: 10,
})

const sheets = ref<any>([])

interface Form {
	[key: string]: any
}

const formProps = [
	{
		label: '报告名称',
		prop: 'reportName',
		field: 'reportName',
		type: 'text',
	},
	{
		label: '权限名称',
		prop: 'permissionName',
		field: 'permissionName',
		type: 'select',
		options: [
			{label: '数据权限', value: '数据权限'},
			{label: '删除权限', value: '删除权限'},
		],
	},
	{
		label: '状态',
		prop: 'status',
		field: 'status',
		type: 'select',
		options: [
			{label: '已生成', value: '已生成'},
			{label: '生成中', value: '生成中'},
		],
	},
]

interface PermissionUsageReportItem {
	id: number
	reportName: string
	permissionName: string
	creator: string
	createTime: string
	status: string
	timeRange?: string
	customStartTime?: string
	customEndTime?: string
	description?: string
}

const tableData = ref<PermissionUsageReportItem[]>([
	{
		id: 1,
		reportName: '2024年第一季度权限运用报告',
		permissionName: '数据权限',
		creator: userInfo.name,
		createTime: '2024-01-15 10:30',
		status: '已生成',
		timeRange: '本季度',
		description: '第一季度数据权限使用情况统计报告',
	},
	{
		id: 2,
		reportName: '2024年第二季度权限运用报告',
		permissionName: '删除权限',
		creator: userInfo.name,
		createTime: '2024-04-15 14:20',
		status: '已生成',
		timeRange: '本季度',
		description: '第二季度删除权限使用情况统计报告',
	},
	{
		id: 3,
		reportName: '2024年第三季度权限运用报告',
		permissionName: '数据权限',
		creator: userInfo.name,
		createTime: '2024-07-15 09:15',
		status: '生成中',
		timeRange: '本季度',
		description: '第三季度数据权限使用情况统计报告',
	},
	{
		id: 4,
		reportName: '2024年第四季度权限运用报告',
		permissionName: '删除权限',
		creator: userInfo.name,
		createTime: '2024-10-15 16:45',
		status: '已生成',
		timeRange: '本季度',
		description: '第四季度删除权限使用情况统计报告',
	},
	{
		id: 5,
		reportName: '年度权限运用综合报告',
		permissionName: '数据权限',
		creator: userInfo.name,
		createTime: '2024-12-01 11:30',
		status: '生成中',
		timeRange: '本年度',
		description: '年度权限运用综合统计分析报告',
	},
])

const deepData = ref<PermissionUsageReportItem[]>([...tableData.value])

watch(
	() => tableData.value.length,
	(newVal) => {
		pagination.value.total = newVal
	}
)

const columns = [
	{prop: 'reportName', label: '报告名称'},
	{prop: 'permissionName', label: '权限名称'},
	{prop: 'creator', label: '创建人'},
	{prop: 'createTime', label: '创建时间'},
	{prop: 'status', label: '状态'},
]

// 表格中的操作列
const buttons: any = [
	{
		code: 'details',
		label: '查看',
		type: 'text',
		verify: 'true',
	},
	{
		code: 'edit',
		label: '编辑',
		type: 'text',
		verify: 'true',
	},
	{
		code: 'delete',
		label: '删除',
		type: 'text',
		verify: 'true',
	},
	{
		code: 'download',
		label: '下载',
		type: 'text',
		verify: 'true',
	},
]

const handleSelectionChange = (val: PermissionUsageReportItem[]) => {
	const list = JSON.parse(JSON.stringify(val))
	sheets.value = list
}

const handlSearch = () => {
	loading.value = true
	useSleep().then(() => {
		let filteredData = deepData.value
		
		if (searchParams.value.reportName) {
			filteredData = filteredData.filter((item: PermissionUsageReportItem) =>
				item.reportName.includes(searchParams.value.reportName)
			)
		}
		
		if (searchParams.value.permissionName) {
			filteredData = filteredData.filter((item: PermissionUsageReportItem) =>
				item.permissionName === searchParams.value.permissionName
			)
		}
		
		if (searchParams.value.status) {
			filteredData = filteredData.filter((item: PermissionUsageReportItem) =>
				item.status === searchParams.value.status
			)
		}
		
		tableData.value = filteredData
		loading.value = false
	})
}

const onConfirm = () => {
	loading.value = true
	useSleep().then(() => {
		loading.value = false
		storage.save(ACTION_KEY.PermissionUsageReport, tableData.value)
		onClose()
	})
}

const onOpen = () => {
	loading.value = true
	useSleep().then(() => {
		tableData.value = storage.get(ACTION_KEY.PermissionUsageReport) || tableData.value
		deepData.value = storage.get(ACTION_KEY.PermissionUsageReport) || deepData.value
		loading.value = false
	})
}

const onClose = () => {
	sheets.value = []
	searchParams.value = {
		reportName: '',
		permissionName: '',
		status: '',
		skipCount: 0,
		maxResultCount: 10
	}
	emits('update:modelValue', false)
}

// 分页相关参数
const pagination = ref({
	total: tableData.value.length,
	page: 1,
	size: 10,
})

// 查看报告详情弹窗
const viewDialogVisible = ref(false)
const viewReportData = ref<any>({})
const chartRef = ref<HTMLElement>()
let chartInstance: any = null

// 表格内操作按钮
const onTableClickButton = ({btn, row, index}: any) => {
	if (btn.code === 'details') {
		viewReportData.value = row
		viewDialogVisible.value = true
	}
	if (btn.code === 'edit') {
		openAddDialog(1, row, index)
	}
	if (btn.code === 'delete') {
		ElMessageBox.confirm(`确定要删除报告"${row.reportName}"吗？`, '删除确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
		.then(() => {
			tableData.value.splice(index, 1)
			deepData.value.splice(index, 1)
			ElMessage.success('删除成功')
		})
		.catch(() => {})
	}
	if (btn.code === 'download') {
		if (row.status === '生成中') {
			ElMessage.warning('报告生成中，请稍后下载')
			return
		}
		ElMessage.success(`下载报告：${row.reportName}`)
	}
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
	if (type === 'page') {
		pagination.value.page = val
		searchParams.value.skipCount = (val - 1) * pagination.value.size
	} else {
		pagination.value.page = 1
		pagination.value.size = val
		searchParams.value.maxResultCount = pagination.value.size
		searchParams.value.skipCount = 0
	}
}

// 批量导出
const batchExport = () => {
	if (sheets.value.length === 0) {
		return ElMessage.warning('请选择要导出的报告')
	}
	exportExcel(sheets.value, [
		{field: 'reportName', title: '报告名称'},
		{field: 'permissionName', title: '权限名称'},
		{field: 'creator', title: '创建人'},
		{field: 'createTime', title: '创建时间'},
		{field: 'status', title: '状态'},
	])
	ElMessage.success('导出成功')
}

// 全部导出
const allExport = () => {
	exportExcel(tableData.value, [
		{field: 'reportName', title: '报告名称'},
		{field: 'permissionName', title: '权限名称'},
		{field: 'creator', title: '创建人'},
		{field: 'createTime', title: '创建时间'},
		{field: 'status', title: '状态'},
	])
	ElMessage.success('导出成功')
}

// 新增报告弹窗相关
const addDialogVisible = ref(false)
const addDialogTitle = ref('')
const editIndex = ref<any>(null)
const addLoading = ref(false)

const addForm = ref<any>({
	reportName: '',
	permissionName: '',
	timeRange: '',
	customStartTime: '',
	customEndTime: '',
	description: '',
})

const timeRangeOptions = [
	{label: '本周', value: '本周'},
	{label: '本月', value: '本月'},
	{label: '本季度', value: '本季度'},
	{label: '本年度', value: '本年度'},
	{label: '自定义', value: '自定义'},
]

// type 1编辑 2查看 3新增
const openAddDialog = (type: number, row?: any, index?: number) => {
	if (type === 1) {
		addDialogTitle.value = '编辑权限运用报告'
		addForm.value = {
			reportName: row.reportName || '',
			permissionName: row.permissionName || '',
			timeRange: row.timeRange || '',
			customStartTime: row.customStartTime || '',
			customEndTime: row.customEndTime || '',
			description: row.description || '',
		}
		editIndex.value = index
	} else if (type === 2) {
		addDialogTitle.value = '查看权限运用报告'
		addForm.value = {
			reportName: row.reportName || '',
			permissionName: row.permissionName || '',
			timeRange: row.timeRange || '',
			customStartTime: row.customStartTime || '',
			customEndTime: row.customEndTime || '',
			description: row.description || '',
		}
	} else if (type === 3) {
		addDialogTitle.value = '新增权限运用报告'
		addForm.value = {
			reportName: '',
			permissionName: '',
			timeRange: '',
			customStartTime: '',
			customEndTime: '',
			description: '',
		}
	}
	addDialogVisible.value = true
}

const onAddClose = () => {
	addForm.value = {
		reportName: '',
		permissionName: '',
		timeRange: '',
		customStartTime: '',
		customEndTime: '',
		description: '',
	}
	addDialogVisible.value = false
	editIndex.value = null
}

const onAddConfirm = () => {
	if (!addForm.value.reportName) {
		return ElMessage.warning('请输入报告名称')
	}
	if (!addForm.value.permissionName) {
		return ElMessage.warning('请选择报告权限')
	}
	if (!addForm.value.timeRange) {
		return ElMessage.warning('请选择时间范围')
	}
	if (addForm.value.timeRange === '自定义' && (!addForm.value.customStartTime || !addForm.value.customEndTime)) {
		return ElMessage.warning('请选择自定义时间范围')
	}

	addLoading.value = true
	useSleep().then(() => {
		addLoading.value = false

		if (addDialogTitle.value === '编辑权限运用报告') {
			// 编辑
			const updatedReport = {
				...tableData.value[editIndex.value],
				reportName: addForm.value.reportName,
				permissionName: addForm.value.permissionName,
				timeRange: addForm.value.timeRange,
				customStartTime: addForm.value.customStartTime,
				customEndTime: addForm.value.customEndTime,
				description: addForm.value.description,
				createTime: getCurrentTimeFormatted()
			}
			tableData.value[editIndex.value] = updatedReport
			deepData.value[editIndex.value] = updatedReport
			ElMessage.success('编辑成功')
		} else if (addDialogTitle.value === '新增权限运用报告') {
			// 新增
			const newReport: PermissionUsageReportItem = {
				id: Date.now(),
				reportName: addForm.value.reportName,
				permissionName: addForm.value.permissionName,
				creator: userInfo.name,
				createTime: getCurrentTimeFormatted(),
				status: '生成中',
				timeRange: addForm.value.timeRange,
				customStartTime: addForm.value.customStartTime,
				customEndTime: addForm.value.customEndTime,
				description: addForm.value.description,
			}

			tableData.value.unshift(newReport)
			deepData.value.unshift(newReport)

			// 模拟报告生成过程
			setTimeout(() => {
				newReport.status = '已生成'
				ElMessage.success('报告生成完成')
			}, 3000)

			ElMessage.success('报告创建成功，正在生成中...')
		}

		onAddClose()
	})
}

// 批量删除
const batchDelete = () => {
	if (sheets.value.length === 0) {
		return ElMessage.warning('请选择要删除的报告')
	}
	ElMessageBox.confirm(`确定要删除选中的 ${sheets.value.length} 个报告吗？`, '批量删除确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
	.then(() => {
		const ids = sheets.value.map((item: any) => item.id)
		const remainingData = tableData.value.filter((item: any) => !ids.includes(item.id))
		const remainingDeepData = deepData.value.filter((item: any) => !ids.includes(item.id))

		tableData.value = remainingData
		deepData.value = remainingDeepData
		sheets.value = []

		ElMessage.success(`成功删除 ${ids.length} 个报告`)
	})
	.catch(() => {})
}

// 初始化图表
const initChart = () => {
	nextTick(() => {
		if (chartRef.value && !chartInstance) {
			chartInstance = echarts.init(chartRef.value)

			const option = {
				title: {
					text: '权限使用趋势',
					left: 'center',
					textStyle: {
						fontSize: 16,
						fontWeight: 'normal'
					}
				},
				xAxis: {
					type: 'category',
					data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
					axisLine: {
						lineStyle: {
							color: '#ddd'
						}
					},
					axisTick: {
						show: false
					}
				},
				yAxis: {
					type: 'value',
					min: 0,
					max: 1000,
					interval: 200,
					axisLine: {
						show: false
					},
					axisTick: {
						show: false
					},
					splitLine: {
						lineStyle: {
							color: '#f0f0f0'
						}
					}
				},
				series: [{
					data: [200, 400, 300, 600, 200, 800, 450, 650, 500, 200, 750, 600],
					type: 'line',
					smooth: true,
					lineStyle: {
						color: '#4A90E2',
						width: 2
					},
					itemStyle: {
						color: '#4A90E2'
					},
					symbol: 'circle',
					symbolSize: 6
				}],
				grid: {
					left: '10%',
					right: '10%',
					bottom: '15%',
					top: '20%'
				}
			}

			chartInstance.setOption(option)
		}
	})
}

// 监听查看弹窗的打开
watch(viewDialogVisible, (newVal) => {
	if (newVal) {
		initChart()
	} else {
		if (chartInstance) {
			chartInstance.dispose()
			chartInstance = null
		}
	}
})

// 生成新报告
const generateReport = () => {
	openAddDialog(3)
}
</script>

<template>
	<Dialog
		v-bind="$attrs"
		:enable-confirm="true"
		:destroy-on-close="true"
		:loading="loading"
		loading-text="正在获取数据"
		width="1100"
		@open="onOpen"
		@click-close="onClose"
		@close="onClose"
		@clickConfirm="onConfirm"
	>
		<el-form :model="searchParams" label-width="auto" :inline="true" ref="formRef">
			<el-form-item
				:label="item.label"
				v-for="(item, index) of formProps"
				:key="index"
				:prop="item.field"
			>
				<el-input
					v-if="item.type === 'text'"
					v-model="searchParams[item.field]"
					:placeholder="`请输入${item.label}`"
				/>
				<!-- 下拉选择 -->
				<el-select
					v-else-if="item.type === 'select'"
					clearable
					v-model="searchParams[item.field]"
					size="default"
					style="width: 200px"
					:filterable="true"
					:placeholder="`请选择${item.label}`"
				>
					<el-option
						v-for="selectItem of item.options"
						:label="selectItem.label"
						:value="selectItem.value"
					/>
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="handlSearch">搜索</el-button>
			</el-form-item>
		</el-form>
		
		<div style="display: flex; align-items: center; margin-bottom: 10px">
			<el-button size="small" type="primary" @click="generateReport">
				新增报告
			</el-button>
			<el-dropdown class="mg-left-10 mg-right-10">
				<el-button size="small" type="primary"> 导出报告 </el-button>
				<template #dropdown>
					<el-dropdown-menu>
						<el-dropdown-item @click="allExport">导出全部</el-dropdown-item>
						<el-dropdown-item @click="batchExport">导出选择</el-dropdown-item>
					</el-dropdown-menu>
				</template>
			</el-dropdown>
			<el-button size="small" type="danger" @click="batchDelete">
				批量删除
			</el-button>
		</div>
		
		<!-- 表格 -->
		<TableV2
			v-model="tableData"
			:enable-selection="true"
			:auto-height="true"
			:max-height="400"
			:columns="columns"
			:buttons="buttons"
			@clickButton="onTableClickButton"
			@selection-change="handleSelectionChange"
			ref="reportTableRef"
			class="mg-top-5"
		>
			<template #status="scope">
				<el-tag :type="scope.row.status === '已生成' ? 'success' : 'warning'">
					{{ scope.row.status }}
				</el-tag>
			</template>
		</TableV2>
		
		<Pagination
			:total="pagination.total"
			:page-size="pagination.size"
			:current-page="pagination.page"
			@current-change="onPaginationChange($event, 'page')"
			@size-change="onPaginationChange($event, 'size')"
		/>

		<!-- 新增/编辑报告弹窗 -->
		<Dialog
			v-model="addDialogVisible"
			:title="addDialogTitle"
			:enable-confirm="true"
			:loading="addLoading"
			:destroy-on-close="true"
			loading-text="正在处理"
			width="600"
			@click-close="onAddClose"
			@close="onAddClose"
			@clickConfirm="onAddConfirm"
		>
			<el-form
				:model="addForm"
				label-width="auto"
				:disabled="addDialogTitle === '查看权限运用报告'"
			>
				<el-form-item label="报告名称" required>
					<el-input
						v-model="addForm.reportName"
						placeholder="请输入报告名称"
						style="width: 100%"
					/>
				</el-form-item>

				<el-form-item label="报告权限" required>
					<el-select
						v-model="addForm.permissionName"
						placeholder="请选择报告权限"
						style="width: 100%"
					>
						<el-option label="数据权限" value="数据权限" />
						<el-option label="删除权限" value="删除权限" />
					</el-select>
				</el-form-item>

				<el-form-item label="时间范围" required>
					<el-select
						v-model="addForm.timeRange"
						placeholder="请选择时间范围"
						style="width: 100%"
					>
						<el-option
							v-for="option in timeRangeOptions"
							:key="option.value"
							:label="option.label"
							:value="option.value"
						/>
					</el-select>
				</el-form-item>

				<el-form-item
					v-if="addForm.timeRange === '自定义'"
					label="自定义时间"
					required
				>
					<div style="display: flex; align-items: center; width: 100%">
						<el-date-picker
							v-model="addForm.customStartTime"
							type="date"
							placeholder="开始时间"
							format="YYYY-MM-DD"
							value-format="YYYY-MM-DD"
							style="flex: 1"
						/>
						<span style="margin: 0 10px">至</span>
						<el-date-picker
							v-model="addForm.customEndTime"
							type="date"
							placeholder="结束时间"
							format="YYYY-MM-DD"
							value-format="YYYY-MM-DD"
							style="flex: 1"
						/>
					</div>
				</el-form-item>

				<el-form-item label="报告描述">
					<el-input
						v-model="addForm.description"
						type="textarea"
						:rows="3"
						placeholder="请输入报告描述"
						style="width: 100%"
					/>
				</el-form-item>
			</el-form>
		</Dialog>

		<!-- 查看报告详情弹窗 -->
		<Dialog
			v-model="viewDialogVisible"
			title="权限运用报告"
			:enable-confirm="true"
			:destroy-on-close="true"
			width="800"
			@click-close="viewDialogVisible = false"
			@close="viewDialogVisible = false"
			@clickConfirm="viewDialogVisible = false"
		>
			<div class="report-detail">
				<!-- 报告基本信息 -->
				<div class="report-header">
					<div class="report-title">数据权限运用报告</div>
					<div class="report-info">
						<div class="info-item">
							<span>生成时间：</span>
							<span>{{ viewReportData.createTime || '2023-09-29 09:23' }}</span>
						</div>
						<div class="info-item">
							<span>打印时间：</span>
							<span>{{ getCurrentTimeFormatted() }}</span>
						</div>
					</div>
				</div>

				<!-- 报告描述 -->
				<div class="report-section">
					<div class="section-title">报告描述</div>
					<div class="section-content">
						{{ viewReportData.description || '这是数据权限运用情况的详细统计报告，包含了权限分配、使用频率、用户活跃度等关键指标的分析。通过本报告可以全面了解系统权限的运用状况，为权限管理优化提供数据支撑。报告涵盖了指定时间范围内的所有权限操作记录，并进行了深度分析和可视化展示。' }}
					</div>
				</div>

				<!-- 权限概况 -->
				<div class="report-section">
					<div class="section-title">权限概况</div>
					<div class="stats-container">
						<div class="stat-item">
							<div class="stat-label">授权用户</div>
							<div class="stat-value">649</div>
						</div>
						<div class="stat-item">
							<div class="stat-label">授权人数</div>
							<div class="stat-value">245</div>
						</div>
						<div class="stat-item">
							<div class="stat-label">授权角色</div>
							<div class="stat-value">3个</div>
						</div>
					</div>
				</div>

				<!-- 权限使用趋势 -->
				<div class="report-section">
					<div class="section-title">权限使用趋势</div>
					<div class="chart-container">
						<div ref="chartRef" style="width: 100%; height: 300px;"></div>
					</div>
				</div>
			</div>
		</Dialog>
	</Dialog>
</template>

<style scoped lang="scss">
.mg-left-10 {
	margin-left: 10px;
}
.mg-right-10 {
	margin-right: 10px;
}
.mg-top-5 {
	margin-top: 5px;
}

.report-detail {
	.report-header {
		text-align: center;
		margin-bottom: 30px;

		.report-title {
			font-size: 20px;
			font-weight: bold;
			margin-bottom: 20px;
		}

		.report-info {
			display: flex;
			justify-content: space-between;
			font-size: 14px;
			color: #666;

			.info-item {
				span:first-child {
					font-weight: 500;
				}
			}
		}
	}

	.report-section {
		margin-bottom: 30px;

		.section-title {
			font-size: 16px;
			font-weight: bold;
			margin-bottom: 15px;
			color: #333;
		}

		.section-content {
			font-size: 14px;
			line-height: 1.6;
			color: #666;
			text-align: justify;
		}

		.stats-container {
			display: flex;
			justify-content: space-around;
			text-align: center;

			.stat-item {
				.stat-label {
					font-size: 14px;
					color: #666;
					margin-bottom: 8px;
				}

				.stat-value {
					font-size: 24px;
					font-weight: bold;
					color: #333;
				}
			}
		}

		.chart-container {
			border: 1px solid #eee;
			border-radius: 4px;
			padding: 20px;
		}
	}
}
</style>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { View, Delete, Download, Plus } from '@element-plus/icons-vue'

// 权限应用报告数据接口
interface PermissionApplicationReportItem {
	id: number
	reportName: string
	reportPermission: string
	status: '已生成' | '生成中'
	createTime: string
	reportDescription?: string
	permissionOverview?: string
	permissionReport?: string
}

// 新增表单接口
interface AddReportForm {
	reportName: string
	reportDescription: string
	reportPermission: string
}

// 查看报告接口
interface ViewReportData {
	reportName: string
	reportPermission: string
	status: string
	createTime: string
	reportDescription: string
	permissionOverview: string
	permissionReport: string
}

// Props
interface Props {
	modelValue: boolean
	title?: string
	width?: string
}

const props = withDefaults(defineProps<Props>(), {
	title: '权限应用报告',
	width: '1200px'
})

const emit = defineEmits<{
	'update:modelValue': [value: boolean]
}>()

// 对话框显示控制
const dialogVisible = computed({
	get: () => props.modelValue,
	set: (value) => emit('update:modelValue', value)
})

// 报告权限选项
const permissionOptions = [
	{ label: '数据查看权限', value: 'data_view' },
	{ label: '数据编辑权限', value: 'data_edit' },
	{ label: '数据删除权限', value: 'data_delete' },
	{ label: '用户管理权限', value: 'user_manage' },
	{ label: '系统配置权限', value: 'system_config' },
	{ label: '报表生成权限', value: 'report_generate' },
	{ label: '审核权限', value: 'audit_permission' },
	{ label: '财务权限', value: 'finance_permission' },
	{ label: '人事权限', value: 'hr_permission' },
	{ label: '项目管理权限', value: 'project_manage' }
]

// 报告数据
const reportData = ref<PermissionApplicationReportItem[]>([
	{
		id: 1,
		reportName: '2024年第一季度数据查看权限应用报告',
		reportPermission: '数据查看权限',
		status: '已生成',
		createTime: '2024-01-15 10:30:00',
		reportDescription: '分析2024年第一季度数据查看权限的使用情况，包括访问频次、用户分布、数据类型等维度的统计分析。',
		permissionOverview: '数据查看权限覆盖全公司15个部门，共计320名用户拥有此权限。权限使用率达到85%，日均访问次数约1200次。主要应用于业务数据查询、报表查看和数据分析等场景。',
		permissionReport: '本季度数据查看权限使用情况良好，用户活跃度高。发现部分用户存在重复查询相同数据的情况，建议优化缓存机制。同时，夜间访问量较低，可考虑在非工作时间进行系统维护。整体权限使用合规，未发现异常访问行为。'
	},
	{
		id: 2,
		reportName: '用户管理权限月度应用分析',
		reportPermission: '用户管理权限',
		status: '生成中',
		createTime: '2024-01-14 14:20:00',
		reportDescription: '对用户管理权限在过去一个月的应用情况进行深度分析，包括权限分配、使用效率、安全风险等方面。',
		permissionOverview: '用户管理权限主要分配给HR部门和IT部门的管理人员，共计25名用户。权限涵盖用户创建、修改、删除和权限分配等功能。',
		permissionReport: '本月用户管理权限使用频次适中，主要集中在新员工入职和离职处理。建议加强权限使用的审计机制，确保操作的合规性。'
	},
	{
		id: 3,
		reportName: '财务权限安全应用评估报告',
		reportPermission: '财务权限',
		status: '已生成',
		createTime: '2024-01-13 09:15:00',
		reportDescription: '评估财务权限的安全应用情况，分析权限使用的合规性、风险点和改进建议。',
		permissionOverview: '财务权限严格限制在财务部门内部，共计12名用户拥有不同级别的财务权限。包括财务数据查看、报表生成、审批流程等功能。',
		permissionReport: '财务权限使用情况整体良好，所有操作均有完整的审计日志。建议定期进行权限回顾，确保权限分配的最小化原则。发现部分临时权限未及时回收，需要建立自动回收机制。'
	},
	{
		id: 4,
		reportName: '系统配置权限年度总结报告',
		reportPermission: '系统配置权限',
		status: '已生成',
		createTime: '2024-01-12 16:45:00',
		reportDescription: '总结系统配置权限在过去一年的应用情况，包括配置变更记录、权限使用统计、系统稳定性影响等。'
	},
	{
		id: 5,
		reportName: '报表生成权限效率分析',
		reportPermission: '报表生成权限',
		status: '生成中',
		createTime: '2024-01-11 11:30:00',
		reportDescription: '分析报表生成权限的使用效率，包括报表类型分布、生成频次、用户满意度等指标。'
	},
	{
		id: 6,
		reportName: '审核权限流程优化报告',
		reportPermission: '审核权限',
		status: '已生成',
		createTime: '2024-01-10 13:20:00',
		reportDescription: '基于审核权限的使用数据，分析审核流程的效率和瓶颈，提出优化建议。'
	},
	{
		id: 7,
		reportName: '数据编辑权限风险评估',
		reportPermission: '数据编辑权限',
		status: '已生成',
		createTime: '2024-01-09 15:10:00',
		reportDescription: '评估数据编辑权限的使用风险，包括误操作统计、数据完整性影响、权限滥用检测等。'
	},
	{
		id: 8,
		reportName: '人事权限合规性检查报告',
		reportPermission: '人事权限',
		status: '生成中',
		createTime: '2024-01-08 10:00:00',
		reportDescription: '检查人事权限使用的合规性，确保符合相关法规和公司政策要求。'
	},
	{
		id: 9,
		reportName: '项目管理权限协作效果分析',
		reportPermission: '项目管理权限',
		status: '已生成',
		createTime: '2024-01-07 14:30:00',
		reportDescription: '分析项目管理权限对团队协作效果的影响，包括项目进度、资源配置、沟通效率等方面。'
	},
	{
		id: 10,
		reportName: '数据删除权限安全监控报告',
		reportPermission: '数据删除权限',
		status: '已生成',
		createTime: '2024-01-06 09:45:00',
		reportDescription: '监控数据删除权限的使用情况，确保删除操作的安全性和可追溯性。'
	},
	{
		id: 11,
		reportName: '权限应用综合分析报告',
		reportPermission: '系统配置权限',
		status: '生成中',
		createTime: '2024-01-05 16:20:00',
		reportDescription: '综合分析各类权限的应用情况，提供整体的权限管理优化建议。'
	},
	{
		id: 12,
		reportName: '临时权限使用效果评估',
		reportPermission: '财务权限',
		status: '已生成',
		createTime: '2024-01-04 11:15:00',
		reportDescription: '评估临时权限的使用效果，分析临时授权的必要性和安全性。'
	}
])

// 筛选表单
const filterForm = ref({
	reportName: ''
})

// 筛选后的数据
const filteredData = computed(() => {
	if (!filterForm.value.reportName.trim()) {
		return reportData.value
	}
	return reportData.value.filter(item => 
		item.reportName.includes(filterForm.value.reportName.trim())
	)
})

// 重置筛选
const resetFilter = () => {
	filterForm.value.reportName = ''
	pagination.page = 1
}

// 表格列配置
const columns = [
	{ prop: 'reportName', label: '报告名称' },
	{ prop: 'reportPermission', label: '报告权限' },
	{ prop: 'status', label: '状态' },
	{ prop: 'createTime', label: '创建时间' },
	{ prop: 'operation', label: '操作', width: '200px' }
]

// 分页配置
const pagination = reactive({
	total: 0,
	size: 10,
	page: 1,
})

// 分页数据
const paginatedData = computed(() => {
	const start = (pagination.page - 1) * pagination.size
	const end = start + pagination.size
	return filteredData.value.slice(start, end)
})

// 分页事件
const onPaginationChange = (value: number, type: string) => {
	if (type === 'page') {
		pagination.page = value
	} else if (type === 'size') {
		pagination.size = value
		pagination.page = 1
	}
}

// 表格选择
const selectedRows = ref<PermissionApplicationReportItem[]>([])
const handleSelectionChange = (selection: PermissionApplicationReportItem[]) => {
	selectedRows.value = selection
}

// 新增对话框
const addDialogVisible = ref(false)
const addForm = ref<AddReportForm>({
	reportName: '',
	reportDescription: '',
	reportPermission: ''
})

// 查看对话框
const viewDialogVisible = ref(false)
const viewData = ref<ViewReportData>({
	reportName: '',
	reportPermission: '',
	status: '',
	createTime: '',
	reportDescription: ''
})

// 新增报告
const handleAdd = () => {
	addForm.value = {
		reportName: '',
		reportDescription: '',
		reportPermission: ''
	}
	addDialogVisible.value = true
}

// 确认新增
const confirmAdd = () => {
	if (!addForm.value.reportName.trim()) {
		ElMessage.warning('请输入报告名称')
		return
	}
	if (!addForm.value.reportDescription.trim()) {
		ElMessage.warning('请输入报告描述')
		return
	}
	if (!addForm.value.reportPermission) {
		ElMessage.warning('请选择报告权限')
		return
	}
	
	const newReport: PermissionApplicationReportItem = {
		id: Date.now(),
		reportName: addForm.value.reportName,
		reportPermission: addForm.value.reportPermission,
		status: '生成中',
		createTime: new Date().toLocaleString('zh-CN', {
			year: 'numeric',
			month: '2-digit',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit'
		}).replace(/\//g, '-'),
		reportDescription: addForm.value.reportDescription
	}
	
	reportData.value.unshift(newReport)
	pagination.total = filteredData.value.length
	ElMessage.success('新增成功，报告正在生成中...')
	addDialogVisible.value = false
	
	// 模拟报告生成过程，3秒后状态变为已生成
	setTimeout(() => {
		const report = reportData.value.find(item => item.id === newReport.id)
		if (report) {
			report.status = '已生成'
			ElMessage.success(`报告"${report.reportName}"生成完成`)
		}
	}, 3000)
}

// 取消新增
const cancelAdd = () => {
	addDialogVisible.value = false
}

// 查看报告
const handleView = (row: PermissionApplicationReportItem) => {
	viewData.value = {
		reportName: row.reportName,
		reportPermission: row.reportPermission,
		status: row.status,
		createTime: row.createTime,
		reportDescription: row.reportDescription || '',
		permissionOverview: row.permissionOverview || '暂无权限概览信息',
		permissionReport: row.permissionReport || '暂无权限报告信息'
	}
	viewDialogVisible.value = true
}

// 下载报告
const handleDownload = (row: PermissionApplicationReportItem) => {
	if (row.status === '生成中') {
		ElMessage.warning('报告正在生成中，请稍后再试')
		return
	}
	
	// 创建下载内容
	const reportContent = {
		reportName: row.reportName,
		reportPermission: row.reportPermission,
		status: row.status,
		createTime: row.createTime,
		reportDescription: row.reportDescription,
		downloadTime: new Date().toLocaleString('zh-CN')
	}
	
	// 创建并下载文件
	const blob = new Blob([JSON.stringify(reportContent, null, 2)], { type: 'application/json' })
	const url = URL.createObjectURL(blob)
	const link = document.createElement('a')
	link.href = url
	link.download = `${row.reportName}.json`
	document.body.appendChild(link)
	link.click()
	document.body.removeChild(link)
	URL.revokeObjectURL(url)
	
	ElMessage.success('下载成功')
}

// 删除报告
const handleDelete = (row: PermissionApplicationReportItem) => {
	ElMessageBox.confirm(
		`确定要删除报告"${row.reportName}"吗？`,
		'确认删除',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}
	)
		.then(() => {
			const index = reportData.value.findIndex(item => item.id === row.id)
			if (index !== -1) {
				reportData.value.splice(index, 1)
				pagination.total = filteredData.value.length
				ElMessage.success('删除成功')
			}
		})
		.catch(() => {})
}

// 删除选中
const handleDeleteSelected = () => {
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请选择要删除的报告')
		return
	}
	
	ElMessageBox.confirm(
		`确定要删除选中的${selectedRows.value.length}条报告吗？`,
		'确认删除',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}
	)
		.then(() => {
			const selectedIds = selectedRows.value.map(row => row.id)
			reportData.value = reportData.value.filter(item => !selectedIds.includes(item.id))
			pagination.total = filteredData.value.length
			selectedRows.value = []
			ElMessage.success('删除成功')
		})
		.catch(() => {})
}

// 删除全部
const handleDeleteAll = () => {
	if (reportData.value.length === 0) {
		ElMessage.warning('没有可删除的报告')
		return
	}
	
	ElMessageBox.confirm(
		`确定要删除全部${reportData.value.length}条报告吗？此操作不可恢复！`,
		'确认删除全部',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}
	)
		.then(() => {
			reportData.value = []
			pagination.total = 0
			selectedRows.value = []
			ElMessage.success('删除成功')
		})
		.catch(() => {})
}

// 下载选中
const handleDownloadSelected = () => {
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请选择要下载的报告')
		return
	}
	
	const generatingReports = selectedRows.value.filter(row => row.status === '生成中')
	if (generatingReports.length > 0) {
		ElMessage.warning(`有${generatingReports.length}个报告正在生成中，无法下载`)
		return
	}
	
	// 创建批量下载内容
	const batchContent = {
		downloadTime: new Date().toLocaleString('zh-CN'),
		totalCount: selectedRows.value.length,
		reports: selectedRows.value.map(row => ({
			reportName: row.reportName,
			reportPermission: row.reportPermission,
			status: row.status,
			createTime: row.createTime,
			reportDescription: row.reportDescription
		}))
	}
	
	// 创建并下载文件
	const blob = new Blob([JSON.stringify(batchContent, null, 2)], { type: 'application/json' })
	const url = URL.createObjectURL(blob)
	const link = document.createElement('a')
	link.href = url
	link.download = `权限应用报告批量下载_${new Date().toISOString().slice(0, 10)}.json`
	document.body.appendChild(link)
	link.click()
	document.body.removeChild(link)
	URL.revokeObjectURL(url)
	
	ElMessage.success(`成功下载${selectedRows.value.length}个报告`)
}

// 下载全部
const handleDownloadAll = () => {
	if (reportData.value.length === 0) {
		ElMessage.warning('没有可下载的报告')
		return
	}
	
	const generatingReports = reportData.value.filter(row => row.status === '生成中')
	if (generatingReports.length > 0) {
		ElMessage.warning(`有${generatingReports.length}个报告正在生成中，将跳过这些报告`)
	}
	
	const downloadableReports = reportData.value.filter(row => row.status === '已生成')
	if (downloadableReports.length === 0) {
		ElMessage.warning('没有可下载的报告')
		return
	}
	
	// 创建全部下载内容
	const allContent = {
		downloadTime: new Date().toLocaleString('zh-CN'),
		totalCount: downloadableReports.length,
		reports: downloadableReports.map(row => ({
			reportName: row.reportName,
			reportPermission: row.reportPermission,
			status: row.status,
			createTime: row.createTime,
			reportDescription: row.reportDescription
		}))
	}
	
	// 创建并下载文件
	const blob = new Blob([JSON.stringify(allContent, null, 2)], { type: 'application/json' })
	const url = URL.createObjectURL(blob)
	const link = document.createElement('a')
	link.href = url
	link.download = `权限应用报告全部下载_${new Date().toISOString().slice(0, 10)}.json`
	document.body.appendChild(link)
	link.click()
	document.body.removeChild(link)
	URL.revokeObjectURL(url)
	
	ElMessage.success(`成功下载${downloadableReports.length}个报告`)
}

// 监听对话框打开
watch(() => props.modelValue, (newValue) => {
	if (newValue) {
		// 对话框打开时初始化分页
		pagination.total = filteredData.value.length
		pagination.page = 1
	}
})

// 监听筛选条件变化
watch(() => filterForm.value.reportName, () => {
	pagination.total = filteredData.value.length
	pagination.page = 1
})
</script>

<template>
	<Dialog
		v-model="dialogVisible"
		:title="title"
		:width="width"
		:destroy-on-close="true"
	>
		<!-- 筛选条件 -->
		<div style="margin-bottom: 16px; padding: 16px; background-color: #f5f7fa; border-radius: 4px;">
			<el-row :gutter="16" align="middle">
				<el-col :span="3">
					<span style="font-weight: 500;">报告名称：</span>
				</el-col>
				<el-col :span="8">
					<el-input
						v-model="filterForm.reportName"
						placeholder="请输入报告名称"
						clearable
						style="width: 100%;"
					/>
				</el-col>
				<el-col :span="3">
					<el-button @click="resetFilter">重置</el-button>
				</el-col>
			</el-row>
		</div>
		
		<!-- 操作按钮 -->
		<div style="margin-bottom: 16px;">
			<el-button type="primary" :icon="Plus" @click="handleAdd">新增</el-button>
			<el-button type="danger" @click="handleDeleteSelected">删除选中</el-button>
			<el-button type="danger" @click="handleDeleteAll">删除全部</el-button>
			<el-button type="success" :icon="Download" @click="handleDownloadSelected">下载选中</el-button>
			<el-button type="success" :icon="Download" @click="handleDownloadAll">下载全部</el-button>
		</div>
		
		<!-- 报告列表表格 -->
		<TableV2
			:defaultTableData="paginatedData"
			:auto-height="true"
			:max-height="400"
			:columns="columns"
			:enable-toolbar="false"
			:enable-create="false"
			:enable-edit="false"
			:enable-delete="false"
			:enable-index="true"
			:enable-selection="true"
			@selection-change="handleSelectionChange"
			class="mg-top-5"
		>
			<template #status="{ row }">
				<el-tag :type="row.status === '已生成' ? 'success' : 'warning'">
					{{ row.status }}
				</el-tag>
			</template>
			<template #operation="{ row }">
				<el-button size="small" type="primary" @click="handleView(row)" :icon="View">查看</el-button>
				<el-button size="small" type="success" @click="handleDownload(row)" :icon="Download">下载</el-button>
				<el-button size="small" type="danger" @click="handleDelete(row)" :icon="Delete">删除</el-button>
			</template>
		</TableV2>
		
		<!-- 分页 -->
		<Pagination
			:total="pagination.total"
			:page-size="pagination.size"
			:current-page="pagination.page"
			@current-change="onPaginationChange($event, 'page')"
			@size-change="onPaginationChange($event, 'size')"
		/>

		<!-- 新增报告对话框 -->
		<el-dialog
			v-model="addDialogVisible"
			title="新增权限应用报告"
			width="600px"
			:close-on-click-modal="false"
		>
			<el-form :model="addForm" label-width="100px">
				<el-form-item label="报告名称" required>
					<el-input
						v-model="addForm.reportName"
						placeholder="请输入报告名称"
						clearable
					/>
				</el-form-item>
				<el-form-item label="报告描述" required>
					<el-input
						v-model="addForm.reportDescription"
						type="textarea"
						:rows="4"
						placeholder="请输入报告描述"
						clearable
					/>
				</el-form-item>
				<el-form-item label="报告权限" required>
					<el-select
						v-model="addForm.reportPermission"
						placeholder="请选择报告权限"
						style="width: 100%;"
						clearable
					>
						<el-option
							v-for="option in permissionOptions"
							:key="option.value"
							:label="option.label"
							:value="option.label"
						/>
					</el-select>
				</el-form-item>
			</el-form>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancelAdd">取消</el-button>
					<el-button type="primary" @click="confirmAdd">确定</el-button>
				</span>
			</template>
		</el-dialog>

		<!-- 查看报告对话框 -->
		<el-dialog
			v-model="viewDialogVisible"
			title="查看权限应用报告"
			width="700px"
			:close-on-click-modal="false"
		>
			<el-descriptions :column="1" border>
				<el-descriptions-item label="报告名称">{{ viewData.reportName }}</el-descriptions-item>
				<el-descriptions-item label="报告权限">{{ viewData.reportPermission }}</el-descriptions-item>
				<el-descriptions-item label="状态">
					<el-tag :type="viewData.status === '已生成' ? 'success' : 'warning'">
						{{ viewData.status }}
					</el-tag>
				</el-descriptions-item>
				<el-descriptions-item label="创建时间">{{ viewData.createTime }}</el-descriptions-item>
				<el-descriptions-item label="报告描述">
					<p style="line-height: 1.6; margin: 0;">{{ viewData.reportDescription }}</p>
				</el-descriptions-item>
				<el-descriptions-item label="权限概览">
					<p style="line-height: 1.6; margin: 0; color: #606266;">{{ viewData.permissionOverview }}</p>
				</el-descriptions-item>
				<el-descriptions-item label="权限报告">
					<p style="line-height: 1.6; margin: 0; color: #606266;">{{ viewData.permissionReport }}</p>
				</el-descriptions-item>
			</el-descriptions>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="viewDialogVisible = false">关闭</el-button>
				</span>
			</template>
		</el-dialog>
	</Dialog>
</template>

<style scoped lang="scss">
// 样式可以根据需要添加
</style>

# 进度追踪功能模块

## 功能概述

本模块实现了完整的进度追踪功能，包括主页面、详情页面、各种弹窗组件和数据管理功能。

## 目录结构

```
src/views/index/progressTracking/
├── index.vue                          # 主页面
├── detail/
│   └── [id].vue                      # 详情页面（动态路由）
├── ruleManagement/
│   └── index.vue                     # 规则管理页面
├── components/
│   ├── SubTaskDialog.vue             # 子任务弹窗
│   ├── SendReminderDialog.vue        # 发送提醒弹窗
│   ├── ViewReminderDialog.vue        # 查看提醒弹窗
│   ├── DetailDataDialog.vue          # 明细数据弹窗
│   ├── ProgressCalculationDialog.vue # 进度计算方式弹窗
│   └── RuleFormDialog.vue            # 规则表单弹窗
├── composables/
│   ├── useProgressTrackingStore.ts   # 数据存储管理
│   └── useIndexedDB.ts               # IndexedDB 工具类
└── README.md                         # 说明文档
```

## 主要功能

### 1. 进度追踪主页面 (index.vue)

**功能特性：**
- 查询条件：任务名称、所属板块、优先级、牵头领导、责任人
- 操作按钮：新增、批量删除、批量导出、批量导入、规则管理
- 数据列表：支持多选、排序、分页
- 更多操作：发送提醒、查看提醒、清空提醒
- 任务名称点击跳转到详情页面

**技术实现：**
- 使用 Element Plus 组件库
- 支持 Excel 导入导出功能
- 使用 localStorage 进行数据缓存
- 响应式设计，适配不同屏幕尺寸

### 2. 进度追踪详情页面 (detail/[id].vue)

**功能特性：**
- 任务基本信息展示
- 业务表数据列表
- 子任务查看
- 提醒管理
- 明细数据查看
- 进度计算方式配置

**技术实现：**
- 动态路由参数获取
- 多个弹窗组件集成
- 数据统计和可视化
- 返回导航功能

### 3. 规则管理页面 (ruleManagement/index.vue)

**功能特性：**
- 统计卡片展示
- 规则列表管理
- 新增/编辑/删除规则
- 规则状态管理

**技术实现：**
- 卡片式布局
- 表格数据管理
- 表单验证
- 状态切换

### 4. 弹窗组件

#### 4.1 子任务弹窗 (SubTaskDialog.vue)
- 子任务列表展示
- 搜索和筛选功能
- 分页支持

#### 4.2 发送提醒弹窗 (SendReminderDialog.vue)
- 提醒内容编辑
- 收件人选择
- 发送时间设置
- 数据保存到 localStorage

#### 4.3 查看提醒弹窗 (ViewReminderDialog.vue)
- 提醒列表展示
- 提醒状态管理
- 批量操作支持
- 新增提醒功能

#### 4.4 明细数据弹窗 (DetailDataDialog.vue)
- 数据统计展示
- 明细数据表格
- 分页功能
- 完成率计算

#### 4.5 进度计算方式弹窗 (ProgressCalculationDialog.vue)
- 计算方式配置
- 当前进度展示
- 历史记录查看
- 可视化进度条

#### 4.6 规则表单弹窗 (RuleFormDialog.vue)
- 规则信息编辑
- 表单验证
- 新增/编辑模式

### 5. 数据管理

#### 5.1 数据存储 (useProgressTrackingStore.ts)
- 统一的数据状态管理
- 增删改查操作
- 数据缓存机制

#### 5.2 IndexedDB 工具 (useIndexedDB.ts)
- 浏览器本地数据库操作
- 异步数据处理
- 错误处理机制

## 技术栈

- **框架：** Vue 3 + TypeScript
- **UI 库：** Element Plus
- **路由：** Vue Router 4
- **状态管理：** Pinia
- **构建工具：** Vite
- **样式：** SCSS
- **数据存储：** localStorage + IndexedDB
- **文件处理：** ExcelJS + file-saver

## 特色功能

1. **Excel 导入导出**
   - 支持模板下载
   - 数据验证和错误提示
   - 批量数据处理

2. **提醒系统**
   - 创建和管理提醒
   - 提醒状态跟踪
   - 批量操作支持

3. **进度可视化**
   - 进度条展示
   - 统计图表
   - 历史数据追踪

4. **响应式设计**
   - 适配不同屏幕尺寸
   - 移动端友好
   - 灵活的布局系统

5. **数据持久化**
   - 本地数据缓存
   - 离线数据支持
   - 数据同步机制

## 使用说明

1. **访问主页面**
   - 路径：`/index/progressTracking`
   - 可进行查询、新增、编辑、删除等操作

2. **查看详情**
   - 点击任务名称跳转到详情页面
   - 路径：`/index/progressTracking/detail/{id}`

3. **规则管理**
   - 点击"规则管理"按钮进入规则管理页面
   - 路径：`/index/progressTracking/ruleManagement`

4. **提醒功能**
   - 通过"更多操作"菜单访问提醒相关功能
   - 支持发送、查看、清空提醒

## 注意事项

1. 数据主要存储在浏览器本地，清除浏览器数据会丢失信息
2. Excel 导入功能支持 .xlsx 和 .xls 格式
3. 文件上传大小限制为 10MB
4. 建议在现代浏览器中使用以获得最佳体验

## 后续扩展

1. 可以集成后端 API 进行数据同步
2. 可以添加更多的数据可视化图表
3. 可以扩展更多的提醒方式（邮件、短信等）
4. 可以添加权限管理功能
5. 可以优化移动端体验

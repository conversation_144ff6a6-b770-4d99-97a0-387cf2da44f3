import { ref } from 'vue'

// IndexedDB 数据库名称和版本
const DB_NAME = 'ProgressTrackingDB'
const DB_VERSION = 3

// 对象存储名称
const STORES = {
  PROGRESS_TRACKING: 'progressTracking',
  SUB_TASKS: 'subTasks',
  RULES: 'rules'
}

// 数据库实例
let db: IDBDatabase | null = null

// 初始化数据库
const initDB = (): Promise<IDBDatabase> => {
  return new Promise((resolve, reject) => {
    if (db) {
      resolve(db)
      return
    }

    const request = indexedDB.open(DB_NAME, DB_VERSION)

    request.onerror = () => {
      reject(new Error('Failed to open database'))
    }

    request.onsuccess = () => {
      db = request.result
      resolve(db)
    }

    request.onupgradeneeded = (event) => {
      const database = (event.target as IDBOpenDBRequest).result

      // 创建进度追踪表
      if (!database.objectStoreNames.contains(STORES.PROGRESS_TRACKING)) {
        const progressStore = database.createObjectStore(STORES.PROGRESS_TRACKING, {
          keyPath: 'id',
          autoIncrement: true
        })
        progressStore.createIndex('taskName', 'taskName', { unique: false })
        progressStore.createIndex('sector', 'sector', { unique: false })
        progressStore.createIndex('priority', 'priority', { unique: false })
      }

      // 创建子任务表
      if (!database.objectStoreNames.contains(STORES.SUB_TASKS)) {
        const subTaskStore = database.createObjectStore(STORES.SUB_TASKS, {
          keyPath: 'id',
          autoIncrement: true
        })
        subTaskStore.createIndex('parentTaskId', 'parentTaskId', { unique: false })
        subTaskStore.createIndex('taskName', 'taskName', { unique: false })
      }

      // 创建规则表
      if (!database.objectStoreNames.contains(STORES.RULES)) {
        const ruleStore = database.createObjectStore(STORES.RULES, {
          keyPath: 'id',
          autoIncrement: true
        })
        ruleStore.createIndex('ruleName', 'ruleName', { unique: false })
        ruleStore.createIndex('status', 'status', { unique: false })
        ruleStore.createIndex('priority', 'priority', { unique: false })
      }
    }
  })
}

// 生成模拟数据
const generateMockData = async () => {
  const database = await initDB()

  // 检查是否已有数据
  const transaction = database.transaction([STORES.PROGRESS_TRACKING], 'readonly')
  const store = transaction.objectStore(STORES.PROGRESS_TRACKING)
  const countRequest = store.count()

  countRequest.onsuccess = () => {
    if (countRequest.result === 0) {
      // 插入模拟数据
      insertMockProgressTrackingData()
      insertMockSubTaskData()
      insertMockRuleData()
    }
  }
}

// 插入模拟进度追踪数据
const insertMockProgressTrackingData = async () => {
  const database = await initDB()
  const transaction = database.transaction([STORES.PROGRESS_TRACKING], 'readwrite')
  const store = transaction.objectStore(STORES.PROGRESS_TRACKING)

  const mockData = [
    {
      taskName: '建立村级便民服务',
      taskType: '数据报表',
      sector: '党的建设',
      leadLeader: '陈站',
      responsible: '陈站',
      priority: '高',
      progress: 25,
      remainingWork: '剩余各数据报表未完成',
      taskDeadline: '2024-01-01~2024-12-31',
      trackingRule: '重点项目进度规则'
    },
    {
      taskName: '开展重点人群就业帮扶任务',
      taskType: '审批流程',
      sector: '经济发展',
      leadLeader: '刘树林',
      responsible: '刘树林',
      priority: '中',
      progress: 25,
      remainingWork: '剩余各数据报表未完成',
      taskDeadline: '2024-01-01~2024-12-31',
      trackingRule: '日常任务进度规则'
    },
    {
      taskName: '开展就业帮扶技能培训',
      taskType: '数据采集',
      sector: '民生服务',
      leadLeader: '李松',
      responsible: '李松',
      priority: '低',
      progress: 25,
      remainingWork: '剩余各数据报表未完成',
      taskDeadline: '2024-01-01~2024-12-31',
      trackingRule: '年度考核进度规则'
    },
    {
      taskName: '投工作岗位开发业务受理',
      taskType: '数据上报',
      sector: '平安法治',
      leadLeader: '牛振祖、陈站',
      responsible: '牛振祖、陈站',
      priority: '高',
      progress: 25,
      remainingWork: '剩余各数据报表未完成',
      taskDeadline: '2024-01-01~2024-12-31',
      trackingRule: '民生项目进度规则'
    },
    {
      taskName: '开展就业创业帮扶',
      taskType: '分析报告',
      sector: '党的建设',
      leadLeader: '盛泽',
      responsible: '盛泽',
      priority: '中',
      progress: 25,
      remainingWork: '剩余各数据报表未完成',
      taskDeadline: '2024-01-01~2024-12-31',
      trackingRule: '临时任务进度规则'
    },
    {
      taskName: '"送文化下基层"文艺演出服务',
      taskType: '数据报表',
      sector: '经济发展',
      leadLeader: '周梦玲',
      responsible: '周梦玲',
      priority: '低',
      progress: 25,
      remainingWork: '剩余各数据报表未完成',
      taskDeadline: '2024-01-01~2024-12-31',
      trackingRule: '重点项目进度规则'
    },
    {
      taskName: '管理易走失流浪乞讨人员',
      taskType: '审批流程',
      sector: '民生服务',
      leadLeader: '李松',
      responsible: '李松',
      priority: '高',
      progress: 25,
      remainingWork: '剩余各数据报表未完成',
      taskDeadline: '2024-01-01~2024-12-31',
      trackingRule: '日常任务进度规则'
    },
    {
      taskName: '城乡居民养老保险参保登记',
      taskType: '数据采集',
      sector: '平安法治',
      leadLeader: '程锐',
      responsible: '程锐',
      priority: '中',
      progress: 25,
      remainingWork: '剩余各数据报表未完成',
      taskDeadline: '2024-01-01~2024-12-31',
      trackingRule: '年度考核进度规则'
    },
    {
      taskName: '持续开展政府住房保障建设工作',
      taskType: '数据上报',
      sector: '党的建设',
      leadLeader: '张富平',
      responsible: '张富平',
      priority: '低',
      progress: 25,
      remainingWork: '剩余各数据报表未完成',
      taskDeadline: '2024-01-01~2024-12-31',
      trackingRule: '民生项目进度规则'
    },
    {
      taskName: '建立巨额人员业务政策、动员等宣传G...',
      taskType: '分析报告',
      sector: '经济发展',
      leadLeader: '刘军',
      responsible: '刘军',
      priority: '高',
      progress: 25,
      remainingWork: '剩余各数据报表未完成',
      taskDeadline: '2024-01-01~2024-12-31',
      trackingRule: '临时任务进度规则'
    }
  ]

  mockData.forEach(item => {
    store.add(item)
  })
}

// 插入模拟子任务数据
const insertMockSubTaskData = async () => {
  const database = await initDB()
  const transaction = database.transaction([STORES.SUB_TASKS], 'readwrite')
  const store = transaction.objectStore(STORES.SUB_TASKS)

  const mockSubTasks = [
    // 任务1：建立村级便民服务 的子任务
    {
      parentTaskId: 1,
      sequence: '01',
      taskName: '便民服务点选址调研',
      completionTime: '2024-03-31',
      businessService: '选址管理系统',
      progress: 85,
      remainingWork: '最终确认选址方案',
      taskDeadline: '2024-01-01~2024-03-31',
      trackingRule: '重点项目进度规则'
    },
    {
      parentTaskId: 1,
      sequence: '02',
      taskName: '便民服务设施采购',
      completionTime: '2024-05-31',
      businessService: '采购管理系统',
      progress: 60,
      remainingWork: '设备安装调试',
      taskDeadline: '2024-04-01~2024-05-31',
      trackingRule: '重点项目进度规则'
    },
    {
      parentTaskId: 1,
      sequence: '03',
      taskName: '服务人员培训',
      completionTime: '2024-06-30',
      businessService: '培训管理系统',
      progress: 40,
      remainingWork: '实操培训和考核',
      taskDeadline: '2024-06-01~2024-06-30',
      trackingRule: '重点项目进度规则'
    },

    // 任务2：开展重点人群就业帮扶任务 的子任务
    {
      parentTaskId: 2,
      sequence: '01',
      taskName: '重点人群摸底调查',
      completionTime: '2024-02-29',
      businessService: '人员信息系统',
      progress: 95,
      remainingWork: '数据核实完善',
      taskDeadline: '2024-01-01~2024-02-29',
      trackingRule: '日常任务进度规则'
    },
    {
      parentTaskId: 2,
      sequence: '02',
      taskName: '就业技能培训',
      completionTime: '2024-08-31',
      businessService: '培训管理系统',
      progress: 30,
      remainingWork: '培训课程设计和实施',
      taskDeadline: '2024-03-01~2024-08-31',
      trackingRule: '日常任务进度规则'
    },

    // 任务3：开展就业帮扶技能培训 的子任务
    {
      parentTaskId: 3,
      sequence: '01',
      taskName: '培训需求调研',
      completionTime: '2024-04-30',
      businessService: '调研管理系统',
      progress: 70,
      remainingWork: '调研报告编制',
      taskDeadline: '2024-01-01~2024-04-30',
      trackingRule: '年度考核进度规则'
    },
    {
      parentTaskId: 3,
      sequence: '02',
      taskName: '培训师资招募',
      completionTime: '2024-05-31',
      businessService: '人力资源系统',
      progress: 50,
      remainingWork: '师资面试和签约',
      taskDeadline: '2024-05-01~2024-05-31',
      trackingRule: '年度考核进度规则'
    },
    {
      parentTaskId: 3,
      sequence: '03',
      taskName: '培训场地准备',
      completionTime: '2024-06-15',
      businessService: '场地管理系统',
      progress: 20,
      remainingWork: '场地装修和设备配置',
      taskDeadline: '2024-06-01~2024-06-15',
      trackingRule: '年度考核进度规则'
    },

    // 任务4：投工作岗位开发业务受理 的子任务
    {
      parentTaskId: 4,
      sequence: '01',
      taskName: '岗位需求收集',
      completionTime: '2024-03-15',
      businessService: '岗位管理系统',
      progress: 80,
      remainingWork: '需求审核和分类',
      taskDeadline: '2024-01-01~2024-03-15',
      trackingRule: '民生项目进度规则'
    },
    {
      parentTaskId: 4,
      sequence: '02',
      taskName: '岗位信息发布',
      completionTime: '2024-04-30',
      businessService: '信息发布系统',
      progress: 45,
      remainingWork: '多渠道发布和推广',
      taskDeadline: '2024-03-16~2024-04-30',
      trackingRule: '民生项目进度规则'
    },

    // 任务5：开展就业创业帮扶 的子任务
    {
      parentTaskId: 5,
      sequence: '01',
      taskName: '创业政策宣传',
      completionTime: '2024-07-31',
      businessService: '宣传管理系统',
      progress: 35,
      remainingWork: '宣传材料制作和发放',
      taskDeadline: '2024-01-01~2024-07-31',
      trackingRule: '临时任务进度规则'
    },
    {
      parentTaskId: 5,
      sequence: '02',
      taskName: '创业资金扶持',
      completionTime: '2024-09-30',
      businessService: '资金管理系统',
      progress: 15,
      remainingWork: '资金申请审核流程',
      taskDeadline: '2024-08-01~2024-09-30',
      trackingRule: '临时任务进度规则'
    }
  ]

  mockSubTasks.forEach(item => {
    store.add(item)
  })
}

// 插入模拟规则数据
const insertMockRuleData = async () => {
  const database = await initDB()
  const transaction = database.transaction([STORES.RULES], 'readwrite')
  const store = transaction.objectStore(STORES.RULES)

  const mockRules = [
    {
      sequence: '01',
      ruleName: '重点项目进度规则',
      description: '用于重点项目的进度计算与监控',
      status: '启用中',
      priority: '高',
      creator: '盛书华',
      createTime: '2025-1-1',
      updateTime: '2025-1-2',
      ruleType: '项目进度',
      responsible: '盛书华',
      department: '技术部',
      progressFormula: '(已完成任务数 / 总任务数) * 100',
      triggerCondition: '当任务状态变更为"进行中"时开始计算进度'
    },
    {
      sequence: '02',
      ruleName: '日常任务进度规则',
      description: '日常任务的进度跟踪与计算规则',
      status: '启用中',
      priority: '中',
      creator: '盛书华',
      createTime: '2025-1-1',
      updateTime: '2025-1-3',
      ruleType: '任务进度',
      responsible: '盛书华',
      department: '技术部',
      progressFormula: '(已完成工作量 / 总工作量) * 100',
      triggerCondition: '日常任务的进度跟踪规则'
    },
    {
      sequence: '03',
      ruleName: '年度考核进度规则',
      description: '年度考核任务的进度跟踪规则',
      status: '启用中',
      priority: '高',
      creator: '盛书华',
      createTime: '2025-1-1',
      updateTime: '2025-1-4',
      ruleType: '项目进度',
      responsible: '盛书华',
      department: '技术部',
      progressFormula: '(已完成任务数 / 总任务数) * 100',
      triggerCondition: '年度考核任务的进度跟踪规则'
    },
    {
      sequence: '04',
      ruleName: '民生项目进度规则',
      description: '民生项目进度计算规则',
      status: '启用中',
      priority: '低',
      creator: '盛书华',
      createTime: '2025-1-1',
      updateTime: '2025-1-5',
      ruleType: '任务进度',
      responsible: '盛书华',
      department: '技术部',
      progressFormula: '(已完成工作量 / 总工作量) * 100',
      triggerCondition: '民生项目进度计算规则'
    },
    {
      sequence: '05',
      ruleName: '临时任务进度规则',
      description: '临时任务的进度跟踪规则',
      status: '启用中',
      priority: '高',
      creator: '盛书华',
      createTime: '2025-1-1',
      updateTime: '2025-1-6',
      ruleType: '项目进度',
      responsible: '盛书华',
      department: '技术部',
      progressFormula: '(已完成任务数 / 总任务数) * 100',
      triggerCondition: '临时任务的进度跟踪规则'
    },
    {
      sequence: '06',
      ruleName: '文艺演出服务进度规则',
      description: '文艺演出服务的进度跟踪规则',
      status: '已停用',
      priority: '中',
      creator: '盛书华',
      createTime: '2025-1-1',
      updateTime: '2025-1-7',
      ruleType: '任务进度',
      responsible: '盛书华',
      department: '技术部',
      progressFormula: '(已完成工作量 / 总工作量) * 100',
      triggerCondition: '文艺演出服务的进度跟踪规则'
    },
    {
      sequence: '07',
      ruleName: '管理易走失流浪乞讨人员进度规则',
      description: '管理易走失流浪乞讨人员的进度跟踪......',
      status: '已停用',
      priority: '高',
      creator: '盛书华',
      createTime: '2025-1-1',
      updateTime: '2025-1-8',
      ruleType: '项目进度',
      responsible: '盛书华',
      department: '技术部',
      progressFormula: '(已完成任务数 / 总任务数) * 100',
      triggerCondition: '管理易走失流浪乞讨人员的进度跟踪....'
    },
    {
      sequence: '08',
      ruleName: '城乡居民养老保险参保登记进度规则',
      description: '城乡居民养老保险参保登记进度',
      status: '启用中',
      priority: '低',
      creator: '盛书华',
      createTime: '2025-1-1',
      updateTime: '2025-1-9',
      ruleType: '任务进度',
      responsible: '盛书华',
      department: '技术部',
      progressFormula: '(已完成工作量 / 总工作量) * 100',
      triggerCondition: '城乡居民养老保险参保登记进度'
    },
    {
      sequence: '09',
      ruleName: '政府住房保障建设工作进度规则',
      description: '政府住房保障建设工作的进度跟踪......',
      status: '启用中',
      priority: '低',
      creator: '盛书华',
      createTime: '2025-1-1',
      updateTime: '2025-1-10',
      ruleType: '项目进度',
      responsible: '盛书华',
      department: '技术部',
      progressFormula: '(已完成任务数 / 总任务数) * 100',
      triggerCondition: '政府住房保障建设工作的进度跟踪......'
    },
    {
      sequence: '10',
      ruleName: '关于加强居民民生项目机制保险政策......',
      description: '关于加强居民民生项目机制保险政策......',
      status: '已停用',
      priority: '低',
      creator: '盛书华',
      createTime: '2025-1-1',
      updateTime: '2025-1-11',
      ruleType: '任务进度',
      responsible: '盛书华',
      department: '技术部',
      progressFormula: '(已完成工作量 / 总工作量) * 100',
      triggerCondition: '关于加强居民民生项目机制保险政策......'
    }
  ]

  mockRules.forEach(item => {
    store.add(item)
  })
}

// 数据操作方法
export const useProgressTrackingStore = () => {
  // 获取进度追踪列表
  const getProgressTrackingList = async (params: any) => {
    const database = await initDB()
    const transaction = database.transaction([STORES.PROGRESS_TRACKING], 'readonly')
    const store = transaction.objectStore(STORES.PROGRESS_TRACKING)

    return new Promise((resolve) => {
      const request = store.getAll()
      request.onsuccess = () => {
        let data = request.result
        
        // 简单的过滤逻辑
        if (params.taskName) {
          data = data.filter(item => item.taskName.includes(params.taskName))
        }
        if (params.sector) {
          data = data.filter(item => item.sector === params.sector)
        }
        if (params.priority) {
          data = data.filter(item => item.priority === params.priority)
        }
        if (params.leadLeader) {
          data = data.filter(item => item.leadLeader.includes(params.leadLeader))
        }
        if (params.responsible) {
          data = data.filter(item => item.responsible.includes(params.responsible))
        }
        if (params.progressStart && params.progressEnd) {
          const start = parseInt(params.progressStart)
          const end = parseInt(params.progressEnd)
          data = data.filter(item => item.progress >= start && item.progress <= end)
        }

        // 分页
        const start = params.skipCount || 0
        const size = params.maxResultCount || 10
        const list = data.slice(start, start + size)

        resolve({
          list,
          total: data.length
        })
      }
    })
  }

  // 获取子任务列表
  const getSubTaskList = async (parentTaskId: string, params: any) => {
    const database = await initDB()
    const transaction = database.transaction([STORES.SUB_TASKS], 'readonly')
    const store = transaction.objectStore(STORES.SUB_TASKS)
    const index = store.index('parentTaskId')

    return new Promise((resolve) => {
      const request = index.getAll(Number(parentTaskId))
      request.onsuccess = () => {
        let data = request.result
        
        // 过滤
        if (params.taskName) {
          data = data.filter(item => item.taskName.includes(params.taskName))
        }

        // 分页
        const start = ((params.page || 1) - 1) * (params.size || 10)
        const size = params.size || 10
        const list = data.slice(start, start + size)

        resolve({
          list,
          total: data.length
        })
      }
    })
  }

  // 获取规则列表
  const getRuleList = async (params: any) => {
    const database = await initDB()
    const transaction = database.transaction([STORES.RULES], 'readonly')
    const store = transaction.objectStore(STORES.RULES)

    return new Promise((resolve) => {
      const request = store.getAll()
      request.onsuccess = () => {
        let data = request.result
        
        // 过滤
        if (params.ruleName) {
          data = data.filter(item => item.ruleName.includes(params.ruleName))
        }
        if (params.description) {
          data = data.filter(item => item.description.includes(params.description))
        }
        if (params.status) {
          data = data.filter(item => item.status === params.status)
        }
        if (params.priority) {
          data = data.filter(item => item.priority === params.priority)
        }
        if (params.createTimeRange && params.createTimeRange.length === 2) {
          const startDate = new Date(params.createTimeRange[0])
          const endDate = new Date(params.createTimeRange[1])
          data = data.filter(item => {
            const createDate = new Date(item.createTime)
            return createDate >= startDate && createDate <= endDate
          })
        }
        if (params.updateTimeRange && params.updateTimeRange.length === 2) {
          const startDate = new Date(params.updateTimeRange[0])
          const endDate = new Date(params.updateTimeRange[1])
          data = data.filter(item => {
            const updateDate = new Date(item.updateTime)
            return updateDate >= startDate && updateDate <= endDate
          })
        }

        // 分页
        const start = params.skipCount || 0
        const size = params.maxResultCount || 10
        const list = data.slice(start, start + size)

        resolve({
          list,
          total: data.length
        })
      }
    })
  }

  // 创建规则
  const createRule = async (ruleData: any) => {
    const database = await initDB()
    const transaction = database.transaction([STORES.RULES], 'readwrite')
    const store = transaction.objectStore(STORES.RULES)

    return new Promise((resolve, reject) => {
      // 生成新的序号
      const sequence = String(Date.now()).slice(-2).padStart(2, '0')

      const request = store.add({
        ...ruleData,
        sequence: sequence
      })
      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  // 更新规则
  const updateRule = async (id: number, ruleData: any) => {
    const database = await initDB()
    const transaction = database.transaction([STORES.RULES], 'readwrite')
    const store = transaction.objectStore(STORES.RULES)

    return new Promise((resolve, reject) => {
      const request = store.put({ ...ruleData, id })
      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  // 删除规则
  const deleteRule = async (id: number) => {
    const database = await initDB()
    const transaction = database.transaction([STORES.RULES], 'readwrite')
    const store = transaction.objectStore(STORES.RULES)

    return new Promise((resolve, reject) => {
      const request = store.delete(id)
      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  // 初始化数据
  const initData = async () => {
    await generateMockData()
  }

  return {
    getProgressTrackingList,
    getSubTaskList,
    getRuleList,
    createRule,
    updateRule,
    deleteRule,
    initData
  }
}

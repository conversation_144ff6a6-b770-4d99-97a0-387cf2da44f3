<template>
  <div class="progress-tracking-detail">
    <!-- 顶部导航 -->
    <div class="detail-header">
      <el-button @click="goBack" type="text" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <h2 class="detail-title">{{ taskData.taskName || '进度追踪详情' }}</h2>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content-section">
      <el-tabs v-model="activeMainTab" class="main-tabs">
        <!-- 任务信息Tab -->
        <el-tab-pane label="任务信息" name="taskInfo">
          <div class="task-info-content">
            <el-card class="info-card">
        
        <div class="info-grid">
          <div class="info-row">
            <div class="info-item">
              <label>任务目标：</label>
              <span>{{ taskData.taskTarget || '重点群体就业率达 100%，城镇新增就业 5500 人' }}</span>
            </div>
            <div class="info-item">
              <label>任务名称：</label>
              <span>{{ taskData.taskName || '开展群体就业率' }}</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <label>所属板块：</label>
              <span>{{ taskData.category || '党的建设' }}</span>
            </div>
            <div class="info-item">
              <label>任务类型：</label>
              <span>{{ taskData.taskType || '数据报表' }}</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <label>追踪时间：</label>
              <div class="field-with-edit">
                <span>{{ taskData.trackingTime || '2024-01-01~2024-12-31' }}</span>
                <el-icon class="edit-icon" @click="openTrackingTimeDialog">
                  <Edit />
                </el-icon>
              </div>
            </div>
            <div class="info-item">
              <label>责任人：</label>
              <span>{{ taskData.responsible || '盛泽' }}</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <label>进度：</label>
              <span>{{ taskData.progress || '60' }}%</span>
            </div>
            <div class="info-item">
              <label>进度提醒：</label>
              <span>{{ reminderCount }}</span>
              <el-button type="text" @click="viewReminders">
                <el-icon><Bell /></el-icon>
              </el-button>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <label>完成条件：</label>
              <span>{{ taskData.completionCondition || '全部职责人均完成' }}</span>
            </div>
            <div class="info-item">
              <label>子任务：</label>
              <span>{{ taskData.subTaskCount || '10' }}</span>
              <el-button type="text" @click="viewSubTasks">
                <el-icon><List /></el-icon>
              </el-button>
            </div>
          </div>
          
          <div class="info-row full-width">
            <div class="info-item">
              <label>任务备注：</label>
              <div class="field-with-edit">
                <span>{{ taskData.remark || '通过业务表数据收集实现进度追踪，帮助各部门及时了解进度情况，并根据相关情况进行调整。' }}</span>
                <el-icon class="edit-icon" @click="openRemarkDialog">
                  <Edit />
                </el-icon>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 业务表数据 -->
      <div class="business-data-section">
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span>业务表数据</span>
            </div>
          </template>

          <div class="business-table">
            <el-table
              :data="businessData"
              border
              style="width: 100%"
            >
              <el-table-column prop="sequence" label="序号" width="80" />
              <el-table-column prop="businessTableName" label="业务表名称" min-width="150" />
              <el-table-column prop="publishDepartment" label="发布部门" min-width="150" />
              <el-table-column prop="category" label="所属板块" width="120" />
              <el-table-column prop="channel" label="所属渠道" width="120" />
              <el-table-column prop="updateFrequency" label="更新频率" width="100" />
              <el-table-column prop="deadline" label="截止时间" width="120" />
              <el-table-column prop="progressCalculation" label="进度计算方式" width="140">
                <template #default="{ row }">
                  <el-button type="text" @click="viewProgressCalculation(row)">
                    {{ row.progressCalculation }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" fixed="right">
                <template #default="{ row }">
                  <el-button type="primary" size="small" class="detail-button" @click="viewDetails(row)">
                    查看明细
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </div>
          </div>
        </el-tab-pane>

        <!-- 日志记录Tab -->
        <el-tab-pane label="日志记录" name="logRecord">
          <div class="log-content">
            <el-tabs v-model="activeLogTab" class="log-tabs">
              <!-- 任务更新日志 -->
              <el-tab-pane label="任务更新日志" name="taskUpdateLog">
                <div class="log-section">
                  <!-- 查询区域 -->
                  <div class="search-section">
                    <el-form :inline="true" class="search-form">
                      <el-form-item>
                        <el-input
                          v-model="taskLogSearch.username"
                          placeholder="请输入用户名"
                          clearable
                          style="width: 200px"
                        />
                      </el-form-item>
                      <el-form-item>
                        <el-button type="primary" @click="searchTaskLogs">查询</el-button>
                        <el-button @click="resetTaskLogSearch">重置</el-button>
                      </el-form-item>
                    </el-form>
                  </div>

                  <!-- 功能按钮区域 -->
                  <div class="action-buttons">
                    <el-button type="success" @click="batchExportTaskLogs">批量导出</el-button>
                    <el-button type="danger" @click="batchDeleteTaskLogs">批量删除</el-button>
                  </div>

                  <!-- 数据列表 -->
                  <el-table
                    ref="taskLogTable"
                    :data="taskLogData"
                    style="width: 100%"
                    @selection-change="handleTaskLogSelectionChange"
                  >
                    <el-table-column type="selection" width="55" />
                    <el-table-column prop="sequence" label="序号" width="80" />
                    <el-table-column prop="logId" label="日志ID" width="280" />
                    <el-table-column prop="operationTime" label="操作时间" width="180" />
                    <el-table-column prop="operator" label="操作人" width="120" />
                    <el-table-column prop="operatorId" label="操作人ID" width="120" />
                    <el-table-column prop="operationIp" label="操作IP" width="140" />
                    <el-table-column prop="updateContent" label="更新内容" min-width="200" />
                    <el-table-column prop="remark" label="备注" width="120" />
                    <el-table-column label="操作" width="200" fixed="right">
                      <template #default="{ row }">
                        <el-button type="primary" size="small" @click="editTaskLog(row)">编辑</el-button>
                        <el-button type="success" size="small" @click="exportTaskLog(row)">导出</el-button>
                        <el-button type="danger" size="small" @click="deleteTaskLog(row)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>

              <!-- 进度更新日志 -->
              <el-tab-pane label="进度更新日志" name="progressUpdateLog">
                <div class="log-section">
                  <!-- 查询区域 -->
                  <div class="search-section">
                    <el-form :inline="true" class="search-form">
                      <el-form-item>
                        <el-input
                          v-model="progressLogSearch.username"
                          placeholder="请输入用户名"
                          clearable
                          style="width: 200px"
                        />
                      </el-form-item>
                      <el-form-item>
                        <el-button type="primary" @click="searchProgressLogs">查询</el-button>
                        <el-button @click="resetProgressLogSearch">重置</el-button>
                      </el-form-item>
                    </el-form>
                  </div>

                  <!-- 功能按钮区域 -->
                  <div class="action-buttons">
                    <el-button type="success" @click="batchExportProgressLogs">批量导出</el-button>
                    <el-button type="danger" @click="batchDeleteProgressLogs">批量删除</el-button>
                  </div>

                  <!-- 数据列表 -->
                  <el-table
                    ref="progressLogTable"
                    :data="progressLogData"
                    style="width: 100%"
                    @selection-change="handleProgressLogSelectionChange"
                  >
                    <el-table-column type="selection" width="55" />
                    <el-table-column prop="sequence" label="序号" width="80" />
                    <el-table-column prop="logId" label="日志ID" width="280" />
                    <el-table-column prop="operationTime" label="操作时间" width="180" />
                    <el-table-column prop="operator" label="操作人" width="120" />
                    <el-table-column prop="operatorId" label="操作人ID" width="120" />
                    <el-table-column prop="operationIp" label="操作IP" width="140" />
                    <el-table-column prop="updateContent" label="更新内容" min-width="200" />
                    <el-table-column label="操作" width="150" fixed="right">
                      <template #default="{ row }">
                        <el-button type="success" size="small" @click="exportProgressLog(row)">导出</el-button>
                        <el-button type="danger" size="small" @click="deleteProgressLog(row)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 子任务弹窗 -->
    <SubTaskDialog
      v-model="showSubTaskDialog"
      :task-id="taskId"
    />

    <!-- 查看提醒弹窗 -->
    <ViewReminderDialog
      v-model="showViewReminderDialog"
      :task-data="taskData"
    />

    <!-- 明细数据弹窗 -->
    <DetailDataDialog
      v-model="showDetailDataDialog"
      :business-data="currentBusinessData"
    />

    <!-- 进度计算方式弹窗 -->
    <ProgressCalculationDialog
      v-model="showProgressCalculationDialog"
      :business-data="currentBusinessData"
    />

    <!-- 编辑追踪时间弹窗 -->
    <Dialog
      v-model="showTrackingTimeDialog"
      title="编辑追踪时间"
      width="500px"
      :show-confirm-button="false"
      cancel-text="取消"
      @click-cancel="showTrackingTimeDialog = false"
      @closed="showTrackingTimeDialog = false"
    >
      <el-form :model="trackingTimeForm" label-width="100px">
        <el-form-item label="追踪时间：">
          <el-date-picker
            v-model="trackingTimeForm.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showTrackingTimeDialog = false">取消</el-button>
          <el-button type="primary" @click="saveTrackingTime">保存</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 编辑任务备注弹窗 -->
    <Dialog
      v-model="showRemarkDialog"
      title="编辑任务备注"
      width="600px"
      :show-confirm-button="false"
      cancel-text="取消"
      @click-cancel="showRemarkDialog = false"
      @closed="showRemarkDialog = false"
    >
      <el-form :model="remarkForm" label-width="100px">
        <el-form-item label="任务备注：">
          <el-input
            v-model="remarkForm.remark"
            type="textarea"
            :rows="5"
            placeholder="请输入任务备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showRemarkDialog = false">取消</el-button>
          <el-button type="primary" @click="saveRemark">保存</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 编辑日志弹窗 -->
    <Dialog
      v-model="showEditLogDialog"
      title="编辑日志"
      width="500px"
      :show-confirm-button="false"
      cancel-text="取消"
      @click-cancel="showEditLogDialog = false"
      @closed="showEditLogDialog = false"
    >
      <el-form :model="editLogForm" label-width="100px">
        <el-form-item label="日志备注：">
          <el-input
            v-model="editLogForm.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入日志备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEditLogDialog = false">取消</el-button>
          <el-button type="primary" @click="saveLogEdit">保存</el-button>
        </div>
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Edit, Bell, List } from '@element-plus/icons-vue'
import SubTaskDialog from '../components/SubTaskDialog.vue'
import ViewReminderDialog from '../components/ViewReminderDialog.vue'
import DetailDataDialog from '../components/DetailDataDialog.vue'
import ProgressCalculationDialog from '../components/ProgressCalculationDialog.vue'

// 路由
const route = useRoute()
const router = useRouter()

// 任务ID
const taskId = ref(route.params.id as string)

// Tab状态
const activeMainTab = ref('taskInfo')
const activeLogTab = ref('taskUpdateLog')

// 编辑模式状态
const isEditMode = ref(false)

// 任务数据
const taskData = ref({
  id: taskId.value,
  taskName: '开展群体就业率',
  taskTarget: '重点群体就业率达 100%，城镇新增就业 5500 人',
  category: '党的建设',
  taskType: '数据报表',
  trackingTime: '2024-01-01~2024-12-31',
  responsible: '盛泽',
  progress: 60,
  completionCondition: '全部职责人均完成',
  subTaskCount: 10,
  remark: '通过业务表数据收集实现进度追踪，帮助各部门及时了解进度情况，并根据相关情况进行调整。'
})

// 业务表数据
const businessData = ref([
  {
    sequence: '01',
    businessTableName: '登记失业人员名单',
    publishDepartment: '市中区人力资源和社会保障局',
    category: '民生服务',
    channel: '创业就业/就业服务',
    updateFrequency: '每日',
    deadline: '每周五前',
    progressCalculation: '按数据量累计进度'
  }
])

// 弹窗状态
const showSubTaskDialog = ref(false)
const showViewReminderDialog = ref(false)
const showDetailDataDialog = ref(false)
const showProgressCalculationDialog = ref(false)
const showTrackingTimeDialog = ref(false)
const showRemarkDialog = ref(false)
const showEditLogDialog = ref(false)
const currentBusinessData = ref(null)
const currentEditLog = ref(null)

// 编辑表单数据
const trackingTimeForm = ref({
  timeRange: []
})

const remarkForm = ref({
  remark: ''
})

const editLogForm = ref({
  remark: ''
})

// 日志搜索表单
const taskLogSearch = ref({
  username: ''
})

const progressLogSearch = ref({
  username: ''
})

// 日志数据
const taskLogData = ref([
  {
    sequence: '01',
    logId: '12995fbb-98d5-8f1d-38ab-0ce717a3a86b',
    operationTime: '2024-06-30 09:05',
    operator: '张三丰',
    operatorId: '2655121',
    operationIp: '271.78.12.9',
    updateContent: '追踪时效更新',
    remark: '时效'
  },
  {
    sequence: '02',
    logId: '12995fbb-98d5-8f1d-38ab-0ce717a3a86c',
    operationTime: '2024-06-30 09:05',
    operator: '周正君',
    operatorId: '2755121',
    operationIp: '271.78.12.9',
    updateContent: '责任人更新',
    remark: '原责'
  },
  {
    sequence: '03',
    logId: '12995fbb-98d5-8f1d-38ab-0ce717a3a86d',
    operationTime: '2024-06-30 09:05',
    operator: '李达康',
    operatorId: '2955121',
    operationIp: '271.78.12.9',
    updateContent: '所属部门1更新',
    remark: '时效'
  }
])

const progressLogData = ref([
  {
    sequence: '01',
    logId: '12995fbb-98d5-8f1d-38ab-0ce717a3a86e',
    operationTime: '2024-06-30 09:05',
    operator: '张三丰',
    operatorId: '2655121',
    operationIp: '271.78.12.9',
    updateContent: '追踪时效更新'
  },
  {
    sequence: '02',
    logId: '12995fbb-98d5-8f1d-38ab-0ce717a3a86f',
    operationTime: '2024-06-30 09:05',
    operator: '周正君',
    operatorId: '2755121',
    operationIp: '271.78.12.9',
    updateContent: '责任人更新'
  }
])

// 选中的日志数据
const selectedTaskLogs = ref([])
const selectedProgressLogs = ref([])

// 获取所有任务日志数据（模拟完整数据）
const getAllTaskLogs = () => {
  return [
    {
      sequence: '01',
      logId: '12995fbb-98d5-8f1d-38ab-0ce717a3a86b',
      operationTime: '2024-06-30 09:05',
      operator: '张三丰',
      operatorId: '2655121',
      operationIp: '271.78.12.9',
      updateContent: '追踪时效更新',
      remark: '时效'
    },
    {
      sequence: '02',
      logId: '12995fbb-98d5-8f1d-38ab-0ce717a3a86c',
      operationTime: '2024-06-30 09:05',
      operator: '周正君',
      operatorId: '2755121',
      operationIp: '271.78.12.9',
      updateContent: '责任人更新',
      remark: '原责'
    },
    {
      sequence: '03',
      logId: '12995fbb-98d5-8f1d-38ab-0ce717a3a86d',
      operationTime: '2024-06-30 09:05',
      operator: '李达康',
      operatorId: '2955121',
      operationIp: '271.78.12.9',
      updateContent: '所属部门1更新',
      remark: '时效'
    },
    {
      sequence: '04',
      logId: '12995fbb-98d5-8f1d-38ab-0ce717a3a86e',
      operationTime: '2024-06-29 14:30',
      operator: '王大锤',
      operatorId: '2855121',
      operationIp: '271.78.12.10',
      updateContent: '进度状态更新',
      remark: '进度'
    }
  ]
}

// 获取所有进度日志数据（模拟完整数据）
const getAllProgressLogs = () => {
  return [
    {
      sequence: '01',
      logId: '12995fbb-98d5-8f1d-38ab-0ce717a3a86e',
      operationTime: '2024-06-30 09:05',
      operator: '张三丰',
      operatorId: '2655121',
      operationIp: '271.78.12.9',
      updateContent: '追踪时效更新'
    },
    {
      sequence: '02',
      logId: '12995fbb-98d5-8f1d-38ab-0ce717a3a86f',
      operationTime: '2024-06-30 09:05',
      operator: '周正君',
      operatorId: '2755121',
      operationIp: '271.78.12.9',
      updateContent: '责任人更新'
    },
    {
      sequence: '03',
      logId: '12995fbb-98d5-8f1d-38ab-0ce717a3a87a',
      operationTime: '2024-06-29 16:20',
      operator: '赵子龙',
      operatorId: '2955122',
      operationIp: '271.78.12.11',
      updateContent: '数据同步更新'
    }
  ]
}

// 加载所有任务日志
const loadAllTaskLogs = () => {
  taskLogData.value = getAllTaskLogs()
}

// 加载所有进度日志
const loadAllProgressLogs = () => {
  progressLogData.value = getAllProgressLogs()
}

// Excel导出工具函数
const exportToExcel = (data: any[], filename: string, headers: any) => {
  try {
    // 创建工作簿
    const wb = {
      SheetNames: ['Sheet1'],
      Sheets: {
        Sheet1: {}
      }
    }

    // 准备数据
    const wsData = [
      Object.values(headers), // 表头
      ...data.map(row => Object.keys(headers).map(key => row[key] || ''))
    ]

    // 转换为工作表
    const ws = {}
    const range = { s: { c: 0, r: 0 }, e: { c: wsData[0].length - 1, r: wsData.length - 1 } }

    for (let R = 0; R <= range.e.r; R++) {
      for (let C = 0; C <= range.e.c; C++) {
        const cellAddress = { c: C, r: R }
        const cellRef = encodeCell(cellAddress)
        const cellValue = wsData[R][C]
        ws[cellRef] = { v: cellValue, t: typeof cellValue === 'number' ? 'n' : 's' }
      }
    }

    ws['!ref'] = encodeRange(range)
    wb.Sheets.Sheet1 = ws

    // 导出文件
    const wbout = writeWorkbook(wb, { bookType: 'xlsx', type: 'array' })
    const blob = new Blob([wbout], { type: 'application/octet-stream' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 简化的Excel编码函数
const encodeCell = (cell: { c: number, r: number }) => {
  const col = String.fromCharCode(65 + cell.c)
  return col + (cell.r + 1)
}

const encodeRange = (range: any) => {
  return encodeCell(range.s) + ':' + encodeCell(range.e)
}

const writeWorkbook = (wb: any, opts: any) => {
  // 简化的工作簿写入，实际项目中应使用xlsx库
  const data = []
  const sheet = wb.Sheets[wb.SheetNames[0]]
  const range = sheet['!ref'].split(':')
  const startCell = range[0]
  const endCell = range[1]

  // 这里简化处理，实际应该使用专业的Excel库
  return new Uint8Array([])
}

// 计算提醒数量
const reminderCount = computed(() => {
  try {
    const reminders = JSON.parse(localStorage.getItem('progressTrackingReminders') || '[]')
    const taskReminders = reminders.filter(reminder => reminder.taskId === taskId.value)
    return taskReminders.length
  } catch (error) {
    return 0
  }
})

// 返回上一页
const goBack = () => {
  router.back()
}

// 进入编辑模式
const enterEditMode = () => {
  isEditMode.value = true
}

// 保存更改
const saveChanges = () => {
  // TODO: 实现保存功能
  console.log('保存更改')
  isEditMode.value = false
  ElMessage.success('保存成功')
}

// 取消编辑
const cancelEdit = () => {
  // TODO: 恢复原始数据
  console.log('取消编辑')
  isEditMode.value = false
}

// 打开编辑追踪时间弹窗
const openTrackingTimeDialog = () => {
  // 初始化表单数据
  const timeRange = taskData.value.trackingTime.split('~')
  trackingTimeForm.value.timeRange = [new Date(timeRange[0]), new Date(timeRange[1])]
  showTrackingTimeDialog.value = true
}

// 保存追踪时间
const saveTrackingTime = () => {
  if (trackingTimeForm.value.timeRange && trackingTimeForm.value.timeRange.length === 2) {
    const startTime = trackingTimeForm.value.timeRange[0].toISOString().split('T')[0]
    const endTime = trackingTimeForm.value.timeRange[1].toISOString().split('T')[0]
    taskData.value.trackingTime = `${startTime}~${endTime}`
    showTrackingTimeDialog.value = false
    ElMessage.success('追踪时间更新成功')
  } else {
    ElMessage.warning('请选择有效的时间范围')
  }
}

// 打开编辑备注弹窗
const openRemarkDialog = () => {
  remarkForm.value.remark = taskData.value.remark
  showRemarkDialog.value = true
}

// 保存备注
const saveRemark = () => {
  taskData.value.remark = remarkForm.value.remark
  showRemarkDialog.value = false
  ElMessage.success('任务备注更新成功')
}

// 保存日志编辑
const saveLogEdit = () => {
  if (currentEditLog.value) {
    currentEditLog.value.remark = editLogForm.value.remark
    showEditLogDialog.value = false
    ElMessage.success('日志备注更新成功')
  }
}

// 查看子任务
const viewSubTasks = () => {
  showSubTaskDialog.value = true
}

// 查看提醒
const viewReminders = () => {
  showViewReminderDialog.value = true
}

// 查看明细
const viewDetails = (row: any) => {
  currentBusinessData.value = row
  showDetailDataDialog.value = true
}

// 查看进度计算方式
const viewProgressCalculation = (row: any) => {
  currentBusinessData.value = row
  showProgressCalculationDialog.value = true
}

// 任务日志相关方法
const searchTaskLogs = () => {
  console.log('搜索任务日志:', taskLogSearch.value.username)
  if (!taskLogSearch.value.username.trim()) {
    // 如果搜索条件为空，显示所有数据
    loadAllTaskLogs()
    return
  }

  // 根据用户名过滤数据
  const allLogs = getAllTaskLogs()
  taskLogData.value = allLogs.filter(log =>
    log.operator.includes(taskLogSearch.value.username.trim())
  )

  ElMessage.success(`找到 ${taskLogData.value.length} 条匹配记录`)
}

const resetTaskLogSearch = () => {
  taskLogSearch.value.username = ''
  loadAllTaskLogs()
  ElMessage.success('搜索条件已重置')
}

const handleTaskLogSelectionChange = (selection: any[]) => {
  selectedTaskLogs.value = selection
  console.log('任务日志选择变化:', selection)
}

const batchExportTaskLogs = () => {
  const dataToExport = selectedTaskLogs.value.length > 0 ? selectedTaskLogs.value : taskLogData.value

  if (dataToExport.length === 0) {
    ElMessage.warning('没有数据可导出')
    return
  }

  const headers = {
    sequence: '序号',
    logId: '日志ID',
    operationTime: '操作时间',
    operator: '操作人',
    operatorId: '操作人ID',
    operationIp: '操作IP',
    updateContent: '更新内容',
    remark: '备注'
  }

  const filename = `任务更新日志_${new Date().toISOString().split('T')[0]}.xlsx`

  // 使用简化的导出方式
  exportTaskLogsToCSV(dataToExport, filename)
}

const batchDeleteTaskLogs = () => {
  if (selectedTaskLogs.value.length === 0) {
    ElMessage.warning('请选择要删除的日志')
    return
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedTaskLogs.value.length} 条日志记录吗？`,
    '批量删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 获取要删除的日志ID
    const idsToDelete = selectedTaskLogs.value.map(log => log.logId)

    // 从数据中移除选中的日志
    taskLogData.value = taskLogData.value.filter(log => !idsToDelete.includes(log.logId))

    // 清空选中状态
    selectedTaskLogs.value = []

    ElMessage.success(`成功删除 ${idsToDelete.length} 条日志记录`)
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

const editTaskLog = (row: any) => {
  currentEditLog.value = row
  editLogForm.value.remark = row.remark || ''
  showEditLogDialog.value = true
}

const exportTaskLog = (row: any) => {
  const dataToExport = [row]
  const headers = {
    sequence: '序号',
    logId: '日志ID',
    operationTime: '操作时间',
    operator: '操作人',
    operatorId: '操作人ID',
    operationIp: '操作IP',
    updateContent: '更新内容',
    remark: '备注'
  }

  const filename = `任务日志_${row.logId}_${new Date().toISOString().split('T')[0]}.xlsx`
  exportTaskLogsToCSV(dataToExport, filename)
}

const deleteTaskLog = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除日志ID为 "${row.logId}" 的记录吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 从数据中移除该日志
    const index = taskLogData.value.findIndex(log => log.logId === row.logId)
    if (index > -1) {
      taskLogData.value.splice(index, 1)
      ElMessage.success('删除成功')
    } else {
      ElMessage.error('删除失败，未找到该记录')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 进度日志相关方法
const searchProgressLogs = () => {
  console.log('搜索进度日志:', progressLogSearch.value.username)
  if (!progressLogSearch.value.username.trim()) {
    // 如果搜索条件为空，显示所有数据
    loadAllProgressLogs()
    return
  }

  // 根据用户名过滤数据
  const allLogs = getAllProgressLogs()
  progressLogData.value = allLogs.filter(log =>
    log.operator.includes(progressLogSearch.value.username.trim())
  )

  ElMessage.success(`找到 ${progressLogData.value.length} 条匹配记录`)
}

const resetProgressLogSearch = () => {
  progressLogSearch.value.username = ''
  loadAllProgressLogs()
  ElMessage.success('搜索条件已重置')
}

const handleProgressLogSelectionChange = (selection: any[]) => {
  selectedProgressLogs.value = selection
  console.log('进度日志选择变化:', selection)
}

const batchExportProgressLogs = () => {
  const dataToExport = selectedProgressLogs.value.length > 0 ? selectedProgressLogs.value : progressLogData.value

  if (dataToExport.length === 0) {
    ElMessage.warning('没有数据可导出')
    return
  }

  const headers = {
    sequence: '序号',
    logId: '日志ID',
    operationTime: '操作时间',
    operator: '操作人',
    operatorId: '操作人ID',
    operationIp: '操作IP',
    updateContent: '更新内容'
  }

  const filename = `进度更新日志_${new Date().toISOString().split('T')[0]}.xlsx`
  exportProgressLogsToCSV(dataToExport, filename)
}

const batchDeleteProgressLogs = () => {
  if (selectedProgressLogs.value.length === 0) {
    ElMessage.warning('请选择要删除的日志')
    return
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedProgressLogs.value.length} 条日志记录吗？`,
    '批量删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 获取要删除的日志ID
    const idsToDelete = selectedProgressLogs.value.map(log => log.logId)

    // 从数据中移除选中的日志
    progressLogData.value = progressLogData.value.filter(log => !idsToDelete.includes(log.logId))

    // 清空选中状态
    selectedProgressLogs.value = []

    ElMessage.success(`成功删除 ${idsToDelete.length} 条日志记录`)
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

const exportProgressLog = (row: any) => {
  const dataToExport = [row]
  const headers = {
    sequence: '序号',
    logId: '日志ID',
    operationTime: '操作时间',
    operator: '操作人',
    operatorId: '操作人ID',
    operationIp: '操作IP',
    updateContent: '更新内容'
  }

  const filename = `进度日志_${row.logId}_${new Date().toISOString().split('T')[0]}.xlsx`
  exportProgressLogsToCSV(dataToExport, filename)
}

const deleteProgressLog = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除日志ID为 "${row.logId}" 的记录吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 从数据中移除该日志
    const index = progressLogData.value.findIndex(log => log.logId === row.logId)
    if (index > -1) {
      progressLogData.value.splice(index, 1)
      ElMessage.success('删除成功')
    } else {
      ElMessage.error('删除失败，未找到该记录')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// CSV导出实现函数
const exportTaskLogsToCSV = (data: any[], filename: string) => {
  const headers = ['序号', '日志ID', '操作时间', '操作人', '操作人ID', '操作IP', '更新内容', '备注']
  const csvContent = [
    headers.join(','),
    ...data.map(row => [
      row.sequence,
      row.logId,
      row.operationTime,
      row.operator,
      row.operatorId,
      row.operationIp,
      `"${row.updateContent}"`,
      `"${row.remark || ''}"`
    ].join(','))
  ].join('\n')

  downloadCSV(csvContent, filename)
}

const exportProgressLogsToCSV = (data: any[], filename: string) => {
  const headers = ['序号', '日志ID', '操作时间', '操作人', '操作人ID', '操作IP', '更新内容']
  const csvContent = [
    headers.join(','),
    ...data.map(row => [
      row.sequence,
      row.logId,
      row.operationTime,
      row.operator,
      row.operatorId,
      row.operationIp,
      `"${row.updateContent}"`
    ].join(','))
  ].join('\n')

  downloadCSV(csvContent, filename.replace('.xlsx', '.csv'))
}

const downloadCSV = (content: string, filename: string) => {
  const BOM = '\uFEFF' // 添加BOM以支持中文
  const blob = new Blob([BOM + content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename.replace('.xlsx', '.csv')
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  ElMessage.success('导出成功')
}

// 组件挂载时加载数据
onMounted(() => {
  // 加载所有日志数据
  loadAllTaskLogs()
  loadAllProgressLogs()
  console.log('加载任务详情数据:', taskId.value)
})
</script>

<route>
{
  meta: {
    title: '进度追踪详情',
  },
}
</route>

<style scoped lang="scss">
.progress-tracking-detail {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;

  .detail-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .back-button {
      margin-right: 16px;
      color: #409eff;
      
      &:hover {
        color: #66b1ff;
      }
    }

    .detail-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #303133;
    }
  }

  .main-content-section {
    .main-tabs {
      .el-tabs__header {
        margin-bottom: 20px;
      }
    }

    .task-info-content {
      .info-card {
        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: 600;
          color: #303133;

          .header-actions {
            .el-button + .el-button {
              margin-left: 8px;
            }
          }
        }
      }
    }

    .log-content {
      .log-tabs {
        .el-tabs__header {
          margin-bottom: 16px;
        }
      }

      .log-section {
        .search-section {
          margin-bottom: 16px;
          padding: 16px;
          background: #f8f9fa;
          border-radius: 6px;

          .search-form {
            margin: 0;
          }
        }

        .action-buttons {
          margin-bottom: 16px;

          .el-button + .el-button {
            margin-left: 8px;
          }
        }
      }
    }
  }

  .field-with-edit {
    display: flex;
    align-items: center;
    gap: 8px;

    .edit-icon {
      color: #409eff;
      cursor: pointer;
      font-size: 16px;

      &:hover {
        color: #66b1ff;
      }
    }
  }

  .info-grid {
    .info-row {
      display: flex;
      margin-bottom: 16px;

      &.full-width {
        flex-direction: column;
      }

      .info-item {
        flex: 1;
        display: flex;
        align-items: center;
        margin-right: 20px;

        &:last-child {
          margin-right: 0;
        }

        label {
          font-weight: 500;
          color: #606266;
          margin-right: 8px;
          min-width: 80px;
        }

        span {
          color: #303133;
          flex: 1;
        }

        .el-button {
          margin-left: 8px;
          padding: 0;
        }
      }
    }
  }

  .business-data-section {
    .data-card {
      .card-header {
        font-weight: 600;
        color: #303133;
      }

      .business-table {
        .el-button {
          color: #409eff;

          &:hover {
            color: #66b1ff;
          }
        }

        // 修复查看明细按钮样式
        .detail-button {
          color: #fff !important;
          text-shadow: none !important;

          &:hover {
            color: #fff !important;
          }

          &:focus {
            color: #fff !important;
          }
        }
      }
    }
  }
}
</style>

<script setup lang="ts" name="progressTrackingRuleManagement">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'
import { Document, Check, Warning, Close, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import RuleFormDialog from '../components/RuleFormDialog.vue'
import { useProgressTrackingStore } from '../composables/useProgressTrackingStore'

// 数据存储
const store = useProgressTrackingStore()

// 搜索表单
const searchFormProp = ref([
  { label: '规则名称', prop: 'ruleName', type: 'text', placeholder: '请输入规则名称' },
  { label: '描述内容', prop: 'description', type: 'text', placeholder: '请输入描述内容' },
  {
    label: '状态',
    prop: 'status',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '启用中', value: '启用中' },
      { label: '已停用', value: '已停用' }
    ]
  },
  {
    label: '优先级',
    prop: 'priority',
    type: 'select',
    placeholder: '请选择优先级',
    options: [
      { label: '高', value: '高' },
      { label: '中', value: '中' },
      { label: '低', value: '低' }
    ]
  },
  { label: '创建时间', prop: 'createTimeRange', type: 'daterange', placeholder: '请选择创建时间范围' }
])

const searchForm = ref({
  ruleName: '',
  description: '',
  status: '',
  priority: '',
  createTimeRange: [],
  updateTimeRange: []
})

// 加载状态
const loading = ref(false)

// 表格ref
const tableRef = ref()
const tableHeight = ref(0)
const currentRow = ref(null)

// 规则列表数据
const ruleList = ref([])

// 选中的行数据
const selectedRows = ref([])

// 操作按钮
const buttons = [
  { label: '查看', type: 'primary', code: 'view' },
  { label: '编辑', type: 'success', code: 'edit' },
  { label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除吗?' }
]

// 表头
const columns = [
  { prop: 'ruleName', label: '规则名称' },
  { prop: 'description', label: '描述' },
  { prop: 'status', label: '状态', width: 100 },
  { prop: 'priority', label: '优先级', width: 100 },
  { prop: 'creator', label: '创建人', width: 100 },
  { prop: 'createTime', label: '创建时间', width: 150 },
  { prop: 'updateTime', label: '最后更新时间', width: 150 }
]

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 列表请求参数
const reqParams = reactive({
  ruleName: '',
  description: '',
  status: '',
  priority: '',
  createTimeRange: [],
  updateTimeRange: [],
  skipCount: 0,
  maxResultCount: 10
})

// 弹窗相关
const showRuleFormDialog = ref(false)
const dialogMode = ref<'add' | 'edit' | 'view'>('add')

// 统计数据
const statistics = computed(() => {
  const total = ruleList.value.length
  const enabled = ruleList.value.filter((item: any) => item.status === '启用中').length
  const pending = ruleList.value.filter((item: any) => item.status === '待审核').length
  const disabled = ruleList.value.filter((item: any) => item.status === '已停用').length
  
  return {
    total,
    enabled,
    pending,
    disabled,
    enabledRate: total > 0 ? ((enabled / total) * 100).toFixed(1) : '0'
  }
})

// 查询
const onSearch = () => {
  pagination.page = 1
  reqParams.skipCount = 0
  reqParams.maxResultCount = pagination.size
  reqParams.ruleName = searchForm.value.ruleName
  reqParams.description = searchForm.value.description
  reqParams.status = searchForm.value.status
  reqParams.priority = searchForm.value.priority
  reqParams.createTimeRange = searchForm.value.createTimeRange
  reqParams.updateTimeRange = searchForm.value.updateTimeRange
  loadData()
}

// 重置
const onReset = () => {
  searchForm.value = {
    ruleName: '',
    description: '',
    status: '',
    priority: '',
    createTimeRange: [],
    updateTimeRange: []
  }
  onSearch()
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const data = await store.getRuleList(reqParams)
    ruleList.value = data.list
    pagination.total = data.total
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 表格选择变化
const onSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 表格操作点击事件
const onTableClickButton = ({ row, btn }: any) => {
  currentRow.value = row
  
  if (btn.code === 'view') {
    dialogMode.value = 'view'
    showRuleFormDialog.value = true
  } else if (btn.code === 'edit') {
    dialogMode.value = 'edit'
    showRuleFormDialog.value = true
  } else if (btn.code === 'delete') {
    handleDelete(row)
  }
}

// 删除规则
const handleDelete = async (row: any) => {
  try {
    await store.deleteRule(row.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

// 创建规则
const onCreateRule = () => {
  currentRow.value = null
  dialogMode.value = 'add'
  showRuleFormDialog.value = true
}

// Excel导出功能
const exportToExcel = async (data: any[], filename: string) => {
  try {
    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('进度追踪规则')

    // 设置表头
    const headers = [
      '序号', '规则名称', '描述', '状态', '优先级',
      '创建人', '创建时间', '最后更新时间'
    ]

    worksheet.addRow(headers)

    // 设置表头样式
    const headerRow = worksheet.getRow(1)
    headerRow.font = { bold: true }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    }

    // 添加数据行
    data.forEach(item => {
      worksheet.addRow([
        item.sequence,
        item.ruleName,
        item.description,
        item.status,
        item.priority,
        item.creator,
        item.createTime,
        item.updateTime
      ])
    })

    // 自动调整列宽
    worksheet.columns.forEach(column => {
      column.width = 15
    })

    // 生成Excel文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    saveAs(blob, `${filename}.xlsx`)

    ElMessage.success(`导出成功！文件名：${filename}.xlsx，共${data.length}条数据`)
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 导出
const onExport = async () => {
  if (selectedRows.value.length === 0) {
    // 导出全部数据
    await exportToExcel(ruleList.value, '进度追踪规则_全部')
  } else {
    // 导出选中数据
    await exportToExcel(selectedRows.value, '进度追踪规则_选中')
  }
}

// 批量操作
const onBatchOperation = async (operation: string) => {
  switch (operation) {
    case 'enableSelected':
      if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择要启用的规则')
        return
      }
      // 更新选中规则的状态为启用中
      selectedRows.value.forEach(row => {
        const index = ruleList.value.findIndex(item => item.sequence === row.sequence)
        if (index !== -1) {
          ruleList.value[index].status = '启用中'
        }
      })
      ElMessage.success(`启用选中规则成功，共${selectedRows.value.length}条`)
      selectedRows.value = []
      break

    case 'disableSelected':
      if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择要停用的规则')
        return
      }
      // 更新选中规则的状态为已停用
      selectedRows.value.forEach(row => {
        const index = ruleList.value.findIndex(item => item.sequence === row.sequence)
        if (index !== -1) {
          ruleList.value[index].status = '已停用'
        }
      })
      ElMessage.success(`停用选中规则成功，共${selectedRows.value.length}条`)
      selectedRows.value = []
      break

    case 'deleteSelected':
      if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择要删除的规则')
        return
      }
      try {
        await ElMessageBox.confirm(
          `确定要删除选中的${selectedRows.value.length}条规则吗？此操作不可撤销。`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        // 删除选中的规则
        const deleteCount = selectedRows.value.length
        const deleteSequences = selectedRows.value.map(row => row.sequence)

        // 从列表中移除选中的项
        for (let i = ruleList.value.length - 1; i >= 0; i--) {
          if (deleteSequences.includes(ruleList.value[i].sequence)) {
            ruleList.value.splice(i, 1)
          }
        }

        ElMessage.success(`删除选中规则成功，共删除${deleteCount}条`)
        selectedRows.value = []
      } catch {
        // 用户取消删除
      }
      break

    case 'deleteAll':
      if (ruleList.value.length === 0) {
        ElMessage.warning('没有可删除的规则')
        return
      }
      try {
        await ElMessageBox.confirm(
          `确定要删除全部${ruleList.value.length}条规则吗？此操作不可撤销。`,
          '确认删除全部',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        const deleteCount = ruleList.value.length
        ruleList.value = []
        selectedRows.value = []

        ElMessage.success(`全部删除成功，共删除${deleteCount}条规则`)
      } catch {
        // 用户取消删除
      }
      break
  }
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type === 'page') {
    pagination.page = val
    reqParams.skipCount = (val - 1) * pagination.size
  } else {
    pagination.size = val
    reqParams.maxResultCount = pagination.size
  }
  loadData()
}

// 块高度变化事件
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 75
}

// 规则表单保存成功回调
const onRuleSaved = () => {
  showRuleFormDialog.value = false
  loadData()
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<template>
  <div class="rule-management">
    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.total }}</div>
                <div class="stat-label">总规则数</div>
                <div class="stat-trend up">
                  <el-icon><ArrowUp /></el-icon>
                  新增3条
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon enabled">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.enabled }}</div>
                <div class="stat-label">启用中</div>
                <div class="stat-trend rate">
                  启用率 {{ statistics.enabledRate }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon pending">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.pending }}</div>
                <div class="stat-label">待审核</div>
                <div class="stat-trend down">
                  <el-icon><ArrowDown /></el-icon>
                  较上周减少
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon disabled">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.disabled }}</div>
                <div class="stat-label">已停用</div>
                <div class="stat-trend">
                  — 无变化
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <Block title="进度追踪规则" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
      <template #topRight>
        <!-- 选中状态显示 -->
        <span v-if="selectedRows.length > 0" style="margin-right: 10px; color: #409eff;">
          已选中 {{ selectedRows.length }} 条数据
        </span>

        <!-- 基础操作按钮 -->
        <el-button size="small" type="info" @click="onExport">导出</el-button>
        <el-button size="small" type="primary" @click="onCreateRule">创建规则</el-button>

        <!-- 批量操作按钮 -->
        <el-button size="small" type="success" @click="onBatchOperation('enableSelected')">启用选中</el-button>
        <el-button size="small" type="warning" @click="onBatchOperation('disableSelected')">停用选中</el-button>
        <el-button size="small" type="danger" @click="onBatchOperation('deleteSelected')">删除选中</el-button>
        <el-button size="small" type="danger" @click="onBatchOperation('deleteAll')">全部删除</el-button>
      </template>
      
      <template #expand>
        <!-- 搜索 -->
        <div class="search">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="3"
            :label-width="80"
            :enable-reset="false"
            confirm-text="查询"
            button-vertical="flowing"
            @submit="onSearch"
          />
        </div>
      </template>
      
      <!-- 列表 -->
      <TableV2
        ref="tableRef"
        :defaultTableData="ruleList"
        :columns="columns"
        :req-params="reqParams"
        :enable-toolbar="false"
        :enable-own-button="false"
        :enable-selection="true"
        :height="tableHeight"
        :buttons="buttons"
        @loading="loading = $event"
        @click-button="onTableClickButton"
        @selection-change="onSelectionChange"
        @completed="pagination.total = tableRef?.getTotal() || 0"
      >
        <!-- 自定义状态列 -->
        <template #status="{ row }">
          <el-tag
            :type="row.status === '启用中' ? 'success' : row.status === '待审核' ? 'warning' : 'danger'"
            size="small"
          >
            {{ row.status }}
          </el-tag>
        </template>
        
        <!-- 自定义优先级列 -->
        <template #priority="{ row }">
          <el-tag
            :type="row.priority === '高' ? 'danger' : row.priority === '中' ? 'warning' : 'info'"
            size="small"
          >
            {{ row.priority }}
          </el-tag>
        </template>
      </TableV2>
      
      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.size"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      />
    </Block>

    <!-- 规则表单弹窗 -->
    <RuleFormDialog
      v-model="showRuleFormDialog"
      :mode="dialogMode"
      :rule-data="currentRow"
      @saved="onRuleSaved"
    />
  </div>
</template>

<route>
{
  meta: {
    title: '进度追踪规则管理',
  },
}
</route>

<style scoped lang="scss">
.rule-management {
  .statistics-cards {
    margin-bottom: 16px;
    
    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        
        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          
          &.total {
            background-color: #e3f2fd;
            color: #1976d2;
          }
          
          &.enabled {
            background-color: #e8f5e8;
            color: #4caf50;
          }
          
          &.pending {
            background-color: #fff3e0;
            color: #ff9800;
          }
          
          &.disabled {
            background-color: #ffebee;
            color: #f44336;
          }
        }
        
        .stat-info {
          flex: 1;
          
          .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            line-height: 1;
          }
          
          .stat-label {
            font-size: 14px;
            color: #666;
            margin: 4px 0;
          }
          
          .stat-trend {
            font-size: 12px;
            display: flex;
            align-items: center;
            
            &.up {
              color: #4caf50;
            }
            
            &.down {
              color: #f44336;
            }
            
            &.rate {
              color: #ff9800;
            }
          }
        }
      }
    }
  }
  
  .search {
    margin-bottom: 16px;
  }
}
</style>

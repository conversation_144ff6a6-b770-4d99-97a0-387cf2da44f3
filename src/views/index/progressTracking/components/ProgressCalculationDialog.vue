<template>
  <Dialog
    v-model="visible"
    title="进度计算方式"
    width="600px"
    :destroy-on-close="true"
    :show-confirm-button="false"
    cancel-text="关闭"
    @closed="handleClose"
  >
    <div class="progress-calculation-dialog">
      <!-- 计算方式配置 -->
      <div class="calculation-config">
        <el-form :model="configForm" label-width="120px">
          <el-form-item label="时间范围：">
            <el-radio-group v-model="configForm.timeRange">
              <el-radio label="taskStart">任务起止时间</el-radio>
              <el-radio label="custom">自定义时间范围</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item v-if="configForm.timeRange === 'custom'" label="自定义时间：">
            <el-input 
              v-model="configForm.customTimeRange" 
              placeholder="请输入时间范围"
              readonly
            />
          </el-form-item>
          
          <el-form-item v-else label="任务起止时间：">
            <el-input
              v-model="configForm.taskTimeRange"
              placeholder="任务时间范围"
              readonly
            />
          </el-form-item>

          <el-form-item label="区域范围：">
            <el-input 
              v-model="configForm.regionRange" 
              placeholder="请输入区域范围"
              readonly
            />
          </el-form-item>

          <el-form-item label="计算方式：">
            <el-input 
              v-model="configForm.calculationMethod" 
              placeholder="请输入计算方式"
              readonly
            />
          </el-form-item>

          <el-form-item label="计算规则：">
            <div class="calculation-rule">
              <span>当数据量累计达到</span>
              <el-input-number 
                v-model="configForm.targetValue" 
                :min="1" 
                :max="10000"
                style="width: 120px; margin: 0 8px;"
              />
              <span>条时，则完成任务</span>
            </div>
          </el-form-item>
        </el-form>
      </div>

<!--      &lt;!&ndash; 当前进度展示 &ndash;&gt;-->
<!--      <div class="current-progress">-->
<!--        <el-divider content-position="left">当前进度</el-divider>-->
<!--        -->
<!--        <div class="progress-info">-->
<!--          <div class="progress-item">-->
<!--            <span class="label">当前数据量：</span>-->
<!--            <span class="value">{{ currentDataCount }} 条</span>-->
<!--          </div>-->
<!--          <div class="progress-item">-->
<!--            <span class="label">目标数据量：</span>-->
<!--            <span class="value">{{ configForm.targetValue }} 条</span>-->
<!--          </div>-->
<!--          <div class="progress-item">-->
<!--            <span class="label">完成进度：</span>-->
<!--            <span class="value">{{ progressPercentage }}%</span>-->
<!--          </div>-->
<!--        </div>-->

<!--        <div class="progress-bar">-->
<!--          <el-progress -->
<!--            :percentage="progressPercentage" -->
<!--            :color="progressColor"-->
<!--            :stroke-width="20"-->
<!--            text-inside-->
<!--          />-->
<!--        </div>-->
<!--      </div>-->

<!--      &lt;!&ndash; 历史记录 &ndash;&gt;-->
<!--      <div class="history-records">-->
<!--        <el-divider content-position="left">进度历史</el-divider>-->
<!--        -->
<!--        <div class="history-list">-->
<!--          <div -->
<!--            v-for="(record, index) in historyRecords" -->
<!--            :key="index"-->
<!--            class="history-item"-->
<!--          >-->
<!--            <div class="history-time">{{ record.time }}</div>-->
<!--            <div class="history-progress">{{ record.progress }}%</div>-->
<!--            <div class="history-data">数据量：{{ record.dataCount }} 条</div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Props {
  modelValue: boolean
  businessData?: any
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  businessData: null
})

const emit = defineEmits(['update:modelValue'])

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 配置表单
const configForm = ref({
  timeRange: 'taskStart',
  customTimeRange: '2024-01-01~2024-12-31',
  taskTimeRange: '2024-01-01~2024-12-31',
  regionRange: '中山路社区、水东门社区、东外街社区',
  calculationMethod: '按数据量累计进度',
  targetValue: 100
})

// 当前数据量（与明细数据保持一致）
const currentDataCount = ref(50)

// 计算进度百分比
const progressPercentage = computed(() => {
  if (configForm.value.targetValue <= 0) return 0
  const percentage = Math.round((currentDataCount.value / configForm.value.targetValue) * 100)
  return Math.min(percentage, 100)
})

// 进度条颜色
const progressColor = computed(() => {
  const percentage = progressPercentage.value
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
})

// 历史记录
const historyRecords = ref([
  {
    time: '2024-01-15',
    progress: 65,
    dataCount: 3247
  },
  {
    time: '2024-01-10',
    progress: 58,
    dataCount: 2890
  },
  {
    time: '2024-01-05',
    progress: 45,
    dataCount: 2250
  },
  {
    time: '2024-01-01',
    progress: 32,
    dataCount: 1600
  }
])

// 关闭弹窗
const handleClose = () => {
  emit('update:modelValue', false)
}

// 监听业务数据变化
watch(() => props.businessData, (newData) => {
  if (newData) {
    // 根据业务数据更新配置
    console.log('更新进度计算配置:', newData)
  }
})
</script>

<style scoped lang="scss">
.progress-calculation-dialog {
  .calculation-config {
    margin-bottom: 24px;

    .calculation-rule {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #606266;
    }
  }

  .current-progress {
    margin-bottom: 24px;

    .progress-info {
      margin-bottom: 16px;

      .progress-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        padding: 8px 0;

        .label {
          color: #606266;
          font-weight: 500;
        }

        .value {
          color: #303133;
          font-weight: 600;
        }
      }
    }

    .progress-bar {
      margin-top: 16px;
    }
  }

  .history-records {
    .history-list {
      max-height: 200px;
      overflow-y: auto;

      .history-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        margin-bottom: 8px;
        background: #f5f7fa;
        border-radius: 8px;
        border-left: 4px solid #409eff;

        .history-time {
          color: #606266;
          font-size: 14px;
        }

        .history-progress {
          color: #409eff;
          font-weight: 600;
          font-size: 16px;
        }

        .history-data {
          color: #909399;
          font-size: 12px;
        }
      }
    }
  }

  :deep(.el-divider__text) {
    font-weight: 600;
    color: #303133;
  }
}
</style>

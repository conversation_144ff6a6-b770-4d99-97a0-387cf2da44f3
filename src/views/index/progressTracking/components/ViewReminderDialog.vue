<template>
  <Dialog
    v-model="visible"
    title="任务提醒"
    width="1200px"
    :destroy-on-close="true"
    :visible-confirm-button="false"
    cancel-text="关闭"
    @click-cancel="handleClose"
    @closed="handleClose"
  >
    <div class="view-reminder-dialog">
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="请输入关键词、提醒内容">
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入关键词、提醒内容"
              clearable
              style="width: 300px"
            />
          </el-form-item>
          <el-form-item label="请选择状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 150px"
            >
              <el-option label="已发送待确认" value="已发送待确认" />
              <el-option label="已确认" value="已确认" />
              <el-option label="待发送" value="待发送" />
              <el-option label="已撤回" value="已撤回" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮区域 -->
      <div class="operation-buttons" style="margin-bottom: 16px;">
        <el-button 
          type="danger" 
          :disabled="selectedRows.length === 0"
          @click="handleBatchDelete"
        >
          删除选中
        </el-button>
        <el-button 
          type="danger" 
          @click="handleDeleteAll"
        >
          全部删除
        </el-button>
        <el-button 
          type="warning" 
          :disabled="selectedRows.length === 0"
          @click="handleBatchRecall"
        >
          撤回选中
        </el-button>
      </div>

      <!-- 提醒列表 -->
      <div class="table-area">
        <el-table
          v-loading="loading"
          :data="filteredReminderList"
          border
          style="width: 100%"
          max-height="400"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="序号" width="80">
            <template #default="{ $index }">
              {{ (pagination.page - 1) * pagination.size + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="reminderTitle" label="提醒标题" min-width="150" show-overflow-tooltip />
          <el-table-column prop="reminderContent" label="提醒内容" min-width="200" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="120">
            <template #default="{ row }">
              <el-tag
                :type="getStatusType(row.status)"
                size="small"
              >
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="最近提醒时间" width="180" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="warning"
                size="small"
                @click="handleRecall(row)"
              >
                撤回
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-area" style="margin-top: 16px; text-align: right;">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 编辑提醒弹窗 -->
    <SendReminderDialog
      v-model="showEditDialog"
      :task-data="currentTaskData"
      :reminder-data="currentReminderData"
      @saved="handleReminderSaved"
    />
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// Dialog 组件已全局注册，无需导入
import SendReminderDialog from './SendReminderDialog.vue'

interface Props {
  modelValue: boolean
  taskData?: any
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  taskData: null
})

const emit = defineEmits(['update:modelValue'])

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 数据状态
const loading = ref(false)
const reminderList = ref([])
const selectedRows = ref([])

// 搜索表单
const searchForm = ref({
  keyword: '',
  status: ''
})

// 分页
const pagination = ref({
  page: 1,
  size: 10,
  total: 0
})

// 编辑弹窗
const showEditDialog = ref(false)
const currentTaskData = ref(null)
const currentReminderData = ref(null)

// 过滤后的提醒列表
const filteredReminderList = computed(() => {
  let filtered = reminderList.value

  // 根据任务ID过滤
  if (props.taskData?.id) {
    filtered = filtered.filter(item => item.taskId === props.taskData.id)
  }

  // 关键词搜索
  if (searchForm.value.keyword) {
    const keyword = searchForm.value.keyword.toLowerCase()
    filtered = filtered.filter(item => 
      item.reminderTitle?.toLowerCase().includes(keyword) ||
      item.reminderContent?.toLowerCase().includes(keyword)
    )
  }

  // 状态过滤
  if (searchForm.value.status) {
    filtered = filtered.filter(item => item.status === searchForm.value.status)
  }

  // 分页
  const start = (pagination.value.page - 1) * pagination.value.size
  const end = start + pagination.value.size
  pagination.value.total = filtered.length

  return filtered.slice(start, end)
})

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '已发送待确认':
      return 'success'
    case '已确认':
      return 'info'
    case '待发送':
      return 'warning'
    case '已撤回':
      return 'danger'
    default:
      return 'info'
  }
}

// 加载提醒数据
const loadReminderData = () => {
  loading.value = true
  try {
    const data = JSON.parse(localStorage.getItem('progressTrackingReminders') || '[]')
    reminderList.value = data
  } catch (error) {
    console.error('加载提醒数据失败:', error)
    reminderList.value = []
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.value.page = 1
}

// 重置
const handleReset = () => {
  searchForm.value = {
    keyword: '',
    status: ''
  }
  pagination.value.page = 1
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 撤回单条
const handleRecall = (row: any) => {
  ElMessageBox.confirm('确定要撤回这条提醒吗？', '确认撤回', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    row.status = '已撤回'
    saveReminderData()
    ElMessage.success('撤回成功')
  })
}

// 编辑
const handleEdit = (row: any) => {
  currentReminderData.value = row
  currentTaskData.value = props.taskData
  showEditDialog.value = true
}

// 删除单条
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除这条提醒吗？', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = reminderList.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      reminderList.value.splice(index, 1)
      saveReminderData()
      ElMessage.success('删除成功')
    }
  })
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的提醒')
    return
  }

  ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 条提醒吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const selectedIds = selectedRows.value.map(item => item.id)
    reminderList.value = reminderList.value.filter(item => !selectedIds.includes(item.id))
    saveReminderData()
    selectedRows.value = []
    ElMessage.success('批量删除成功')
  })
}

// 全部删除
const handleDeleteAll = () => {
  ElMessageBox.confirm('确定要删除所有提醒吗？', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    if (props.taskData?.id) {
      // 只删除当前任务的提醒
      reminderList.value = reminderList.value.filter(item => item.taskId !== props.taskData.id)
    } else {
      // 删除所有提醒
      reminderList.value = []
    }
    saveReminderData()
    ElMessage.success('全部删除成功')
  })
}

// 批量撤回
const handleBatchRecall = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要撤回的提醒')
    return
  }

  ElMessageBox.confirm(`确定要撤回选中的 ${selectedRows.value.length} 条提醒吗？`, '确认撤回', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    selectedRows.value.forEach(row => {
      row.status = '已撤回'
    })
    saveReminderData()
    selectedRows.value = []
    ElMessage.success('批量撤回成功')
  })
}

// 保存提醒数据
const saveReminderData = () => {
  localStorage.setItem('progressTrackingReminders', JSON.stringify(reminderList.value))
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.page = 1
}

const handleCurrentChange = (page: number) => {
  pagination.value.page = page
}

// 提醒保存回调
const handleReminderSaved = () => {
  loadReminderData()
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 监听弹窗显示状态，重新加载数据
watch(() => props.modelValue, (newVisible) => {
  if (newVisible) {
    loadReminderData()
  }
})

// 组件挂载时加载数据
onMounted(() => {
  loadReminderData()
})
</script>

<style scoped lang="scss">
.view-reminder-dialog {
  .search-area {
    margin-bottom: 16px;
    padding: 16px;
    background: #f5f7fa;
    border-radius: 4px;
  }

  .operation-buttons {
    display: flex;
    gap: 8px;
  }

  .table-area {
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }

  .pagination-area {
    margin-top: 16px;
  }
}
</style>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useProgressTrackingStore } from '../composables/useProgressTrackingStore'

interface Props {
  modelValue: boolean
  taskId: string
}

const props = defineProps<Props>()
const emits = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 数据存储
const store = useProgressTrackingStore()

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emits('update:modelValue', value)
})

// 搜索表单
const searchForm = ref({
  taskName: '',
  status: ''
})

// 子任务列表
const subTaskList = ref([])
const loading = ref(false)

// 分页
const pagination = ref({
  page: 1,
  size: 10,
  total: 0
})

// 表头
const columns = [
  { prop: 'sequence', label: '序号', width: 80 },
  { prop: 'taskName', label: '任务名称' },
  { prop: 'completionTime', label: '完成截止时间' },
  { prop: 'businessService', label: '关联业务表' },
  { prop: 'progress', label: '进度', width: 100 },
  { prop: 'remainingWork', label: '剩余工作量' },
  { prop: 'taskDeadline', label: '任务时效' },
  { prop: 'trackingRule', label: '进度追踪规则' }
]

// 加载子任务数据
const loadSubTasks = async () => {
  if (!props.taskId) return
  
  loading.value = true
  try {
    const data = await store.getSubTaskList(props.taskId, {
      taskName: searchForm.value.taskName,
      status: searchForm.value.status,
      page: pagination.value.page,
      size: pagination.value.size
    })
    subTaskList.value = data.list
    pagination.value.total = data.total
  } catch (error) {
    console.error('加载子任务数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const onSearch = () => {
  pagination.value.page = 1
  loadSubTasks()
}

// 重置
const onReset = () => {
  searchForm.value = {
    taskName: '',
    status: ''
  }
  onSearch()
}

// 分页事件
const onPaginationChange = (val: any, type: string) => {
  if (type === 'page') {
    pagination.value.page = val
  } else {
    pagination.value.size = val
    pagination.value.page = 1
  }
  loadSubTasks()
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  // 重置数据
  subTaskList.value = []
  searchForm.value = {
    taskName: '',
    status: ''
  }
  pagination.value = {
    page: 1,
    size: 10,
    total: 0
  }
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.taskId) {
    loadSubTasks()
  }
})
</script>

<template>
  <Dialog
    v-model="visible"
    title="子任务"
    width="1200px"
    :destroy-on-close="true"
    :visible-confirm-button="false"
    cancel-text="关闭"
    @click-cancel="handleClose"
    @closed="handleClose"
  >
    <div class="sub-task-dialog">
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="任务名称">
            <el-input
              v-model="searchForm.taskName"
              placeholder="请输入任务名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="进度状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择进度状态"
              clearable
              style="width: 150px"
            >
              <el-option label="进行中" value="进行中" />
              <el-option label="已完成" value="已完成" />
              <el-option label="已暂停" value="已暂停" />
              <el-option label="已取消" value="已取消" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 子任务列表 -->
      <div class="table-area">
        <el-table
          v-loading="loading"
          :data="subTaskList"
          border
          style="width: 100%"
          max-height="400"
        >
          <el-table-column
            v-for="column in columns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            show-overflow-tooltip
          >
            <template v-if="column.prop === 'progress'" #default="{ row }">
              <el-progress :percentage="row.progress" :width="50" />
            </template>
            <template v-else-if="column.prop === 'trackingRule'" #default="{ row }">
              <el-tag
                :type="getTrackingRuleType(row.trackingRule)"
                size="small"
              >
                {{ row.trackingRule }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-area">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onPaginationChange($event, 'size')"
          @current-change="onPaginationChange($event, 'page')"
        />
      </div>
    </div>
  </Dialog>
</template>

<script lang="ts">
// 获取追踪规则标签类型
const getTrackingRuleType = (rule: string) => {
  const typeMap: Record<string, string> = {
    '重点项目进度规则': 'danger',
    '日常任务进度规则': 'warning', 
    '年度考核进度规则': 'success',
    '民生项目进度规则': 'info',
    '临时任务进度规则': 'primary'
  }
  return typeMap[rule] || 'default'
}
</script>

<style scoped lang="scss">
.sub-task-dialog {
  .search-area {
    margin-bottom: 16px;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .table-area {
    margin-bottom: 16px;
  }

  .pagination-area {
    display: flex;
    justify-content: flex-end;
  }
}
</style>

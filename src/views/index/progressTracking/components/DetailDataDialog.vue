<template>
  <Dialog
    v-model="visible"
    title="明细数据"
    width="1200px"
    :destroy-on-close="true"
    :show-confirm-button="false"
    cancel-text="关闭"
    @closed="handleClose"
  >
    <div class="detail-data-dialog">
      <!-- 明细数据表格 -->
      <div class="detail-table">
        <el-table
          v-loading="loading"
          :data="detailData"
          border
          style="width: 100%"
          max-height="400"
        >
          <el-table-column prop="sequence" label="序号" width="80" />
          <el-table-column prop="community" label="所属村社" min-width="120" />
          <el-table-column prop="name" label="姓名" width="100" />
          <el-table-column prop="gender" label="性别" width="80" />
          <el-table-column prop="birthDate" label="出生日期" width="120" />
          <el-table-column prop="idCard" label="身份证号" min-width="180" />
          <el-table-column prop="education" label="学历" width="100" />
          <el-table-column prop="currentAddress" label="现在家庭住址" min-width="200" />
          <el-table-column prop="phone" label="联系电话" width="130" />
          <el-table-column prop="hasTraining" label="是否提供免费就业培训" width="160" />
          <el-table-column prop="hasRecruitment" label="是否提供招聘" width="120" />
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-area">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Props {
  modelValue: boolean
  businessData?: any
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  businessData: null
})

const emit = defineEmits(['update:modelValue'])

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 数据状态
const loading = ref(false)
const detailData = ref([])

// 分页
const pagination = ref({
  page: 1,
  size: 10,
  total: 0
})

// 模拟明细数据
const mockDetailData = [
  {
    sequence: '01',
    community: '中山路社区',
    name: '李海',
    gender: '男',
    birthDate: '1989-03-02',
    idCard: '500***********19',
    education: '初中',
    currentAddress: '重庆市-渝北区',
    phone: '152****4744',
    hasTraining: '是',
    hasRecruitment: '是'
  },
  {
    sequence: '02',
    community: '中山路社区',
    name: '张婷',
    gender: '男',
    birthDate: '1991-12-21',
    idCard: '500***********1X',
    education: '本科',
    currentAddress: '重庆市-渝北区',
    phone: '182****7127',
    hasTraining: '是',
    hasRecruitment: '是'
  },
  {
    sequence: '03',
    community: '中山路社区',
    name: '刘树林',
    gender: '男',
    birthDate: '1992-06-04',
    idCard: '500***********3J',
    education: '本科',
    currentAddress: '重庆市-渝北区',
    phone: '157****3386',
    hasTraining: '是',
    hasRecruitment: '是'
  },
  {
    sequence: '04',
    community: '中山路社区',
    name: '刘闯',
    gender: '男',
    birthDate: '1994-04-12',
    idCard: '500***********36',
    education: '专科',
    currentAddress: '重庆市-巴南区',
    phone: '156****2903',
    hasTraining: '是',
    hasRecruitment: '否'
  },
  {
    sequence: '05',
    community: '中山路社区',
    name: '李俊峰',
    gender: '男',
    birthDate: '1993-03-24',
    idCard: '500***********1J',
    education: '专科',
    currentAddress: '重庆市-巴南区',
    phone: '138****2006',
    hasTraining: '是',
    hasRecruitment: '是'
  },
  {
    sequence: '06',
    community: '中山路社区',
    name: '张海燕',
    gender: '女',
    birthDate: '1997-11-23',
    idCard: '500***********17',
    education: '本科',
    currentAddress: '重庆市-巴南区',
    phone: '135****7857',
    hasTraining: '是',
    hasRecruitment: '是'
  },
  {
    sequence: '07',
    community: '中山路社区',
    name: '刘艳梅',
    gender: '女',
    birthDate: '1986-08-12',
    idCard: '500***********30',
    education: '初中',
    currentAddress: '重庆市-九龙坡区',
    phone: '158****9233',
    hasTraining: '是',
    hasRecruitment: '是'
  },
  {
    sequence: '08',
    community: '中山路社区',
    name: '谢苏飞',
    gender: '男',
    birthDate: '1997-07-03',
    idCard: '500***********3X',
    education: '高中',
    currentAddress: '重庆市-渝北区',
    phone: '139****5131',
    hasTraining: '否',
    hasRecruitment: '否'
  },
  {
    sequence: '09',
    community: '中山路社区',
    name: '张磊',
    gender: '男',
    birthDate: '1997-04-18',
    idCard: '500***********18',
    education: '高中',
    currentAddress: '重庆市-九龙坡区',
    phone: '159****6566',
    hasTraining: '是',
    hasRecruitment: '是'
  },
  {
    sequence: '10',
    community: '中山路社区',
    name: '万彩彬',
    gender: '男',
    birthDate: '1990-10-03',
    idCard: '500***********17',
    education: '本科',
    currentAddress: '重庆市-大渡口',
    phone: '173****3156',
    hasTraining: '是',
    hasRecruitment: '否'
  }
]

// 生成更多模拟数据，总共50条
const generateMoreData = () => {
  const additionalData = []
  const communities = ['水东门社区', '东外街社区', '南山路社区', '北环路社区']
  const names = ['王强', '李娜', '陈明', '赵丽', '孙伟', '周芳', '吴刚', '郑红', '冯军', '何静']
  const educations = ['初中', '高中', '专科', '本科', '研究生']
  const addresses = ['重庆市-渝北区', '重庆市-巴南区', '重庆市-九龙坡区', '重庆市-大渡口', '重庆市-沙坪坝区']

  for (let i = 11; i <= 50; i++) {
    const nameIndex = (i - 11) % names.length
    const communityIndex = Math.floor((i - 11) / 10) % communities.length
    const educationIndex = (i - 11) % educations.length
    const addressIndex = (i - 11) % addresses.length
    const hasTraining = Math.random() > 0.2 ? '是' : '否'
    const hasRecruitment = hasTraining === '是' ? (Math.random() > 0.3 ? '是' : '否') : '否'

    additionalData.push({
      sequence: i.toString().padStart(2, '0'),
      community: communities[communityIndex],
      name: names[nameIndex],
      gender: Math.random() > 0.5 ? '男' : '女',
      birthDate: `19${85 + Math.floor(Math.random() * 15)}-${(Math.floor(Math.random() * 12) + 1).toString().padStart(2, '0')}-${(Math.floor(Math.random() * 28) + 1).toString().padStart(2, '0')}`,
      idCard: `500***********${Math.floor(Math.random() * 100).toString().padStart(2, '0')}`,
      education: educations[educationIndex],
      currentAddress: addresses[addressIndex],
      phone: `1${Math.floor(Math.random() * 9) + 3}${Math.floor(Math.random() * 10)}****${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
      hasTraining,
      hasRecruitment
    })
  }
  return additionalData
}

// 合并所有数据
const allMockData = [...mockDetailData, ...generateMoreData()]

// 统计数据已移除

// 加载明细数据
const loadDetailData = () => {
  loading.value = true

  // 模拟异步加载
  setTimeout(() => {
    const start = (pagination.value.page - 1) * pagination.value.size
    const end = start + pagination.value.size
    detailData.value = allMockData.slice(start, end)
    pagination.value.total = allMockData.length
    loading.value = false
  }, 500)
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.page = 1
  loadDetailData()
}

const handleCurrentChange = (page: number) => {
  pagination.value.page = page
  loadDetailData()
}

// 关闭弹窗
const handleClose = () => {
  emit('update:modelValue', false)
}

// 监听弹窗显示状态
watch(() => props.modelValue, (visible) => {
  if (visible) {
    loadDetailData()
  }
})
</script>

<style scoped lang="scss">
.detail-data-dialog {
  .data-stats {
    margin-bottom: 20px;
    padding: 16px;
    background: #f5f7fa;
    border-radius: 8px;

    .stat-item {
      text-align: center;
      padding: 16px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: #409eff;
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 14px;
        color: #606266;
      }
    }
  }

  .detail-table {
    margin-bottom: 16px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }

  .pagination-area {
    text-align: right;
  }
}
</style>

<template>
  <Dialog
    v-model="visible"
    title="创建提醒内容"
    width="600px"
    :destroy-on-close="true"
    :loading="loading"
    loading-text="保存中"
    @closed="handleClose"
    @click-confirm="onDialogConfirm"
  >
    <el-form
      ref="dialogFormRef"
      :model="dialogForm"
      :rules="dialogFormRules"
      label-width="120px"
    >
      <el-form-item label="提醒标题" prop="reminderTitle">
        <el-input
          v-model="dialogForm.reminderTitle"
          placeholder="请输入提醒标题"
        />
      </el-form-item>

      <el-form-item label="提醒内容" prop="reminderContent">
        <el-input
          v-model="dialogForm.reminderContent"
          type="textarea"
          placeholder="请输入提醒内容"
        />
      </el-form-item>

      <el-form-item label="关联任务">
        <el-input
          :value="dialogForm.relatedTask"
          readonly
          placeholder="自动关联当前任务"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="接收人" prop="recipients">
        <el-select
          v-model="dialogForm.recipients"
          multiple
          placeholder="请选择接收人"
          style="width: 100%"
        >
          <el-option
            v-for="option in recipientOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="提醒时间" prop="reminderTime">
        <el-select
          v-model="dialogForm.reminderTime"
          placeholder="请选择提醒时间"
          style="width: 100%"
        >
          <el-option
            v-for="option in reminderTimeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <!-- 动态时间字段 -->
      <el-form-item
        v-if="dialogForm.reminderTime === '每天'"
        label="具体时间"
        prop="specificDateTime"
      >
        <el-time-picker
          v-model="dialogForm.specificDateTime"
          format="HH:mm:ss"
          value-format="HH:mm:ss"
          placeholder="请选择时分秒"
          style="width: 100%"
        />
      </el-form-item>

      <template v-if="dialogForm.reminderTime === '每周'">
        <el-form-item label="星期" prop="weekDay">
          <el-select
            v-model="dialogForm.weekDay"
            placeholder="请选择星期"
            style="width: 100%"
          >
            <el-option
              v-for="option in weekDayOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="具体时间" prop="specificTime">
          <el-time-picker
            v-model="dialogForm.specificTime"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            placeholder="请选择时分秒"
            style="width: 100%"
          />
        </el-form-item>
      </template>

      <el-form-item
        v-if="dialogForm.reminderTime === '工作日'"
        label="具体时间"
        prop="specificDateTime"
      >
        <el-time-picker
          v-model="dialogForm.specificDateTime"
          format="HH:mm:ss"
          value-format="HH:mm:ss"
          placeholder="请选择时分秒"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item
        v-if="dialogForm.reminderTime === '按进度'"
        label="进度达到"
        prop="progressThreshold"
      >
        <el-input-number
          v-model="dialogForm.progressThreshold"
          :min="0"
          :max="100"
          placeholder="请输入进度百分比（0-100）"
          style="width: 50%"
        />
        <span style="margin-left: 8px; color: #909399;">%</span>
      </el-form-item>

      <el-form-item label="保存并发送" prop="saveAndSend">
        <el-switch v-model="dialogForm.saveAndSend" />
      </el-form-item>
    </el-form>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
// Dialog 和 Form 组件已全局注册，无需导入

interface Props {
  modelValue: boolean
  taskData?: any
  reminderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  taskData: null,
  reminderData: null
})

const emit = defineEmits(['update:modelValue', 'saved'])

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单ref
const dialogFormRef = ref()
const loading = ref(false)

// 表单数据
const dialogForm = ref({
  reminderTitle: '',
  reminderContent: '',
  relatedTask: '',
  recipients: [],
  reminderTime: '立即发送',
  specificDateTime: '',
  weekDay: '',
  specificTime: '',
  progressThreshold: '',
  saveAndSend: false
})

// taskOptions 已移除，关联任务自动填充

// 模拟接收人数据
const recipientOptions = ref([
  { label: '张原长', value: '张原长' },
  { label: '李主任', value: '李主任' },
  { label: '王经理', value: '王经理' },
  { label: '刘总监', value: '刘总监' },
  { label: '陈主管', value: '陈主管' }
])

// 提醒时间选项
const reminderTimeOptions = ref([
  { label: '立即发送', value: '立即发送' },
  { label: '每天', value: '每天' },
  { label: '每周', value: '每周' },
  { label: '工作日', value: '工作日' },
  { label: '按进度', value: '按进度' }
])

// 星期选项
const weekDayOptions = ref([
  { label: '周一', value: '1' },
  { label: '周二', value: '2' },
  { label: '周三', value: '3' },
  { label: '周四', value: '4' },
  { label: '周五', value: '5' },
  { label: '周六', value: '6' },
  { label: '周日', value: '0' }
])

// 表单配置已移除，现在使用直接的 el-form 配置

// 表单验证规则
const dialogFormRules = {
  reminderTitle: [
    { required: true, message: '请输入提醒标题', trigger: 'blur' }
  ],
  reminderContent: [
    { required: true, message: '请输入提醒内容', trigger: 'blur' }
  ],
  recipients: [
    { required: true, message: '请选择接收人', trigger: 'change' }
  ],
  reminderTime: [
    { required: true, message: '请选择提醒时间', trigger: 'change' }
  ],
  specificDateTime: [
    { required: true, message: '请选择具体时间', trigger: 'change' }
  ],
  weekDay: [
    { required: true, message: '请选择星期', trigger: 'change' }
  ],
  specificTime: [
    { required: true, message: '请选择具体时间', trigger: 'change' }
  ],
  progressThreshold: [
    { required: true, message: '请输入进度百分比', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '进度百分比必须在0-100之间', trigger: 'blur' }
  ]
}

// 监听提醒数据变化，用于编辑模式
watch(() => props.reminderData, (newReminderData) => {
  if (newReminderData && newReminderData.id) {
    // 只有在编辑模式（有id）时才回显数据
    dialogForm.value = {
      reminderTitle: newReminderData.reminderTitle || '',
      reminderContent: newReminderData.reminderContent || '',
      relatedTask: newReminderData.relatedTask || '',
      recipients: newReminderData.recipients || [],
      reminderTime: newReminderData.reminderTime || '立即发送',
      specificDateTime: newReminderData.specificDateTime || '',
      weekDay: newReminderData.weekDay || '',
      specificTime: newReminderData.specificTime || '',
      progressThreshold: newReminderData.progressThreshold || '',
      saveAndSend: newReminderData.saveAndSend || false
    }
  }
}, { immediate: true })

// 监听任务数据变化，自动填充关联任务（优先级更高，在reminderData监听器之后）
watch(() => props.taskData, (newTaskData) => {
  if (newTaskData && newTaskData.taskName) {
    dialogForm.value.relatedTask = newTaskData.taskName
  }
}, { immediate: true })

// 监听提醒时间类型变化，清空相关字段
watch(() => dialogForm.value.reminderTime, (newType, oldType) => {
  if (newType !== oldType) {
    // 清空时间相关字段
    dialogForm.value.specificDateTime = ''
    dialogForm.value.weekDay = ''
    dialogForm.value.specificTime = ''
    dialogForm.value.progressThreshold = ''
  }
})

// 根据提醒时间类型确定提醒状态
const getStatusByReminderTime = (reminderTime: string, saveAndSend: boolean) => {
  if (reminderTime === '立即发送') {
    return '已发送待确认'
  } else {
    // 每天、每周、工作日、按进度都是预设时间，状态为待发送
    return '待发送'
  }
}

// 生成递增的ID
const generateIncrementalId = () => {
  const existingReminders = JSON.parse(localStorage.getItem('progressTrackingReminders') || '[]')
  if (existingReminders.length === 0) {
    return 1
  }
  // 找到最大的ID并加1
  const maxId = Math.max(...existingReminders.map(item => parseInt(item.id) || 0))
  return maxId + 1
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  // 重置表单，但保留关联任务
  const currentRelatedTask = dialogForm.value.relatedTask
  dialogForm.value = {
    reminderTitle: '',
    reminderContent: '',
    relatedTask: currentRelatedTask, // 保留关联任务
    recipients: [],
    reminderTime: '立即发送',
    specificDateTime: '',
    weekDay: '',
    specificTime: '',
    progressThreshold: '',
    saveAndSend: false
  }
}

// 确认保存
const onDialogConfirm = async () => {
  if (!dialogFormRef.value) return

  try {
    await dialogFormRef.value.validate()
    loading.value = true

    const existingReminders = JSON.parse(localStorage.getItem('progressTrackingReminders') || '[]')

    if (props.reminderData) {
      // 编辑模式
      const index = existingReminders.findIndex(item => item.id === props.reminderData.id)
      if (index > -1) {
        existingReminders[index] = {
          ...props.reminderData,
          ...dialogForm.value,
          status: getStatusByReminderTime(dialogForm.value.reminderTime, dialogForm.value.saveAndSend),
          updateTime: new Date().toLocaleString(),
          taskId: props.taskData?.id || props.taskData?.序号 || existingReminders[index].taskId,
          taskName: props.taskData?.taskName || dialogForm.value.relatedTask || existingReminders[index].taskName
        }
      }
      ElMessage.success('提醒编辑成功')
    } else {
      // 新增模式
      const reminderData = {
        id: generateIncrementalId(),
        ...dialogForm.value,
        status: getStatusByReminderTime(dialogForm.value.reminderTime, dialogForm.value.saveAndSend),
        createTime: new Date().toLocaleString(),
        taskId: props.taskData?.id || props.taskData?.序号 || 'default',
        taskName: props.taskData?.taskName || dialogForm.value.relatedTask
      }
      existingReminders.push(reminderData)
      ElMessage.success('提醒创建成功')
    }

    // 保存到localStorage
    localStorage.setItem('progressTrackingReminders', JSON.stringify(existingReminders))

    emit('saved', dialogForm.value)
    handleClose()
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
// 样式可以根据需要添加
</style>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useProgressTrackingStore } from '../composables/useProgressTrackingStore'

interface Props {
  modelValue: boolean
  mode: 'add' | 'edit' | 'view'
  ruleData?: any
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'add',
  ruleData: null
})

const emits = defineEmits<{
  'update:modelValue': [value: boolean]
  'saved': []
}>()

// 数据存储
const store = useProgressTrackingStore()

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emits('update:modelValue', value)
})

// 加载状态
const loading = ref(false)

// 表单ref
const dialogFormRef = ref()

// 弹窗标题
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'add':
      return '创建进度追踪规则'
    case 'edit':
      return '编辑进度追踪规则'
    case 'view':
      return '查看进度追踪规则'
    default:
      return '进度追踪规则'
  }
})

// 表单数据
const dialogForm = ref({
  ruleName: '',
  status: '启用中',
  description: '',
  priority: '高',
  ruleType: '项目进度',
  responsible: '',
  department: '',
  progressFormula: '',
  triggerCondition: ''
})

// 表单属性
const dialogFormProps = computed(() => [
  { label: '规则名称', prop: 'ruleName', type: 'text', required: true },
  {
    label: '状态',
    prop: 'status',
    type: 'select',
    options: [
      { label: '启用中', value: '启用中' },
      { label: '已停用', value: '已停用' }
    ]
  },
  { label: '规则描述', prop: 'description', type: 'textarea' },
  {
    label: '优先级',
    prop: 'priority',
    type: 'select',
    options: [
      { label: '高', value: '高' },
      { label: '中', value: '中' },
      { label: '低', value: '低' }
    ]
  },
  {
    label: '规则类型',
    prop: 'ruleType',
    type: 'select',
    options: [
      { label: '项目进度', value: '项目进度' },
      { label: '任务进度', value: '任务进度' },
      { label: '工作量进度', value: '工作量进度' },
      { label: '时间进度', value: '时间进度' }
    ]
  },
  { label: '负责人', prop: 'responsible', type: 'text' },
  { label: '所属部门', prop: 'department', type: 'text' },
  {
    label: '进度计算公式',
    prop: 'progressFormula',
    type: 'textarea',
    placeholder: '(已完成任务数 / 总任务数) * 100'
  },
  {
    label: '触发条件',
    prop: 'triggerCondition',
    type: 'textarea',
    placeholder: '当任务状态变更为"进行中"时开始计算进度'
  }
])

// 表单校验规则
const dialogFormRules = {
  ruleName: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  ruleType: [{ required: true, message: '请选择规则类型', trigger: 'change' }]
}

// 是否只读模式
const isReadonly = computed(() => props.mode === 'view')

// 是否显示确认按钮
const showConfirmButton = computed(() => props.mode !== 'view')

// 确认按钮文本
const confirmText = computed(() => {
  return props.mode === 'add' ? '保存规则' : '保存修改'
})

// 格式化日期
const formatDate = (date: Date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 表单提交
const onDialogConfirm = () => {
  if (isReadonly.value) return

  dialogFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true
      try {
        const currentTime = formatDate(new Date())

        if (props.mode === 'add') {
          // 新增时设置创建时间和更新时间
          const ruleData = {
            ...dialogForm.value,
            // 如果用户没有输入，使用默认值
            progressFormula: dialogForm.value.progressFormula || '(已完成任务数 / 总任务数) * 100',
            triggerCondition: dialogForm.value.triggerCondition || '当任务状态变更为"进行中"时开始计算进度',
            createTime: currentTime,
            updateTime: currentTime,
            creator: '盛书华' // 这里可以从用户信息中获取
          }
          await store.createRule(ruleData)
          ElMessage.success('创建成功')
        } else if (props.mode === 'edit') {
          // 编辑时只更新最后更新时间
          const ruleData = {
            ...dialogForm.value,
            updateTime: currentTime
          }
          await store.updateRule(props.ruleData.id, ruleData)
          ElMessage.success('编辑成功')
        }
        emits('saved')
      } catch (error) {
        ElMessage.error(props.mode === 'add' ? '创建失败' : '编辑失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  dialogForm.value = {
    ruleName: '',
    status: '启用中',
    description: '',
    priority: '高',
    ruleType: '项目进度',
    responsible: '',
    department: '',
    progressFormula: '',
    triggerCondition: ''
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  resetForm()
}

// 监听弹窗打开，初始化表单数据
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    if (props.mode === 'edit' || props.mode === 'view') {
      if (props.ruleData) {
        Object.assign(dialogForm.value, props.ruleData)
      }
    } else {
      // 新增模式，重置表单
      resetForm()
    }
  }
})
</script>

<template>
  <Dialog
    v-model="visible"
    :title="dialogTitle"
    width="600px"
    :destroy-on-close="true"
    :loading="loading"
    loading-text="保存中"
    :visible-confirm-button="showConfirmButton"
    :confirm-text="confirmText"
    @closed="handleClose"
    @click-confirm="onDialogConfirm"
  >
    <Form
      ref="dialogFormRef"
      v-model="dialogForm"
      :props="dialogFormProps"
      :rules="dialogFormRules"
      :enable-button="false"
      :disabled="isReadonly"
    />
  </Dialog>
</template>

<style scoped lang="scss">
// 样式可以根据需要添加
</style>

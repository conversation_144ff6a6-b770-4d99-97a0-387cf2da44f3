<script setup lang="ts" name="progressTracking">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'
import SubTaskDialog from './components/SubTaskDialog.vue'
import SendReminderDialog from './components/SendReminderDialog.vue'
import ViewReminderDialog from './components/ViewReminderDialog.vue'

// 路由
const $router = useRouter()

// 搜索表单
const searchFormProp = ref([
  { label: '任务名称', prop: 'taskName', type: 'text' },
  {
    label: '所属板块',
    prop: 'sector',
    type: 'select',
    options: [
      { label: '党的建设', value: '党的建设' },
      { label: '经济发展', value: '经济发展' },
      { label: '民生服务', value: '民生服务' },
      { label: '平安法治', value: '平安法治' }
    ]
  },
  {
    label: '优先级',
    prop: 'priority',
    type: 'select',
    options: [
      { label: '高', value: '高' },
      { label: '中', value: '中' },
      { label: '低', value: '低' }
    ]
  },
  { label: '牵头领导', prop: 'leadLeader', type: 'text' },
  { label: '责任人', prop: 'responsible', type: 'text' },
  { label: '任务时效', prop: 'taskDeadlineRange', type: 'daterange' },
  { label: '进度范围起', prop: 'progressStart', type: 'text', inputType: 'number', placeholder: '请输入起始进度' },
  { label: '进度范围止', prop: 'progressEnd', type: 'text', inputType: 'number', placeholder: '请输入结束进度' }
])

const searchForm = ref({
  taskName: '',
  sector: '',
  priority: '',
  leadLeader: '',
  responsible: '',
  taskDeadlineRange: [],
  progressStart: '',
  progressEnd: ''
})

// 加载状态
const loading = ref(false)

// 表格ref
const tableRef = ref()
const tableHeight = ref(0)
const currentRow = ref(null)

// 模拟数据
const mockProgressTrackingData = [
  {
    id: 1,
    taskName: '建立村级便民服务',
    taskType: '数据报表',
    sector: '党的建设',
    leadLeader: '陈站',
    responsible: '陈站',
    priority: '高',
    progress: 65,
    remainingWork: '服务人员培训和设备调试',
    taskDeadline: '2024-01-01~2024-12-31',
    trackingRule: '重点项目进度规则'
  },
  {
    id: 2,
    taskName: '开展重点人群就业帮扶任务',
    taskType: '审批流程',
    sector: '经济发展',
    leadLeader: '刘树林',
    responsible: '刘树林',
    priority: '中',
    progress: 45,
    remainingWork: '就业技能培训实施',
    taskDeadline: '2024-01-01~2024-12-31',
    trackingRule: '日常任务进度规则'
  },
  {
    id: 3,
    taskName: '开展就业帮扶技能培训',
    taskType: '数据采集',
    sector: '民生服务',
    leadLeader: '李松',
    responsible: '李松',
    priority: '低',
    progress: 35,
    remainingWork: '培训场地准备和师资招募',
    taskDeadline: '2024-01-01~2024-12-31',
    trackingRule: '年度考核进度规则'
  },
  {
    id: 4,
    taskName: '投工作岗位开发业务受理',
    taskType: '数据上报',
    sector: '平安法治',
    leadLeader: '牛振祖、陈站',
    responsible: '牛振祖、陈站',
    priority: '高',
    progress: 75,
    remainingWork: '岗位信息发布和推广',
    taskDeadline: '2024-01-01~2024-12-31',
    trackingRule: '民生项目进度规则'
  },
  {
    id: 5,
    taskName: '开展就业创业帮扶',
    taskType: '分析报告',
    sector: '党的建设',
    leadLeader: '盛泽',
    responsible: '盛泽',
    priority: '中',
    progress: 20,
    remainingWork: '创业政策宣传和资金扶持',
    taskDeadline: '2024-01-01~2024-12-31',
    trackingRule: '临时任务进度规则'
  },
  {
    id: 6,
    taskName: '"送文化下基层"文艺演出服务',
    taskType: '数据报表',
    sector: '经济发展',
    leadLeader: '周梦玲',
    responsible: '周梦玲',
    priority: '低',
    progress: 85,
    remainingWork: '演出效果评估和总结',
    taskDeadline: '2024-01-01~2024-12-31',
    trackingRule: '重点项目进度规则'
  },
  {
    id: 7,
    taskName: '管理易走失流浪乞讨人员',
    taskType: '审批流程',
    sector: '民生服务',
    leadLeader: '李松',
    responsible: '李松',
    priority: '高',
    progress: 25,
    remainingWork: '剩余各数据报表未完成',
    taskDeadline: '2024-01-01~2024-12-31',
    trackingRule: '日常任务进度规则'
  },
  {
    id: 8,
    taskName: '城乡居民养老保险参保登记',
    taskType: '数据采集',
    sector: '平安法治',
    leadLeader: '程锐',
    responsible: '程锐',
    priority: '中',
    progress: 25,
    remainingWork: '剩余各数据报表未完成',
    taskDeadline: '2024-01-01~2024-12-31',
    trackingRule: '年度考核进度规则'
  },
  {
    id: 9,
    taskName: '持续开展政府住房保障建设工作',
    taskType: '数据上报',
    sector: '党的建设',
    leadLeader: '张富平',
    responsible: '张富平',
    priority: '低',
    progress: 25,
    remainingWork: '剩余各数据报表未完成',
    taskDeadline: '2024-01-01~2024-12-31',
    trackingRule: '民生项目进度规则'
  },
  {
    id: 10,
    taskName: '建立巨额人员业务政策、动员等宣传G...',
    taskType: '分析报告',
    sector: '经济发展',
    leadLeader: '刘军',
    responsible: '刘军',
    priority: '高',
    progress: 25,
    remainingWork: '剩余各数据报表未完成',
    taskDeadline: '2024-01-01~2024-12-31',
    trackingRule: '临时任务进度规则'
  }
]

// 表格数据
const tableData = ref([])

// 选中的行数据
const selectedRows = ref([])

// 操作按钮
const buttons = [
  { label: '查看', type: 'primary', code: 'view' },
  { 
    label: '更多操作', 
    type: 'default', 
    code: 'more',
    dropdown: [
      { label: '发送提醒', code: 'sendReminder' },
      { label: '查看提醒', code: 'viewReminder' },
      { label: '清空提醒', code: 'clearReminder' }
    ]
  }
]

// 表头
const columns = [
  { prop: 'taskName', label: '任务名称' },
  { prop: 'taskType', label: '任务类型' },
  { prop: 'sector', label: '所属板块' },
  { prop: 'leadLeader', label: '牵头领导' },
  { prop: 'responsible', label: '责任人' },
  { prop: 'priority', label: '优先级', width: 80, type: 'text' },
  { prop: 'progress', label: '进度', width: 100 },
  { prop: 'remainingWork', label: '剩余工作量' },
  { prop: 'taskDeadline', label: '任务时效' },
  { prop: 'trackingRule', label: '进度追踪规则' },
  { prop: 'subTasks', label: '子任务', width: 80 }
]

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 列表请求参数
const reqParams = reactive({
  taskName: '',
  sector: '',
  priority: '',
  leadLeader: '',
  responsible: '',
  taskDeadlineRange: [],
  progressStart: '',
  progressEnd: '',
  skipCount: 0,
  maxResultCount: 10
})

// 子任务弹窗
const showSubTaskDialog = ref(false)
const currentTaskId = ref('')

// 查询
const onSearch = () => {
  pagination.page = 1
  reqParams.skipCount = 0
  reqParams.maxResultCount = pagination.size
  reqParams.taskName = searchForm.value.taskName
  reqParams.sector = searchForm.value.sector
  reqParams.priority = searchForm.value.priority
  reqParams.leadLeader = searchForm.value.leadLeader
  reqParams.responsible = searchForm.value.responsible
  reqParams.taskDeadlineRange = searchForm.value.taskDeadlineRange
  reqParams.progressStart = searchForm.value.progressStart
  reqParams.progressEnd = searchForm.value.progressEnd
  loadData()
}

// 重置
const onReset = () => {
  searchForm.value = {
    taskName: '',
    sector: '',
    priority: '',
    leadLeader: '',
    responsible: '',
    taskDeadlineRange: [],
    progressStart: '',
    progressEnd: ''
  }
  onSearch()
}

// 加载数据
const loadData = () => {
  loading.value = true

  // 使用本地模拟数据
  let filteredData = [...mockProgressTrackingData]

  // 应用过滤条件
  if (reqParams.taskName) {
    filteredData = filteredData.filter(item => item.taskName.includes(reqParams.taskName))
  }
  if (reqParams.sector) {
    filteredData = filteredData.filter(item => item.sector === reqParams.sector)
  }
  if (reqParams.priority) {
    filteredData = filteredData.filter(item => item.priority === reqParams.priority)
  }
  if (reqParams.leadLeader) {
    filteredData = filteredData.filter(item => item.leadLeader.includes(reqParams.leadLeader))
  }
  if (reqParams.responsible) {
    filteredData = filteredData.filter(item => item.responsible.includes(reqParams.responsible))
  }
  if (reqParams.progressStart && reqParams.progressEnd) {
    const start = parseInt(reqParams.progressStart)
    const end = parseInt(reqParams.progressEnd)
    filteredData = filteredData.filter(item => item.progress >= start && item.progress <= end)
  }

  // 分页
  const start = reqParams.skipCount || 0
  const size = reqParams.maxResultCount || 10
  const list = filteredData.slice(start, start + size)

  tableData.value = list
  pagination.total = filteredData.length

  console.log('加载的数据:', list)
  loading.value = false
}

// 表格操作点击事件
const onTableClickButton = ({ row, btn }: any) => {
  currentRow.value = row

  if (btn.code === 'view') {
    // 查看详情 - 跳转到详情页面
    onTaskNameClick(row)
  } else if (btn.code === 'sendReminder') {
    // 发送提醒
    onSendReminder(row)
  } else if (btn.code === 'viewReminder') {
    // 查看提醒
    onViewReminder(row)
  } else if (btn.code === 'clearReminder') {
    // 清空提醒
    onClearReminder(row)
  }
}

// 任务名称点击事件 - 跳转到详情页面
const onTaskNameClick = (row: any) => {
  // 跳转到详情页面
  $router.push(`/progressTracking/detail/${row.id}`)
}

// 子任务点击事件
const onSubTaskClick = (row: any) => {
  currentTaskId.value = row.id
  showSubTaskDialog.value = true
}

// 更多操作按钮
const onMoreOperations = (operation: string) => {
  switch (operation) {
    case 'ruleManagement':
      // 跳转到规则管理页面
      $router.push('/progressTracking/ruleManagement')
      break
    case 'dataSyncSettings':
      ElMessage.info('数据同步设置功能开发中')
      break
    case 'loadingAnimationSettings':
      ElMessage.info('进度追踪加载动画设置功能开发中')
      break
    case 'analysisIndicatorConfig':
      ElMessage.info('进度分析指标配置功能开发中')
      break
    case 'multiDimensionalAnalysis':
      ElMessage.info('多维度统计分析功能开发中')
      break
  }
}



// 弹窗状态
const showSendReminderDialog = ref(false)
const showViewReminderDialog = ref(false)
const currentTaskData = ref(null)

// 行操作 - 发送提醒
const onSendReminder = (row: any) => {
  currentTaskData.value = row
  showSendReminderDialog.value = true
}

// 行操作 - 查看提醒
const onViewReminder = (row: any) => {
  currentTaskData.value = row
  showViewReminderDialog.value = true
}

// 行操作 - 清空提醒
const onClearReminder = (row: any) => {
  ElMessageBox.confirm(
    `确定要清空任务"${row.taskName}"的所有提醒记录吗？`,
    '确认清空',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 从localStorage中删除该任务的所有提醒
    const existingReminders = JSON.parse(localStorage.getItem('progressTrackingReminders') || '[]')
    const filteredReminders = existingReminders.filter(reminder => reminder.taskId !== row.id)
    localStorage.setItem('progressTrackingReminders', JSON.stringify(filteredReminders))

    ElMessage.success('提醒记录已清空')
  }).catch(() => {
    // 用户取消操作
  })
}

// 提醒保存回调
const onReminderSaved = () => {
  // 不需要额外的提示，SendReminderDialog中已经有提示了
}

// 表格选择变化
const onSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// Excel导出功能
const exportToExcel = async (data: any[], filename: string) => {
  try {
    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('进度追踪数据')

    // 设置表头
    const headers = [
      '任务名称', '任务类型', '所属板块', '牵头领导', '责任人',
      '优先级', '进度(%)', '剩余工作量', '任务时效', '进度追踪规则'
    ]

    worksheet.addRow(headers)

    // 设置表头样式
    const headerRow = worksheet.getRow(1)
    headerRow.font = { bold: true }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    }

    // 添加数据行
    data.forEach(item => {
      worksheet.addRow([
        item.taskName,
        item.taskType,
        item.sector,
        item.leadLeader,
        item.responsible,
        item.priority,
        item.progress,
        item.remainingWork,
        item.taskDeadline,
        item.trackingRule
      ])
    })

    // 自动调整列宽
    worksheet.columns.forEach(column => {
      column.width = 15
    })

    // 生成Excel文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    saveAs(blob, `${filename}.xlsx`)

    ElMessage.success(`导出成功！文件名：${filename}.xlsx，共${data.length}条数据`)
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 打印功能
const printData = (data: any[]) => {
  const printWindow = window.open('', '_blank')
  if (!printWindow) {
    ElMessage.error('无法打开打印窗口，请检查浏览器设置')
    return
  }

  // 生成表格行
  const tableRows = data.map(item =>
    '<tr>' +
    '<td>' + item.taskName + '</td>' +
    '<td>' + item.taskType + '</td>' +
    '<td>' + item.sector + '</td>' +
    '<td>' + item.leadLeader + '</td>' +
    '<td>' + item.responsible + '</td>' +
    '<td>' + item.priority + '</td>' +
    '<td>' + item.progress + '%</td>' +
    '<td>' + item.remainingWork + '</td>' +
    '<td>' + item.taskDeadline + '</td>' +
    '<td>' + item.trackingRule + '</td>' +
    '</tr>'
  ).join('')

  const htmlContent = '<!DOCTYPE html>' +
    '<html>' +
    '<head>' +
    '<title>进度追踪数据</title>' +
    '<style>' +
    'body { font-family: Arial, sans-serif; margin: 20px; }' +
    'h1 { text-align: center; color: #333; }' +
    'table { width: 100%; border-collapse: collapse; margin-top: 20px; }' +
    'th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }' +
    'th { background-color: #f2f2f2; font-weight: bold; }' +
    'tr:nth-child(even) { background-color: #f9f9f9; }' +
    '@media print { body { margin: 0; } table { font-size: 12px; } }' +
    '</style>' +
    '</head>' +
    '<body>' +
    '<h1>进度追踪数据</h1>' +
    '<table>' +
    '<thead>' +
    '<tr>' +
    '<th>任务名称</th><th>任务类型</th><th>所属板块</th><th>牵头领导</th><th>责任人</th>' +
    '<th>优先级</th><th>进度(%)</th><th>剩余工作量</th><th>任务时效</th><th>进度追踪规则</th>' +
    '</tr>' +
    '</thead>' +
    '<tbody>' +
    tableRows +
    '</tbody>' +
    '</table>' +
    '<scr' + 'ipt>' +
    'window.onload = function() {' +
    'window.print();' +
    'window.onafterprint = function() { window.close(); }' +
    '}' +
    '</scr' + 'ipt>' +
    '</body>' +
    '</html>'

  printWindow.document.write(htmlContent)
  printWindow.document.close()
}

// 批量操作
const onBatchOperation = async (operation: string) => {
  console.log('批量操作:', operation, '选中数据:', selectedRows.value.length)
  switch (operation) {
    case 'export':
      if (selectedRows.value.length === 0) {
        // 导出全部数据
        await exportToExcel(tableData.value, '进度追踪数据_全部')
      } else {
        // 导出选中数据
        await exportToExcel(selectedRows.value, '进度追踪数据_选中')
      }
      break
    case 'print':
      if (selectedRows.value.length === 0) {
        // 打印全部数据
        printData(tableData.value)
        ElMessage.success(`打印全部数据，共${tableData.value.length}条`)
      } else {
        // 打印选中数据
        printData(selectedRows.value)
        ElMessage.success(`打印选中数据，共${selectedRows.value.length}条`)
      }
      break
    case 'batchDelete':
      if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择要删除的数据')
        return
      }
      // 实际删除数据
      const deleteCount = selectedRows.value.length
      const deleteIds = selectedRows.value.map(row => row.id)

      // 从模拟数据中删除选中的项
      for (let i = mockProgressTrackingData.length - 1; i >= 0; i--) {
        if (deleteIds.includes(mockProgressTrackingData[i].id)) {
          mockProgressTrackingData.splice(i, 1)
        }
      }

      ElMessage.success(`批量删除成功，共删除${deleteCount}条数据`)
      // 清空选中状态
      selectedRows.value = []
      // 重新加载数据
      loadData()
      break
    case 'batchExport':
      if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择要导出的数据')
        return
      }
      await exportToExcel(selectedRows.value, '进度追踪数据_批量导出')
      break
    case 'exportAll':
      await exportToExcel(tableData.value, '进度追踪数据_全部导出')
      break
  }
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type === 'page') {
    pagination.page = val
    reqParams.skipCount = (val - 1) * pagination.size
  } else {
    pagination.size = val
    reqParams.maxResultCount = pagination.size
  }
  loadData()
}

// 块高度变化事件
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 75
}

// 初始化
onMounted(() => {
  console.log('进度追踪页面初始化')
  try {
    loadData()
    console.log('数据加载完成')
  } catch (error) {
    console.error('初始化错误:', error)
  }
})
</script>

<template>
  <div class="progress-tracking">
    <Block title="进度追踪" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
      <template #topRight>
        <!-- 选中状态显示 -->
        <span v-if="selectedRows.length > 0" style="margin-right: 10px; color: #409eff;">
          已选中 {{ selectedRows.length }} 条数据
        </span>

        <!-- 批量操作按钮 -->
        <el-button size="small" type="success" @click="onBatchOperation('export')">导出</el-button>
        <el-button size="small" type="info" @click="onBatchOperation('print')">打印</el-button>
        <el-button size="small" type="danger" @click="onBatchOperation('batchDelete')">批量删除</el-button>
        <el-button size="small" type="warning" @click="onBatchOperation('batchExport')">批量导出</el-button>
        <el-button size="small" type="primary" @click="onBatchOperation('exportAll')">全部导出</el-button>

        <!-- 更多操作下拉菜单 -->
        <el-dropdown @command="onMoreOperations" style="padding-left: 10px">
          <el-button size="small" type="primary">
            更多操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="ruleManagement">进度追踪规则管理</el-dropdown-item>
              <el-dropdown-item command="dataSyncSettings">数据同步设置</el-dropdown-item>
              <el-dropdown-item command="loadingAnimationSettings">进度追踪加载动画设置</el-dropdown-item>
              <el-dropdown-item command="analysisIndicatorConfig">进度分析指标配置</el-dropdown-item>
              <el-dropdown-item command="multiDimensionalAnalysis">多维度统计分析</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
      
      <template #expand>
        <!-- 搜索 -->
        <div class="search">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="5"
            :label-width="100"
            :enable-reset="false"
            confirm-text="查询"
            button-vertical="flowing"
            @submit="onSearch"
          />
        </div>
      </template>

      <!-- 列表 -->
      <el-table
        ref="tableRef"
        :data="tableData"
        :height="tableHeight"
        v-loading="loading"
        border
        stripe
        @selection-change="onSelectionChange"
      >
        <!-- 多选列 -->
        <el-table-column type="selection" width="55" />

        <!-- 数据列 -->
        <el-table-column
          v-for="column in columns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          show-overflow-tooltip
        >
          <template v-if="column.prop === 'taskName'" #default="{ row }">
            <el-button
              type="primary"
              link
              @click="onTaskNameClick(row)"
              style="padding: 0; height: auto; font-weight: normal;"
            >
              {{ row.taskName }}
            </el-button>
          </template>
          <template v-else-if="column.prop === 'progress'" #default="{ row }">
            <el-progress :percentage="row.progress" :width="50" />
          </template>
          <template v-else-if="column.prop === 'subTasks'" #default="{ row }">
            <el-button
              type="primary"
              link
              size="small"
              @click="onSubTaskClick(row)"
            >
              子任务
            </el-button>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="onTableClickButton({ row, btn: { code: 'view' } })"
              style="margin-right: 8px;"
            >
              查看
            </el-button>
            <el-dropdown @command="(command) => onTableClickButton({ row, btn: { code: command } })">
              <el-button type="primary" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="sendReminder">发送提醒</el-dropdown-item>
                  <el-dropdown-item command="viewReminder">查看提醒</el-dropdown-item>
                  <el-dropdown-item command="clearReminder">清空提醒</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.size"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      />
    </Block>

    <!-- 子任务弹窗 -->
    <SubTaskDialog
      v-model="showSubTaskDialog"
      :task-id="currentTaskId"
    />

    <!-- 发送提醒弹窗 -->
    <SendReminderDialog
      v-model="showSendReminderDialog"
      :task-data="currentTaskData"
      @saved="onReminderSaved"
    />

    <!-- 查看提醒弹窗 -->
    <ViewReminderDialog
      v-model="showViewReminderDialog"
      :task-data="currentTaskData"
    />
  </div>
</template>

<route>
{
  meta: {
    title: '进度追踪',
  },
}
</route>

<style scoped lang="scss">
.progress-tracking {
  .search {
    margin-bottom: 16px;
  }
}
</style>

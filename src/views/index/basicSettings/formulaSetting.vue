<script setup lang="ts" name="formulasetting">
import {computed, reactive, ref, watch} from 'vue'
import {useRouter} from 'vue-router'
import {ElMessage} from 'element-plus'
import {getLedgerListByUser, allRegionNames, getDetailByLedgerId} from '@/api/LedgerApi'
import {PostFormulaEngine, PutFormulaEngine, DeleteFormulaEngine} from '@/api/FormulaEngineApi'

const router = useRouter()
const loading = ref(false)
const loadingText = ref('正在获取业务表')

const calculationTypeOptions = [
	{label: '按数据量计算进度', value: 0},
	{label: '按枚举值计算进度', value: 1},
	{label: '按累加值计算进度', value: 2},
	{label: '按百分比计算进度', value: 3},
]

const calculationSymbolsOptions = [
	{label: '加', value: 0},
	{label: '减', value: 1},
	{label: '乘', value: 2},
	{label: '除', value: 3},
]

const columns = [
	{label: '公式名称', prop: 'name'},
	{label: '函数示例', prop: 'functionExample'},
	{label: '关联业务表', prop: 'ledgerIds'},
	{label: '计算方式', prop: 'calculationType'},
	{label: '更新时间', prop: 'lastModificationTime'},
]

const formProps = ref([{label: '公式名称', prop: 'name', type: 'text'}])
const form: any = ref({})

const formDataRef = ref()
const formDataProps = ref([
	{label: '公式名称', prop: 'name', type: 'text'},
	{label: '函数示例', prop: 'functionExample', type: 'text'},
	{label: '时间范围', prop: 'timeRange', type: 'datetimerange'}, // startTime, endTime
	{label: '关联业务表', prop: 'ledgerIds', type: 'select', attrs: {filterable: true}},
	{label: '区域范围', prop: 'regionIds', type: 'select'},
	{label: '计算方式', prop: 'calculationType', type: 'select', options: calculationTypeOptions},
	{label: '计算规则', prop: 'calculationRole'},
])
const formRules = ref({
	name: [{required: true, message: '请输入公式名称', trigger: 'blur'}],
	functionExample: [{required: true, message: '请输入函数示例', trigger: 'blur'}],
	timeRange: [{required: true, message: '请选择时间范围', trigger: 'blur'}],
	ledgerIds: [{required: true, message: '请选择关联业务表', trigger: 'blur'}],
	regionIds: [{required: true, message: '请选择区域范围', trigger: 'blur'}],
	calculationType: [{required: true, message: '请选择计算方式', trigger: 'blur'}],
	calculationRole: [{required: true, message: '请选择计算规则', trigger: 'blur'}],
})
const formData: any = ref({})

const tableRef = ref()
const currentRow: any = ref(null)
const tableHeight = ref(0)
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})
const reqParams = reactive({
	name: null,
	isTemplate: false,
	skipCount: 0,
	maxResultCount: 10,
})

const showDialog = ref(false)
const isNew = ref(true)
const rules: any = ref({})
const rawFields: any = ref([])
const fields: any = ref([])
const fieldOption: any = ref([])
const conditionModeOptions = ref([
	{label: '等于=', value: 0},
	{label: '大于>', value: 1},
	{label: '大于等于>=', value: 2},
	{label: '小于<', value: 3},
	{label: '小于等于≤', value: 4},
])

const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.page = val
		reqParams.skipCount = (val - 1) * pagination.size
	} else {
		pagination.size = val
		reqParams.maxResultCount = pagination.size
	}
}

const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}

const onTableButtonClick = ({btn, row, index}: any) => {
	if (btn.code == 'edit') {
		isNew.value = false
		currentRow.value = row
		Object.assign(formData.value, {
			name: row.name,
			functionExample: row.functionExample,
			timeRange: [row.startTime, row.endTime],
			ledgerIds: row.ledgerIds[0],
			regionIds: row.regionIds,
			calculationType: row.calculationType,
		})
		rules.value = JSON.parse(row.calculationRole)
		showDialog.value = true
	} else if (btn.code == 'delete') {
		DeleteFormulaEngine(row.id).then(() => {
			ElMessage.success('删除成功')
			tableRef.value.reload()
		})
	}
}

const onSearch = () => {
	reqParams.name = form.value?.name
	reqParams.skipCount = 0
	pagination.page = 1
}

const onDialogOpen = async () => {
	if (!formDataProps.value?.[4].options || formDataProps.value?.[4].options?.length == 0) {
		loading.value = true
		loadingText.value = '正在获取区域'
		await allRegionNames()
			.then((res) => {
				formDataProps.value[4].options = res.data.map((item: any) => {
					return {
						label: item.name,
						value: item.id,
						_raw: JSON.parse(JSON.stringify(item)),
					}
				})
				formDataProps.value[4].options?.unshift({
					label: '本镇街',
					value: '3a0b36e8-d86e-4d05-79aa-1477d4b56561',
				})
			})
			.finally(() => {
				loading.value = false
			})
	}

	if (!formDataProps.value?.[3].options || formDataProps.value?.[3].options?.length == 0) {
		loading.value = true
		loadingText.value = '正在获取业务表'
		await getLedgerListByUser({
			skipCount: 0,
			maxResultCount: 99,
			isOnline: true,
			ignoreRunways: '市级共性台账',
		})
			.then((res: any) => {
				formDataProps.value[3].options = res.data.items.map((item: any) => {
					return {
						label: item.ledger.name,
						value: item.ledger.id,
					}
				})
			})
			.finally(() => {
				loading.value = false
			})
	}
}

watch(
	() => rules.value,
	(newVal) => {
		formData.value.calculationRole = JSON.stringify(newVal)
	},
	{deep: true}
)
const onFormChange = (val: any, item: any) => {
	if (item.prop == 'ledgerIds') {
		getDetailByLedgerId(val).then((res: any) => {
			rawFields.value = res.data.tableInfo.fields.map((item: any) => {
				return {
					label: item.displayName,
					value: item.name,
					_raw: JSON.parse(JSON.stringify(item)),
				}
			})
		})
	} else if (item.prop == 'calculationType') {
		console.log(8899, rawFields.value)
		rules.value = {}
		switch (val) {
			case 0:
				break
			case 1:
				fields.value = rawFields.value.filter(
					(item: any) => item._raw.type == 'string' && item._raw.multiple === false
				)
				break
			case 2:
				fields.value = rawFields.value.filter(
					(item: any) => item._raw.type === 'int' || item._raw.type === 'decimal'
				)
				break
			case 3:
				fields.value = rawFields.value.filter(
					(item: any) => item._raw.type == 'string' && item._raw.multiple === false
				)
				break
		}
	}
}

const onConfirm = () => {
	formDataRef.value.validate((valid: boolean) => {
		if (!valid) {
			return
		}
		const reqData = {
			name: formData.value.name,
			functionExample: formData.value.functionExample,
			startTime: formData.value.timeRange?.[0],
			endTime: formData.value.timeRange?.[1],
			ledgerIds: [formData.value.ledgerIds],
			regionIds: formData.value.regionIds,
			calculationType: formData.value.calculationType,
			calculationRole: JSON.stringify(rules.value),
		}

		if (Object.keys(rules.value).length == 0) {
			ElMessage.error('请填写计算规则')
			return
		}

		loading.value = true
		loadingText.value = '正在保存'

		const success = () => {
			ElMessage.success('保存成功')
			rules.value = {}
			formData.value = {}
			tableRef.value.reload()
			loading.value = false
			showDialog.value = false
		}

		if (isNew.value) {
			PostFormulaEngine(reqData).then(() => success())
		} else if (currentRow.value) {
			PutFormulaEngine(currentRow.value.id, reqData).then(() => success())
		}
	})
}

const onBatchDelete = () => {
	const rows = tableRef.value.getSelectionRows()
	if (rows.length == 0) {
		ElMessage.error('请选择要删除的公式')
		return
	}
	loading.value = true
	const promise: any[] = []
	rows.forEach((item: any) => {
		promise.push(DeleteFormulaEngine(item.id))
	})
	Promise.all(promise)
		.then(() => {
			ElMessage.success('删除成功')
			tableRef.value.reload()
			tableRef.value.clearSelection()
		})
		.finally(() => {
			loading.value = false
		})
}

const onFieldChange = (name: any) => {
	fieldOption.value.length = 0
	fieldOption.value = rawFields.value
		.find((item: any) => item.value == name)
		?._raw.options.map((val: any) => {
			return {
				label: val,
				value: val,
			}
		})
}

const onFormulaTemplate = () => {
	router.push('/basicSettings/formulaTemplate')
}

// 存储台账ID与名称的映射关系
const ledgerNameMap = ref<Record<string, string>>({})

// 获取台账名称并存储到映射中
const getLedgerName = async (id: string) => {
	if (!ledgerNameMap.value[id]) {
		const res = await getDetailByLedgerId(id)
		ledgerNameMap.value[id] = res.data.name
	}
	return ledgerNameMap.value[id] || ''
}
</script>
<template>
	<div class="formula-setting">
		<Block title="公式引擎" :enable-fixed-height="true" @heightChanged="onBlockHeightChanged">
			<template #topRight>
				<el-button size="small" type="danger" @click="onBatchDelete">批量删除</el-button>
				<el-button size="small" type="primary" @click="onFormulaTemplate"
					>计算公式模版管理</el-button
				>
				<el-button
					size="small"
					type="primary"
					@click=";(isNew = true), (showDialog = true), (formData = {})"
				>
					新增计算公式
				</el-button>
			</template>
			<template #expand>
				<div class="search">
					<Form
						:props="formProps"
						v-model="form"
						:column-count="2"
						:label-width="74"
						:enable-reset="false"
						confirm-text="查询"
						button-vertical="flowing"
						@submit="onSearch"
					></Form>
				</div>
			</template>
			<TableV2
				ref="tableRef"
				url="/api/newFeature/formula-engine"
				:columns="columns"
				:req-params="reqParams"
				:enable-toolbar="false"
				:height="tableHeight"
				:enable-own-button="false"
				:buttons="[
					{
						code: 'edit',
						label: '编辑',
						type: 'primary',
					},
					{
						code: 'delete',
						label: '删除',
						type: 'danger',
						popconfirm: '确认删除吗？',
					},
				]"
				:enable-selection="true"
				@click-button="onTableButtonClick"
				@completed="
					() => {
						pagination.total = tableRef.getTotal()
						tableRef.getTableData().forEach((item: any) => getLedgerName(item.ledgerIds[0]))
					}
				"
			>
				<template #ledgerIds="scoped">
					{{ ledgerNameMap[scoped.row.ledgerIds[0]] || '-' }}
				</template>
				<template #calculationType="scoped">
					{{
						calculationTypeOptions.find(
							(item) => item.value === scoped.row.calculationType
						)?.label
					}}
				</template>
			</TableV2>
			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			></Pagination>
		</Block>

		<Dialog
			v-model="showDialog"
			:loading="loading"
			:loading-text="loadingText"
			:title="isNew ? '新增公式' : '编辑公式'"
			:destroy-on-close="true"
			@click-confirm="onConfirm"
			@open="onDialogOpen"
		>
			<Form
				ref="formDataRef"
				v-model="formData"
				:props="formDataProps"
				:rules="formRules"
				:column-count="1"
				:label-width="100"
				:enable-button="false"
				@change="onFormChange"
			>
				<template #form-regionIds="scoped">
					<el-select
						v-model="scoped.form.regionIds"
						placeholder="请选择"
						multiple
						collapse-tags
						collapse-tags-tooltip
						:max-collapse-tags="3"
					>
						<el-option
							v-for="item in formDataProps[4].options as any"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
				</template>
				<template #form-calculationRole>
					<!-- <div class="w-full mg-bottom-10">
						当数据量累计达到
						<el-input
							v-model="formData.calculationFormula"
							type="number"
							placeholder="请输入"
							class="flx mg-right-10"
							style="width: 100px"
						></el-input>
						条
					</div>
					<div class="df w-full">
						且
						<el-select
							v-model="formData.calculationSymbols"
							placeholder="请选择计算符号"
							class="flx mg-right-10 mg-left-10"
						>
							<el-option
								v-for="item of calculationSymbolsOptions"
								:key="item.label"
								:label="item.label"
								:value="item.value"
							></el-option>
						</el-select>
						全为
						<el-select
							v-model="formData.calculationSymbols"
							placeholder="请选择计算符号"
							class="flx mg-left-10 mg-right-10"
						>
							<el-option
								v-for="item of calculationSymbolsOptions"
								:key="item.label"
								:label="item.label"
								:value="item.value"
							></el-option>
						</el-select>
						时,则完成任务
					</div> -->
					<div v-show="typeof formData.calculationType !== 'number'">-</div>

					<div v-show="formData.calculationType === 0" class="df flx">
						当数量累计达到
						<el-input
							v-model="rules.total"
							type="number"
							placeholder="请输入"
							style="width: 200px"
							class="mg-left-10 mg-right-10"
						></el-input>
						条时，则完成任务
					</div>

					<div v-show="formData.calculationType === 1" class="df flx">
						当
						<el-select
							v-model="rules.field"
							placeholder="请选择数据项"
							@change="onFieldChange"
							class="mg-left-10"
							style="width: 180px"
						>
							<el-option
								v-for="item in fields"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>

						<div class="mg-left-10">全为</div>

						<el-select
							v-model="rules.fieldValue"
							placeholder="请选择枚举值"
							class="mg-left-10 mg-right-10"
							style="width: 180px"
						>
							<el-option
								v-for="item in fieldOption"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
						时，则完成任务
					</div>

					<div v-show="formData.calculationType === 2" class="df flx">
						当
						<el-select
							v-model="rules.field"
							placeholder="请选择数据项"
							style="width: 180px"
							@change=""
							class="mg-left-10"
						>
							<el-option
								v-for="item in fields"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
						<div class="mg-left-10">累加值达到</div>

						<el-input
							v-model.number="rules.fieldValue"
							type="number"
							style="width: 180px"
							placeholder="请输入数字"
							class="mg-left-10 mg-right-10"
						/>
						时，则完成任务
					</div>

					<div v-show="formData.calculationType === 3">
						<div class="df flx mg-bottom-15">
							当
							<el-select
								v-model="rules.field"
								placeholder="请选择数据项"
								@change="onFieldChange"
								style="width: 180px"
								class="mg-left-10"
							>
								<el-option
									v-for="item in fields"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>

							<div class="mg-left-10 mg-right-10">枚举值为</div>

							<el-select
								v-model="rules.fieldValue"
								placeholder="请选择枚举值"
								style="width: 180px"
							>
								<el-option
									v-for="item in fieldOption"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</div>

						<div class="df flx">
							数据量
							<el-select
								v-model="rules.conditionMode"
								placeholder="请选择计算类型"
								class="mg-left-10"
								style="width: 180px"
							>
								<el-option
									v-for="item in conditionModeOptions"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
							<div class="mg-left-10 mg-right-10">总数据量</div>

							<el-input
								v-model.number="rules.conditionValue"
								type="number"
								style="width: 180px"
								placeholder="请输入数字"
								class="mg-right-10"
							/>
							% 时，则完成任务
						</div>
					</div>
				</template>
			</Form>
		</Dialog>
	</div>
</template>
<style scoped lang="scss"></style>
<route>
    {
        meta: {
            title: '公式引擎',
        },
    }
</route>

<script setup lang="ts">
import {
	computed,
	nextTick,
	onActivated,
	onDeactivated,
	onMounted,
	onUnmounted,
	reactive,
	ref,
	watch,
} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import {
	getLedgerListByUserlittle,
	setLedgerTopByUser,
	setLedgerSetReadByUser,
	getMyLedgerTypes,
	exportMyLedgersLittleAsync,
} from '@/api/LedgerApi'
import {ReminderCycleList, RunwayList, warnLevelList} from '@/define/ledger.define'
import {useUserStore} from '@/stores/useUserStore'
import Worker from '@/worker/LedgerUpdateAmount.ts?worker'
import {APIConfig} from '@/api/config'
import {ElMessage, ElMessageBox} from 'element-plus'
import dayjs from 'dayjs'
import LedgerTable from './components/LedgerTable.vue'
import {getDataLedger} from '@/api/LedgerApi'
import {DEPARTMENT_OPTIONS_ENUM} from '@/define/organization.define'
import {useMScroll} from '@/hooks/useMScroll'
import {useViewStore} from '@/stores/useViewStore'
import {ElNotification} from 'element-plus'
import {SystematicTypeEnum} from '@/define/Runway.define'

const userStore = useUserStore()
const viewStore = useViewStore()
const runwayList = RunwayList.filter((v) => v.id !== '1')
const router = useRouter()
const currentWork: any = ref([])
const showLedgerRunway = computed(
	() => JSON.parse(localStorage.getItem('currentDepartmentInfo') as string).region?.grade >= 4
)
const isLoading = ref(false)
const organizeRef = ref()
const searchForm = reactive({
	ReminderInterval: null,
	organize: '',
	value: '',
	autoFillStatus: '',
	departmentId: '',
	LedgerRunwayId: undefined,
	LitStatus: undefined,
})
const currentGrade: any = ref(null)
const cascadeValue: any = ref([])
const cascadeRunwayValue = ref([])
// const searchOptions = [
// 	{label: '全部业务表状态', value: 0},
// 	{label: '即将超期', value: 1},
// ]
const searchOptions = ReminderCycleList
const isFull = ref(false)
const ledgerList: any = ref([])
const currentFilterLedgerIds = ref([])
const fullLedgerList: any = ref({})
const isLastRunway = computed(() => {
	const ledgers = ledgerList.value.filter((f: any) => f.items.length > 0)
	return (runway: string) =>
		ledgers.length % 2 !== 0 && ledgers[ledgers.length - 1].items[0].ledger?.runway === runway
})

const updateTime = ref([
	{label: '全部', value: 0},
	{label: '本日', value: 1},
	{label: '本周', value: 2},
	{label: '本月', value: 3},
	{label: '本年', value: 4},
])
const currentUpdateTime = ref()
const updataCountList: any = ref([])
const fromTime = ref('')
const toTime = ref('')
const totalCount = ref(-1)
const toggle = ref(false)
const ledgerTableRef = ref()
const activeMoreRunway = ref('')
const closeSearch = ref(false)
const dataLedgerList = ref<any[]>([])

const userLevel: any = userStore.getUserInfo.community
	? 'community'
	: userStore.getUserInfo.street
	? 'street'
	: userStore.getUserInfo.district
	? 'district'
	: userStore.getUserInfo.city
	? 'city'
	: null

const oneSelectProps = ref({
	lazy: true,
	url: '/api/platform/department/generalDepartmentQuery',
	method: 'GET',
	multiple: false,
	checkStrictly: true,
	beforeCompleted: (node: any, data: any, query: any) => {
		const first = DEPARTMENT_OPTIONS_ENUM.map((f: any) => f.label)
		let grade = 2
		let parentId = ''

		if (first.includes(node.label)) {
			grade = node.value
		} else {
			grade = node.pathNodes[0].value
			parentId = node.value
		}

		query = {
			keyWord: '',
			grade,
			parentId,
		}
		return {data, query}
	},
})

const runwayPlate: any = ref([])
const runwayData = computed(() => {
	return runwayPlate.value.map((v: any) => ({
		label: v.businessName,
		value: v.id,
	}))
})

const oneSelectRunwayProps = ref({
	lazy: true,
	url: '/api/ledger-service/ledger-runway-manages/get-runway-List',
	method: 'GET',
	multiple: false,
	checkStrictly: false,
	query: {
		systematicType: SystematicTypeEnum.Street,
	},
	beforeCompleted: (node: any, data: any, query: any) => {
		const currentDeparetmentInfo = JSON.parse(
			localStorage.getItem('currentDepartmentInfo') as string
		)

		query = {
			parentId: node.value,
		}

		return {data, query}
	},
})
const getRunwayPlate = () => {
	GetRunwayPlate({systematicType: SystematicTypeEnum.Street}).then((res) => {
		runwayPlate.value = res.data
		// searchFormProps.value[1].options = res.data.map((v: any) => ({
		// 	value: v.businessName,
		// 	label: v.businessName,
		// 	_raw: JSON.parse(JSON.stringify(v)),
		// }))
	})
}
// const oneSelectRunwayProps = ref({
// 	lazy: true,
// 	url: '/api/ledger-service/ledger-runway-manages',
// 	method: 'GET',
// 	multiple: false,
// 	checkStrictly: false,
// 	beforeCompleted: (node: any, data: any, query: any) => {
// 		const currentDeparetmentInfo = JSON.parse(
// 			localStorage.getItem('currentDepartmentInfo') as string
// 		)
// 		if (node.level === 1) {
// 			query = {
// 				regionId: currentDeparetmentInfo.region?.id,
// 				gridCode: currentDeparetmentInfo.region?.telecomCode,
// 				belongBlock: node.label,
// 			}
// 		} else {
// 			query = {
// 				regionId: currentDeparetmentInfo.region?.id,
// 				gridCode: currentDeparetmentInfo.region?.telecomCode,
// 				parentId: node.value,
// 			}
// 		}

// 		return {data, query}
// 	},
// })
const getUpdateCountById = computed(() => {
	return (id: string) => {
		const item = updataCountList.value.find((f: any) => f.id === id)
		return item ? item.data.updateCount : -1
	}
})
const getUpdateTotalById = computed(() => {
	return (id: string) => {
		const item = updataCountList.value.find((f: any) => f.id === id)
		return item ? item.data.totalCount : -1
	}
})
const getLitStatusById = computed(() => {
	return (id: string) => {
		const item = updataCountList.value.find((f: any) => f.id === id)
		return item ? item.data.litStatus : 0
	}
})

const getUpdateLastModificationTime = computed(() => {
	return (id: string) => {
		const item = updataCountList.value.find((f: any) => f.id === id)
		return item
			? item.data.dataLastModifyTime !== null
				? dayjs(item.data.dataLastModifyTime).format('YYYY/MM/DD')
				: null
			: null
	}
})

const onClickLedger = (str: string, item: any) => {
	userStore.setCurrentLedgerUserPermissions(item.permissions)
	router.push({
		path: '/ledger/fill',
		query: {
			ledgerId: item.ledgerId,
			interval: item?.littleLedgerDto?.taskInterval,
		},
	})
}

let currentFull: any = null
const scrollOver = ref(false)

const onFull = (runway: any, index: number, search: boolean = false, isScroll: boolean = false) => {
	isLoading.value = true
	currentFull = {runway, index, search, page: currentFull?.page || 0}

	const hasFullStorage = localStorage.getItem('OVERVIEW_ISFULL')
	if (runway.label === activeMoreRunway.value && !search && !isScroll && !hasFullStorage) {
		activeMoreRunway.value = ''
		ledgerList.value[index].items.length = 4
		getAllRunway()
		return
	}

	if (runway) {
		localStorage.setItem('currentRunway', JSON.stringify(runway))
	}

	if (currentFull.page === 0) {
		ledgerList.value[index].items.length = 0
	}

	isFull.value = true
	activeMoreRunway.value = runway.label

	getLedgerListByUserlittle({
		skipCount: currentFull.page * 40,
		maxResultCount: 40,
		isOnline: true,
		runway: runway.name,

		filter: searchForm.value,
		ReminderInterval: searchForm.ReminderInterval,
		autoFillStatus: searchForm.autoFillStatus,
		grade: currentGrade.value,
		DepartmentId: searchForm.departmentId,
		LedgerRunwayId: searchForm.LedgerRunwayId,
		LitStatus: searchForm.LitStatus,
		'RegionDepartment.City': userStore.getUserInfo.city,
		'RegionDepartment.District': userStore.getUserInfo.district,
		'RegionDepartment.Street': userStore.getUserInfo.street,
		'RegionDepartment.Community': userStore.getUserInfo.community,
		ignoreRunways: ['市级共性业务表'],
	})
		.then((res: any) => {
			isLoading.value = false
			activeMoreRunway.value = runway.label
			if (currentFull.page === 0) {
				ledgerList.value[index] = res.data
			} else {
				ledgerList.value[index].items = ledgerList.value[index].items.concat(res.data.items)
			}
			totalCount.value = res.data.totalCount

			startUpWorker(
				res.data.items.map((f: any) => f.ledger.id),
				false
			)
			function hasScrollbar(element: any) {
				return element.scrollHeight > element.clientHeight
			}
			if (!hasScrollbar(document.querySelector('.container-body'))) {
				if (ledgerList.value[index].items.length < totalCount.value) {
					currentFull.page++
					onFull(currentFull.runway, currentFull.index, currentFull.search, true)
				}
			}
		})
		.catch((err: any) => {
			if (err.response?.status === 500) {
				ElNotification.error('当前操作“我的业务表”用户量较多，请5分钟后再试')
			}
		})
		.finally(() => {
			isLoading.value = false
			isDown.value = false
		})
}

const onOrganizeChecked = (data: any) => {
	if (data && data.length > 0) {
		searchForm.organize = data[0].name
	} else {
		searchForm.organize = ''
	}
}
function removeFunctionsFromObject(obj: any) {
	for (let key in obj) {
		if (typeof obj[key] === 'function') {
			delete obj[key]
		}
	}
}
const getAllRunway = async (needWork: boolean = true) => {
	isLoading.value = true

	const promise: any = []
	const data = {
		skipCount: 0,
		maxResultCount: 4,
		isOnline: true,
		runway: '',
		filter: searchForm.value,
		// departmentNames: '',

		ReminderInterval: searchForm.ReminderInterval,
		autoFillStatus: searchForm.autoFillStatus,
		LedgerRunwayId: searchForm.LedgerRunwayId,
		grade: currentGrade.value,
		DepartmentId: searchForm.departmentId,
		LitStatus: searchForm.LitStatus,
		// IsRemind: searchForm.type === 1,
		'RegionDepartment.City': userStore.getUserInfo.city,
		'RegionDepartment.District': userStore.getUserInfo.district,
		'RegionDepartment.Street': userStore.getUserInfo.street,
		'RegionDepartment.Community': userStore.getUserInfo.community,
		ignoreRunways: ['市级共性业务表'],
	}

	totalCount.value = -1
	runwayList.forEach((f: any) => {
		const params = JSON.parse(JSON.stringify(data))
		params.runway = f.label
		console.log(params)
		promise.push(getLedgerListByUserlittle(params))
	})

	try {
		const res = await Promise.all(promise)

		const all = res.map((f: any) => f.data)
		const index = runwayList.findIndex((f: any) => f.label === activeMoreRunway.value)

		if (activeMoreRunway.value && all[index].items.length > 0) {
			onFull(runwayList[index], index, true)
		} else {
			let ids: any = []
			let total = 0

			ledgerList.value = all
			// 得到当前所有业务表 id
			for (let i = 0; i < ledgerList.value.length; i++) {
				const list = ledgerList.value[i].items
				if (i === 0) {
					ids = list
						.filter((f: any, index: number) => index < 4)
						.map((f: any) => f.ledger?.id)
				} else {
					ids = ids.concat(
						list
							.filter((f: any, index: number) => index < 6)
							.map((f: any) => f.ledger?.id)
					)
				}
				total += ledgerList.value[i].totalCount
			}

			totalCount.value = total

			currentFilterLedgerIds.value = ids
			isLoading.value = false
			isFull.value = false
			currentFull = null
			needWork && startUpWorker(ids)
		}
	} catch (err: any) {
		if (err.response?.status === 500) {
			ElNotification.error('当前操作“我的业务表”用户量较多，请5分钟后再试')
		}
	} finally {
		isLoading.value = false
	}
}

const onReset = () => {
	currentUpdateTime.value = null
	searchForm.ReminderInterval = null
	searchForm.value = ''
	searchForm.organize = ''
	searchForm.autoFillStatus = ''
	searchForm.departmentId = ''
	searchForm.LedgerRunwayId = undefined
	searchForm.LitStatus = undefined
	;(reqParams.value.ParentRunway = ''), (reqParams.value.Runway = ''), (cascadeValue.value = [])
	currentGrade.value = null
	cascadeRunwayValue.value = []
	activeMoreRunway.value = ''

	organizeRef.value?.reset()
	getAllRunway()
	ledgerTableRef.value?.reload()
}

const onSearch = () => {
	activeMoreRunway.value = ''
	if (searchForm.autoFillStatus !== 2) {
		searchForm.LitStatus = undefined
	}
	getAllRunway(true)
	ledgerTableRef.value?.reload()
	// if (toggle.value) {
	// 	ledgerTableRef.value?.reload()
	// } else {
	// 	getAllRunway()
	// }
}

let workerTime: any = null
const startUpWorker = (ids: string[], isClearUpdateList: Boolean = true) => {
	isClearUpdateList && cleanWorker()

	const worker = new Worker()
	if (isClearUpdateList) {
		updataCountList.value = []
	} else {
		workerTime && clearTimeout(workerTime)
	}
	currentWork.value.push(worker)
	worker.postMessage({
		url: `${APIConfig('ledger')}/api/ledger-service/ledger/{id}/card/dynamic-data${
			'?FromDateTime=' +
				fromTime.value +
				'&ToDateTime=' +
				toTime.value +
				'&Region.City=' +
				userStore.getUserInfo.city ||
			'' + '&Region.District=' + userStore.getUserInfo.district ||
			'' + '&Region.Street=' + userStore.getUserInfo.street ||
			'' + '&Region.Community=' + userStore.getUserInfo.community ||
			''
		}`,
		ids,
		token: `Bearer ${userStore.getToken}`,
	})

	worker.onmessage = (e) => {
		// console.log('LedgerUnpdateAmountWorker', e.data)
		updataCountList.value.push(e.data)
		if (e.data.isComplete) {
			console.log('已经完成所有任务')
			isClearUpdateList && cleanWorker()
		}
	}
}

const cleanWorker = () => {
	currentWork.value.forEach((worker: any, index: any) => {
		if (worker) {
			worker.terminate()
			console.log('已经清理任务')
		}
	})
	currentWork.value = []
}

const onLedgerTop = (item: any, runway: any) => {
	setLedgerTopByUser(userStore.getUserInfo.id, item.ledger.id, !item.isTop)
		.then((res: any) => {
			getAllRunway()
			ElMessage(`${item.ledger.name} 已${item.isTop ? '取消置顶' : '置顶'}`)
		})
		.catch((err: any) => {
			if (err.response?.status === 500) {
				ElNotification.error('当前操作“我的业务表”用户量较多，请5分钟后再试')
			}
		})
}
const changeswitch = (item: any, runway: any, type: boolean) => {
	setLedgerSetReadByUser(userStore.getUserInfo.id, item.ledgerId, item.isRead).then(
		(res: any) => {
			if (type) {
				getAllRunway()
			} else {
				startUpWorker(fullLedgerList.value.items.map((f: any) => f.ledgerId))
			}
			ElMessage(`${item.ledgerName} 已${item.isRead ? '设置已读' : '取消已读'}`)
		}
	)
}

function isFirstHalfOfMonth() {
	const currentDate = new Date()
	const dayOfMonth = currentDate.getDate()
	return dayOfMonth <= 15
}
function isFirstHalfOfYear() {
	const currentDate = new Date()
	const monthOfYear = currentDate.getMonth()
	return monthOfYear < 6
}
const getReadTime = computed(() => {
	const date = new Date()
	const weekDays = ['一', '二', '三', '四', '五', '六', '日']
	const year = date.getFullYear()
	const month = date.getMonth() + 1
	const day = date.getDate()
	const week = date.getDay() == 0 ? 7 : date.getDay()
	const qtr = month <= 3 ? 1 : month <= 6 ? 2 : month <= 9 ? 3 : 4
	return (time: any) => {
		if (time.interval == 2) {
			if (time.weeklyDeadlineDayOfWeek !== null) {
				return `周${weekDays[time.weeklyDeadlineDayOfWeek - 1]}`
			} else {
				return '未设置'
			}
		} else if (time.interval == 3) {
			if (time.monthlyDeadlineDay) {
				return `${time.monthlyDeadlineDay}日`
			} else {
				return '未设置'
			}
		} else if (time.interval == 4) {
			if (time['quarterlyDeadlineDay' + qtr] !== null) {
				const text = new Date(
					time['quarterlyDeadlineDay' + qtr].substring(4).replace(/./, year + '-')
				)
				return `${text.getMonth() + 1}月${text.getDate()}日`
			} else {
				return '未设置'
			}
		} else if (time.interval == 5) {
			if (time.yearlyDeadlineDay !== null) {
				const text = new Date(time.yearlyDeadlineDay.substring(4).replace(/./, year + '-'))
				return `${text.getMonth() + 1}月${text.getDate()}日`
			} else {
				return '未设置'
			}
		} else if (time.interval == 6) {
			if (isFirstHalfOfMonth()) {
				// 上半个月
				if (time.downHalfMonthReminderTime) {
					return `${time.downHalfMonthReminderTime}日`
				} else {
					return '未设置'
				}
			} else {
				if (time.downHalfMonthDateOnlyTime) {
					return `${time.downHalfMonthDateOnlyTime}日`
				} else {
					return '未设置'
				}
			}
		} else if (time.interval == 7) {
			if (isFirstHalfOfYear()) {
				if (time.upHalfYearDateOnlyTime !== null) {
					const text = new Date(
						time.upHalfYearDateOnlyTime.substring(4).replace(/./, year + '-')
					)
					return `${text.getMonth() + 1}月${text.getDate()}日`
				} else {
					return '未设置'
				}
			} else {
				if (time.downHalfYearDateOnlyTime !== null) {
					const text = new Date(
						time.downHalfYearDateOnlyTime.substring(4).replace(/./, year + '-')
					)
					return `${text.getMonth() + 1}月${text.getDate()}日`
				} else {
					return '未设置'
				}
			}
		} else if (time.interval == 0) {
			// 不提醒
			return '-'
		}
	}
})
const getReadCycle = computed(() => {
	return (interval: number) => {
		switch (interval) {
			case 0:
				return '不提醒'
			case 2:
				return '每周'
			case 3:
				return '每月'
			case 4:
				return '每季度'
			case 5:
				return '每年'
			case 6:
				return '每半月'
			case 7:
				return '每半年'
			default:
				return '-'
		}
	}
})
const getEndtimeStatus = (d: number) => {
	if (d === 1 || d === 0) {
		return getImage('danger')
	}
	if (d > 1 && d <= 3) {
		return getImage('warning')
	}
	if (d > 3 || d < 0) {
		return getImage('normal')
	}
}

const autoFillStatusList: any = [
	{
		value: 0,
		label: '无需填报',
	},
	{
		value: 1,
		label: '需填报已填报',
	},
	{
		value: 2,
		label: '需填报未填报',
	},
	{
		value: 3,
		label: '其他（不定期填报）',
	},
]
// const getFillStatus = computed(() => {
// 	return (time: any) => {
// 		const date = new Date()
// 		const month = date.getMonth() + 1
// 		const qtr = month <= 3 ? 1 : month <= 6 ? 2 : month <= 9 ? 3 : 4
// 		const year = date.getFullYear()
// 		switch (time.interval) {
// 			case 0:
// 				return getImage('-')
// 			case 2:
// 				if (time.weeklyDeadlineDayOfWeek !== null) {
// 					const dayOfweek = dayjs().day()
// 					if (time.weeklyDeadlineDayOfWeek - dayOfweek < 0) {
// 						return getImage('normal')
// 					} else {
// 						return getEndtimeStatus(time.weeklyDeadlineDayOfWeek - dayOfweek)
// 					}
// 				}
// 				break
// 			case 3:
// 				if (time.monthlyDeadlineDay) {
// 					const dayOfMonth = dayjs().date()
// 					if (time.monthlyDeadlineDay - dayOfMonth < 0) {
// 						return getImage('normal')
// 					} else {
// 						return getEndtimeStatus(time.monthlyDeadlineDay - dayOfMonth)
// 					}
// 					break
// 				}
// 			case 4:
// 				if (time['quarterlyDeadlineDay' + qtr] !== null) {
// 					const text = new Date(
// 						time['quarterlyDeadlineDay' + qtr].substring(4).replace(/./, year + '-')
// 					)
// 					const endTIme = dayjs(
// 						dayjs().year() + '-' + (Number(text.getMonth()) + 1) + '-' + text.getDate()
// 					)

// 					return getEndtimeStatus(endTIme.diff(dayjs(), 'day'))
// 				}
// 				break
// 			case 5:
// 				if (time.yearlyDeadlineDay !== null) {
// 					const date = new Date()
// 					const year = date.getFullYear()
// 					const text = new Date(time.yearlyDeadlineDay.substring(4).replace(/./, year + '-'))
// 					const endTIme = dayjs(
// 						dayjs().year() + '-' + (Number(text.getMonth()) + 1) + '-' + text.getDate()
// 					)
// 					return getEndtimeStatus(endTIme.diff(dayjs(), 'day'))
// 				}
// 				break
// 			case 6:
// 				const dayOfMonth = dayjs().date()
// 				if (isFirstHalfOfMonth()) {
// 					// 上半个月
// 					if (time.downHalfMonthReminderTime) {
// 						if (time.downHalfMonthReminderTime - dayOfMonth < 0) {
// 							return getImage('normal')
// 						} else {
// 							return getEndtimeStatus(time.downHalfMonthReminderTime - dayOfMonth)
// 						}
// 					}
// 				} else {
// 					if (time.downHalfMonthDateOnlyTime) {
// 						if (time.downHalfMonthDateOnlyTime - dayOfMonth < 0) {
// 							return getImage('normal')
// 						} else {
// 							return getEndtimeStatus(time.downHalfMonthDateOnlyTime - dayOfMonth)
// 						}
// 					}
// 				}
// 				break
// 			case 7:
// 				if (isFirstHalfOfYear()) {
// 					if (time.upHalfYearDateOnlyTime !== null) {
// 						const text = new Date(time.upHalfYearDateOnlyTime.substring(4).replace(/./, year + '-'))
// 						const endTIme = dayjs(
// 							dayjs().year() + '-' + (Number(text.getMonth()) + 1) + '-' + text.getDate()
// 						)
// 						return getEndtimeStatus(endTIme.diff(dayjs(), 'day'))
// 					}
// 					break
// 				} else {
// 					if (time.downHalfYearDateOnlyTime !== null) {
// 						const text = new Date(
// 							time.downHalfYearDateOnlyTime.substring(4).replace(/./, year + '-')
// 						)
// 						const endTIme = dayjs(
// 							dayjs().year() + '-' + (Number(text.getMonth()) + 1) + '-' + text.getDate()
// 						)

// 						return getEndtimeStatus(endTIme.diff(dayjs(), 'day'))
// 					}
// 				}
// 				break
// 			default:
// 				return '-'
// 		}
// 	}
// })

const getImage = (type: number) => {
	let imgType
	switch (type) {
		case 1:
			imgType = 'normal'
			break
		case 2:
			imgType = 'warning'
			break
		case 3:
			imgType = 'danger'
			break
	}
	// console.log(type)

	// if (!type || type === '-') return
	return new URL(`../../../assets/image/${imgType}.svg`, import.meta.url).href
}

const getContent = (type: number) => {
	switch (type) {
		case 1:
			return ''
		case 2:
			return '距离周期截止日期剩余3天，请尽快更新'
		case 3:
			return '距离周期截止日期剩余1天，请尽快更新'
	}
}

const getTimesInfo = (type: number) => {
	switch (type) {
		case 2:
			return '周'
		case 6:
			return '半月'
		case 3:
			return '月'
		case 4:
			return '季度'
		case 7:
			return '半年'
		case 5:
			return '年'
		case 0:
			return '不定期'
	}
}
const onChangeUpdateTime = (index: number) => {
	currentUpdateTime.value = index
	switch (index) {
		case 1:
			fromTime.value = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
			toTime.value = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
			break
		case 2:
			fromTime.value = dayjs().startOf('week').format('YYYY-MM-DD HH:mm:ss')
			toTime.value = dayjs().endOf('week').format('YYYY-MM-DD HH:mm:ss')
			break
		case 3:
			fromTime.value = dayjs().startOf('month').format('YYYY-MM-DD HH:mm:ss')
			toTime.value = dayjs().endOf('month').format('YYYY-MM-DD HH:mm:ss')
			break
		case 4:
			fromTime.value = dayjs().startOf('year').format('YYYY-MM-DD HH:mm:ss')
			toTime.value = dayjs().endOf('year').format('YYYY-MM-DD HH:mm:ss')
			break
		default:
			fromTime.value = ''
			toTime.value = ''
			break
	}

	onSearch()
	// startUpWorker(toRaw(currentFilterLedgerIds.value))
}

const onToggleTable = () => {
	toggle.value = !toggle.value
	if (toggle.value) {
		cleanWorker()
	} else {
		// startUpWorker(toRaw(currentFilterLedgerIds.value))
		console.log(1111, activeMoreRunway, currentFull)

		if (isFull.value) {
			onFull(currentFull.runway, currentFull.index, true)
		} else {
			getAllRunway()
		}
	}
}

const onLedgerTableSuccess = (items: any, total: number) => {
	console.log('onLedgerTableSuccess', items)
	totalCount.value = total
	startUpWorker(items.map((f: any) => f.id))
}

// 发布部门搜索框
const selectLoading = ref<any>(false)
const departmentRunway = ref<any>([]) //所有发布部门
const departmentFilter = ref<any>([]) //过滤后的
const ledgerRunwayList = () => {
	getMyLedgerTypes()
		.then((res) => {
			let assemblyDepartments = [] as any
			res.data.forEach((item: any) => {
				if (item?.name) {
					let obj = {label: item.name, value: item.name}
					assemblyDepartments.push(obj)
				}
			})
			departmentFilter.value = assemblyDepartments
			departmentRunway.value = assemblyDepartments
		})
		.catch((err: any) => {
			if (err.response?.status === 500) {
				ElNotification.error('当前操作“我的业务表”用户量较多，请5分钟后再试')
			}
		})
}

const onCascadeElChange = (val: any, nodes: any) => {
	// console.log(val[0])
	currentGrade.value = val ? val[0] : ''
	if (val && val.length === 1) {
		searchForm.departmentId = ''
	} else {
		searchForm.departmentId = val ? val[val.length - 1] : ''
	}
	onSearch()
}
const onCascadeRunwayElChange = (val: any) => {
	searchForm.LedgerRunwayId = val[2]
}
const remoteMethod = (query: string) => {
	if (query) {
		selectLoading.value = false
		const fliterValue = departmentRunway.value.filter((f: any) => {
			return f.value.includes(query)
		})
		departmentFilter.value = fliterValue
	} else {
		console.log('进入了else')

		departmentFilter.value = departmentRunway.value
		console.log(departmentFilter.value)
	}
}

const isDeactivated = ref(false)
const isDown = ref(false)

watch(
	() => viewStore.getMScroll,
	(val) => {
		if (isFull.value && !isLoading.value && !isDeactivated.value && !scrollOver.value) {
			if (useMScroll().isScrollBottom(100)) {
				currentFull.page++
				isDown.value = true
				onFull(currentFull.runway, currentFull.index, currentFull.search, true)
			}
		}
	},
	{deep: true}
)

onMounted(() => {
	getAllRunway()
	setTimeout(async () => {
		if (totalCount.value === 0) {
			const id = JSON.parse(
				localStorage.getItem('currentDepartmentInfo') as string
			).departmentExtendId
			if (id === null) return ElMessage.warning('当前用户未绑定大部门!')

			const resData = await getDataLedger(id)

			dataLedgerList.value = resData.data.items

			ElMessageBox.alert(
				`当前暂无业务表，您可尝试切换系统右上角的“当前部门”，或者您可联系部门的数据管理员为您授权业务表。（查询到您当前部门的数据管理员为${dataLedgerList.value
					.map((v) => `${v.name}(${v.phone ?? ''})`)
					.toString()}`,
				'提示',
				{
					confirmButtonText: '确定',
					cancelButtonText: '',
				}
			).then(async () => {})
		}
	}, 1000)
	// getRunwayPlate()
})

onActivated(async () => {
	isDeactivated.value = false
	// 清除详情的滚动信息
	localStorage.removeItem('tableScroll')

	const isList = useRoute().query.isList as string
	const grade = useRoute().query.grade as string
	const departmentId = useRoute().query.departmentId as string

	if (grade) {
		currentGrade.value = grade
		cascadeValue.value = [Number(grade)]
	}

	if (departmentId) {
		searchForm.departmentId = departmentId
		cascadeValue.value = [Number(grade), departmentId]
	}

	if (isList === 'true') {
		toggle.value = false
		onToggleTable()
		return
	}

	// 获取所有跑道发布部门
	ledgerRunwayList()

	await getAllRunway()
})
onDeactivated(() => {
	isDeactivated.value = true
	cleanWorker()
	if (isFull.value) {
		localStorage.setItem(
			'OVERVIEW_ISFULL',
			JSON.stringify({isFull: true, runway: activeMoreRunway.value})
		)
	}
})
onUnmounted(() => {
	cleanWorker()
})

defineExpose({
	getData: () => getAllRunway(),
})

const onReport = () => {
	router.push('/ledger/reportProgressList')
}
const onOffline = () => {
	router.push('/ledger/offline')
}

const onClear = () => {
	cascadeValue.value = []
	searchForm.departmentId = ''
}
const reqParams = ref<any>({
	ParentRunway: '',
	Runway: '',
})
const onCascadeElChangeRunway = (val: any, nodes: any) => {
	searchForm.LedgerRunwayId = val[2]
}
const isExportRecord = ref(false)
// 导出所有数据
const exportRecord = () => {
	exportMyLedgersLittleAsync().then((res) => {
		isExportRecord.value = true
	})
}
</script>
<template>
	<div class="ledger">
		<div class="ledger-toolbar">
			<div class="top">
				<span class="label fs-16 mg-right-30">
					业务表总数:
					<template v-if="totalCount > -1">
						<span class="mg-left-5 mg-right-5">
							{{ totalCount }}
						</span>
						个
					</template>
					<el-icon v-else class="loading-animation mg-left-5"><Loading /></el-icon>
				</span>
				<div class="btns">
					<el-button type="primary" @click="onReport"> 填报进度 </el-button>
					<el-dropdown trigger="click" class="mg-left-10">
						<el-button type="primary"
							>导出数据<el-icon class="el-icon--right"><arrow-down /></el-icon
						></el-button>
						<template #dropdown>
							<el-dropdown-item @click="exportRecord"> 导出数据 </el-dropdown-item>
							<el-dropdown-item @click="isExportRecord = true">
								导出记录
							</el-dropdown-item>
						</template>
					</el-dropdown>
					<el-button type="primary" @click="onOffline"> 已下线业务表 </el-button>
					<el-button
						pr-10px
						type="primary"
						@click="closeSearch = !closeSearch"
						class="pd-right-10"
					>
						{{ closeSearch ? '展开' : '收起' }}搜索
						<i
							data-v-dbc6b36d=""
							class="icon i-ic-baseline-expand-more"
							:style="{rotate: closeSearch ? '0deg' : '180deg'}"
						></i>
					</el-button>
					<el-button-group>
						<el-button :type="!toggle ? 'primary' : 'default'" @click="onToggleTable"
							>卡片</el-button
						>
						<el-button :type="toggle ? 'primary' : 'default'" @click="onToggleTable">
							列表
						</el-button>
					</el-button-group>
				</div>
			</div>
			<div class="search" :class="{'hide-search': closeSearch}" v-action:enter="onSearch">
				<el-select
					v-model="currentUpdateTime"
					@change="onChangeUpdateTime"
					placeholder="请选择更新量统计范围"
					clearable
					size="default"
				>
					<el-option
						v-for="(item, index) in updateTime"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					/>
				</el-select>

				<Cascade
					v-model="cascadeValue"
					:one-select="true"
					:one-select-props="oneSelectProps"
					:show-all-levels="true"
					:options="DEPARTMENT_OPTIONS_ENUM"
					:keys="['name', 'id']"
					max-level="3"
					@el-change="onCascadeElChange"
					@clear="onClear"
					placeholder="请选择发布部门"
				></Cascade>
				<Cascade
					v-if="showLedgerRunway"
					v-model="cascadeRunwayValue"
					:one-select="true"
					:one-select-props="oneSelectRunwayProps"
					:show-all-levels="true"
					:options="runwayData"
					:max-level="2"
					:keys="['businessName', 'id']"
					@el-change="onCascadeElChangeRunway"
					placeholder="请选择所属跑道"
				></Cascade>

				<el-select
					v-model="searchForm.ReminderInterval"
					clearable
					placeholder="请选择业务表更新周期"
					size="default"
					@change="onSearch"
				>
					<el-option
						v-for="item in searchOptions"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					/>
				</el-select>
				<el-select
					v-model="searchForm.autoFillStatus"
					clearable
					filterable
					@change="onSearch"
					placeholder="请选择填报状态"
				>
					<el-option
						v-for="(item, index) in autoFillStatusList"
						:key="index"
						:label="item.label"
						:value="item.value"
					/>
				</el-select>
				<el-select
					v-if="searchForm.autoFillStatus === 2"
					v-model="searchForm.LitStatus"
					clearable
					placeholder="请选择预警等级"
					size="default"
					@change="onSearch"
				>
					<el-option
						v-for="item in warnLevelList"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					/>
				</el-select>
				<el-select
					v-else
					v-model="searchForm.LitStatus"
					clearable
					:disabled="true"
					placeholder="请选择预警等级"
					size="default"
					@change="onSearch"
				>
					<el-option
						v-for="item in []"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					/>
				</el-select>
				<el-input
					class="search-input"
					v-model="searchForm.value"
					placeholder="请输入业务表名称"
					size="default"
					clearable
				></el-input>
				<el-button type="primary" @click="onSearch" :loading="isLoading"> 查询 </el-button>
				<el-button type="default" @click="onReset"> 清空 </el-button>
			</div>
		</div>

		<div class="card-items" v-if="ledgerList.length > 0 && !toggle">
			<template v-for="(run, index) of runwayList as any">
				<div
					v-if="
						(activeMoreRunway && run.label === activeMoreRunway) ||
						(!activeMoreRunway && ledgerList[index]?.items.length > 0)
					"
					class="card-item half"
					:class="{
						activeMore: run.label === activeMoreRunway,
						last: isLastRunway(run.label),
					}"
				>
					<div class="card-title">
						<i class="title-icon">
							<svg
								v-if="run.label === '党的建设'"
								xmlns="http://www.w3.org/2000/svg"
								xmlns:xlink="http://www.w3.org/1999/xlink"
								t="1714376572675"
								class="icon mg-top-5"
								viewBox="0 0 1024 1024"
								version="1.1"
								p-id="85462"
								width="18"
								height="18"
							>
								<path
									d="M535.600762 0c206.583873 22.093206 345.986032 118.584889 418.283682 289.515683 72.297651 170.930794 59.201016 340.520635-39.334603 508.810158L1003.68254 905.057524 885.365841 1024l-96.17473-97.202794c-120.758857 82.590476-259.019175 107.796317-414.744381 75.658159-77.568-16.026413-143.059302-43.162413-196.477968-81.444571l-7.078603 5.827047-2.320254-2.519365c0.824889 4.644571 1.300317 9.451683 1.300317 14.336 0 42.772317-33.475048 77.470476-74.776381 77.470476C53.792508 1016.124952 20.31746 981.463365 20.31746 938.654476c0-42.772317 33.475048-77.429841 74.735746-77.429841 6.725079 0 13.21854 0.906159 19.390984 2.596571a392.102603 392.102603 0 0 1-61.163682-92.554158l75.836952-80.225524c83.427556 78.299429 171.771937 126.780952 265.037207 145.448635 93.261206 18.708317 183.300063 5.985524 270.108444-38.204953l-320.182857-335.396571-97.19873 94.996317-134.009905-156.200635 245.170794-243.947682 0.475428 0.353524-1.101206-1.381588c22.653968 9.256635 43.422476 14.457905 62.264889 15.599746l4.091936-0.788317 7.001397-1.852952c25.961651-7.952254 53.613714-22.170413 83.114667-42.650413l-2.832254 1.930159 2.084571-2.917588 75.991365 77.275429-0.154412 0.08127 0.94273 0.979301-120.283429 132.331683 318.610286 338.078476-318.415238-338.074413-0.195048 0.272254 319.553016 339.378794c68.758349-71.444317 80.083302-182.706794 33.946413-333.746794C776.996571 191.605841 681.138794 77.389206 535.600762 0z"
									p-id="85463"
									fill="#1764CE"
								/>
							</svg>
							<svg
								v-if="run.label === '经济发展'"
								xmlns="http://www.w3.org/2000/svg"
								xmlns:xlink="http://www.w3.org/1999/xlink"
								t="1714376737525"
								class="icon mg-top-5"
								viewBox="0 0 1024 1024"
								version="1.1"
								p-id="97403"
								width="18"
								height="18"
							>
								<path
									d="M1023.976411 534.211283c-0.899979-65.0985-54.398747-117.997282-119.497248-118.197277-41.499044-0.199995-78.198199 20.799521-99.797701 52.698786-4.499896 6.699846-13.299694 9.09979-20.499527 5.399876L398.590818 280.017139c-6.999839-3.499919-10.39976-11.69973-7.999816-19.199557 6.099859-19.199558 9.399783-39.599088 9.399783-60.7986C399.990785 88.721546 308.092902-1.676371 196.795466 0.023589 87.797977 1.72355 0 90.621502 0 200.018982c0 92.597867 62.898551 170.396075 148.196586 193.195549 6.999839 1.899956 11.799728 8.199811 11.799728 15.399646v253.994148c0 7.199834-4.799889 13.399691-11.599733 15.399646C81.198129 697.007533 31.999263 758.806109 31.999263 832.004423c0 88.597959 71.998341 160.296307 160.5963 159.996314 88.09797-0.299993 159.396328-71.798346 159.396328-159.996314v-1.399968c0-6.099859 3.399922-11.69973 8.799797-14.399668l425.990187-214.495059c6.999839-3.499919 15.499643-1.499965 20.099537 4.79989 21.799498 29.999309 57.198682 49.598857 97.197761 49.598857 66.798461-0.099998 120.897215-54.798738 119.897238-121.897192z m-282.59349 18.599572l-393.990924 198.395429c-7.299832 3.699915-16.299625 1.199972-20.699523-5.699868-20.799521-32.399254-52.998779-56.698694-90.897906-67.498445-6.899841-1.999954-11.799728-8.199811-11.799728-15.399646V412.314091c0-7.699823 5.499873-14.299671 12.9997-15.699638 42.299026-7.899818 79.898159-29.09933 108.297505-59.098638 4.799889-5.099883 12.499712-6.399853 18.799567-3.199927l377.291309 189.995623c11.69973 5.899864 11.69973 22.599479 0 28.499344z"
									p-id="97404"
									fill="#1764CE"
								/>
							</svg>
							<svg
								v-if="run.label === '民生服务'"
								xmlns="http://www.w3.org/2000/svg"
								xmlns:xlink="http://www.w3.org/1999/xlink"
								t="1714376661224"
								class="icon"
								viewBox="0 0 1024 1024"
								version="1.1"
								p-id="91372"
								width="30"
								height="30"
							>
								<path
									d="M158.0544 508.0064A188.672 188.672 0 0 1 102.4 373.76c0-50.688 19.8144-98.4064 55.6544-134.2464 71.68-71.68 196.7616-71.7312 268.544 0.0512l53.504 53.4528-97.4336 99.4304c-43.1104 44.0832-44.6976 115.3536-2.4064 162.6112 20.3264 22.784 53.76 31.2832 85.504 31.2832 31.8464 0 49.7152-12.1856 80.0256-34.3552l101.12-99.7376 137.472 137.472-236.032 236.032c-0.7168 0.7168-1.7408 0.9728-2.5088 1.6384-19.712 17.2544-51.456 16.9472-70.144-1.6384L158.0544 508.0064z"
									p-id="91373"
									fill="#1764CE"
								/>
								<path
									d="M866.0992 508.0576l-52.2752 52.2752-124.416-124.416-0.4096-0.4096-16.9984-16.9984-0.0512 0.0512-27.136-26.88-131.2768 132.608c-24.6784 22.4768-76.5952 21.1968-100.5568-2.7136-23.8592-23.8592-23.3984-73.4208 0.6144-97.4336l109.5168-110.5408 0.1536 0.1536 74.24-74.24a190.1568 190.1568 0 0 1 268.5952-0.0512 190.2592 190.2592 0 0 1 0 268.5952z"
									p-id="91374"
									fill="#1764CE"
								/>
							</svg>
							<svg
								v-if="run.label === '平安法治'"
								xmlns="http://www.w3.org/2000/svg"
								xmlns:xlink="http://www.w3.org/1999/xlink"
								t="1714376688569"
								class="icon mg-top-5"
								viewBox="0 0 1024 1024"
								version="1.1"
								p-id="94357"
								width="20"
								height="20"
							>
								<path
									d="M878.144 885.632a11.648 11.648 0 0 1-7.936-11.136v-18.048a30.912 30.912 0 0 0-30.912-30.912H226.304a30.912 30.912 0 0 0-30.912 30.912v13.376a11.712 11.712 0 0 1-9.92 11.584 112 112 0 0 0-94.72 110.656h864.576a112.064 112.064 0 0 0-77.184-106.432z m0 0M213.76 638.464a21.76 21.76 0 0 0 15.104 6.016 21.952 21.952 0 0 0 15.104-37.824l-130.304-123.52a21.76 21.76 0 0 0-31.04 0.768 21.76 21.76 0 0 0 0.832 30.976L213.76 638.464z m0 0M238.016 712.96a21.952 21.952 0 0 0-25.216-18.112l-161.664 26.624a21.952 21.952 0 1 0 7.168 43.328l161.664-26.624a21.952 21.952 0 0 0 18.048-25.216z m0 0M973.12 713.152l-327.552-305.088 117.76-117.696 0.064 0.064a27.776 27.776 0 0 0 39.232 0l1.152-1.088a27.84 27.84 0 0 0 0-39.296L593.856 40.128a27.84 27.84 0 0 0-39.296 0l-1.152 1.088a27.712 27.712 0 0 0 0 39.296h0.064L260.16 373.824l-0.064-0.064a27.84 27.84 0 0 0-39.296 0l-1.024 1.088a27.84 27.84 0 0 0 0 39.296l209.92 209.984a27.84 27.84 0 0 0 39.296 0l1.088-1.152a27.776 27.776 0 0 0 0-39.232l-0.064-0.064 117.696-117.76 305.088 327.552a56.768 56.768 0 0 0 81.792 1.408 56.768 56.768 0 0 0-1.472-81.728zM342.272 398.336a11.52 11.52 0 0 1-16.256 0l-0.512-0.512a11.52 11.52 0 0 1 0-16.256l235.712-235.712c4.48-4.48 11.84-4.48 16.32 0l0.384 0.448a11.52 11.52 0 0 1 0 16.256L342.272 398.336z m0 0"
									fill="#1764CE"
									p-id="94358"
								/>
							</svg>
						</i>

						<span class="label"
							>{{ run.label }} ({{ ledgerList[index].totalCount }})</span
						>
						<span
							class="more"
							v-if="
								(index === 0 && ledgerList[index].totalCount > 4) ||
								(index > 0 && ledgerList[index].totalCount > 4)
							"
							@click="onFull(run, index)"
						>
							<span>
								{{ activeMoreRunway ? '收起' : '更多' }}
								<i
									:class="
										activeMoreRunway
											? 'i-ic-baseline-keyboard-double-arrow-left'
											: 'i-ic-baseline-keyboard-double-arrow-right'
									"
								></i>
							</span>
						</span>
					</div>
					<div class="cards" style="height: calc(100% - 50px)">
						<template v-for="(item, itemIndex) of ledgerList[index].items">
							<div
								class="item"
								:class="{star: item.isTop}"
								@click="onClickLedger('fill', item)"
							>
								<!-- 'pending-upgrade-border': true -->
								<el-tooltip
									effect="dark"
									:content="item.ledgerName"
									placement="top"
								>
									<div class="info" relative>
										<div v-if="item.isNew" class="new">
											<span>NEW</span>
										</div>
										<div class="info-context">
											<h2
												style="
													width: 95%;
													display: flex;
													justify-content: space-between;
												"
											>
												<div style="width: calc(100% - 20px); flex: 1">
													<span style="max-width: 60%">{{
														item.ledgerName
													}}</span>
													<span
														style="
															left: 3%;
															font-size: 14px;
															padding: 1px 5px;
															background: rgb(239, 238, 250);
															color: rgb(23, 100, 206);
															text-align: center;
														"
														>{{
															getTimesInfo(
																item?.littleLedgerDto?.taskInterval
															)
														}}</span
													>
												</div>
												<span
													style="
														width: 30px;
														flex: none;
														justify-content: flex-end;
													"
													class="df"
													v-if="item?.department?.litStatus && item?.permissions.some((per:number) => per === 1 || per === 2 || per === 3)&&item?.autoFillStatus !==  1 && item?.littleLedgerDto?.taskInterval !== 0"
												>
													<el-tooltip
														class="box-item"
														effect="dark"
														v-if="item?.department?.litStatus !== 1"
														:content="
															getContent(item?.department?.litStatus)
														"
														placement="top-start"
													>
														<img
															style="width: 20px; height: 20px"
															:src="
																getImage(
																	item?.department?.litStatus
																)
															"
															alt=""
														/>
													</el-tooltip>
													<img
														v-else
														style="width: 20px; height: 20px"
														:src="getImage(item?.department?.litStatus)"
														alt=""
													/>
												</span>
												<span
													style="
														width: 30px;
														flex: none;
														justify-content: flex-end;
													"
													class="df"
													v-if="item?.autoFillStatus === 1"
												>
													<img
														style="width: 20px; height: 20px"
														:src="isPass"
														alt=""
													/>
												</span>
											</h2>

											<div
												class="w-full fs-14 df mg-top-15"
												style="justify-content: space-between"
											>
												<div class="count">
													<div>数据总量</div>
													<label
														v-if="
															getUpdateTotalById(item.ledgerId) > -1
														"
													>
														{{ getUpdateTotalById(item.ledgerId) }}
													</label>
													<el-icon
														v-else
														class="loading-animation mg-top-10"
														><Loading
													/></el-icon>
												</div>
												<div class="count">
													<div>更新量</div>
													<label
														v-if="
															getUpdateCountById(item.ledgerId) > -1
														"
													>
														{{ getUpdateCountById(item.ledgerId) }}
													</label>
													<el-icon
														v-else
														class="loading-animation mg-top-10"
														><Loading
													/></el-icon>
												</div>
												<div
													class="count"
													v-if="
														item?.littleLedgerDto &&
														item?.littleLedgerDto?.taskInterval !== 0
													"
												>
													<div>
														开始时间<template
															v-if="item.cycleName?.includes('/')"
															>/截止时间</template
														>
													</div>
													<label>
														{{ item.cycleName }}
													</label>
												</div>
												<div class="count" v-else>
													<div>开始时间/截止时间</div>
													<label> 按实际情况更新 </label>
												</div>
											</div>
											<div
												class="df w-full mg-top-5"
												style="justify-content: space-between"
											>
												<div
													class="department"
													:title="item.ledgerTypeName"
													style="
														max-height: 30px;
														display: flex;
														align-items: center;
													"
												>
													#{{ item.ledgerTypeName }}
												</div>
												<div
													style="
														max-width: 50%;
														overflow: hidden;
														text-overflow: ellipsis;
														white-space: nowrap;
														text-align: end;
														display: flex;
														flex-direction: column-reverse;
													"
												>
													<div
														title="最后更新时间"
														v-if="
															item.IsDepartmentalAuthorization
																? item.department
																		?.ledgerDataLastUpdatedTime
																: getUpdateLastModificationTime(
																		item.ledgerId
																  )
														"
													>
														<!-- <el-icon><Clock /></el-icon> -->
														<!-- 更新时间:{{ getUpdateLastModificationTime(item.ledgerId) }} -->
														更新时间:{{
															item.IsDepartmentalAuthorization
																? item.department
																		?.ledgerDataLastUpdatedTime
																: getUpdateLastModificationTime(
																		item.ledgerId
																  )
														}}
													</div>
													<div
														:title="
															item.ledgerRunwayRelations[0]
																.ledgerRunway.parent?.businessName +
															'/' +
															item.ledgerRunwayRelations[0]
																.ledgerRunway.businessName
														"
														v-if="
															item.ledgerRunwayRelations.length !==
																0 && showLedgerRunway
														"
														style="
															width: 100%;
															overflow: hidden;
															text-overflow: ellipsis;
															white-space: nowrap;
															margin-bottom: 5px;
														"
													>
														所属跑道:{{
															item.ledgerRunwayRelations[0]
																.ledgerRunway?.parent
																?.businessName +
															'/' +
															item.ledgerRunwayRelations[0]
																.ledgerRunway.businessName
														}}
													</div>
												</div>
											</div>

											<Icons
												style="position: absolute; top: 17px; right: 10px"
												name="Thumbtack"
												@click.stop="onLedgerTop(item, run)"
												:color="
													item.isTop ? '#3063c7' : 'rgba(102,102,102,0.5)'
												"
											></Icons>
											<i
												class="star i-pajamas-thumbtack-solid"
												@click.stop="onLedgerTop(item, run)"
												style="top: 16px"
											></i>
										</div>
									</div>
								</el-tooltip>
							</div>
						</template>
						<template v-if="ledgerList[index].items.length === 0">
							<el-empty
								:description="isLoading ? '努力获取中...' : '暂无数据'"
								style="padding: 0; margin: 0 auto"
								:image-size="80"
							/>
						</template>
					</div>
				</div>
			</template>
		</div>
		<LoadingTransition v-if="isDown" />

		<LedgerTable
			v-if="toggle"
			ref="ledgerTableRef"
			:user-level="userLevel"
			:total-count="[
				ledgerList[0]?.totalCount,
				ledgerList[1]?.totalCount,
				ledgerList[2]?.totalCount,
				ledgerList[3]?.totalCount,
			]"
			:grade="currentGrade"
			:search-form="searchForm"
			:update-count="updataCountList"
			:on-success="onLedgerTableSuccess"
			@loading="isLoading = $event"
		></LedgerTable>
		<BusinessTableExport
			v-model="isExportRecord"
			width="1000"
			title="导出记录"
			:interval="2000"
			@close="isExportRecord = false"
			@click-close="isExportRecord = false"
		></BusinessTableExport>
	</div>
</template>
<style scoped lang="scss">
.ellipsis {
	max-width: 200px;
	overflow: hidden;
	text-overflow: ellipsis;
}
.top-comp {
	height: unset;
	margin-bottom: 20px;
}

.ledger-toolbar {
	background-color: #fff;
	margin-bottom: 20px;

	.top {
		align-items: center;
		border-bottom: 1px solid var(--z-line);
		display: flex;
		height: 50px;
		padding: 0 10px;
		.label {
			align-items: center;
			display: flex;
			font-size: 14px;
			flex: 1;
			margin-right: 10px;
			white-space: nowrap;
			width: 100px;

			span {
				color: var(--z-main);
				font-weight: 500;
				font-size: 16px;
			}
		}

		.btns {
			> * {
				margin-left: 10px;
			}
		}
	}

	.search {
		align-items: center;
		display: flex;
		height: 62px;
		overflow: hidden;
		padding: 15px;
		transition: all 0.15s linear;
		white-space: nowrap;
		width: 100%;

		> div {
			margin-right: 15px;
			width: 25%;
		}

		&.hide-search {
			height: 0;
			padding: 0;
		}
	}
}

.card-items {
	align-items: stretch;
	display: flex;
	flex-wrap: wrap;

	.card-item {
		background: #fff;
		border: 1px solid var(--z-line);
		margin-bottom: 20px;
		min-height: 235px;
		max-height: 600px;
		overflow: hidden;
		transition: all 0.15s linear;
		width: 100%;

		.card-title {
			align-items: center;
			border-bottom: 1px solid var(--z-line);
			color: var(--z-font-color);
			display: flex;
			font-size: 16px;
			font-weight: 400;
			height: 40px;
			margin-bottom: 15px;
			padding: 10px;

			.label {
				flex: 1;
			}

			.more {
				cursor: pointer;
				color: var(--z-main);
				font-size: 14px;
				span {
					align-items: center;
					display: flex;
				}
			}
		}

		&.half {
			margin-right: 20px;
			width: calc(50% - 10px);

			&:nth-child(even) {
				margin-right: 0;
			}

			.item:nth-child(even) {
				margin-right: 0;
			}
		}

		&.last {
			margin-right: 0;
			width: 100%;
			.cards {
				.item {
					margin-right: 10px;
					width: calc(25% - 8px);

					&:nth-child(4n) {
						margin-right: 0;
					}
				}
			}
		}

		&.activeMore {
			margin-right: 0;
			max-height: 100%;
			width: 100%;
			.cards {
				.item {
					margin-right: 10px;
					width: calc(25% - 8px);
					&:nth-child(4n) {
						margin-right: 0;
					}
				}
			}
		}
	}
}
</style>

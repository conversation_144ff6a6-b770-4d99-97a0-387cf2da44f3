<script setup lang="ts" name="fill">
import {
	computed,
	nextTick,
	onActivated,
	onDeactivated,
	onMounted,
	onUnmounted,
	ref,
	toRaw,
	watch,
} from 'vue'
import {
	ElMessage,
	ElMessageBox,
	FormInstance,
	FormRules,
	UploadFile,
	UploadFiles,
	ElNotification,
} from 'element-plus'
import {USER_ROLES_ENUM} from '@/define/organization.define'
import {useRouter, useRoute} from 'vue-router'
import {
	getDetailByLedgerId,
	downloadExcelTemplateByLedgerId,
	executeImportNotify,
	getLedgerTableListByLedgerId,
	newGetLedgerTableListByLedgerId,
	getLedgerDataInfoById,
	sendCode,
	sendValidateCode,
	ledgerReminder,
	ExportLedgerDataAsync,
	ExportLedgerDataAllAsync,
	getLedgerTableApplyAddByLedgerId,
	updateAuditLegderTableData,
	AuditDeleteLedgerData,
	getAuditLedgerDataInfoById,
	batchDeleteLedgerData,
	getLedgerAuxiliaryFilling,
	getSingleLedgerData,
	getDownLoadData,
	getTableFiledValidateRule,
	postledgerDataAnalysis,
	putOnLineAnalysisConfig,
	addOnLineAnalysisConfig,
	dataAnalysis,
	isExistLedgerDataAnalysis,
	putledgerDataAnalysis,
	getApiAuxiliaryFillingList,
	deleteAllLedgerData,
	ledgerDataAnalysisBatchDelete,
	getApiDataComparison,
	dataComparisonRecordbenchmarkInfo,
	dataComparisonRecordList,
	getDataSetAuxiliaryFilling,
	GetLedgerDetailById,
	getAllSize,
	CanNoDataAudit,
	getLedgerStatisticsByField,
} from '@/api/LedgerApi'
import {useUserStore} from '@/stores/useUserStore'
import {APIConfig} from '@/api/config'
import {saveAs} from 'file-saver'
import ImportHistory from './components/importHistory.vue'
import ExportHistory from './components/exportHistory.vue'
import ExportData from './components/exportData.vue'
import {useArrayToTree} from '@/hooks/useConvertHook'
import fill from '@/plugin/fill'
import collapseForm from './components/collapseForm.vue'
import SubmitAudit from './components/submitAudit.vue'
import auditRecord from './components/auditRecord.vue'
import auditRecordBatch from './components/auditRecordBatch.vue'
import fillProgress from './components/fillProgress.vue'
import dataComparisonOne from './components/dataComparisonOne.vue'
import {deleteLabel, updateLabelTitle} from '@/hooks/useLabels'
import submitModel from './components/submitModel.vue'
import dataComparisonAll from './components/dataComparisonAll.vue'
import {
	isEqual,
	isNotEqual,
	isGreaterThan,
	isGreaterThanOrEqual,
	isLessThan,
	isLessThanOrEqual,
	isBetweenExclusive,
	isNotBetween,
} from '@/define/numberRules'
import {GetUserDepartmentById} from '@/api/OrganizeApi'
import FillScreenData from './components/fillScreenData.vue'
import {dayjs} from 'element-plus'
import util from '@/plugin/util'
import Worker from '@/worker/ProcessingWorker.ts?worker'
import {useEvalHook} from '@/hooks/useEvalHook'
import ExportDataDeclaration from '@/components/common/ExportDataDeclaration.vue'
import {useViewStore} from '@/stores/useViewStore'
import CryptoJS from 'crypto-js'
import {useValidateCode} from '@/hooks/useValidateCode'

enum Permissions {
	View = 0,
	Add = 1,
	Edit = 2,
	Delete = 3,
	Export = 4,
	Approve = 5,
}
const route = useRoute()
const router = useRouter()

const viewStore = useViewStore()
const useCode = useValidateCode()

/* 是否可以编辑：
1. 表头没有desensitizationType都为null, 可以编辑
2 不都为null
列表接口带了xx字段可编辑，不带不可编辑 */

const currentUser = ref(JSON.parse(localStorage.getItem('currentUserInfo') as string))
// 数据管理岗位权限
const role = ref(currentUser.value.staffRole.some((item) => item === USER_ROLES_ENUM.DATA_LEADER))
const userStore = useUserStore()
const fixedFields = [
	'UpdateTime',
	'Community',
	'Street',
	'District',
	'City',
	'Informant',
	'Editor',
	'DataSource',
]

const conditionList = ref<any>([])

const loading = ref(false)
const ledger: any = ref(null)
const tableRef = ref()
const uploadRef = ref()
const searchValue = ref('')
const searchOpen = ref(false)
const cancelOpen = ref(false)
const remindOpen = ref(false)
const showDeclaration = ref(false)
const declarationResult = ref(false)
const iscommunity = route.query.iscommunity as string
// 设置当前的worker
const currentWork: any = ref(null)
// 验证码弹出验证
const codeTitle = ref('导出数据')
const cancelCode: any = ref({
	val1: '',
	val2: '',
	val3: '',
	val4: '',
})

// const timesType = {
// 	{name: '周一', value: dayjs().day(1).format('YYYY-MM-DD')},
// 	{name: '周二', value: dayjs().day(2).format('YYYY-MM-DD')},
// 	{name: '周三', value: dayjs().day(3).format('YYYY-MM-DD')},
// 	{name: '周四', value: dayjs().day(4).format('YYYY-MM-DD')},
// 	{name: '周五', value: dayjs().day(5).format('YYYY-MM-DD')},
// }
const auditOpen = ref(false)
const addOpen = ref(false)
const importOpen = ref(false)
const historyOpen = ref(false)
const historyExport = ref(false)
const height = ref(0)
const fields: any = ref([])
const colData = ref<any[]>([{title: '-', field: '-', fixed: '-'}])
const TilingColData = ref<any>([])
const TilingTableData: any = ref([])
const tableData: any = ref([])
const buttons: any = ref([])
const submitAuditRef = ref()
// 查看更多弹窗显示控制参数
const viewMoreInfoModalIsVisible = ref(false)
const pending = ref(false)
const cascadeOptions: any = ref([])
const searchFormRef = ref()
const tableOffsetHeight = ref(-190)
const blockRef = ref()
const iTime = ref()
const showAddLedgerDataModelisVisible = ref(false)
let noPermission = false
const isPermission = computed(() => {
	clearTimeout(iTime.value)
	return (code: number) => {
		const p: any = useUserStore().getCurrentLedgerUserPermissions

		if (p?.length > 0) {
			buttons.value.forEach((f: any, i: any) => {
				// 修改
				if (p.indexOf(2) == -1 && f.title == '编辑') {
					buttons.value.splice(i, 1)
				}
				// 删除
				if (p.indexOf(3) == -1 && f.title == '删除') {
					buttons.value.splice(i, 1)
				}
			})
			pending.value = false
			return p.includes(code)
		} else {
			const ledgerId = route.query.ledgerId as string
			const hasAdminRole = userStore.getUserInfo.baseRoles.indexOf('管理员') !== -1
			const hasDistrictRole = userStore.getUserInfo.staffRole.indexOf('区县台账运维员') !== -1
			if (ledgerId && ledger.value) {
				if (hasAdminRole || hasDistrictRole) {
					useUserStore().setCurrentLedgerUserPermissions(ledger.value.permissions)
				} else {
					if (ledger.value.permissions.length === 0) {
						iTime.value = setTimeout(function () {
							ElMessage.warning('您没有权限访问该业务表')
							deleteLabel({path: router.currentRoute.value.fullPath})
						}, 900)
					} else {
						useUserStore().setCurrentLedgerUserPermissions(ledger.value.permissions)
					}
				}
				// if (
				// 	ledger.value.permissions.length === 0 &&
				// 	(hasAdminRole === false || hasDistrictRole === false) &&
				// 	!noPermission
				// ) {
				// 	noPermission = true
				// 	ElMessage.warning('您没有权限访问该业务表')
				// 	deleteLabel({path: router.currentRoute.value.fullPath})
				// 	router.push('/ledger/overview')
				// } else {
				// 	useUserStore().setCurrentLedgerUserPermissions(ledger.value.permissions)
				// }
			}
		}
		return false
	}
})

const addFormRef = ref()
const formArray: any = ref([])
const formSearch: any = ref([])
const formTitle: any = ref('新增')
const currentEditData: any = ref(null)
const uploadUrl = ref(`${APIConfig('ledger')}/api/files/static`)
const uploadData = ref({
	Overwrite: true,
	Bucket: 'ledger-import-excel-data',
	Path: useUserStore().getUserInfo.id,
	FileName: +new Date() + '.xlsx',
	__isFile: false,
})

const formRules = ref<FormRules>({
	// name: [{required: true, message: '请输入姓名', trigger: 'blur'}],
})

const pageation = ref({
	currentPage: 1,
	pageSize: 10,
	total: 0,
	pageSizeArray: [10, 30, 50, 100, 300, 500],
})

const importModelValue = ref(1)
const importModel = [
	{label: '仅更新数据', value: 1},
	{label: '仅新增数据', value: 2},
	{label: '更新及新增数据', value: 3},
]

const advancedSearchForm: any = ref(null)
const cascadeData = ref({})
const isClearSearch = ref(false)

function truncateDecimal(num, maxDecimalPlaces = 3) {
	// 转换为字符串
	const strNum = String(num)

	// 找到小数点的位置
	const decimalIndex = strNum.indexOf('.')

	// 如果没有小数点，直接返回原始字符串
	if (decimalIndex === -1) {
		return parseFloat(strNum)
	}

	// 计算小数部分的长度
	const decimalLength = strNum.length - decimalIndex - 1

	// 如果小数部分长度已经超过最大小数位数，则截断
	if (decimalLength > maxDecimalPlaces) {
		return parseFloat(strNum.slice(0, decimalIndex + 1 + maxDecimalPlaces))
	}

	// 否则返回原始字符串
	return parseFloat(strNum)
}
const evalRPN = (expression: string) => {
	let str = expression
	const split = expression.match(/[0-9a-fA-F]{8}(?:-[0-9a-fA-F]{4}){3}-[0-9a-fA-F]{12}/g)

	const fields = formArray.value.filter((f: any) => split?.includes(f.raw.id))
	const target = formArray.value.find(
		(f: any) => f.raw.calculateRule?.rpnExpression === expression
	)

	fields.forEach((f: any) => {
		// str = eval(
		// 	`str.replace(/${f.raw.id}/, ${Number(addFormRef.value.getFieldValue(f.field) || 0)})`
		// )

		str = useEvalHook(
			`argument[0].replace(/${f.raw.id}/, ${Number(
				addFormRef.value.getFieldValue(f.field) || 0
			)})`,
			str
		)
	})

	let val: any = 0
	const splitArray: any = str
		.split(' ')
		.map((m: any) => Number(m))
		.filter((item: any) => !Object.is(item, NaN))
	if (str.includes('max')) {
		val = Math.max(...splitArray)
	} else if (str.includes('min')) {
		val = Math.min(...splitArray)
	} else {
		val = rpn.rpnCalculate(str)
	}

	if (target.type === 'int') {
		val = val.toFixed(0)
	} else {
		val = truncateDecimal(val, target.raw.precision)
	}

	addFormRef.value.setFieldValue(target.field, val)
}

const getEvalRPNText = (expression: string) => {
	let str = expression
	const split = expression.match(/[0-9a-fA-F]{8}(?:-[0-9a-fA-F]{4}){3}-[0-9a-fA-F]{12}/g)
	let fields = formArray.value.filter((f: any) => split?.includes(f.raw.id))

	const target = formArray.value.find(
		(f: any) => f.raw.calculateRule?.rpnExpression === expression
	)

	let newUuidParts = split
		?.map((uuid, index) => {
			if (index < fields.length) {
				return fields[index].title
			} else {
				return uuid
			}
		})
		.join(' ')

	const replacements = {
		'+': '求和',
		'/': '求平均',
		max: '求最大值',
		min: '求最小值',
	}
	if (str.includes('max')) {
		newUuidParts = target.title + '=' + newUuidParts + ' 求最大值'
	} else if (str.includes('min')) {
		newUuidParts = target.title + '=' + newUuidParts + ' 求最小值'
	} else if (str.includes('+') && !str.includes('/')) {
		newUuidParts = target.title + '=' + newUuidParts + ' 求和'
	} else if (str.includes('/') && str.includes('/')) {
		newUuidParts = target.title + '=' + newUuidParts + ' 求平均值'
	}

	// if (newUuidParts.includes('max')) {
	// 	newUuidParts= target.title + "=" + newUuidParts.replace(/max/g, replacements['max'])
	// } else if (newUuidParts.includes('min')) {
	// 	newUuidParts= target.title + "=" + newUuidParts.replace(/min/g, replacements['min'])
	// }  else if (newUuidParts.includes('+')) {
	// 	newUuidParts =	target.title + "=" + newUuidParts.replace(/\+/g, replacements['+'])
	// }  else if (newUuidParts.includes('/')) {
	// 	newUuidParts= target.title + "=" + newUuidParts.replace(/\//g, replacements['/'])
	// }

	formArray.value.forEach((item: any) => {
		if (item.raw.id === target.raw.id) {
			if (target.type === 'int') {
				item.tip = newUuidParts + ',结果四舍五入'
				item.disabled = true
			} else {
				item.tip = newUuidParts
				item.disabled = true
			}
		}
	})
}

const parseIdCard = (idCard: any) => {
	const year = idCard.substring(6, 10)
	const month = idCard.substring(10, 12)
	const day = idCard.substring(12, 14)
	const birthDate = `${year}-${month}-${day}`

	const genderCode = idCard.substring(16, 17)
	const gender = genderCode % 2 === 0 ? '女' : '男'

	const today = new Date()
	const birthDateObj = new Date(birthDate)
	const age = today.getFullYear() - birthDateObj.getFullYear()

	const m = today.getMonth() - birthDateObj.getMonth()

	// if (m < 0 || (m === 0 && today.getDate() < birthDateObj.getDate())) {
	// 	age--
	// }
	return {
		age,
		birthDate,
		gender,
	}
}

const checkIdCard = (idCard: any) => {
	const year = idCard.substring(6, 10)
	const month = idCard.substring(10, 12)
	const day = idCard.substring(12, 14)
	const birthDateString = `${year}-${month}-${day}`
	const birthDate = new Date(birthDateString)
	const today = new Date()

	// 校验出生日期是否早于当前日期
	if (birthDate > today) {
		return {isValid: false, message: '出生日期晚于当前日期'}
	}
	// 计算年龄
	let age = today.getFullYear() - birthDate.getFullYear()
	const m = today.getMonth() - birthDate.getMonth()
	// if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
	// 	age--
	// }

	return {
		isValid: true,
		birthDate: birthDateString,
		age: age,
		message: '身份证号有效',
	}
}

const formChanges = (val: any, field: string, type: any, _form: any, raws: any) => {
	if (field === '_cascade') {
		cascadeData.value = val
	}
	formArray.value.forEach((v: any) => {
		if (v.type === 'int' || v.type === 'decimal') {
			let val = truncateDecimal(addFormRef.value.getFieldValue(v.field), v.raw.precision)
			if (isNaN(val)) {
				val = null
			}
			addFormRef.value.setFieldValue(v.field, val)
		}
	})

	if (type) {
		const _field = formArray.value.find((f: any) => f.field === field)
		const rules = formArray.value.map((m: any) => m.raw.calculateRule).filter(Boolean)

		if (rules.length > 0) {
			rules.forEach((f: any) => {
				evalRPN(f.rpnExpression)
			})
		}
	}
	if (type === 'select' && raws.raw.options.length >= 1 && !raws.raw.fieldMultipleDto) {
		const fieldMultiple = formArray.value
			.map((m: any) => m.raw.fieldMultipleDto)
			.filter(Boolean)
		if (fieldMultiple.length > 0) {
			evalSelect(raws)
		}
	}
	if (type === 'select' && ledgerAuxiliaryFillingList.value.length !== 0) {
		if (field === ledgerAuxiliaryFillingList.value[0].field.name) {
			formArray.value.forEach((v: any) => {
				if (v.field === 'District' || v.field === 'City') return
				Object.keys(selectFields.value).map((keys) => {
					if (v.field === selectFields.value[keys]) {
						v.default = val[keys]
					}
				})
				// v.default = val[selectFields.value[v.field]]
			})

			addFormRef.value?.reset()
		}
	}
	if (type === 'certificate') {
		const fieldMultiple = formArray.value
			.map((item: any) => item.raw.validationRule)
			.filter(Boolean)
		if (fieldMultiple.length > 0) {
			evalCardSelect(raws, val)
		}
	}
	// if(type === 'datetime' && (raws.raw.displayForm&&raws.raw.displayForm === 4)) {

	// 	const fieldMultiple = formArray.value.map((m: any) => m.raw.displayForm === 4).filter(Boolean)
	// 	if (fieldMultiple.length > 0) {
	// 		evalTimePicker(raws)
	// 	}
	// }
	// if (type === 'identification_number') {

	// }
}
const evalCardSelect = (row: any, val: any) => {
	const fieldMultiple = formArray.value.filter((item: any) => item.raw.validationRule)
	const target = fieldMultiple.find(
		(f: any) => f.raw.validationRule?.relatedFieldId === row.raw.id
	)
	const findTargetRule = ruleData.value.find((item: any) => item.name === val)
	if (target && findTargetRule) {
		addFormRef.value.setFieldValue(target.field, '')

		formRules.value[target.raw.name] = [
			{
				required: target.raw.isNullable == false ? true : false,
				trigger: 'blur',
				validator: (rule: any, value: any, callback: any) => {
					const Reg = new RegExp(findTargetRule.expression)
					const res = Reg.test(value)
					if (
						target.raw.isNullable &&
						(value == '' || value == undefined || value == null)
					) {
						return callback()
					}
					if (!res) {
						callback(new Error(`请输入正确的${target.raw.displayName}`))
					} else {
						callback()
					}
				},
			},
		]
	}
}
const evalSelect = (raws: any) => {
	const fieldMultiple = formArray.value.map((m: any) => m.raw?.fieldMultipleDto).filter(Boolean)

	const target = fieldMultiple.find((f: any) => f.multipleArrId === raws.raw.id)

	if (target && raws.raw.id === target.multipleArrId) {
		const targetArr = fieldMultiple.filter((f: any) => target.multipleArrId === f.multipleArrId)
		const originVal: any = addFormRef.value.getFieldValue(raws.field)

		const IndexArr = findIndicesInA(formArray.value, targetArr)
		IndexArr.forEach((f: any) => {
			let findChild = formArray.value[f].raw?.fieldMultipleDto?.multipleArr.find(
				(f: any) => f.value === originVal
			)
			formArray.value[f].data = findChild && findChild.children ? findChild.children : []
			addFormRef.value.setFieldValue(formArray.value[f].field, '')
			findChild = {}
		})
	}
}
function formatDateWithQuarter(dateTimeStr: any) {
	// 1. 解析日期时间字符串
	const [year, month, day, ...rest] = dateTimeStr.split('-').map(Number)
	const date = new Date(year, month - 1, day, ...rest.slice(0, 3)) // 注意月份是从0开始的

	// 2. 计算季度
	let quarter
	if (month >= 1 && month <= 3) {
		quarter = '第一季度'
	} else if (month >= 4 && month <= 6) {
		quarter = '第二季度'
	} else if (month >= 7 && month <= 9) {
		quarter = '第三季度'
	} else {
		quarter = '第四季度'
	}

	// 3. 格式化日期和季度
	return `${year}年${quarter}`
}

const evnCardId = (raws: any) => {
	formArray.value.forEach((f: any) => {
		if (raws.id === f.raw.id) {
			f.disabled = true
			f.__disabled = true
		}
	})
}

const evnFormSearch = (expression: string) => {
	const fields = formSearch.value.filter((f: any) => expression?.includes(f.raw?.id))

	if (fields) {
		const Indexs = formSearch.value.findIndex((f: any) => f.raw?.id === fields[0].raw?.id)

		formSearch.value[Indexs].multiple = false
	}
}
const evnFormSearchRest = (expression: string, val: any) => {
	const fieldMultiple = formArray.value.map((m: any) => m.raw?.fieldMultipleDto).filter(Boolean)
	const fields = formArray.value.filter((f: any) => expression?.includes(f.raw?.id))[0]

	if (fields) {
		const targetArr = fieldMultiple.filter((f: any) => fields.raw?.id === f.multipleArrId)

		const IndexArr = findIndicesInA(formArray.value, targetArr)
		IndexArr.forEach((f: any) => {
			if (val[formArray.value[f].field]) {
				addFormRef.value.setFieldValue(
					formArray.value[f].field,
					val[formArray.value[f].field].split(',')
				)
			}

			// formArray.value[f].data = []
		})
	}
}

const evnFormSearch2 = (expression: string) => {
	const fields = formArray.value.filter((f: any) => expression?.includes(f.raw?.id))

	if (fields && fields.length !== 0) {
		const Indexs = formArray.value.findIndex((f: any) => f.raw?.id === fields[0].raw?.id)

		formArray.value[Indexs].multiple = false
	}
}
const evalSelectSearch = (raws) => {
	const fieldMultiple = formSearch.value.map((m: any) => m.raw?.fieldMultipleDto).filter(Boolean)
	const target = fieldMultiple.find((f: any) => f.multipleArrId === raws.raw.id)

	if (target && raws.raw.id === target.multipleArrId) {
		const targetArr = fieldMultiple.filter((f: any) => target.multipleArrId === f.multipleArrId)

		const originVal: any = searchFormRef.value.getValue(raws.field)

		const IndexArr = findIndicesInA(formSearch.value, targetArr)
		IndexArr.forEach((f: any) => {
			let findChild = formSearch.value[f].raw?.fieldMultipleDto?.multipleArr.find(
				(f: any) => f.value === originVal
			)
			formSearch.value[f].data = findChild && findChild.children ? findChild.children : []
			searchFormRef.value.setValue(formSearch.value[f].field, '')
			findChild = {}
		})
	}
}

function findIndicesInA(A, B) {
	let indicesMap = {}
	A.forEach((item: any, index: number) => {
		if (!indicesMap[item.raw?.fieldMultipleDto?.multipleArrId]) {
			indicesMap[item.raw?.fieldMultipleDto?.multipleArrId] = []
		}
		indicesMap[item.raw?.fieldMultipleDto?.multipleArrId].push(index)
	})

	let idToFind = B[0].multipleArrId

	return indicesMap[idToFind] || []
}
const formChange = (val: any, field: string, type?: any, raws?: any) => {
	// debugger
	if (field === '_cascade') {
		cascadeData.value = val

		formSubmit(searchFormRef.value.getForm(), searchFormRef?.value?.getElFormRef(), {})
	}

	if (type === 'select') {
		const fieldMultiple = formSearch.value
			.map((m: any) => m.raw?.fieldMultipleDto)
			.filter(Boolean)
		if (fieldMultiple.length > 0) {
			evalSelectSearch(raws)
		}
	}
}

// 高级搜索
const formSubmit = (val: any, formRef: FormInstance | undefined, formCondition: any) => {
	// 表单验证

	let _formCondition = formCondition || searchFormRef?.value?.formCondition()

	let _val = searchFormRef.value.getForm() || val
	let _formRef = formRef || searchFormRef?.value?.getElFormRef()

	_formRef
		?.validate()
		.then((validate: boolean) => {
			// debugger
			let timesObj: any = {}
			formSearch.value.forEach((m: any) => {
				if (m.raw?.displayForm && m.raw.displayForm === 4) {
					const times = searchFormRef.value.getValue(m.field)
					timesObj[m.field] = times ? formatDateWithQuarter(times) : null
				}
			})
			let form: any = dealWithFrom(toRaw(_val))

			if (Object.keys(cascadeData.value).length > 0) {
				form = Object.assign(form, cascadeData.value)
			}

			advancedSearchForm.value = form

			localStorage.setItem(
				'LDF_ASF',
				JSON.stringify({id: ledger.value.id, filter: advancedSearchForm.value})
			)
			// debugger

			getLedgerTableList({...form, ...timesObj}, {}, _formCondition)
		})
		.catch((err) => {})
}

const formClean = () => {
	advancedSearchForm.value = null
	isClearSearch.value = true
	getLedgerTableList()
	nextTick(() => (isClearSearch.value = false))
}
const addLoading = ref(false)
function replaceNaNAndStringNaNWithNull(obj: any) {
	Object.keys(obj).forEach((key) => {
		// 检查属性值是否为NaN（数值中的NaN）
		if (isNaN(obj[key])) {
			obj[key] = null
		}
		// 检查属性值是否为字符串'NaN'
		else if (typeof obj[key] === 'string' && obj[key] === 'NaN') {
			obj[key] = null
		}
		// 注意：这里没有else分支来修改其他属性，所以只有满足上述条件的属性会被修改
	})

	return obj
}
const formSubmitAdd = (val: any, formRef: FormInstance | undefined) => {
	// 表单验证
	formRef
		?.validate()
		.then((validate: boolean) => {
			let timesObj: any = {}
			formArray.value.forEach((m: any) => {
				if (m.raw.displayForm && m.raw.displayForm === 4) {
					const times = addFormRef.value.getFieldValue(m.field)

					timesObj[m.field] = times ? formatDateWithQuarter(times) : null
				}
			})
			let form: any = dealWithFrom(toRaw(val))
			if (Object.keys(cascadeData.value).length > 0) {
				form = Object.assign(form, cascadeData.value)
			}

			delete form._cascade

			for (let key in form) {
				if (
					form[key] === '' ||
					form[key] === null ||
					form[key] === undefined ||
					form[key] === 'undefined' ||
					form[key] === 'null' ||
					(typeof form[key] === 'object' && form[key] == '')
				) {
					form[key] = null
				} else {
					form[key] = form[key].toString()
				}
			}

			// 在设置所属XX前校验是否有全空数据
			if (
				Object.keys(form).every(
					(keys) => form[keys] === null || form[keys] === '' || form[keys] === undefined
				)
			) {
				ElMessage.warning('请勿提交空数据')
				return
			}

			const regionInfo = JSON.parse(window.localStorage.getItem('currentUserInfo') as string)
			if (!form.City) {
				form.City = regionInfo.city
			}
			if (!form.District) {
				form.District = regionInfo.district
			}
			if (!form.Street) form.Street = regionInfo.street
			if (!form.Community) {
				form.Community = regionInfo.community
			}

			const fullEmptyCheck = formArray.value.every((val: any) => !val)
			addLoading.value = true
			if (formTitle.value === '新增') {
				getLedgerTableApplyAddByLedgerId(ledger.value.id, {
					data: {...form, ...timesObj},
				})
					.then((res) => {
						ElMessage.success('新增成功,请点击提交审核按钮进行提交！')
						addOpen.value = false
						showAddLedgerDataModelisVisible.value = false
						cascadeData.value = {}
						getLedgerTableList(toRaw(searchFormRef.value.getForm()))
						if (submitAuditRef.value) {
							submitAuditRef.value?.$reloadCount(ledger.value.id)
						}
						if (localStorage.getItem('temporarySaveData')) {
							const data = JSON.parse(
								localStorage.getItem('temporarySaveData') || '[]'
							)

							localStorage.setItem(
								'temporarySaveData',
								JSON.stringify(
									data.filter((v) => v.ledgerId !== route.query.ledgerId)
								)
							)
						}
					})
					.catch((err: any) => {
						if (err.response?.status === 500) {
							ElNotification.error('数据正在依次上传，请耐心等待，无需重复提交')
						}
					})
					.finally(() => {
						addLoading.value = false

						const fieldMultiple = formArray.value
							.map((m: any) => m.raw?.fieldMultipleDto)
							.filter(Boolean)

						if (fieldMultiple.length > 0) {
							fieldMultiple.forEach((item: any) => {
								evnFormSearchRest(item.multipleArrId, val)
							})
						}
					})
			} else if (formTitle.value === '编辑' && currentEditData.value) {
				const keys = Object.keys(form)
				for (let i = 0; i < keys.length; i++) {
					if (Array.isArray(form[keys[i]])) {
						if (form[keys[i]].length == 0) {
							form[keys[i]] = null
						} else {
							form[keys[i]] = form[keys[i]].join(',')
						}
					}
				}

				updateAuditLegderTableData(
					ledger.value.id,
					currentEditData.value.id ?? currentEditData.value.Id,
					{data: {...form, ...timesObj}, desensitizeCode: correctCode.value}
				)
					.then((res: any) => {
						currentEditData.value = null
						ElMessage.success('修改成功，请点击提交审核按钮进行提交！')
						addOpen.value = false

						showAddLedgerDataModelisVisible.value = false
						cascadeData.value = {}
						getLedgerTableList()
						if (submitAuditRef.value) {
							submitAuditRef.value?.$reloadCount(ledger.value.id)
						}
					})
					.catch((err: any) => {
						if (err.response?.status === 500) {
							ElNotification.error('数据正在依次上传，请耐心等待，无需重复提交')
						}
					})
					.finally(() => {
						addLoading.value = false

						const fieldMultiple = formArray.value
							.map((m: any) => m.raw?.fieldMultipleDto)
							.filter(Boolean)

						if (fieldMultiple.length > 0) {
							fieldMultiple.forEach((item: any) => {
								evnFormSearchRest(item.multipleArrId, val)
							})
						}
					})
			}
		})
		.catch((err) => {
			addLoading.value = false
		})
}
const onTemporarySave = (val: any) => {
	let form: any = dealWithFrom(toRaw(val))
	if (Object.keys(cascadeData.value).length > 0) {
		form = Object.assign(form, cascadeData.value)
	}

	if (
		Object.keys(form).every(
			(keys) => form[keys] === null || form[keys] === '' || form[keys] === undefined
		)
	) {
		return ElMessage.warning('无暂存数据')
	}
	const historyData = JSON.parse(localStorage.getItem('temporarySaveData') || '[]')
	let data = null
	if (historyData && historyData.length > 0) {
		const d = historyData.filter((item: any) => item.ledgerId !== route.query.ledgerId)
		data = [
			...d,
			{
				ledgerId: route.query.ledgerId,
				data: form,
			},
		]
	} else {
		data = [
			{
				ledgerId: route.query.ledgerId,
				data: form,
			},
		]
	}
	localStorage.setItem('temporarySaveData', JSON.stringify(data))
	ElMessage.success('暂存成功')
}
const selectFields = ref()

const onIdentificationClick = (f: any, v: string, row: any) => {
	if (v === '') return
	formArray.value.forEach((v: any) => {
		if (v.type === 'int' || v.type === 'decimal') {
			let val: any = truncateDecimal(addFormRef.value.getFieldValue(v.field), v.raw.precision)
			if (isNaN(val)) {
				val = null
			}
			addFormRef.value.setFieldValue(v.field, val)
		}
	})

	if (row.type === 'identification_number') {
		const displayNameArr = formArray.value
			.filter((item: any) => {
				return item.type === 'birthday' || item.type === 'age' || item.raw.type === 'sex'
			})
			.filter((item: any) => {
				return row.id === item.raw?.relevanceCalculateRule?.relevanceFieldId
			})
		addFormRef.value.validateValue(f).then((res) => {
			if (!checkIdCard(v).isValid) {
				return ElMessage.warning('出生日期晚于当前日期,请重新输入')
			}

			displayNameArr.forEach((item: any) => {
				if (item.type === 'age') {
					addFormRef.value.setFieldValue(item.field, parseIdCard(v).age)
				} else if (item.type === 'birthday') {
					addFormRef.value.setFieldValue(item.field, parseIdCard(v).birthDate)
				} else if (item.raw.type === 'sex') {
					addFormRef.value.setFieldValue(item.field, parseIdCard(v).gender)
				}
			})
		})
		// return
		// 针对无需填报数据项的回显操作
	}
}
// 失去焦点---辅助填报功能
const onBlur = (val: any, f: any, v: string, row: any) => {
	if (v === '') return
	formArray.value.forEach((v: any) => {
		if (v.type === 'int' || v.type === 'decimal') {
			let val = truncateDecimal(addFormRef.value.getFieldValue(v.field), v.raw.precision)
			if (isNaN(val)) {
				val = null
			}
			addFormRef.value.setFieldValue(v.field, val)
		}
	})
}
const remoteMethod = (val: any) => {
	// debugger
	if (val) {
		let obj: any = {}
		ledgerAuxiliaryFillingList.value[0].configs.forEach((el: any) => {
			obj[el.sourceFiled.name] = el.field.name
		})
		// if (ledgerAuxiliaryFillingList.value[0].field) {
		// 	ledgerAuxiliaryFillingList.value[0].selectField = ledgerAuxiliaryFillingList.value[0].field
		// }
		selectFields.value = obj
		let searchPms
		if (auxiliaryFillingType.value === 0) {
			searchPms = {
				ledgerId: route.query.ledgerId,
				maxResultCount: 9999,
				skipCount: 0,
				SourceId:
					ledgerAuxiliaryFillingList.value[0].sourceLedgerId ??
					ledgerAuxiliaryFillingList.value[0].sourceTableDataSetId,
				whereFields: {
					[ledgerAuxiliaryFillingList.value[0].configs.filter(
						(e) => e.fieldId === ledgerAuxiliaryFillingList.value[0].fieldId
					)[0].sourceFiled.name]: {
						0: val,
					},
				},
				selectFields: obj,
				keyword: val,
			}
		}
		if (auxiliaryFillingType.value === 1) {
			searchPms = {
				ledgerId: route.query.ledgerId,
				maxResultCount: 9999,
				skipCount: 0,
				SourceId:
					ledgerAuxiliaryFillingList.value[0].sourceLedgerId ??
					ledgerAuxiliaryFillingList.value[0].sourceTableDataSetId,
				whereFields: {
					[ledgerAuxiliaryFillingList.value[0].selectField.name]: {
						0: val,
					},
				},
				selectFields: obj,
				keyword: val,
			}
		}
		if (auxiliaryFillingType.value === 2) {
			searchPms = {
				ledgerId: route.query.ledgerId,
				maxResultCount: 9999,
				skipCount: 0,
				SourceId:
					ledgerAuxiliaryFillingList.value[0].sourceLedgerId ??
					ledgerAuxiliaryFillingList.value[0].sourceTableDataSetId,
				whereFields: {
					[ledgerAuxiliaryFillingList.value[0].field.name]: {
						0: val,
					},
				},
				selectFields: obj,
				keyword: val,
			}
		}

		getSingleLedgerData(searchPms)
			.then((res: any) => {
				if (res.status === 200) {
					// if (res.data.data.length === 0) return ElMessage.warning('未查询到数据。')
					// if (res.data.data === null || Object.keys(res.data.data).length === 0)
					// 	return ElMessage.warning('未查询到数据。')

					formArray.value.forEach((v: any) => {
						if (v.field === 'District' || v.field === 'City') return
						if (Object.values(obj).includes(v.field)) {
							// v.default = res.data[v.field]
							v.data = res.data.data.map((item: any) => ({
								label: res.data.preview
									.map((x) => item[x])
									.toString()
									.split(',')
									.join(' '),
								value: item,
							}))
						}
					})

					// addFormRef.value?.reset()
				} else {
					formArray.value.forEach((v: any) => {
						if (v.field === 'District' || v.field === 'City') return
						if (Object.values(obj).includes(v.field)) {
							// v.default = res.data[v.field]
							v.data = []
						}
					})
				}
			})
			.finally(() => {})
	}
}
const showAuxiliaryFillingDataModal = ref(false)

const auxiliaryFillingDataSelected = ref([])
const getSingleData = ref<any>()
const onAuxiliaryFillingSearch = (field, val) => {
	if (val) {
		showAuxiliaryFillingDataModal.value = true
		let obj: any = {}
		ledgerAuxiliaryFillingList.value[0].configs.forEach((el: any) => {
			obj[el.sourceFiled.name] = el.field.name
		})
		// if (ledgerAuxiliaryFillingList.value[0].field) {
		// 	ledgerAuxiliaryFillingList.value[0].selectField = ledgerAuxiliaryFillingList.value[0].field
		// }
		selectFields.value = obj
		let searchPms
		if (auxiliaryFillingType.value === 0) {
			searchPms = {
				ledgerId: route.query.ledgerId,
				maxResultCount: 9999,
				skipCount: 0,
				SourceId:
					ledgerAuxiliaryFillingList.value[0].sourceLedgerId ??
					ledgerAuxiliaryFillingList.value[0].sourceTableDataSetId,
				whereFields: {
					[ledgerAuxiliaryFillingList.value[0].configs.filter(
						(e) => e.fieldId === ledgerAuxiliaryFillingList.value[0].fieldId
					)[0].sourceFiled.name]: {
						0: val,
					},
				},
				selectFields: obj,
				keyword: val,
			}
		}
		if (auxiliaryFillingType.value === 1) {
			searchPms = {
				ledgerId: route.query.ledgerId,
				maxResultCount: 9999,
				skipCount: 0,
				SourceId:
					ledgerAuxiliaryFillingList.value[0].sourceLedgerId ??
					ledgerAuxiliaryFillingList.value[0].sourceTableDataSetId,
				whereFields: {
					[ledgerAuxiliaryFillingList.value[0].selectField.name]: {
						0: val,
					},
				},
				selectFields: obj,
				keyword: val,
			}
		}
		if (auxiliaryFillingType.value === 2) {
			searchPms = {
				ledgerId: route.query.ledgerId,
				maxResultCount: 9999,
				skipCount: 0,
				SourceId:
					ledgerAuxiliaryFillingList.value[0].sourceLedgerId ??
					ledgerAuxiliaryFillingList.value[0].sourceTableDataSetId,
				whereFields: {
					[ledgerAuxiliaryFillingList.value[0].field.name]: {
						0: val,
					},
				},
				selectFields: obj,
				keyword: val,
			}
		}

		getSingleLedgerData(searchPms)
			.then((res: any) => {
				if (res.status === 200) {
					getSingleData.value = res.data
				} else {
				}
			})
			.finally(() => {})
	} else {
		ElMessage.warning('请输入内容')
	}
}
const onAuxiliaryFillingCOnfirm = (val) => {
	if (auxiliaryFillingDataSelected.value.length === 0) {
		return ElMessage.warning('请选择要填充的数据')
	}
	showAuxiliaryFillingDataModal.value = false
	// 开始填充
	// auxiliaryFillingDataSelected
	const data = auxiliaryFillingDataSelected.value[0]
	formArray.value.forEach((v: any) => {
		if (v.field === 'District' || v.field === 'City') return

		Object.keys(selectFields.value).map((keys) => {
			if (v.field === selectFields.value[keys]) {
				// v.default = data[keys]
				addFormRef.value.setFieldValue(v.field, data[keys])
			}
		})

		// if (Object.keys(data).includes(v.field)) {
		// 	v.default = data[v.field]
		// }
	})

	// addFormRef.value?.reset()

	// 全部重置
	auxiliaryFillingDataSelected.value = []
	getSingleData.value = null
}
const handleRemove = () => {}
const batchDelete = () => {
	if (tableData.value.length > 0) {
		// const hasDeleteRows = allSeletePageData.value.some((row: any) => {
		// 	if (
		// 		row.DataStatus === 3 &&
		// 		(row.LockUserId === null || row.LockUserId === useUserStore().getUserInfo.id)
		// 	) {
		// 		ElMessage.warning('勾选包含已删除数据，无法批量删除')
		// 		return true
		// 	}
		// })
		const hasOther = allSeletePageData.value.some(
			(v: any) =>
				v.DataStatus === 0 || v.DataStatus === 2 || v.DataStatus === 3 || v.DataStatus === 4
		)
		if (hasOther) {
			ElMessageBox.confirm(
				'仅可删除‘待提交’，‘已审核’，‘已驳回’状态数据，且新增的待提交数据无需审核，将会直接删除，是否确认删除',
				'提示',
				{
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning',
				}
			).then(() => {
				ElMessageBox.confirm('确定删除选中数据吗？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning',
				})
					.then(async () => {
						const ids = allSeletePageData.value.map((item: any) => item.Id)

						batchDeleteLedgerData(ledger.value.id, ids).then((res) => {
							getLedgerTableList()
							if (submitAuditRef.value) {
								submitAuditRef.value?.$reloadCount(ledger.value.id)
							}
							ElMessage.success('删除操作已执行，请在提交审核页面提交')
						})
						// const ids = tableData.value.filter((item: any) => item.checked).map((item: any) => item.id)
					})
					.catch((err: any) => {
						if (err.response?.status === 500) {
							ElNotification.error('当前操作人员较多，请5分钟后再试')
						}
					})
				return
				// submitModalVisible.value = true
			})
		} else {
			ElMessageBox.confirm('确定删除选中数据吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(async () => {
					const ids = allSeletePageData.value.map((item: any) => item.Id)

					batchDeleteLedgerData(ledger.value.id, ids).then((res) => {
						getLedgerTableList()
						if (submitAuditRef.value) {
							submitAuditRef.value?.$reloadCount(ledger.value.id)
						}
						ElMessage.success('删除操作已执行，请在提交审核页面提交')
					})
					// const ids = tableData.value.filter((item: any) => item.checked).map((item: any) => item.id)
				})
				.catch((err: any) => {
					if (err.response?.status === 500) {
						ElNotification.error('当前操作人员较多，请5分钟后再试')
					}
				})
		}
		// if (hasDeleteRows) {
		// 	return
		// }
	}
}
// 删除所有数据
const deleteAll = () => {
	ElMessageBox.confirm('确定删除所有数据吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			let whereForm = advancedSearchForm.value ?? ''
			const whereFields: any = {}
			const __whereForm = JSON.parse(JSON.stringify(whereForm ?? ''))

			if (__whereForm) {
				Object.keys(__whereForm).forEach((key) => {
					const field = fields.value.find((f: any) => f.name === key)
					const fieldType = field?.type

					if (
						__whereForm[key] &&
						(__whereForm[key].length > 0 || typeof __whereForm[key] === 'number')
					) {
						if (
							Array.isArray(__whereForm[key]) &&
							(fieldType === 'datetime' || fieldType === 'date')
						) {
							whereFields[key] = {
								'3': __whereForm[key][0],
								'5': __whereForm[key][1],
							}
						} else if (Array.isArray(__whereForm[key]) && fieldType === 'string') {
							whereFields[key] = {'15': __whereForm[key].join(',')}
						} else if (fieldType === 'string' && field.multiple !== null) {
							// 单/复选改下拉处理
							whereFields[key] = {'0': __whereForm[key]}
						} else if (fieldType === 'string') {
							whereFields[key] = {'1': __whereForm[key]}
						} else if (fieldType === 'int' || fieldType === 'decimal') {
							whereFields[key] = {'0': Number(__whereForm[key])}
						} else {
							whereFields[key] = {'0': __whereForm[key]}
						}
					}
				})
			}
			let datas: any = {}

			if (OrderFields.value === '' || OrderFields.value === null) {
				datas = {
					desensitizeCode: '',
					ledgerId: ledger.value?.id,
					whereFields,
					OrderFields: ['UpdateTime desc'],
				}
			} else {
				datas = {
					desensitizeCode: '',
					ledgerId: ledger.value?.id,
					whereFields,
					OrderFields: [OrderFields.value],
				}
			}
			// if (dataStatusList) {
			// 	datas.dataStatusList = dataStatusList
			// }
			if (filterStatus.value.length !== 0) {
				datas.dataStatusList = filterStatus.value
			}
			if (datas.desensitizeCode.length === 0) {
				const ValidateCode = localStorage.getItem('ValidateCode')
				if (ValidateCode != null) {
					datas.desensitizeCode = ValidateCode
				} else {
					delete datas.desensitizeCode
				}
			}
			// 获取查询参数

			deleteAllLedgerData(route.query.ledgerId as string, datas).then((res) => {
				ElMessage.success('正在提交您的删除申请，请耐心等候。')
				startUpWorker()
			})
		})
		.catch(() => {})
}
const workerTimer = ref<any>()
const isDataComparisonOne = ref(false)
const isDataComparisonAll = ref(false)
const currentRowComparison = ref<any>()
const currentRowComparisonAll = ref<any>()
const startUpWorker = (isActivated: Boolean = false, isFirst: Boolean = false) => {
	const ledgerId = route.query.ledgerId
	cleanWorker()
	const worker = new Worker()
	currentWork.value = worker
	worker.postMessage({
		url: `${APIConfig(
			'ledger'
		)}/api/ledger-service/ledger-data/{ledgerId}/apply-all-data/processing`,
		ledgerId,
		token: `Bearer ${userStore.getToken}`,
	})
	worker.onmessage = (e) => {
		loading.value = true
		if (e.data.data === false) {
			cleanWorker()
			clearTimeout(workerTimer.value)
			loading.value = false
			// ElMessage.success('已删除全部数据，请注意提交审核。')
			!isActivated && getLedgerTableList()
			if (submitAuditRef.value && ledger.value && !isFirst) {
				submitAuditRef.value?.$reloadCount(ledger.value.id)
			}
		}
	}
	workerTimer.value = setTimeout(() => {
		startUpWorker()
	}, 3000)
}
const cleanWorker = () => {
	if (currentWork.value) {
		currentWork.value.terminate()
		currentWork.value = null
	}
}
const expendSearch = (height: number = 0, expand?: boolean) => {
	// searchFormRef.value.clear()
	if (!formSearch.value.length) return

	if (formSearch.value.length === 0) {
		ElMessage.info('请先配置业务表搜索字段')
		return
	}

	tableOffsetHeight.value = -(height + 190)
	tableRef.value.resize()
}

const dealWithFrom = (formVal: any) => {
	const keys: any = Object.keys(formVal)
	keys.forEach((key: any) => {
		const field = fields.value.find((field: any) => field.name === key)
		if (field && !formVal[key]) {
			if (field.type === 'date' || field.type === 'datetime') {
				formVal[key] = null
			}

			if (field.type === 'string' && field.multiple) {
				formVal[key] = []
			}
		}
	})
	return formVal
}

const onTableClickButton = async ({btn, scope}: any) => {
	if (btn.code === 'view') {
		getTableScroll()
		// return
		localStorage.setItem('ledgerData', JSON.stringify(ledger.value))
		localStorage.setItem('ledgerFields', JSON.stringify(fields.value))
		router.push({
			path: '/ledger/fill-data',
			query: {
				ledgerId: ledger.value.id,
				ledgerDataId: scope.Id,
			},
		})
	} else if (btn.code === 'danger') {
		AuditDeleteLedgerData(ledger.value.id, scope.Id).then((res: any) => {
			if (submitAuditRef.value) {
				submitAuditRef.value?.$reloadCount(ledger.value.id)
			}
			ElMessage.success('提交成功!请前往提交审核进行提交')
			getLedgerTableList()
		})
	} else if (btn.code === 'edit') {
		if (!isPermission.value(Permissions.Edit)) {
			ElMessage.warning('您无权编辑业务表数据')
			return
		}
		// debugger
		let isDesensitization = false //不需要脱敏

		// columnsList.value.forEach((item: any) => {
		// 	if (item.desensitizationType != null) {
		// 		isDesensitization = true
		// 		;

		// 	}
		// })
		isDesensitization = columnsList.value.some((item: any) => item.desensitizationType !== null)

		let ValidateCode = localStorage.getItem('ValidateCode')

		if (isDesensitization && (ValidateCode == '' || ValidateCode == null)) {
			ElMessage.warning('请解除脱敏后再进行编辑')
			return
		}
		if ((await ValidateCodeFun()) == false) {
			ElMessage.warning('当前验证码已失效或错误请重新进行解除脱敏')
			return
		}
		const rowData = ref<any>(null)
		if (scope.DataStatus == 0 || scope.DataStatus == 3) {
			const {data} = await getLedgerDataInfoById(ledger.value.id, scope.id ?? scope.Id, {
				desensitizeCode: ValidateCode,
			})
			rowData.value = data
		} else if (scope.DataStatus == 1 || scope.DataStatus === 5) {
			rowData.value = scope
			// debugger
			// const {data} = await getAuditLedgerDataInfoById(ledger.value.id, scope.id ?? scope.Id)
			// //
			// if (data === '') {
			// 	const {data} = await getLedgerDataInfoById(ledger.value.id, scope.id ?? scope.Id, {
			// 		desensitizeCode: ValidateCode,
			// 	})
			// 	rowData.value = data
			// } else {
			// 	if (data.operationType == 2) {
			// 		rowData.value = data.oldData
			// 	} else {
			// 		rowData.value = data.data
			// 	}
			// }
		}

		formArray.value = formArrayCop.value.filter(
			(v) => v.raw?.isDepartmentEditField || v.field === '_cascade'
		)

		formisDepartmentEditField.value = formArrayCop.value.filter(
			(v) => !v.raw.isDepartmentEditField && v.field !== '_cascade'
		)
		const {community, street, district, city} = userStore.getUserInfo
		formArray.value.forEach((f: any) => {
			if (f.field === '_cascade') {
				f.cascadeOptions.forEach((e) => {
					e.beforeInitOptions = (val: any, next: any, item: any) => {
						const id = item.options.find((o: any) => o.value === val)?.raw.id
						if (id) {
							next.cascadeParams = {guid: id}
						}
					}
				})
			}
			f.disabled = f.__disabled
			if (f.type === 'int' || f.type === 'decimal') {
				f.disabled = f.raw?.calculateRule !== null || f.raw?.editDisabled
			}
		}) //momo

		for (let [key, value] of Object.entries(rowData.value)) {
			if (key !== 'Id') {
				formArray.value.forEach((f: any) => {
					if (f.field === key) {
						if (f.type === 'checkbox' || f.__type === 'checkbox') {
							let val: any = value
							if (val) {
								if (val.indexOf('[') != -1) {
									val = JSON.parse(val.replace(/，/g, ','))
									if (val.length == 1) {
										val = val[0]
											.trim()
											.split(',')
											.map((item: any) => item.trim())
									}
									f.default = val
								} else {
									f.default = val.split(',')
								}
							} else {
								f.default = []
							}
						} else {
							f.default = value
						}
					}
					if (f.type === 'date' || f.type === 'datetime') {
						f.datetype = 'day'
					}

					if (fixedFields.includes(f.raw.name)) {
						if (f.raw.name === 'Community') {
							f.disabled = community ? true : false
						}
						if (f.raw.name === 'Street') {
							f.disabled = street ? true : false
						}
						if (f.raw.name === 'District') {
							f.disabled = district ? true : false
						}
						if (f.raw.name === 'UpdateTime') {
							f.disabled = true
						}
						if (f.raw.name === 'City') {
							f.disabled = true
						}
					}

					if (f.__type === 'radio') {
						f.multiple = false
					}
				})

				formisDepartmentEditField.value.forEach((f: any) => {
					f.disabled = true
					if (f.field === key) {
						if (f.type === 'checkbox' || f.__type === 'checkbox') {
							let val: any = value
							if (val) {
								if (val.indexOf('[') != -1) {
									val = JSON.parse(val.replace(/，/g, ','))
									if (val.length == 1) {
										val = val[0]
											.trim()
											.split(',')
											.map((item: any) => item.trim())
									}
									f.default = val
								} else {
									f.default = val.split(',')
								}
							} else {
								f.default = []
							}
						} else {
							f.default = value
						}
					}
					if (f.type === 'date' || f.type === 'datetime') {
						f.datetype = 'day'
					}

					if (fixedFields.includes(f.raw.name)) {
						if (f.raw.name === 'Community') {
							f.disabled = community ? true : false
						}
						if (f.raw.name === 'Street') {
							f.disabled = street ? true : false
						}
						if (f.raw.name === 'District') {
							f.disabled = district ? true : false
						}
						if (f.raw.name === 'UpdateTime') {
							f.disabled = true
						}
						if (f.raw.name === 'City') {
							f.disabled = true
						}
					}

					if (f.__type === 'radio') {
						f.multiple = false
					}
				})
			}
		}

		// 级联选择
		const fieldMultiple = formArray.value
			.map((m: any) => m.raw.fieldMultipleDto)
			.filter(Boolean)

		if (fieldMultiple.length > 0) {
			fieldMultiple.forEach((f) => {
				evnGetSelect2(f.multipleArrId, rowData.value)
			})
		}

		if (auxiliaryFillingType.value === 0) {
			const res = await getLedgerAuxiliaryFilling({
				LedgerId: route.query.ledgerId as string,
				isPaged: false,
			})
			if (res) {
				ledgerAuxiliaryFillingList.value = res.data.items
			}
		}
		if (auxiliaryFillingType.value === 1) {
			const res = await getDataSetAuxiliaryFilling({
				LedgerId: route.query.ledgerId as string,
				isPaged: false,
			})
		}
		if (auxiliaryFillingType.value === 2) {
			const res = await getApiAuxiliaryFillingList(route.query.ledgerId as string)
			if (res.data) {
				// 构建和业务表辅助填报相类似得数据结构以便可以复用某些方法
				ledgerAuxiliaryFillingList.value = [
					{
						...res.data,
						field: {name: res.data.ledgerKey},
						configs: res.data.rules.map((v: any) => ({
							field: {name: v.fieldKey},
							sourceFiled: {name: v.fieldValue},
						})),
					},
				]
			}
		}
		formArray.value.forEach((it: any) => {
			if (it.type === 'datetime' && it.raw.displayForm && it.raw.displayForm === 4) {
				if (rowData.value.hasOwnProperty(it.field)) {
					it.default = parseQuarterToDate(rowData.value[it.field])
				}
			}
		})

		formisDepartmentEditField.value.forEach((it: any) => {
			if (it.type === 'datetime' && it.raw.displayForm && it.raw.displayForm === 4) {
				if (rowData.value.hasOwnProperty(it.field)) {
					it.default = parseQuarterToDate(rowData.value[it.field])
				}
			}
		})

		formArray.value.forEach((item: any) => {
			if (!item.raw.isDepartmentEditField) {
				item.disabled = true
			}
			if (item.cascadeOptions) {
				item.cascadeOptions.forEach((item: any) => {
					item.disabled = !item.isDepartmentEditField
				})
			}
		})

		// const currentUser = JSON.parse(localStorage.getItem('currentUserInfo') as any)
		// if (currentUser.community) {
		// 	formArray.value = formArray.value.filter((v) => !v.raw.isNullable)
		// }

		formTitle.value = '编辑'
		currentEditData.value = scope
		showAddLedgerDataModelisVisible.value = true

		formArray.value.forEach((f: any) => {
			if (f.field === '_cascade') {
				f.cascadeOptions.forEach((ff: any) => {
					ff.value = scope[ff.prop]
					ff.default = scope[ff.prop]
				})
			}
		})
		addOpen.value = true
	} else if (btn.code === 'comparison') {
		const {ledgerId}: any = route.query

		getApiDataComparison(ledgerId, {
			WhereFields: {
				Id: {
					0: scope.Id,
				},
			},
		}).then((res: any) => {
			isDataComparisonOne.value = true
			currentRowComparison.value = {...res.data, dataId: scope.Id}
		})
	} else if (btn.code === 'submit') {
		allSeletePageData.value = [scope]
		openSubmit('select')
		// submitModalVisible.value = true
	}
}

// watch(() => searchFormRef.value?.form(), (val:any) => {
//
const allFiled = ref<any>([])
const tableFiled = ref<any>([])
const exportTableFiled = ref<any>([])
const tableGroup = ref<any>([])
const filterField = ref<any>([])
const tableGroupInfo = ref<any>([])
const handleData = ref<any>([])
const auxiliaryFillingType = ref(3)
const isCanComparison = ref(false)
const associatedSyns = ref([])
const formisDepartmentEditField = ref()
const ruleData = ref()
const fieldsAll: any = ref([])
const formSearchClone: any = ref([])
const formArrayCop: any = ref([])
const initTable = async (callback?: any) => {
	handleData.value = []
	const {ledgerId}: any = route.query
	if (ledgerId) {
		// 判断缓存中是否有正则表达的验证数据
		const cache: any = viewStore.LDF_RULE || {}
		let ruleRes: any
		if (cache[ledgerId] === undefined) {
			const res = await getTableFiledValidateRule()
			cache[ledgerId] = res
			// localStorage.setItem('LDF_RULE', JSON.stringify(cache))
			viewStore.setRule(cache)
			ruleRes = res
		} else {
			ruleRes = cache[ledgerId]
		}
		const ruleResData = ruleRes.data
		ruleData.value = ruleRes.data
		// getDetailByLedgerId(ledgerId)
		loading.value = true
		GetLedgerDetailById(ledgerId)
			.then((res: any) => {
				const {data} = res
				isCanAssociatedSyn.value = data.isCanAssociatedSyn
				associatedSyns.value = data.associatedSyns

				fieldsAll.value = data.tableInfo.fields
				const {tableFieldGroups} = data.tableInfo
				isCanComparison.value = data.isCanComparison
				data.tableInfo.fields.forEach((item: any) => {
					item.tzName = data.tableInfo.displayName
					item.bm = item.name
				})
				columnsList.value = JSON.parse(JSON.stringify(data.tableInfo.fields))

				const advancedSearchFormHistory = localStorage.getItem('LDF_ASF')
				auxiliaryFillingType.value = data.auxiliaryFillingType
				let historySearch = advancedSearchFormHistory
					? JSON.parse(advancedSearchFormHistory)
					: null
				// 如果不是同一个业务表，清除历史搜索条件
				if (data.id !== historySearch?.id) {
					localStorage.removeItem('LDF_ASF')
					historySearch = null
				}
				const {community, street, district, city} = userStore.getUserInfo
				const communityCheck =
					!community &&
					data.tableInfo.fields
						.filter((v) => v.isListField)
						.some((v: any) => v.name === 'Community')
				const streetCheck =
					!street &&
					data.tableInfo.fields
						.filter((v) => v.isListField)
						.some((f: any) => f.name === 'Street' || f.name === 'Community')
				const districtCheck =
					!district &&
					data.tableInfo.fields
						.filter((v) => v.isListField)
						.some(
							(f: any) =>
								f.name === 'District' ||
								f.name === 'Street' ||
								f.name === 'Community'
						)

				// if (!userStore.getUserInfo.community) {
				// 	if (
				// 		data.tableInfo.fields
				// 			.filter((v) => v.isListField)
				// 			.some(
				// 				(f: any) => f.name === 'District' || f.name === 'Street' || f.name === 'Community'
				// 			) &&
				// 		(streetCheck || districtCheck)
				// 	) {
				// 		res.data.tableInfo.fields.unshift({
				// 			name: '_cascade',
				// 			displayName: '所属区域',
				// 			type: 'cascade',
				// 		})
				// 	}
				// }

				const showCascade = (allFiled.value = data.tableInfo.fields.filter((f: any) =>
					['District', 'Street', 'Community'].includes(f.name)
				)).some((f: any) => f.isListField)

				if (
					showCascade &&
					!userStore.getUserInfo.community &&
					(streetCheck || districtCheck || communityCheck)
				) {
					res.data.tableInfo.fields.unshift({
						name: '_cascade',
						displayName: '所属区域',
						type: 'cascade',
					})
				}

				// 过滤有权限字段
				tableFiled.value = data.tableInfo.fields.filter(
					(f: any) => f.isDepartmentField && f.name !== 'UpdateTime' && f.isListField //&& f.isListField  && f.name !== 'UpdateTime'
				)
				exportTableFiled.value = data.tableInfo.fields
					.filter(
						(f: any) => f.isDepartmentField && f.name !== 'UpdateTime' && f.isListField
					)
					.concat(data.tableInfo.fields.filter((f: any) => f.name === 'UpdateTime')[0])
				tableGroup.value = data.tableInfo.tableFieldGroups

				let tableFieldField: any = []
				tableFieldGroups.forEach((item: any) => {
					tableFieldField = [
						...tableFieldField,
						...item.tableFields.filter(
							(f: any) => f.isDepartmentField && f.isListField
						), //&& f.isListField
					]
				})
				filterField.value = data.tableInfo.fields.filter((f: any) => {
					return !tableFieldField.some((f2: any) => f2.id === f.id && f.isDepartmentField)
				})
				// .filter((v) => v.isListField || v.isUnique || !v.isNullable)
				const group: any = []
				tableFieldGroups.forEach((item: any, index: any) => {
					if (item.tableFields) {
						group.push(
							Object.assign(item, {
								id: item.id,
								label: item.name,
								parentId: item.parentId,
								tableFields: toRaw(
									item.tableFields.filter(
										(f: any) => f.isDepartmentField && f.isListField
									) //&& f.isListField
								),
							})
						)
						item.tableFields.forEach((tf: any) => {
							if (tf.isListField && tf.isDepartmentField) {
								group.push({
									label: tf.displayName,
									field: tf.name,
									parentId: item.id,
									id: tf.id,
									...tf,
								})
							}
						})
					} else {
						group.push({label: item.name, id: item.id, parentId: item.parentId})
					}
				})
				const LedgerGroup = useArrayToTree(
					group,
					'id',
					'parentId',
					'label',
					false,
					'children',
					true
				)

				filterField.value = [...filterField.value, ...LedgerGroup]

				filterField.value.sort((a: any, b: any) => a.sort - b.sort)

				let tempArr: any = []
				let arr: any = []
				filterField.value.forEach((item: any, index: any) => {
					if (!item.children) {
						arr.push(item)
					} else {
						if (arr.length > 0) {
							tempArr.push(arr)
						}
						tempArr.push(item)
						// activeNames.value.push(item.id)
						recursionFun(tempArr[tempArr.length - 1])
						// 使用递归去添加item数据
						arr = []
					}
					if (filterField.value.length - 1 == index) {
						if (arr.length > 0) {
							tempArr.push(arr)
						}
						arr = []
					}
				})

				handleData.value = tempArr
				filterField.value = tableFiled.value.filter((f: any) => {
					return !tableFieldField.some((f2: any) => f2.id === f.id)
				})
				// 暂时列表隐藏所属部门字段
				const __fields = tableFiled.value.filter(
					(f: any) => f.isListField && f.isDepartmentField && f.name !== 'Department'
				)

				const groups: any = []
				const columns: any = tableFieldGroups.length === 0 ? __fields : tableFieldGroups

				columns.forEach((f: any) => {
					if (f.tableFields) {
						groups.push(
							Object.assign(f, {
								id: f.id,
								title: f.name,
								align: 'left',
								parentId: f.parentId,
								__isGroup: true,
								sort: f.sort,
								sortable:
									f.type === 'datetime' ||
									f.type === 'date' ||
									f.type === 'decimal' ||
									f.type === 'int',
							})
						)
						f.tableFields
							.filter((ff: any) => ff.isListField)
							.forEach((tf: any) => {
								groups.push({
									title: tf.displayName ?? tf.name,
									field: tf.name,
									raw: tf,
									parentId: f.id,
									sort: tf.sort,
									sortable:
										tf.type === 'datetime' ||
										tf.type === 'date' ||
										tf.type === 'decimal' ||
										tf.type === 'int',
								})
							})
					} else {
						groups.push({
							id: f.id,
							title: f.displayName ?? f.name,
							align: 'left',
							field: tableFieldGroups.length === 0 ? f.name : null,
							parentId: f.parentId,
							raw: f,
							canClick: f.type === 'images' || f.type === 'attachments',
							sort: f.sort,
							__isGroup: tableFieldGroups.length !== 0,
							sortable:
								f.type === 'datetime' ||
								f.type === 'date' ||
								f.type === 'decimal' ||
								f.type === 'int',
						})
					}
				})

				// 处理没分组字段
				__fields.forEach((f: any) => {
					if (!groups.some((g: any) => !g.__isGroup && g.field === f.name)) {
						groups.push({
							title: f.displayName ?? f.name,
							field: f.name,
							raw: f,
							parentId: null,
							sort: f.sort,
							sortable:
								f.type === 'datetime' ||
								f.type === 'date' ||
								f.type === 'decimal' ||
								f.type === 'int',
						})
					}
				})

				groups.forEach((v: any) => {
					if (v.children) v.children = []
					if (!v.raw) {
						v.raw = {
							isListField: true,
						}
					}
				})
				colData.value = useArrayToTree(
					groups,
					'id',
					'parentId',
					'title',
					false,
					'children',
					true
				)
				// const StatusCon = await get
				const rightArr: any = [
					{
						title: '状态',
						field: 'DataStatus',
						fixed: 'right',
						filters: [
							{text: '已审核', value: 0, checked: true},
							{text: '待提交', value: 1, checked: true},
							{text: '待审核', value: 2, checked: true},
							{text: '已删除', value: 3, checked: false},
							// {text: '修改中', value: 4, checked: true},
							// {text: '已驳回', value: 5, checked: true},
						],
						filterValue: [0, 1, 2, 4, 5],
					},
				]
				const isUpdateTime = res.data.tableInfo.fields.find(
					(item: any) =>
						item.displayName === '更新时间' &&
						item.isDepartmentField &&
						item.isListField
				)

				if (isUpdateTime !== undefined) {
					rightArr.unshift({
						title: '更新时间',
						field: 'UpdateTime',
						fixed: 'right',
						sortable: true,
					})
				}

				colData.value = [
					...colData.value.filter((col: any) => col.raw.isListField),
					...rightArr,
				]
				// 请勿删除下行代码 大屏测试用
				// localStorage.setItem('colData233', JSON.stringify(colData.value))

				TilingColData.value = util.treeToArray(colData.value)

				fields.value = data.tableInfo.fields.sort((a: any, b: any) => a.sort - b.sort)
				fields.value = [
					...fields.value,
					...[
						{title: '状态', name: 'DataStatus', field: 'DataStatus'},
						{title: '锁定人', name: 'LockUserId', field: 'LockUserId'},
						{title: '删除人', name: 'DeleteUserId', field: 'DeleteUserId'},
					],
				]

				// 表格
				fields.value.unshift({
					name: 'Id',
					displayName: 'Id',
					type: 'string',
				})
				// 过滤表单字段
				const filterFiledName = [
					'Id',
					'Department',
					'DataStatus',
					'LockUserId',
					'DeleteUserId',
					'City',
					'District',
					'Street',
					'Community',
				]
				const departmentInfo = JSON.parse(localStorage.getItem('currentUserInfo') as any)

				const __fieldsFilter = fields.value
					.filter(
						(f: any) => !filterFiledName.includes(f.name) //(f.isListField || !f.isNullable)
					)
					.map((m: any) => {
						const item: any = {
							type: m.type,
							title: m.displayName,
							field: m.name,
							disabled: m.editDisabled || m.calculateRule,
							__disabled: m.editDisabled || m.calculateRule,
							raw: m,
						}

						if (m.type === 'date') {
							item.datetype = 'days'
						}

						if (m.type === 'datetime') {
							item.datetype = 'times'
						}

						//TODO: 单、复选改下拉, 复选下拉可多选, 勿删
						// if (m.type === 'string' && typeof m.multiple === 'boolean') {
						// 	item.__type = m.multiple ? 'checkbox' : 'radio'
						// 	item.multiple = m.multiple
						// 	item.type = 'select'
						// 	item.data = m.options.map((o: any) => ({label: o, value: o}))
						// 	item.full = true
						// }

						//TODO: 单、复选改下拉, 单、副选下拉都支持多选, 在搜索条件下
						if (
							m.type === 'string' &&
							m.multiple !== null
							// (m.type === 'sex' && m.multiple === null)
						) {
							item.__type = m.multiple ? 'checkbox' : 'radio'
							item.multiple = true
							item.type = 'select'
							item.data = m.options.map((o: any) => ({label: o, value: o}))
							item.full = true

							if (item.title == '单选被关联') {
							}
						}
						if (m.type === 'sex') {
							item.__type = m.multiple ? 'checkbox' : 'radio'
							item.multiple = false
							item.type = 'select'
							item.data = [
								{value: '男', lable: '男'},
								{value: '女', lable: '女'},
							]
							item.full = true
						}

						if (m.type === 'cascade') {
							const options = []
							item.disabled = false
							item.__disabled = false

							if (
								!departmentInfo.city &&
								fields.value.some((f: any) => f.name === 'City' && f.isListField)
							) {
								options.push({
									prop: 'City',
									type: 'select',
									filterable: true,
									cascadeUrl: '/api/platform/department/my-region-names',
									cascadeKeys: ['name', 'name'],
									beforeInitOptions: (val: any, next: any, item: any) => {
										const id = item.options.find((o: any) => o.value === val)
											?.raw.id
										if (id) {
											next.cascadeParams = {guid: id}
										}
									},
									placeholder: '请选择城市',
									options: [],
									isDepartmentEditField: fields.value.some(
										(f: any) => f.name === 'City' && f.isDepartmentEditField
									),
									value: historySearch?.filter?.City || '',
								})
							}

							if (
								!departmentInfo.district &&
								fields.value.some(
									(f: any) => f.name === 'District' && f.isListField
								)
							) {
								options.push({
									prop: 'District',
									type: 'select',
									filterable: true,
									cascadeUrl: '/api/platform/department/my-region-names',
									beforeInitOptions: (val: any, next: any, item: any) => {
										const id = item.options.find((o: any) => o.value === val)
											?.raw.id
										if (id) {
											next.cascadeParams = {guid: id}
										}
									},
									placeholder: '请选择区域',
									options: [],
									isDepartmentEditField: fields.value.some(
										(f: any) => f.name === 'District' && f.isDepartmentEditField
									),
									value: historySearch?.filter?.District || '',
								})
							}
							if (
								!departmentInfo.street &&
								fields.value.some((f: any) => f.name === 'Street' && f.isListField)
							) {
								options.push({
									prop: 'Street',
									type: 'select',
									filterable: true,
									cascadeUrl: '/api/platform/department/my-region-names',
									beforeInitOptions: (val: any, next: any, item: any) => {
										const id = item.options.find((o: any) => o.value === val)
											?.raw.id
										if (id) {
											next.cascadeParams = {guid: id}
										}
									},
									placeholder: '请选择街道',
									options: [],
									isDepartmentEditField: fields.value.some(
										(f: any) => f.name === 'Street' && f.isDepartmentEditField
									),
									value: historySearch?.filter?.Street || '',
								})
							}

							if (
								!departmentInfo.community &&
								fields.value.some(
									(f: any) => f.name === 'Community' && f.isListField
								)
							) {
								options.push({
									prop: 'Community',
									type: 'select',
									filterable: true,
									cascadeUrl: '/api/platform/department/my-region-names',
									beforeInitOptions: (val: any, next: any, item: any) => {
										const id = item.options.find((o: any) => o.value === val)
											?.raw.id
										if (id) {
											next.cascadeParams = {guid: id}
										}
									},
									placeholder: '请选择社区',
									options: [],
									isDepartmentEditField: fields.value.some(
										(f: any) =>
											f.name === 'Community' && f.isDepartmentEditField
									),
									value: historySearch?.filter?.Community || '',
								})
							}
							item.cascadeOptions = options

							item.cascadeKeys = ['name', 'name']
							cascadeOptions.value = options
						}

						// if (
						// 	[
						// 		'identification_number',
						// 		'phone',
						// 		'disability_certificate_number',
						// 		'sex',
						// 		'age',
						// 		'email',
						// 		'birthday',
						// 	].includes(m.type)
						// ) {
						// 	item.type = 'string'
						// }

						return item
					})
				// return
				// 新增/编辑
				formArray.value = __fieldsFilter.filter(
					(f: any) =>
						(!['Informant', 'DataSource', 'UpdateTime', 'Editor'].includes(
							f.raw.name
						) &&
							(f.raw.hasOwnProperty('isListField') ? f.raw.isListField : true) &&
							(f.raw.hasOwnProperty('isDepartmentField')
								? f.raw.isDepartmentField
								: true)) ||
						f.raw.isUnique ||
						!f.raw.isNullable
				)

				formArray.value = formArray.value.filter(
					(v) => v.field === '_cascade' || v.raw.isDepartmentField
				)
				formisDepartmentEditField.value = []

				// 必填字段
				formArray.value
					.filter((f: any) => f.raw.isNullable == false)
					.forEach((f: any) => {
						formRules.value[f.raw.name] = [
							{
								required: true,
								message: `请输入${f.raw.displayName}`,
								trigger: 'blur',
							},
						]
					})

				// 整数和小数配置了限制范围
				formArray.value
					.filter(
						(item: any) =>
							(item.type === 'int' || item.type === 'decimal') &&
							item.raw.customValueLimit
					)
					.forEach((f: any) => {
						formRules.value[f.raw.name] = [
							{
								required: !f.raw.isNullable,
								trigger: 'blur',
								validator: (rule: any, value: any, callback: any) => {
									// if (value === 0) {
									// 	callback()
									// } else {
									const numberRules: any = {
										0: '等于',
										1: '不等于',
										2: '大于',
										3: '大于等于',
										4: '小于',
										5: '小于等于',
										6: '包含在',
										7: '不包含在',
									}
									if (
										!f.raw.isNullable &&
										(value === null || value === undefined)
									) {
										callback(new Error(`请输入数据`))
									}
									if (
										f.raw.isNullable &&
										(value === null || value === undefined)
									) {
										callback()
									}
									if (f.raw.customValueLimit.type === 0) {
										if (!isEqual(value, f.raw.customValueLimit.rangeOne)) {
											callback(
												new Error(
													`允许输入数据${
														numberRules[f.raw.customValueLimit.type]
													}${f.raw.customValueLimit.rangeOne}`
												)
											)
										} else {
											callback()
										}
									} else if (f.raw.customValueLimit.type === 1) {
										if (!isNotEqual(value, f.raw.customValueLimit.rangeOne)) {
											callback(
												new Error(
													`允许输入数据${
														numberRules[f.raw.customValueLimit.type]
													}${f.raw.customValueLimit.rangeOne}`
												)
											)
										} else {
											callback()
										}
									} else if (f.raw.customValueLimit.type === 2) {
										if (
											!isGreaterThan(value, f.raw.customValueLimit.rangeOne)
										) {
											callback(
												new Error(
													`允许输入数据${
														numberRules[f.raw.customValueLimit.type]
													}${f.raw.customValueLimit.rangeOne}`
												)
											)
										} else {
											callback()
										}
									} else if (f.raw.customValueLimit.type === 3) {
										if (
											!isGreaterThanOrEqual(
												value,
												f.raw.customValueLimit.rangeOne
											)
										) {
											callback(
												new Error(
													`允许输入数据${
														numberRules[f.raw.customValueLimit.type]
													}${f.raw.customValueLimit.rangeOne}`
												)
											)
										} else {
											callback()
										}
									} else if (f.raw.customValueLimit.type === 4) {
										if (!isLessThan(value, f.raw.customValueLimit.rangeOne)) {
											callback(
												new Error(
													`允许输入数据${
														numberRules[f.raw.customValueLimit.type]
													}${f.raw.customValueLimit.rangeOne}`
												)
											)
										} else {
											callback()
										}
									} else if (f.raw.customValueLimit.type === 5) {
										if (
											!isLessThanOrEqual(
												value,
												f.raw.customValueLimit.rangeOne
											)
										) {
											callback(
												new Error(
													`允许输入数据${
														numberRules[f.raw.customValueLimit.type]
													}${f.raw.customValueLimit.rangeOne}`
												)
											)
										} else {
											callback()
										}
									} else if (f.raw.customValueLimit.type === 6) {
										if (
											!isBetweenExclusive(
												value,
												f.raw.customValueLimit.rangeOne,
												f.raw.customValueLimit.rangeTwo
											)
										) {
											callback(
												new Error(
													`允许输入数据${
														numberRules[f.raw.customValueLimit.type]
													}${f.raw.customValueLimit.rangeOne}与${
														f.raw.customValueLimit.rangeTwo
													}之间`
												)
											)
										} else {
											callback()
										}
									} else if (f.raw.customValueLimit.type === 7) {
										if (
											!isNotBetween(
												value,
												f.raw.customValueLimit.rangeOne,
												f.raw.customValueLimit.rangeTwo
											)
										) {
											callback(
												new Error(
													`允许输入数据${
														numberRules[f.raw.customValueLimit.type]
													}${f.raw.customValueLimit.rangeOne}与${
														f.raw.customValueLimit.rangeTwo
													}之间`
												)
											)
										} else {
											callback()
										}
									}
									// }
								},
							},
						]
					})

				// formArray.value.forEach((item) => {
				// 	if (item.raw.calculateRule) {
				// 		item.disabled = true
				// 	}
				// 	if(item.raw.editDisabled) {
				// 		item.disabled = true
				// 	}
				// })

				// 规则字段
				formArray.value
					.filter((obj1) => ruleResData.some((obj2) => obj1.type === obj2.name))
					.map((item1: any) => {
						const matchedItem = ruleResData.find((item2) => item1.type === item2.name)
						return {
							...item1,
							__rules: matchedItem.expression, // 添加新属性 names
						}
					})
					.forEach((f: any) => {
						// if (f.raw.type !== 'birthday') {
						formRules.value[f.raw.name] = [
							{
								type: f.raw.type === 'birthday' ? 'date' : null,
								required: f.raw.isNullable == false ? true : false,
								// trigger: f.raw.type === 'birthday' ? 'change' : 'blur',
								trigger: 'blur',
								validator: (rule: any, value: any, callback: any) => {
									if (value === 0) {
										callback()
									}
									if (!value || value === 'Invalid Date') {
										if (f.raw.isNullable == false) {
											callback(new Error(`请输入${f.raw.displayName}`))
										} else {
											callback()
										}
									} else {
										const Reg = f.__rules ? new RegExp(f.__rules) : /.*/
										const res = Reg.test(value)

										if (!res && f.raw.type === 'phone') {
											callback(
												new Error(
													`请输入正确的${f.raw.displayName},13888888888,023-88888888`
												)
											)
										} else if (!res && f.raw.type != 'birthday') {
											callback(new Error(`请输入正确的${f.raw.displayName}`))
										} else {
											callback()
										}
										// callback(new Error('Please input the password'))
									}
								},
							},
						]
						// }
					})

				// 提示文案
				const rules = formArray.value.map((m: any) => m.raw.calculateRule).filter(Boolean)

				if (rules.length > 0) {
					rules.forEach((f: any) => {
						getEvalRPNText(f.rpnExpression)
					})
				}

				// 身份证相关联
				const rulesId = formArray.value
					.map((m: any) => m.raw.relevanceCalculateRule)
					.filter(Boolean)
				if (rulesId.length > 0) {
					rulesId.forEach((f: any) => {
						evnCardId(f)
					})
				}

				// 搜索
				formSearch.value = JSON.parse(
					JSON.stringify(__fieldsFilter.filter((f: any) => f.raw.isQueryField))
				)

				// 请勿删除下行代码 大屏测试用
				// localStorage.setItem('formSearch222', JSON.stringify(formSearch.value))

				let valuesFound: any = ['City', 'District', 'Street', 'Community']

				if (
					fields.value
						.filter((v: any) => v.isQueryField)
						.filter((v: any) => valuesFound.includes(v.name)).length === 4
				) {
					formSearch.value.unshift({
						type: 'cascade',
						field: '_cascade',
						title: '所属区域',
						cascadeOptions: [],
					})
				}

				conditionList.value = []

				formSearch.value.forEach((f: any) => {
					// ;

					if (f.type === 'cascade') {
						// 城市不添加过滤
						conditionList.value.push({
							type: f.type,
							field: f.field,
							title: f.title,
							options: [],
						})
					} else if (f.raw.type === 'int' || f.raw.type === 'decimal') {
						// 整数 小数
						conditionList.value.push({
							type: f.raw.type,
							field: f.field,
							title: f.title,

							options: [
								{
									value: '等于',
									label: '等于',
								},
								{
									value: '为空',
									label: '为空',
								},
							],
						})
					} else if (f.raw.type === 'datetime') {
						// 日期和时间
						conditionList.value.push({
							type: f.raw.type,
							field: f.field,
							title: f.title,
							options: [
								{
									value: '包含',
									label: '包含',
								},
								{
									value: '为空',
									label: '为空',
								},
							],
						})
					} else if (f.raw.multiple === false) {
						// 多选
						// 单选

						conditionList.value.push({
							type: f.raw.type,
							field: f.field,
							title: f.title,
							options: [
								{
									value: '等于',
									label: '等于',
								},
								{
									value: '为空',
									label: '为空',
								},
							],
						})
					} else if (f.raw.multiple === true) {
						// 多选
						conditionList.value.push({
							type: f.raw.type,
							field: f.field,
							title: f.title,
							options: [
								{
									value: '包含',
									label: '包含',
								},
								{
									value: '为空',
									label: '为空',
								},
							],
						})
					} else if (
						f.raw.type === 'identification_number' ||
						f.raw.type === 'certificate' ||
						f.raw.validationRule?.relatedFieldId
					) {
						// 居民身份证号

						// 证件号 type certificate 忽略不处理
						// 取证件号后面的输入框字段
						conditionList.value.push({
							type: f.raw.type,
							field: f.field,
							title: f.title,
							options: [
								{
									value: '等于',
									label: '等于',
								},
								{
									value: '为空',
									label: '为空',
								},
							],
						})
					} else if (
						['phone', 'age', 'sex', 'birthday', 'email'].indexOf(f.raw.type) > -1
					) {
						// 联系电话 性别年龄  出生日期
						conditionList.value.push({
							type: f.raw.type,
							field: f.field,
							title: f.title,
							options: [
								{
									value: '等于',
									label: '等于',
								},
								{
									value: '为空',
									label: '为空',
								},
							],
						})
					} else if (
						f.raw.type === 'string' &&
						(f.raw.maxLength === 0 || f.raw.maxLength === 500)
					) {
						// 长文本、短文本
						conditionList.value.push({
							type: f.raw.type,
							field: f.field,
							title: f.title,
							options: [
								{
									value: '包含',
									label: '包含',
								},
								{
									value: '为空',
									label: '为空',
								},
							],
						})
					}
				})

				formSearch.value.forEach((f: any) => {
					f.disabled = f.__disabled = false
					if (f.__type === 'checkbox' || f.__type === 'radio') {
						f.full = false
					}
				})
				// 级联选择第一级必须是单选
				const fieldMultiple = formSearch.value
					.map((m: any) => m.raw?.fieldMultipleDto)
					.filter(Boolean)

				if (fieldMultiple.length > 0) {
					fieldMultiple.forEach((item) => {
						evnFormSearch(item.multipleArrId)
					})
				}
				const fieldMultiple2 = formArray.value
					.map((m: any) => m.raw?.fieldMultipleDto)
					.filter(Boolean)

				if (fieldMultiple2.length > 0) {
					fieldMultiple2.forEach((item) => {
						evnFormSearch2(item.multipleArrId)
					})
				}
				let arrEdit = formArray.value

				let isDepartmentEditFields = arrEdit.filter(
					(item: any) => item.raw?.isDepartmentEditField || item.type === 'cascade'
				)
				// formArray.value = arrEdit

				// 专门对村社的无需填报任务项。
				// const currentUser = JSON.parse(localStorage.getItem('currentUserInfo') as any)
				// if (currentUser.community) {

				// }
				ledger.value = data
				tabList.value = []
				tabList.value[0] = {
					name: `${data.name}的详情（${
						ledger.value?.runway ?? ledger.value?.ledgerType?.runway
					} - ${ledger.value?.ledgerType?.name}）`,
					isActive: true,
					index: 1,
				}
				getisExistLedgerDataAnalysis()
				if (!advancedSearchFormHistory) {
					ValidateCodeFun()
					if (filterStatus.value.length !== 0) {
						getLedgerTableList('', filterStatus.value)
					} else {
						getLedgerTableList()
					}
				} else {
					nextTick(() => {
						if (historySearch) {
							const {filter} = historySearch
							searchOpen.value = false

							Object.keys(filter).forEach((key) => {
								if (filter[key]) {
									searchFormRef.value?.setValue(key, filter[key])
								}
							})

							const fieldMultiple = formSearch.value
								.map((m: any) => m.raw?.fieldMultipleDto)
								.filter(Boolean)

							if (fieldMultiple.length > 0) {
								fieldMultiple.forEach((f) => {
									evnGetSelect(f.multipleArrId, filter)
								})
							}
							getLedgerTableList(filter)
							nextTick(() => expendSearch())
						} else {
							localStorage.removeItem('LDF_ASF')
							getLedgerTableList()
							if (searchOpen.value) {
								nextTick(() => expendSearch())
							}
						}
					})
				}

				buttons.value = [
					{
						code: 'view',
						title: '详情',
						icon: '<i i-majesticons-eye-line></i>',
						verify: `true`,
					},
					// {
					// 	code: 'submit',
					// 	type: 'primary',
					// 	title: '提交',
					// 	icon: '<i i-majesticons-eye-line></i>',
					// 	verify: `(${!iscommunity})&& (row.DataStatus === 5 || row.DataStatus === 1)`,
					// },
					{
						code: 'edit',
						type: 'primary',
						title: '编辑',
						icon: '<i i-majesticons-eye-line></i>',
						verify: `(${!iscommunity})&&(row.DataStatus == 0 || row.DataStatus === 5 ||
                         row.DataStatus === 1  && "${
								useUserStore().getUserInfo.id
							}" == row.LockUserId ||
						 row.DataStatus === 1 && row.LockUserId  == null ||
                         row.DataStatus === 3 && "${
								useUserStore().getUserInfo.id
							}" == row.LockUserId ||
						 row.DataStatus === 3 && row.LockUserId  == null )`,
					},
					{
						code: 'danger',
						type: 'danger',
						title: '删除',
						icon: '<i i-majesticons-eye-line></i>',
						verify: `((row.DataStatus == 0 || row.DataStatus == 1 || row.DataStatus == 5) &&  ${isPermission.value(
							Permissions.Delete
						)})&&(${!iscommunity})`,
					},
					{
						code: 'comparison',
						type: 'primary',
						title: '数据比对',
						verify: 'true',
						showBtn: isCanComparison.value,
					},
				]

				// GetLedgerSubmitAuditCount(ledger.value.id).then((res: any) => {
				// 	auditDataNumber.value = res.data
				// })
				submitAuditRef.value?.$reloadCount(ledger.value.id)
				typeof callback === 'function' && callback()

				// debugger

				formSearch.value.forEach((e: any) => {
					// debugger

					// if(e?.raw?.type === 'string') {
					// 	;
					// }
					if (e?.raw?.type === 'string' && typeof e.raw.multiple === 'boolean') {
						e.multiple = true
					} else {
						// ;
					}
				})

				formSearch.value.forEach((item: any) => {
					if (item.raw?.fieldMultipleDto?.multipleArr?.length) {
						const isLength = item.raw?.fieldMultipleDto?.multipleArr.length
						isLength &&
							item.raw?.fieldMultipleDto?.multipleArr.forEach((k: any) => {
								// debugger

								k.children.forEach((t: any) => {
									const u = {
										label: t.value,
										value: t.value,
									}

									if (Array.isArray(item.data)) {
										item.data.push(u)
									}
									//  Array.isArray(item.data) && item.data.push(u)
								})
							})
					}
				})
				formSearchClone.value = []
				formSearchClone.value = JSON.parse(JSON.stringify(formSearch.value))
				formArrayCop.value = JSON.parse(JSON.stringify(formArray.value))
			})
			.catch((err: any) => {
				if (err.response?.status === 500) {
					ElNotification.error('数据正在更新，请5分钟后再试')
				}
			})
		// getTableFiledValidateRule()
		// 	.then((ruleRes: any) => {
		// 		const ruleResData = ruleRes.data
		// 		ruleData.value = ruleRes.data

		// 	})
		// 	.catch((err: any) => {
		// 		if (err.response?.status === 500) {
		// 			ElNotification.error('数据正在更新，请5分钟后再试')
		// 		}
		// 	})
	}
}
const evnGetSelect = (selectId: any, data: any) => {
	const fieldMultiple = formSearch.value.map((m: any) => m.raw?.fieldMultipleDto).filter(Boolean)
	const fields = formSearch.value.filter((f: any) => selectId?.includes(f.raw?.id))[0]

	if (fields) {
		const targetArr = fieldMultiple.filter((f: any) => fields.raw?.id === f.multipleArrId)

		const originVal: any = data[fields.field]

		const IndexArr = findIndicesInA(formSearch.value, targetArr)
		IndexArr.forEach((f: any) => {
			let findChild = formSearch.value[f].raw?.fieldMultipleDto?.multipleArr.find(
				(f: any) => f.value === originVal
			)

			formSearch.value[f].data = findChild && findChild.children ? findChild.children : []
			findChild = {}
		})
	}

	//
	// const targetObj = formArray.value.find(
	// 	(f: any) => f.raw.fieldMultipleDto?.multipleArrId === selectId
	// )
	// const findChild = targetObj.raw.fieldMultipleDto?.multipleArr.find(
	// 	(f: any) => f.value === originVal
	// )
	// const Indexs = formArray.value.findIndex(
	// 	(f: any) => f.raw.fieldMultipleDto?.multipleArrId === selectId
	// )
	//
	// formArray.value[Indexs].data = findChild.children
}
const evnGetSelect2 = (selectId: any, data: any) => {
	const fieldMultiple = formArray.value.map((m: any) => m.raw?.fieldMultipleDto).filter(Boolean)
	const fields = formArray.value.filter((f: any) => selectId?.includes(f.raw.id))[0]
	const fieldsIndex = formArray.value.findIndex((f: any) => selectId?.includes(f.raw.id))

	if (fields) {
		const targetArr = fieldMultiple.filter((f: any) => fields.raw.id === f.multipleArrId)

		const originVal: any = data[fields.field]
		const IndexArr = findIndicesInA(formArray.value, targetArr)
		IndexArr.forEach((f: any) => {
			let findChild = formArray.value[f].raw?.fieldMultipleDto?.multipleArr.find(
				(f: any) => f.value === originVal
			)

			formArray.value[f].data = findChild && findChild.children ? findChild.children : []
			findChild = {}
		})

		formArray.value[fieldsIndex].default = data[fields.field]
			? String(data[fields.field])
			: null
	}
}
// 递归
const recursionFun = (obj: any) => {
	let arr: any = []
	obj.modifyChildren = []
	obj?.children?.forEach((item: any, index: any) => {
		if (!item.displayName) {
			if (arr.length > 0) {
				obj.modifyChildren.push(arr)
			}
			obj.modifyChildren.push(item)
			// activeNames.value.push(item.id)
			recursionFun(obj.modifyChildren[obj.modifyChildren.length - 1])
			// 把这一段放进去递归
			arr = []
		} else {
			arr.push(item)
		}
		if (obj.children.length - 1 == index) {
			if (arr.length > 0) {
				obj.modifyChildren.push(arr)
			}
			arr = []
		}
	})
}
const fileName = ref()
const onBeforeUpload = (file: any) => {
	uploadData.value.__isFile = true
	fileName.value = file.name
	uploadData.value.FileName =
		CryptoJS.MD5(file.name.split('.')[0]).toString().toUpperCase() +
		'.' +
		file.name.split('.')[1]
}

const onUploadChange = (uploadFile: UploadFile, uploadFiles: UploadFiles) => {
	uploadData.value.__isFile = uploadFiles.length > 0
}

const onUploadSuccess = (res: any) => {
	ElMessage.success(' 成功, 请从导入历史查看进度')
	executeImportNotify({
		tableInfoId: ledger.value.id,
		fileName: fileName.value,
		filePath: res.path,
		FileDataMd5: uploadData.value.FileName.split('.')[0],
	})
	pageation.value.currentPage = 1
	historyOpen.value = true
	importOpen.value = false

	uploadRef.value!.clearFiles()
	uploadData.value.__isFile = false
	// initTable()
}

const onUploadError = (err: any) => {
	ElMessage.error('上传失败')
}

const onUpload = () => {
	if (!uploadData.value.__isFile) {
		ElMessage.warning('请先选择文件')
		return
	}
	try {
		uploadRef.value!.submit()
	} catch (error: any) {
		if (error.response?.status === 500) {
			ElNotification.error('数据正在依次导入，请耐心等待，无需重复上传')
		}
	}
}

const onDownloadExcelTemplae = () => {
	downloadExcelTemplateByLedgerId(ledger.value.id).then((res: any) => {
		const blob = res.data
		saveAs(
			new Blob([blob], {
				type: 'apilication/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			}),
			`${ledger.value.name}导入模板.xlsx`
		)
	})
}

const onImportHistoryFirstItemCompleted = (isFirstItemCompleted: boolean) => {
	if (isFirstItemCompleted) {
		// ElMessage.success('导入成功')
		getLedgerTableList()
		if (submitAuditRef.value) {
			submitAuditRef.value?.$reloadCount(ledger.value.id)
		}
	}
}
const OrderFields = ref<string>('')
const DataStatusEnumList = ref<any[]>([])
const getLedgerTableList = (whereForm?: any, dataStatusList?: any, formCondition?: any) => {
	tableRef.value?.clearSelection()
	const whereFields: any = {}
	const __whereForm = JSON.parse(JSON.stringify(whereForm ?? ''))

	if (__whereForm) {
		Object.keys(__whereForm).forEach((key) => {
			const field = fields.value.find((f: any) => f.name === key)
			const fieldType = field?.type

			if (
				__whereForm[key] &&
				(__whereForm[key].length > 0 || typeof __whereForm[key] === 'number')
			) {
				if (
					Array.isArray(__whereForm[key]) &&
					(fieldType === 'datetime' || fieldType === 'date')
				) {
					whereFields[key] = {
						'3': __whereForm[key][0],
						'5': __whereForm[key][1],
					}
				} else if (Array.isArray(__whereForm[key]) && fieldType === 'string') {
					whereFields[key] = {'15': __whereForm[key].join(',')}
				} else if (fieldType === 'string' && field.multiple !== null) {
					// 单/复选改下拉处理
					whereFields[key] = {'0': __whereForm[key]}
				} else if (fieldType === 'string') {
					whereFields[key] = {'1': __whereForm[key]}
				} else if (fieldType === 'int' || fieldType === 'decimal') {
					whereFields[key] = {'0': Number(__whereForm[key])}
				} else if (fieldType === 'identification_number') {
					whereFields[key] = {'1': __whereForm[key]}
				} else if (fieldType === 'phone') {
					whereFields[key] = {'1': __whereForm[key]}
				} else {
					whereFields[key] = {'0': __whereForm[key]}
				}
			}
		})
	}
	let datas: any = {}
	searchFields.value = whereFields
	if (OrderFields.value === '' || OrderFields.value === null) {
		datas = {
			desensitizeCode: '',
			ledgerId: ledger.value?.id,
			skipCount: (pageation.value.currentPage - 1) * pageation.value.pageSize,
			maxResultCount: pageation.value.pageSize,
			whereFields,
			dataStatusList: filterStatus.value,
			OrderFields: ['UpdateTime desc'],
		}
	} else {
		datas = {
			desensitizeCode: '',
			ledgerId: ledger.value?.id,
			skipCount: (pageation.value.currentPage - 1) * pageation.value.pageSize,
			maxResultCount: pageation.value.pageSize,
			whereFields,
			dataStatusList: filterStatus.value,
			OrderFields: [OrderFields.value],
		}
	}
	// if (dataStatusList) {
	// 	datas.dataStatusList = dataStatusList
	// }
	if (filterStatus.value.length !== 0) {
		datas.dataStatusList = filterStatus.value
		if (filterStatus.value.includes(StatusList['已删除'])) {
			datas.DisableSoftDeleteFilter = true
		}
	}
	if (datas.desensitizeCode.length === 0) {
		const ValidateCode = localStorage.getItem('ValidateCode')
		if (ValidateCode != null) {
			datas.desensitizeCode = ValidateCode
		} else {
			delete datas.desensitizeCode
		}
	}
	loading.value = true

	let whereFieldsCopy = datas.whereFields

	// debugger

	// ;

	if (formCondition) {
		let keys: any = Object.keys(formCondition)
		if (keys.length) {
			keys.forEach((key: any) => {
				fieldsAll.value.forEach((f: any) => {
					if (f.name === key) {
						if (f.type === 'int' || f.type === 'decimal') {
							// 整数 小数
							if (formCondition[key] === '为空') {
								datas.whereFields[key] = {
									'11': null,
								}
							} else if (formCondition[key] === '等于') {
								// 等于
								datas.whereFields[key] = {
									'0': __whereForm[key],
								}
							}
						} else if (f.type === 'datetime') {
							// 日期和时间
							if (formCondition[key] === '为空') {
								datas.whereFields[key] = {
									'11': null,
								}
							}
						} else if (f.multiple === false || f.multiple === true) {
							// 单选或多选
							if (formCondition[key] === '为空') {
								datas.whereFields[key] = {
									'11': null,
								}
							}
						} else if (
							f.type === 'identification_number' ||
							f.type === 'certificate' ||
							f.validationRule?.relatedFieldId
						) {
							// 居民身份证号或证件号
							if (formCondition[key] === '为空') {
								datas.whereFields[key] = {
									'11': null,
								}
							} else if (formCondition[key] === '等于') {
								// 等于
								datas.whereFields[key] = {
									'0': __whereForm[key],
								}
							}
						} else if (
							['phone', 'age', 'sex', 'birthday', 'email'].indexOf(f.type) > -1
						) {
							// 联系电话、性别、年龄、出生日期、邮箱
							if (formCondition[key] === '为空') {
								datas.whereFields[key] = {
									'11': null,
								}
							} else if (formCondition[key] === '等于') {
								// 等于
								datas.whereFields[key] = {
									'0': __whereForm[key],
								}
							}
						} else if (
							f.type === 'string' &&
							(f.maxLength === 0 || f.maxLength === 500)
						) {
							// 长文本、短文本
							if (formCondition[key] === '为空') {
								datas.whereFields[key] = {
									'11': null,
								}
							} else if (formCondition[key] === '包含') {
								// 包含
								datas.whereFields[key] = {
									'1': __whereForm[key],
								}
							}
						}
					}
				})
			})
		}
	}

	getLedgerTableListByLedgerId(datas)
		.then(async (res: any) => {
			const keys = fields.value.map((m: any) => m.name)

			const {items} = res.data
			const reslut: any = []

			for (let item of items) {
				const row: any = {}
				for (let key of keys) {
					const value = item[key]

					if (
						typeof value === 'string' &&
						value?.includes('[') &&
						value.startsWith('[', 0) &&
						value?.includes(']') &&
						value.endsWith(']') &&
						fields.value.filter((f) => f.name === key)[0].type != 'string'
					) {
						if (
							fields.value.filter((f) => f.name === key)[0].type === 'images' ||
							fields.value.filter((f) => f.name === key)[0].type === 'attachments'
						) {
							row[key] = value
						} else {
							row[key] = JSON.parse(value.replace(/，/g, ',')).join(',')
						}
					} else {
						row[key] = value
					}
				}

				if (item.LockUserId) {
					const lockuser = await GetLockUserNameById(item.LockUserId)
					row._lockuser = lockuser
				}

				reslut.push(row)
			}

			const filedNames = ledger.value.tableInfo.fields.filter((f: any) => f.type === 'date')
			reslut.forEach((item: any) => {
				filedNames.forEach((item2: any) => {
					if (item[item2.name]) {
						item[item2.name] = item[item2.name].split(' ')[0]
					}
				})
			})

			//
			// colData.value.forEach((f) => {
			// 	if (f.raw?.type === 'images' || f.raw?.type === 'attachments') {
			// 		reslut.forEach((item: any) => {
			// 			if (item[f.raw.name] === '[]') {
			// 				item[f.raw.name] = ''
			// 			}
			// 			if (item[f.raw.name] !== '') {
			// 				item[f.raw.name] =
			// 					JSON.parse(item[f.raw.name])[0]?.name === '[]'
			// 						? '-'
			// 						: JSON.parse(item[f.raw.name])[0]?.name
			// 			}
			// 		})
			// 		// item[f.name] = item[f.name].split(',')
			// 	}
			// })

			tableData.value = reslut
			// 请勿删除下行代码 大屏测试用
			// localStorage.setItem('tableData233', JSON.stringify(tableData.value))

			pageation.value.total = res.data.totalCount
			addSeletPageDataAndCheckbox()
			showMoreButton()

			blockRef.value?.resize()
		})
		.catch((err: any) => {
			if (err.response?.status === 500) {
				ElNotification.error('数据正在更新，请5分钟后再试')
			}
		})
		.finally(() => {
			loading.value = false
		})

	console.log(dataStatusList)

	// if (!dataStatusList) {
	// getLedgerStatisticsByField('DataStatus', 0, datas).then((res) => {
	// 	console.log(res)
	// 	if (res.data) {
	// 		DataStatusEnumList.value = res.data
	// 		const statusCounts: any = {
	// 			0: 0,
	// 			1: 0,
	// 			2: 0,
	// 			3: 0,
	// 			4: 0,
	// 			5: 0,
	// 		}

	// 		DataStatusEnumList.value.forEach((item) => {
	// 			if (item.type in statusCounts) {
	// 				statusCounts[item.type] = item.count
	// 			}
	// 		})
	// 		// 完全重建表格列配置，强制更新过滤器
	// 		// 创建新的状态列
	// 		const statusColumn = {
	// 			title: '状态',
	// 			field: 'DataStatus',
	// 			fixed: 'right',
	// 			filters: [
	// 				{
	// 					text: `已审核(${statusCounts[0]})`,
	// 					value: 0,
	// 					checked: dataStatusList !== undefined ? dataStatusList.includes('0') : true,
	// 				},
	// 				{
	// 					text: `待提交(${statusCounts[1]})`,
	// 					value: 1,
	// 					checked: dataStatusList !== undefined ? dataStatusList.includes('1') : true,
	// 				},
	// 				{
	// 					text: `待审核(${statusCounts[2]})`,
	// 					value: 2,
	// 					checked: dataStatusList !== undefined ? dataStatusList.includes('2') : true,
	// 				},
	// 				{
	// 					text: `已删除(${statusCounts[3]})`,
	// 					value: 3,
	// 					checked:
	// 						dataStatusList !== undefined ? dataStatusList.includes('3') : false,
	// 				},
	// 				{
	// 					text: `修改中(${statusCounts[4]})`,
	// 					value: 4,
	// 					checked: dataStatusList !== undefined ? dataStatusList.includes('4') : true,
	// 				},
	// 				{
	// 					text: `已驳回(${statusCounts[5]})`,
	// 					value: 5,
	// 					checked: dataStatusList !== undefined ? dataStatusList.includes('5') : true,
	// 				},
	// 			],
	// 			filterValue: [0, 1, 2, 4, 5],
	// 		}

	// 		// 保存当前的过滤值
	// 		const oldStatusColumn = colData.value.find((col: any) => col.field === 'DataStatus')
	// 		if (oldStatusColumn && oldStatusColumn.filterValue) {
	// 			statusColumn.filterValue = [...oldStatusColumn.filterValue]
	// 		}

	// 		// 重建列数组，删除旧的DataStatus列，添加新的
	// 		const otherColumns = colData.value.filter((col: any) => col.field !== 'DataStatus')

	// 		// 先清空列数组，然后重新设置
	// 		colData.value = []

	// 		// 在下一个tick中设置新的列配置
	// 		nextTick(() => {
	// 			// 设置新的列数组
	// 			colData.value = [...otherColumns, statusColumn]

	// 			// 强制表格重新布局
	// 			nextTick(() => {
	// 				if (tableRef.value) {
	// 					try {
	// 						// 强制表格重新布局
	// 						tableRef.value.getElTableRef().doLayout()

	// 						// 尝试手动触发过滤器更新
	// 						const elTable = tableRef.value.getElTableRef()
	// 						if (elTable && elTable.store) {
	// 							// 强制重置过滤器状态
	// 							elTable.store.states.filters.value = {}
	// 							// 重新应用过滤器
	// 							setTimeout(() => {
	// 								if (
	// 									elTable.store &&
	// 									typeof elTable.store.commit === 'function'
	// 								) {
	// 									elTable.store.commit('filterChange', {
	// 										column: {property: 'DataStatus'},
	// 										values: statusColumn.filterValue,
	// 									})
	// 								}
	// 							}, 100)
	// 						}
	// 					} catch (e) {
	// 						console.error('Error updating filters:', e)
	// 					}
	// 				}
	// 			})
	// 		})
	// 	}
	// })

	// }
}
const showMoreButtonVisible = ref(false)
// 判断是否溢出
function showMoreButton() {
	const dom: any = document.getElementById('updateDescription')
	if (dom?.clientHeight < dom?.scrollHeight) {
		showMoreButtonVisible.value = true
	} else {
		showMoreButtonVisible.value = false
	}
}
const onPageationChange = (val: number, type: string) => {
	if (type === 'size') {
		pageation.value.pageSize = val
	} else {
		pageation.value.currentPage = val
	}

	if (advancedSearchForm.value) {
		getLedgerTableList(advancedSearchForm.value)
	} else {
		getLedgerTableList()
	}
}
const ledgerAuxiliaryFillingList = ref<any[]>([])
const onOpenAdd = async () => {
	showAddLedgerDataModelisVisible.value = true
	formArray.value = formArrayCop.value
	formArray.value.forEach((v) => {
		if (v.field === '_cascade') {
			v.cascadeOptions.forEach((e) => {
				e.beforeInitOptions = (val: any, next: any, item: any) => {
					const id = item.options.find((o: any) => o.value === val)?.raw.id
					if (id) {
						next.cascadeParams = {guid: id}
					}
				}
			})
		}
	})
	formisDepartmentEditField.value = []
	formArray.value.forEach((f: any) => {
		f.default = ''
		f.disabled = false
		f.disabled = f.raw?.name === 'UpdateTime'
		if (f.type === 'int' || f.type === 'decimal') {
			f.disabled = f.raw?.calculateRule !== null
		}
		if (f.type === 'age' || f.type === 'sex' || f.type === 'birthday') {
			f.disabled = f.raw?.relevanceCalculateRule !== null
		}
		const {community, street, district, city} = userStore.getUserInfo

		if (fixedFields.includes(f.raw?.name)) {
			if (f.raw.name === 'Community') {
				f.disabled = community ? true : false
				f.default = community
			}
			if (f.raw.name === 'Street') {
				f.disabled = street ? true : false
				f.default = street
			}
			if (f.raw.name === 'District') {
				f.disabled = district ? true : false
				f.default = district
			}
			if (f.raw.name === 'City') {
				f.disabled = true
				f.default = city
			}
		}

		if (f.type === 'date') {
			f.datetype = 'day'
		}

		if (f.type === 'datetime') {
			f.datetype = 'time'
		}

		// 复选框
		if (f.type === 'checkbox' && f.raw?.multiple) {
			f.default = []
		}

		// 处理单复选改下拉的数据
		if (f.__type === 'checkbox' && f.default) {
			f.default = f.default.split(',')
		}

		// 将单选下拉禁止多选, 在编辑或新增的时候
		if (f.__type === 'radio') {
			f.multiple = false
		}

		if (f.type === 'cascade') {
			f.cascadeOptions.forEach((ff: any) => ((ff.value = ''), (ff.default = '')))
		}
	})
	formArray.value.forEach((item: any) => {
		// if (item.raw.isDepartmentEditField) {
		// 	item.disabled = false
		// } else {
		// 	item.disabled = true
		// }
		if (item.cascadeOptions) {
			item.cascadeOptions.forEach((item: any) => {
				item.disabled = false
			})
		}
		// if (item.raw.editDisabled) {
		// 	item.disabled = true
		// }
	})

	if (auxiliaryFillingType.value === 0) {
		try {
			const res = await getLedgerAuxiliaryFilling({
				LedgerId: route.query.ledgerId as string,
				isPaged: false,
			})
			if (res) {
				ledgerAuxiliaryFillingList.value = res.data.items
				if (ledgerAuxiliaryFillingList.value) {
					// formArray.value.forEach((c) => {
					// 	if (c.field === ledgerAuxiliaryFillingList.value[0].field.name) {
					// 		c.type = 'select'
					// 	}
					// })
				}
			}
		} catch (error: any) {
			if (error.response?.status === 500) {
				ElNotification.error('当前操作人员较多，请5分钟后再试')
			}
		}
	}
	if (auxiliaryFillingType.value === 1) {
		try {
			const res = await getDataSetAuxiliaryFilling({
				LedgerId: route.query.ledgerId as string,
				isPaged: false,
			})

			ledgerAuxiliaryFillingList.value = [
				{
					...res.data.items[0],
					field: {
						name: res.data.items[0].configs.filter(
							(x) => x.dbTableFieldId === res.data.items[0].selectFieldId
						)[0].field.name,
					},
					configs: res.data.items[0].configs.map((v) => ({
						...v,
						sourceFiled: v.dbTableField,
					})),
				},
			]
			if (ledgerAuxiliaryFillingList.value) {
				formArray.value.forEach((c) => {
					// if (
					// 	c.field ===
					// 	res.data.items[0].configs.filter(
					// 		(x) => x.dbTableFieldId === res.data.items[0].selectFieldId
					// 	)[0].field.name
					// ) {
					// 	c.type = 'select'
					// }
				})
			}
		} catch (error: any) {
			if (error.response?.status === 500) {
				ElNotification.error('当前操作人员较多，请5分钟后再试')
			}
		}
	}
	if (auxiliaryFillingType.value === 2) {
		try {
			const res = await getApiAuxiliaryFillingList(route.query.ledgerId as string)

			if (res.data) {
				// 构建和业务表辅助填报相类似得数据结构以便可以复用某些方法
				ledgerAuxiliaryFillingList.value = [
					{
						...res.data,
						field: {name: res.data.ledgerKey},
						configs: res.data.rules.map((v: any) => ({
							field: {name: v.fieldKey},
							sourceFiled: {name: v.fieldValue},
						})),
					},
				]
				if (ledgerAuxiliaryFillingList.value) {
					// formArray.value.forEach((c) => {
					// 	if (c.field === ledgerAuxiliaryFillingList.value[0].field.name) {
					// 		c.type = 'select'
					// 	}
					// })
				}
			}
		} catch (error: any) {
			if (error.response?.status === 500) {
				ElNotification.error('当前操作人员较多，请5分钟后再试')
			}
		}
	}

	formTitle.value = '新增'

	// addFormRef
	if (localStorage.getItem('temporarySaveData')) {
		const temporaryData = JSON.parse(localStorage.getItem('temporarySaveData') || '[]')
		if (
			temporaryData &&
			temporaryData.length > 0 &&
			temporaryData.some((v: any) => v.ledgerId === route.query.ledgerId)
		) {
			const datas = temporaryData.filter((v: any) => v.ledgerId === route.query.ledgerId)[0]
				.data

			formArray.value.forEach((f: any) => {
				if (f.field === '_cascade') {
					f.cascadeOptions.forEach((ff: any) => {
						ff.value = datas[ff.prop]
						ff.default = datas[ff.prop]
					})
				}
			})
			addOpen.value = true
			nextTick(() => {
				Object.keys(datas).forEach((keys) => {
					addFormRef.value?.setFieldValue(keys, datas[keys])
				})
			})
		} else {
			addOpen.value = true
		}
	} else {
		addOpen.value = true
	}
}

const onBatchExportLedgerData = (code: any) => {
	let form: any = dealWithFrom(toRaw(searchFormRef.value.getForm()))

	if (Object.keys(cascadeData.value).length > 0) {
		form = Object.assign(form, cascadeData.value)
	}

	const whereFields: any = {}
	const __whereForm = JSON.parse(JSON.stringify(form ?? ''))

	if (__whereForm) {
		Object.keys(__whereForm).forEach((key) => {
			const field = fields.value.find((f: any) => f.name === key)
			const fieldType = field?.type

			if (
				__whereForm[key] &&
				(__whereForm[key].length > 0 || typeof __whereForm[key] === 'number')
			) {
				if (
					Array.isArray(__whereForm[key]) &&
					(fieldType === 'datetime' || fieldType === 'date')
				) {
					whereFields[key] = {
						'3': __whereForm[key][0],
						'5': __whereForm[key][1],
					}
				} else if (Array.isArray(__whereForm[key]) && fieldType === 'string') {
					whereFields[key] = {'15': __whereForm[key].join(',')}
				} else if (fieldType === 'string' && field.multiple !== null) {
					// 单/复选改下拉处理
					whereFields[key] = {'0': __whereForm[key]}
				} else if (fieldType === 'string') {
					whereFields[key] = {'1': __whereForm[key]}
				} else if (fieldType === 'int' || fieldType === 'decimal') {
					whereFields[key] = {'0': Number(__whereForm[key])}
				} else {
					whereFields[key] = {'0': __whereForm[key]}
				}
			}
		})
	}

	const selectFields: any = {}
	// 选中的字段
	exportFileds.value.forEach((item: any) => {
		selectFields[item.name] = item.name
	})

	const data = {
		remark: exportDes.value,
		desensitizeCode: code,
		selectFields: selectFields,
		whereFields: whereFields,
		dataStatusList: filterStatus.value,
		OrderFields:
			OrderFields.value === '' || OrderFields.value === null
				? ['UpdateTime desc']
				: [OrderFields.value],
	}
	if (handClickExportBtn.value == 'exportAll') {
		ExportLedgerDataAllAsync(route.query.ledgerId as string, {}, data)
			.then((res: any) => {
				// saveAs(res.data, `${ledger.value.name}导出数据.xlsx`)
				historyExport.value = true
			})
			.catch(() => {
				ElMessage.warning('无可导出数据')
			})
	} else if (handClickExportBtn.value == 'exportSelet') {
		const selectIds = ref<any>()
		selectIds.value = allSeletePageData.value.map((item: any) => String(item.Id)).join(',') //选中的数据
		let selectData = {
			'6': selectIds.value,
		}

		if (selectIds.value != '') data.whereFields.Id = selectData
		ExportLedgerDataAsync(route.query.ledgerId as string, {}, data)
			.then((res: any) => {
				// saveAs(res.data, `${ledger.value.name}导出数据.xlsx`)
				historyExport.value = true
			})
			.catch(() => {
				ElMessage.warning('无可导出数据')
			})
	}
}

const safeRelease = (val: any) => {
	const type = val === '' ? useCode.codeType.tm : useCode.codeType.dc

	if (!useCode.expired(ledger.value.id, type)) {
		const historyCode: any = useCode.getCode(ledger.value.id, type)

		if (type === useCode.codeType.tm) {
			ElMessage.success('当前业务表脱敏验证码未过期')
			correctCode.value = historyCode?.code
		} else {
			// 导出直接下载
			if (val == 'exportAll') {
				// 导出全部
				onBatchExportLedgerData(historyCode?.code)
			} else if (val == 'exportSelet') {
				// 导出选中的数据
				onBatchExportLedgerData(historyCode?.code)
			}
		}
		return
	}

	cancelOpen.value = true
	cancelCode.value.val1 = ''
	cancelCode.value.val2 = ''
	cancelCode.value.val3 = ''
	cancelCode.value.val4 = ''
	handClickExportBtn.value = val //给空值是点击列表的脱敏
	if (val != '') {
		codeTitle.value = '导出数据'
	} else {
		codeTitle.value = '解除脱敏'
	}
}
const handelSendCode = () => {
	ElMessage.success('验证码已发送至您的渝快政，有效期五分钟。')
	// 是否需要做判断去发送不同类型的验证码
	const num = handClickExportBtn.value === '' ? 1 : 2
	sendCode(ledger.value.id, {codeType: num})
}
const correctCode = ref('')
const verifyCode = (code: any) => {
	// 去处理验证的事情
	loading.value = true
	const type = handClickExportBtn.value
	const num = type === '' ? 1 : 2
	sendValidateCode(ledger.value.id, {code, codeType: num})
		.then((res: any) => {
			if (res.data) {
				ElMessage.success('验证成功')
				if (type == '') {
					localStorage.setItem('ValidateCode', code)
					correctCode.value = res.data ? code : ''
					getLedgerTableList()
				} else if (type == 'exportAll') {
					// 导出全部
					onBatchExportLedgerData(code)
				} else if (type == 'exportSelet') {
					// 导出选中的数据
					onBatchExportLedgerData(code)
				}
				cancelOpen.value = false

				useCode.setCode(
					ledger.value.id,
					code,
					type === '' ? useCode.codeType.tm : useCode.codeType.dc
				)
			} else {
				ElMessage.error('验证失败')
			}
		})
		.finally(() => {
			loading.value = false
		})
	//重新调列表
}
// 验证 验证码是否超时或错误
const ValidateCodeFun = async () => {
	let isDesensitization = false //不需要脱敏
	allFiled.value.forEach((item: any) => {
		if (item.desensitizationType != null) {
			isDesensitization = true
		}
	})
	if (isDesensitization) {
		let ValidateCode = localStorage.getItem('ValidateCode')
		if (ValidateCode == null || ValidateCode == '') {
			ValidateCode = ''
		}
		const res = await sendValidateCode(ledger.value.id, {code: ValidateCode})
		if (res.data) {
			localStorage.setItem('ValidateCode', ValidateCode as any)
		} else {
			localStorage.setItem('ValidateCode', ValidateCode)
		}
		return res.data
	}
}

const reminderFilling = () => {
	ledgerReminder(ledger.value.id)
	ElMessage.success('提醒填报成功')
}

const onSearch = () => {
	const form: any = {}
	formSearch.value.forEach((f: any) => {
		form[f.field] = searchValue.value
	})

	getLedgerTableList(form)
}
// 临时增加统计页
const onClickStatistics = (type: any) => {
	if (route.query.ledgerId === '3a119be1-92e1-d313-2850-9e464a26954c') {
		router.push({
			path: '/ledger/specialStatistics',
			query: {
				name: ledger.value?.name,
				id: route.query.ledgerId,
			},
		})
	} else {
		router.push({
			path: '/ledger/statistics',
			query: {
				name: ledger.value?.name,
				id: route.query.ledgerId,
				type: type,
			},
		})
	}
}

const tsName = ref()
const tsGridStatistics = ref(false)
// 获取分页选择的数据
const result = ref<any>() //当前页面存储的数据
const handleSelectionChange = (val: any) => {
	allSeletePageData.value = [...allSeletePageData.value, ...val] // 点击的时候保存数据
	allSeletePageData.value = allSeletePageData.value.reduce((accumulator: any, current: any) => {
		const hasObject = accumulator.some((item: any) => item.Id == current.Id)
		if (!hasObject) {
			accumulator.push(current)
		}
		return accumulator
	}, [])
	result.value = tableData.value.filter((item1: any) => {
		return allSeletePageData.value.some((item2: any) => {
			return item2.Id == item1.Id
		})
	})
	const difference1 = result.value.filter(
		(item1: any) => !val.some((item2: any) => item2.Id == item1.Id)
	)
	difference1.forEach((item: any) => {
		let findindex = allSeletePageData.value.findIndex((f: any) => item.Id == f.Id)

		if (findindex != -1) {
			allSeletePageData.value.splice(findindex, 1)
		}
	})
}
// 点击分页和大小执行的
// 添加选中的数据并且勾选选择框
const allSeletePageData = ref<any>([])
const addSeletPageDataAndCheckbox = () => {
	if (allSeletePageData.value && allSeletePageData.value.length > 0) {
		allSeletePageData.value.forEach((item: any) => {
			tableRef.value.selected(item.Id, true)
		})
	}
}

const exportOpen = ref<any>(false) //是否打开导出数据的弹窗
const handClickExportBtn = ref<any>('')

const onClickDeclaration = () => {
	nextTick(() => {
		if (declarationResult.value) {
			if (handClickExportBtn.value == 'exportAll') {
				exportOpen.value = true
			} else if (handClickExportBtn.value == 'exportSelet') {
				if (allSeletePageData.value.length !== 0) {
					exportOpen.value = true
				} else {
					ElMessage.warning('请选择要导出的数据')
				}
			} else if (handClickExportBtn.value == 'exportRecord') {
			}
		}
	})
}

const exportTableData = (val: String) => {
	showDeclaration.value = true
	handClickExportBtn.value = val
}

// 子传父
const exportFileds = ref([])
const exportDes = ref('')
const getExportTableFiled = async (filed: any, des: any) => {
	// 如果是导出全部
	if (handClickExportBtn.value == 'exportAll') {
		if (filed.some((v: any) => v.type === 'images' || v.type === 'attachments')) {
			let attachmentType = 0
			let hasImages = false
			let hasAttachments = false
			filed.forEach((item: any) => {
				if (item.type === 'images') {
					hasImages = true
				}
				if (item.type === 'attachments') {
					hasAttachments = true
				}
			})
			if (hasImages && hasAttachments) {
				attachmentType = 0
			} else if (hasImages) {
				attachmentType = 2
			} else if (hasAttachments) {
				attachmentType = 1
			}
			const allSize = await getAllSize(route.query.ledgerId as string, attachmentType)

			if (allSize && allSize.data) {
				if (allSize.data > 1024) {
					return ElMessage.warning(
						'当前系统设定了文件导出限制，当文件大小超过 1024Mb（即 1GB）时，无法进行导出操作，建议你可分批次导出数据。'
					)
				}
			}
		}
	} else {
		// 如果是导出已选数据
		let allSize = 0
		filed.forEach((item: any) => {
			if (item.type === 'images' || item.type === 'attachments') {
				// const data = JSON.parse(allSeletePageData.value)
				allSeletePageData.value.forEach((item1: any) => {
					const size =
						item1[item.name] !== '[]' && item1[item.name] !== null
							? JSON.parse(item1[item.name])[0].size
							: 0

					allSize += size
				})
			}
		})
		if (allSize > 1024 * 1024 * 1024) {
			ElMessage.warning(
				'当前系统设定了文件导出限制，当文件大小超过 1024Mb（即 1GB）时，无法进行导出操作，建议你可分批次导出数据。'
			)
			return
		}
	}

	//传递出来的数据

	exportFileds.value = filed
	exportDes.value = des

	exportOpen.value = false
	// 去打开脱敏弹窗
	safeRelease(handClickExportBtn.value)
}
const input1Ref = ref<any>()
const input2Ref = ref<any>()
const input3Ref = ref<any>()
const input4Ref = ref<any>()
const handleInput = (index: any) => {
	if (index == 1) input2Ref.value.focus()
	else if (index == 2) input3Ref.value.focus()
	else if (index == 3) input4Ref.value.focus()
}

function onSortableChange(e: any) {
	if (e.order === null) {
		OrderFields.value = ''
	} else {
		OrderFields.value = `${e.prop} ${e.order === 'ascending' ? 'asc' : 'desc'}`
	}
	getLedgerTableList(searchFormRef.value.getForm())
}
enum StatusList {
	'已审核' = '0',
	'待提交' = '1',
	'待审核' = '2',
	'已删除' = '3',
	// '修改中' = '4',
	// '已驳回' = '5',
}
const filterStatus = ref<any[]>(['0', '1', '2', '4', '5'])
const searchFields = ref()
function handleFilter(val: any) {
	// 正则表达式匹配汉字
	const chineseCharRegex = /[\u4e00-\u9fa5]/g

	// 对数组中的每个字符串进行处理
	const filteredVal = val.map((str: string) => {
		// 使用正则表达式提取汉字
		const chineseChars = str.match(chineseCharRegex)
		// 如果匹配到汉字，则返回匹配结果，否则返回空字符串
		return chineseChars ? chineseChars.join('') : ''
	})

	// 处理后的结果
	console.log(filteredVal)
	let status: any = []
	if (filteredVal && Array.isArray(filteredVal)) {
		filteredVal.forEach((v) => {
			// @ts-ignore
			status.push(StatusList[v])
		})
	}
	filterStatus.value = status
	getLedgerTableList('', status)
}
const auditDataNumber = ref(0)
const tableAuditList = ref<any>([])

// 审核记录弹窗
const auditRecordModal = ref(false)
const auditRecordBatchModal = ref(false)
const auditedBatchId = ref('')
const openAuditRecord = () => {
	auditRecordBatchModal.value = true
}
const handleClickItem = (val: any) => {
	auditRecordModal.value = true
	auditedBatchId.value = val.id
}
const openAudit = () => {
	// 赋值数据

	tableAuditList.value = allSeletePageData.value //先用选中的数据进行测试
	//打开提交审核弹窗
	auditOpen.value = true
	historyOpen.value = false
}
// 关闭审核弹窗
const changeAuditVisible = (val: any) => {
	auditOpen.value = val
	// getLedgerTableList()
}
const uploadLedgenTbaleListData = (bool: any) => {
	if (bool) {
		pageation.value.currentPage = 1
		getLedgerTableList()
	}
}

// const imagePreviewModalIsVisible = ref(false)
const imageUrl = ref()
const download = async (data: any, field: any) => {
	// return
	if (data[field.field] === '' || data[field.field] === '[]') {
		return
	}
	let type = ''
	switch (JSON.parse(data[field.field])[0]?.extension) {
		case 'xlsx':
			type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
			break
		case 'xls':
			type = 'application/vnd.ms-excel'
	}
	// const getDownData = {
	// 	file: JSON.parse(data[field.field])[0],
	// }
	const getDownData = {
		ledgerId: route.query.ledgerId,
		dataId: data.Id,
		fieldName: field.field,
		fileName: JSON.parse(data[field.field])[0].name,
	}

	const res = await getDownLoadData(getDownData)
	if (res) {
		const blob = new Blob([res.data], {type})
		const downloadElement = document.createElement('a')
		const href = window.URL.createObjectURL(blob)
		//
		if (field.raw.type === 'images') {
			// imagePreviewModalIsVisible.value = true
			imageUrl.value = href
			return
		}
		downloadElement.href = href
		downloadElement.download = JSON.parse(data[field.field])[0].name
		document.body.appendChild(downloadElement)
		downloadElement.click()
		document.body.removeChild(downloadElement)
		window.URL.revokeObjectURL(href)
	}
}

const GetLockUserNameById = async (id: string) => {
	try {
		// const res = await GetUserInfoById(id)
		const res = await GetUserDepartmentById(id)
		const {user, departments} = res.data
		return `用户${user?.name}${departments?.map(
			(f: any) => '<p>' + f?.largeDepartment?.name + '-' + f?.name + '</p>'
		)}正在修改数据`
	} catch (error) {
		return `您无权查看此数据`
	}
}
// 数据分析
// 当前业务表有多少个数据分析
const dataAnalysisList: any = ref([])
// 查看业务表是否存在业务表数据分析详细
const getisExistLedgerDataAnalysis = () => {
	let ledgerId = route.query.ledgerId ? route.query.ledgerId : ''
	isExistLedgerDataAnalysis(ledgerId).then((res) => {
		const {data} = res
		let dataAnalysis = <any>[]
		data.forEach((item: any, index: any) => {
			if (item.onLineAnalysisConfig) {
				dataAnalysis.push(item)
			}
		})
		dataAnalysisList.value = dataAnalysis
		tabList.value.splice(1)
		dataAnalysis.forEach((item: any, index: any) => {
			tabList.value.push({
				name: item.onLineAnalysisConfig?.onLineAnalysisConfigName,
				isActive: false,
				index: index + 2,
			})
		})
	})
}
const fxdownload = async (data: any, field: any) => {
	// debugger
	// return
	if (data[field.bm] === '' || data[field.bm] === '[]') {
		return
	}
	let type = ''
	switch (JSON.parse(data[field.bm])[0]?.extension) {
		case 'xlsx':
			type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
			break
		case 'xls':
			type = 'application/vnd.ms-excel'
	}
	const getDownData = {
		ledgerId: route.query.ledgerId,
		dataId: data.Id,
		fieldName: field.field,
		fileName: JSON.parse(data[field.field])[0].name,
	}

	const res = await getDownLoadData(getDownData)
	if (res) {
		const blob = new Blob([res.data], {type})
		const downloadElement = document.createElement('a')
		const href = window.URL.createObjectURL(blob)
		//
		if (field.type === 'images') {
			// imagePreviewModalIsVisible.value = true
			imageUrl.value = href
			return
		}
		downloadElement.href = href
		downloadElement.download = JSON.parse(data[field.bm])[0].name
		document.body.appendChild(downloadElement)
		downloadElement.click()
		document.body.removeChild(downloadElement)
		window.URL.revokeObjectURL(href)
	}
}
const showScreenData = ref(false)
// 筛选数据回显需要
const ConditionalList: any = ref([])
// 创建分析回显数据
const groupByModelData = ref([])
// 分析名称
const onLineAnalysisConfigName = ref('')
const screenDataName = ref('')
const analysisDescription = ref('')
const selectByModelData: any = ref({
	showModelValue: [],
	analysisListData: [],
})
const groupByModel: any = ref([])
const selectByModel: any = ref([])
// 筛选数据或者创建分析是要的id
const screeningAndAnalysisId = ref('')
// 查询当前提交数据id
const ledgerDataAnalysisId = ref()

const tabList: any = ref([])
// 设置提交和数据筛选后的数据
const stagingForm: any = ref()
// 设置表格高度计算
const tableHeight = ref(0)
// const onBlockHeightChanged = (height: any) => {
// 	tableHeight.value = height - 130
// }
const onBlockHeightChanged = (height: any) => {
	if (Math.abs(height - tableHeight.value) > 1) {
		tableHeight.value = height - 130
	}
}
// 切换分页
const switchPaging = ref(1)
//控制是否处理返回按钮
const isBack = ref(false)
// 数据分析详情页的返回按钮
const backMethod = (val: any) => {
	changeTab(1)
}
const changeTab = (index: number) => {
	// departmentForm.value = {
	// 	currentPage: 1,
	// 	MaxResultCount: 10,
	// 	total: 0,
	// }
	switchPaging.value = index
	reqParams.value = {
		skipCount: 0,
		maxResultCount: 10,
	}
	pagination.value = {
		total: 0,
		page: 1,
		size: 10,
	}
	tabList.value.forEach((item: {name: string; isActive: boolean; index: number}) => {
		if (index === item.index) {
			item.isActive = true
		} else {
			item.isActive = false
		}
	})
	if (index != 1) {
		isBack.value = true
		stagingForm.value = dataAnalysisList.value[index - 2]
		ledgerDataAnalysisId.value = stagingForm.value.id
		setForm(stagingForm.value)
		if (stagingForm.value.onLineAnalysisConfig.onLineAnalysisConfigName) {
			setshowModelValue(stagingForm.value)
		}
		let timerId = setInterval(function () {
			if (tableColumns.value.length) {
				getdataAnalysis()
				clearInterval(timerId)
			}
		}, 5)
	} else {
		isBack.value = false
		ledgerDataAnalysisId.value = ''
		stagingForm.value = []
	}
}
const deleteAnalysis = () => {
	let data: any = [ledgerDataAnalysisId.value]
	ElMessageBox.confirm(`是否要删除数据分析?`, '消息确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			ledgerDataAnalysisBatchDelete(data).then(() => {
				ElMessage.success('删除成功！')
				changeTab(1)
				getisExistLedgerDataAnalysis()
			})
		})
		.catch(() => {})
}
// 通过id获取选中业务表的表头
const setLedgerData = async (ledgerId: any) => {
	try {
		const res = await getDetailByLedgerId(ledgerId)
		columnsList.value = res.data.tableInfo.fields
		tableColumns.value = []
		TilingTableData.value = []
		res.data.tableInfo.fields.forEach((item, index) => {
			item.bm = item.name
			tableColumns.value.push({
				prop: item.bm,
				label: item.displayName,
				type: item.type,
			})
			if (item.type == 'datetime' || item.type === 'images' || item.type === 'attachments') {
				TilingTableData.value.push(item)
			}
		})
	} catch (err: any) {
		if (err.response?.status === 500) {
			ElNotification.error('数据正在更新，请5分钟后再试')
		}
	}
}
// 创建分析后的表头
const setshowModelValue = (form: any) => {
	tableColumns.value = []
	TilingTableData.value = []
	if (form.tableInfoId && selectByModel.value.length == 0) {
		setLedgerData(form.tableInfoId)
	}
	groupByModelData.value.forEach((item: any) => {
		tableColumns.value.push({
			prop: item.raw.bm,
			label: item.raw.displayName,
			type: item.raw.type,
		})
		if (
			item.raw.type == 'datetime' ||
			item.raw.type === 'images' ||
			item.raw.type === 'attachments'
		) {
			TilingTableData.value.push(item.raw)
		}
	})
	// 统计数据量
	if (selectByModelData.value?.analysisListData.length) {
		tableColumns.value.push({
			prop: `Count`,
			label: `统计数据量`,
			type: `Count`,
		})
	}
	columnsList.value.forEach((item) => {
		selectByModelData.value?.showModelValue.forEach((items) => {
			if (items[0] == item.name) {
				if (items[1] == 'AggregateCount') {
					// tableColumns.value.push({
					// 	prop:`Count`,
					// 	label:`统计数据量`,
					// 	type: `Count`
					// })
				} else if (items[1] == 'AggregateAvg') {
					tableColumns.value.push({
						prop: `${items[0]}Avg`,
						label: `"${item.displayName}" 平均值`,
						type: `${items[0]}Count`,
					})
				} else if (items[1] == 'AggregateSum') {
					tableColumns.value.push({
						prop: `${items[0]}Sum`,
						label: `"${item.displayName}" 总和`,
						type: `${items[0]}Count`,
					})
				} else if (items[1] == 'AggregateMax') {
					tableColumns.value.push({
						prop: `${items[0]}Max`,
						label: `"${item.displayName}" 最大值`,
						type: `${items[0]}Count`,
					})
				} else if (items[1] == 'AggregateMin') {
					tableColumns.value.push({
						prop: `${items[0]}Min`,
						label: `"${item.displayName}" 最小值`,
						type: `${items[0]}Count`,
					})
				}
			}
		})
	})
}
// 查看业务表数据分析最终分页数据
const getdataAnalysis = () => {
	dataAnalysis(ledgerDataAnalysisId.value, {
		skipCount: (pagination.value.page - 1) * pagination.value.size,
		MaxResultCount: pagination.value.size,
	})
		.then((ress) => {
			let data = ress.data
			pagination.value.total = data.totalCount
			defaultTableData.value = []
			if (data.items.length > 0) {
				data.items.forEach((item: any) => {
					defaultTableData.value.push(item)
				})
			}
		})
		.catch((err: any) => {
			if (err.response?.status === 500) {
				ElNotification.error('数据正在更新，请5分钟后再试')
			}
		})
}
// 分析数据提交
const setFreateAnalysis = (data: any) => {
	let form: any = {
		name: data.name,
		analysisDescription: data.analysisDescription,
		analysisType: 0,
		chartType: 0,
		associatedconfigs: [],
		tableInfoId: route.query.ledgerId,
	}
	if (ledgerDataAnalysisId.value) {
		if (
			data.name !== screenDataName.value ||
			data.analysisDescription != analysisDescription.value
		) {
			form.ledgerDataAnalysisId = ledgerDataAnalysisId.value
			putledgerDataAnalysis(form).then((res) => {
				dataAnalysisList.value[switchPaging.value - 2].name = data.name
				dataAnalysisList.value[switchPaging.value - 2].analysisDescription =
					data.analysisDescription
			})
		}
		lineAnalysisConfig(data)
	} else {
		form.ledgerDataAnalysisId = getUuid()
		postledgerDataAnalysis(form).then((res) => {
			let query = res.data
			ledgerDataAnalysisId.value = query.id
			lineAnalysisConfig(data)
		})
	}
}
const filterData = ref([])
// 创建分析和编辑分析
const lineAnalysisConfig = (data: any) => {
	let firmData = {
		id: screeningAndAnalysisId.value ? screeningAndAnalysisId.value : getUuid(),
		ledgerDataAnalysisId: ledgerDataAnalysisId.value,
		conditionalByModel: data.conditionalByModel,
		filterConditionalByModel: data.filterCriteria,
		OnLineAnalysisConfigName: data.OnLineAnalysisConfigName,
		groupByModel: data.groupByModel,
		selectByModel: data.selectByModel,
		// 回显需要的数据暂时没有完成
		filterData: JSON.stringify(data.filterData),
		groupByModelData: JSON.stringify(data.groupByModelData),
		selectByModelData: JSON.stringify(data.selectByModelData),
	}
	if (screeningAndAnalysisId.value) {
		putOnLineAnalysisConfig(firmData).then((res) => {
			let data = res.data
			dataAnalysisList.value[switchPaging.value - 2] = data
			tabList.value[switchPaging.value - 1].name =
				data.onLineAnalysisConfig.onLineAnalysisConfigName
			stagingForm.value = res.data
			setForm(res.data)
			changeTab(switchPaging.value)
		})
	} else {
		addOnLineAnalysisConfig(firmData).then((res) => {
			let data = res.data
			tabList.value.push({
				name: data.onLineAnalysisConfig.onLineAnalysisConfigName,
				isActive: false,
				index: tabList.value.length + 1,
			})
			stagingForm.value = res.data
			dataAnalysisList.value.push(res.data)
			setForm(res.data)
			changeTab(tabList.value.length)
		})
	}
}
// 设置提取数据回显数据
const setForm = (form: any) => {
	if (form.onLineAnalysisConfig != null) {
		screenDataName.value = form.name
		analysisDescription.value = form.analysisDescription
		// 筛选数据或者创建分析是要的id
		if (form.onLineAnalysisConfig.id)
			screeningAndAnalysisId.value = form.onLineAnalysisConfig.id
		// 筛选数据回显
		if (form.onLineAnalysisConfig.conditionalByModel.length > 0) {
			ConditionalList.value = form.onLineAnalysisConfig.conditionalByModel[0].ConditionalList
		}
		if (form.onLineAnalysisConfig.filterData) {
			filterData.value = JSON.parse(form.onLineAnalysisConfig.filterData)
		}
		if (form.onLineAnalysisConfig.groupByModelData) {
			groupByModelData.value = JSON.parse(form.onLineAnalysisConfig.groupByModelData)
		}
		if (form.onLineAnalysisConfig.selectByModelData) {
			selectByModelData.value = JSON.parse(form.onLineAnalysisConfig.selectByModelData)
		}
		if (form.onLineAnalysisConfig.onLineAnalysisConfigName) {
			onLineAnalysisConfigName.value = form.onLineAnalysisConfig.onLineAnalysisConfigName
		}

		if (form.onLineAnalysisConfig.groupByModel != null) {
			groupByModel.value = form.onLineAnalysisConfig.groupByModel
		}
		if (form.onLineAnalysisConfig.selectByModel != null) {
			selectByModel.value = form.onLineAnalysisConfig.selectByModel
		}
	}
}
// 表中的内容
const defaultTableData = ref([])
// 表头
const tableColumns: any = ref([])
// 筛选表头
const columnsList: any = ref([])
//表格与分页关联
const reqParams = ref({
	skipCount: 0,
	maxResultCount: 10,
})
// 分页相关参数
const pagination = ref({
	total: 0,
	page: 1,
	size: 10,
})
// 分页事件
const onPaginationChange = (val: any, type: any) => {
	if (type === 'page') {
		pagination.value.page = val
		reqParams.value.skipCount = (val - 1) * pagination.value.size
	} else {
		pagination.value.page = 1
		pagination.value.size = val
		reqParams.value.maxResultCount = pagination.value.size
	}
	getdataAnalysis()
}
const onTableBeforeComplete = ({items, next}: any) => {
	next(items)
}
// 点击数据分析
const showDataAnalysis = () => {
	if (switchPaging.value == 1) {
		groupByModelData.value = []
		selectByModelData.value = {
			showModelValue: [],
			analysisListData: [],
		}
		onLineAnalysisConfigName.value = ''
		screenDataName.value = ''
		analysisDescription.value = ''
		screeningAndAnalysisId.value = ''
	} else {
		setForm(dataAnalysisList.value[switchPaging.value - 2])
	}
	showScreenData.value = true
}
const getUuid = () => {
	let s: any = []
	let hexDigits = '0123456789abcdef'
	for (let i = 0; i < 36; i++) {
		s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
	}
	s[14] = '4' // bits 12-15 of the time_hi_and_version field to 0010
	s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01
	s[8] = s[13] = s[18] = s[23] = '-'
	let uuid = s.join('')
	return uuid
}

function parseQuarterToDate(quarterStr: any) {
	if (quarterStr === null) return null
	// 假设输入格式为 "YYYY年一/二/三/四季度"，其中YYYY是年份
	const match = quarterStr.match(/^(\d{4})年第(一|二|三|四)季度$/)
	if (!match) {
		throw new Error('无效的季度字符串格式')
	}

	const year = parseInt(match[1], 10)
	const quarterChinese = match[2] // 获取汉字季度

	// 根据汉字季度确定月份
	let month
	switch (quarterChinese) {
		case '一':
			month = 1
			break
		case '二':
			month = 4
			break
		case '三':
			month = 7
			break
		case '四':
			month = 10
			break
		default:
			// 理论上不会执行到这里，因为正则已经确保了quarterChinese是有效的汉字季度
			throw new Error('内部错误：无效的季度值')
	}

	// 创建一个新的Date对象，设置为该季度的第一个月的第一天（即1号）
	const date = new Date(year, month - 1, 1, 0, 0, 0) // 月份在Date对象中是从0开始的

	// 格式化日期时间字符串
	const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
		2,
		'0'
	)}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(
		2,
		'0'
	)}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`

	return formattedDate
}
const onCancelCodeOpen = () => {
	const cancelCode = document.querySelector('.cancel-code')
	if (cancelCode) {
		const inputs = cancelCode.querySelectorAll('input')
		setTimeout(() => inputs[0].focus(), 0)
	}
}

const viewStatistics = ref(false)
const statistics = ref(false)
const getTableScroll = () => {
	const scroll = tableRef.value.onscroll()
	localStorage.setItem('tableScroll', JSON.stringify(scroll))
}
// 业务表填报进度
const fillProgressModal = ref(false)
const openProgressModal = () => {
	// fillProgressModal.value = true
	// 20250305 跳转详情
	router.push({
		path: '/ledger/reportProgressDetail',
		query: {
			ledgerId: route.query.ledgerId,
			fillStatisticsType: 0,
			name: ledger.value.name,
		},
	})
}

const onCellDblClick = (r, c, s, f) => {}

const getQuarterString = (dateStr) => {
	const date = dayjs(dateStr)
	const year = date.year()
	const month = date.month() + 1 // Day.js的month()方法返回的月份是从0开始的

	let quarter
	if (month >= 1 && month <= 3) {
		quarter = '第一季度'
	} else if (month >= 4 && month <= 6) {
		quarter = '第二季度'
	} else if (month >= 7 && month <= 9) {
		quarter = '第三季度'
	} else {
		quarter = '第四季度'
	}

	return `${year}${quarter}`
}

const onSuccessData = (val: any) => {
	if (val) {
		getLedgerTableList(toRaw(searchFormRef.value.getForm()))
		if (submitAuditRef.value) {
			submitAuditRef.value?.$reloadCount(ledger.value.id)
		}
	}
}

const onApplyNewDataAll = (val: any) => {
	if (val) {
		getLedgerTableList(toRaw(searchFormRef.value.getForm()))
		if (submitAuditRef.value) {
			submitAuditRef.value?.$reloadCount(ledger.value.id)
		}
	}
}

const onDateTimesCount = () => {
	isDataComparisonOne.value = false
}

const onDateTimesCancel = () => {
	isDataComparisonOne.value = false
}

const onDateAllCancel = () => {
	isDataComparisonAll.value = false
}
const onDateAllCount = () => {
	isDataComparisonAll.value = false
}

const onOpenAllDatas = () => {
	dataComparisonRecordbenchmarkInfo(route.query.ledgerId, {whereFields: {}}).then(
		(fullData: any) => {
			dataComparisonRecordList({
				ledgerId: route.query.ledgerId,
				skipCount: (1 - 1) * 10,
				MaxResultCount: 10,
			}).then((res: any) => {
				currentRowComparisonAll.value = {...fullData.data, list: {...res.data}}
				isDataComparisonAll.value = true
			})
		}
	)
}

// 20250408修改 所见即所得功能

// 已核实无更新
const canNoDataAudit = ref(false)
const openType = ref('')
const isNoDataAudit = async (ledgerId: string) => {
	const res = await CanNoDataAudit(ledgerId)
	if (res) {
		canNoDataAudit.value = res.data
	}
}

const submitModalVisible = ref(false)
// 提交审核弹窗打开 提交全部：all 已核实无更新：none 提交已选：select
const openSubmit = (type: 'all' | 'none' | 'select') => {
	openType.value = type

	if (
		allSeletePageData.value.some(
			(v: any) =>
				v.DataStatus === 0 || v.DataStatus === 2 || v.DataStatus === 3 || v.DataStatus === 4
		)
	) {
		ElMessageBox.confirm(
			'仅可提交‘待提交’，‘已驳回’，状态数据，是否确认提交，点击确认后，将只会提交符合状态的数据',
			'提示',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}
		).then(() => {
			submitModalVisible.value = true
		})
	} else {
		submitModalVisible.value = true
	}
}
// 提交审核弹窗关闭
const changeSubmitAuditVisible = () => {
	submitModalVisible.value = false
	tableRef.value?.clearSelection()
}

// 修改结束

onMounted(() => {
	console.log(localStorage.getItem('urlCode'))
	if (localStorage.getItem('urlCode')) {
		deleteLabel({path: localStorage.getItem('urlCode')})
	}
	localStorage.removeItem('urlCode')
	// if(route.query.routerPath) return deleteLabel({path:})
})
onActivated(() => {
	changeTab(1)
	const {account} = userStore.getUserInfo
	const currentDepartmentId = JSON.parse(
		localStorage.getItem('currentDepartmentInfo') as string
	).id
	isNoDataAudit(route.query.ledgerId as string)
	tsName.value = fill.tsName(account, route.query.ledgerId)
	// 市委工作部 专职网格员查阅统计
	tsGridStatistics.value = fill.tsGridStatistics(account, route.query.ledgerId as string)
	if (
		route.query.ledgerId == '3a0e63e0-0a47-5ed4-acee-bc5efb51fb55' ||
		route.query.ledgerId == '3a0e676f-1e8c-5281-0504-d353c2fd07b9' ||
		route.query.ledgerId == '3a0f8cae-05da-3e72-5c0c-40c7321b3b73'
	) {
		if (
			account == 'ykz-***********' ||
			account == 'ykz-***********' ||
			account == 'ykz-***********'
		) {
			viewStatistics.value = true
		}
	}
	if (route.query.ledgerId == '3a0e676f-1e8c-5281-0504-d353c2fd07b9') {
		if (
			account == 'ykz-***********' ||
			account == 'ykz-***********' ||
			account == 'ykz-***********' ||
			account == 'ykz-zhouyuan1' ||
			account == 'ykz-sunbenlong'
		) {
			statistics.value = true
		}
	}
	// 村庄清洁行动业务表 // 五清理一活动
	if (
		route.query.ledgerId == '3a120771-05c9-b0cf-ec27-5c699ac4382c' ||
		route.query.ledgerId == '3a1207a5-ac12-7dfe-e2fb-eb9f9f183e6b' ||
		route.query.ledgerId == '3a12079b-6f90-e652-90ad-9e0be852152b'
	) {
		if (
			account == 'ykz-***********' ||
			account == 'ykz-***********' ||
			account == 'ykz-***********' ||
			account == 'ykz-***********' ||
			account == 'ykz-***********' ||
			account == 'ykz-***********' ||
			currentDepartmentId == '3a1378e4-b8de-8363-4d6a-ecee9b15e989'
		) {
			statistics.value = true
		}
	}

	// 司法局安置帮教对象花名册业务表
	if (route.query.ledgerId == '3a119be1-92e1-d313-2850-9e464a26954c') {
		statistics.value = true
	}
	// if (route.query.ledgerId == '3a1283a6-1890-4682-79d8-8d39f4cbcf2b') {
	// }

	initTable(() => {
		if (tableRef.value) {
			const tableScrollData = JSON.parse(localStorage.getItem('tableScroll') as string)
			if (tableScrollData !== null)
				tableRef.value.setScroll(tableScrollData.top, tableScrollData.left)
			// tableRef.value.setScrollLeft(eft)
		}
		if (route.query.exportRecord != undefined) {
			historyExport.value = true
		}
		startUpWorker(true, true)
		if (localStorage.getItem('isTableImport')) {
			historyOpen.value = true
			localStorage.removeItem('isTableImport')
		}

		updateLabelTitle({
			path: router.currentRoute.value.fullPath,
			title: `查看-我的业务表-${ledger.value.name}`,
		})
	})
})
onDeactivated(() => {
	noPermission = false
	localStorage.removeItem('currentLedgerUserPermissions')
	formArray.value.length = 0
	allSeletePageData.value = []
	cleanWorker()
	if (workerTimer.value) {
		clearTimeout(workerTimer.value)
	}
})

onUnmounted(() => {})
const hasExportRole = computed(() =>
	JSON.parse(localStorage.getItem('currentUserInfo') as string).staffRole.includes('数据导出')
)
watch(
	() => allSeletePageData.value,
	() => {}
)
const onBusinessTableImport = () => {
	router.push({
		path: '/ledger/businessTableImport',
		query: {
			ledgerId: associatedSyns.value[0].ledgerId,
			sourceLedgerId: associatedSyns.value[0].sourceLedgerId,
			name: associatedSyns.value[0].sourceLedger.name,
			ledgerName: ledger.value.name,
		},
	})
}
const isCanAssociatedSyn = ref(false)
</script>
<template>
	<div class="layout" style="height: 100%">
		<Block
			ref="blockRef"
			:enable-expand-content="switchPaging == 1"
			:enable-fixed-height="true"
			:enableExpand="false"
			:expand-content="false"
			:isBack="isBack"
			@backMethod="backMethod"
			@heightChanged="onBlockHeightChanged"
			@content-expand="expendSearch"
		>
			<template #title>
				<!-- <div style="display: flex;align-items: center;" v-if="switchPaging == 1">
					<el-icon mr-5px>
						<Document />
					</el-icon>
					<el-tooltip effect="dark" :content="ledger?.name" placement="top">
						<span class="ellipsis">
							{{ ledger?.name }}
						</span>
					</el-tooltip>
					的详情
					<small ml-10px>(</small>
					<small
						:title="`${ledger?.runway ?? ledger?.ledgerType?.runway}-${ledger?.ledgerType?.name}`"
						style="font-weight: 400"
						class="ellipsis"
					>
						{{ ledger?.runway ?? ledger?.ledgerType?.runway }} - {{ ledger?.ledgerType?.name }}
					</small>
					<small>)</small>
				</div> -->
				<div v-for="(item, index) in tabList" :key="index">
					<div v-if="item.isActive">
						<el-tooltip effect="dark" :content="item.name" placement="bottom-start">
							<div
								class="ellipsis"
								:style="{'max-width': tabList.length > 1 ? '200px' : '400px'}"
							>
								<!-- <el-icon mr-5px>
										<Document />
									</el-icon> -->
								{{ item.name }}
							</div>
						</el-tooltip>
					</div>
				</div>
			</template>

			<template #topRight>
				<el-button
					type="danger"
					size="small"
					v-if="
						allSeletePageData.length > 0 &&
						isPermission(Permissions.Delete) &&
						switchPaging == 1
					"
					@click="batchDelete"
					class="mg-left-10"
				>
					删除已选
				</el-button>
				<!-- usePermissions('Ledger.LedgerData.Export') -->
				<el-button
					v-if="
						allSeletePageData.length > 0 &&
						switchPaging == 1 &&
						!iscommunity &&
						hasExportRole
					"
					@click="exportTableData('exportSelet')"
					type="primary"
					size="small"
				>
					导出已选
				</el-button>

				<el-button
					type="primary"
					size="small"
					v-if="isPermission(Permissions.Add) && switchPaging == 1 && !iscommunity"
					@click="onOpenAdd"
					class="mg-left-10"
				>
					<!-- <i class="icon i-ic-sharp-lock-open"></i>  		-->
					新增数据
				</el-button>
				<!-- <el-button
					v-if="allSeletePageData.length > 0 && switchPaging == 1"
					type="primary"
					size="small"
					@click="openSubmit('select')"
				>
					提交已选
				</el-button> -->

				<!-- 
				<el-button type="primary" size="small" @click="openSubmit('all')">
					提交全部
				</el-button> -->
				<!-- <el-button
					type="primary"
					size="small"
					v-if="canNoDataAudit"
					@click="openSubmit('none')"
				>
					已核实无更新
				</el-button> -->

				<el-button
					type="primary"
					size="small"
					@click="importOpen = true"
					v-if="switchPaging == 1 && isPermission(Permissions.Add) && !iscommunity"
				>
					导入数据
				</el-button>
				<!-- v-if="
						(isPermission(Permissions.Add) ||
							isPermission(Permissions.Edit) ||
							isPermission(Permissions.Delete)) &&
						switchPaging == 1 &&
						!iscommunity
					" -->
				<!-- 提交审核 -->
				<el-button
					type="primary"
					size="small"
					@click="onBusinessTableImport"
					v-if="switchPaging == 1 && isCanAssociatedSyn"
				>
					业务表导入
				</el-button>
				<el-button-group class="mg-left-10">
					<el-button
						type="primary"
						size="small"
						v-if="
							(isPermission(Permissions.Add) ||
								isPermission(Permissions.Edit) ||
								isPermission(Permissions.Delete)) &&
							switchPaging == 1 &&
							!iscommunity
						"
						@click="openAudit"
						style="position: relative"
					>
						提交审核
						<el-badge
							v-if="auditDataNumber > 0"
							:max="99"
							:value="auditDataNumber"
							style="
								position: absolute;
								top: -10px;
								right: -10px;
								transform: scale(0.8);
								z-index: 999;
							"
						>
						</el-badge>
					</el-button>
				</el-button-group>

				<el-dropdown trigger="click" v-if="tabList.length > 1" class="mg-left-10">
					<el-button type="primary" size="small">查看分析</el-button>
					<template #dropdown>
						<el-dropdown-menu>
							<div v-for="(item, index) in tabList" :key="index">
								<el-dropdown-item
									v-if="index > 0"
									@click="changeTab(item.index)"
									:style="
										item.isActive
											? 'color: rgb(23,100,206); background-color: rgba(23,97,230,0.2);'
											: ''
									"
								>
									<el-tooltip
										effect="dark"
										:content="item.name"
										placement="top-start"
									>
										<div class="ellipsis">
											{{ item.name }}
										</div>
									</el-tooltip>
								</el-dropdown-item>
							</div>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
				<el-button
					v-if="role && switchPaging !== 1"
					size="small"
					type="primary"
					@click="showDataAnalysis"
					style="margin-left: 10px"
				>
					编辑分析
				</el-button>
				<el-dropdown trigger="click" style="margin-left: 10px" v-else>
					<el-button type="primary" size="small">
						更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
					</el-button>
					<template #dropdown>
						<div class="more-operations">
							<div class="mo-box">
								<span>查看</span>
								<!-- (isPermission(Permissions.Add) ||
											isPermission(Permissions.Delete) ||
											isPermission(Permissions.Edit)) -->
								<!-- 20250306 20:44 只判断有周期性的 -->
								<!-- isPermission(Permissions.View) && -->
								<el-button
									v-if="route.query.interval !== '0' && switchPaging == 1"
									size="small"
									type="primary"
									@click="openProgressModal"
								>
									填报进度
								</el-button>

								<el-button
									v-if="
										(tsName && switchPaging == 1) ||
										(statistics && switchPaging == 1) ||
										(tsGridStatistics && switchPaging == 1 && !iscommunity)
									"
									@click="onClickStatistics('')"
									type="primary"
									size="small"
								>
									查阅统计
								</el-button>

								<!-- <el-button
									v-if="role && ledgerDataAnalysisId && !iscommunity"
									size="small"
									type="primary"
									@click="deleteAnalysis"
								>
									删除分析
								</el-button> -->

								<el-button
									v-if="viewStatistics && switchPaging == 1 && !iscommunity"
									@click="onClickStatistics('evaluate')"
									type="primary"
									size="small"
								>
									综合评价
								</el-button>

								<el-button
									type="danger"
									size="small"
									@click="safeRelease('')"
									v-if="switchPaging == 1"
									class="mg-left-10"
								>
									解除列表脱敏
								</el-button>
							</div>

							<div class="mo-box">
								<span>操作</span>

								<el-button
									v-if="role"
									size="small"
									type="primary"
									@click="showDataAnalysis"
								>
									{{ switchPaging == 1 ? '新建分析' : '编辑分析' }}
								</el-button>

								<el-button
									v-if="isCanComparison"
									type="primary"
									size="small"
									@click="onOpenAllDatas"
								>
									数据对比
								</el-button>
								<!-- usePermissions('Ledger.LedgerData.Export') -->
								<el-button
									v-if="switchPaging == 1 && !iscommunity && hasExportRole"
									@click="exportTableData('exportAll')"
									type="primary"
									size="small"
								>
									导出全部
								</el-button>

								<el-button
									v-if="
										isPermission(Permissions.Delete) &&
										pageation.total !== 0 &&
										switchPaging == 1 &&
										!iscommunity
									"
									@click="deleteAll"
									type="danger"
									size="small"
								>
									删除全部
								</el-button>
							</div>

							<div class="mo-box">
								<span>记录</span>

								<el-button
									v-if="switchPaging == 1 && !iscommunity"
									@click="historyOpen = true"
									type="primary"
									size="small"
								>
									导入记录
								</el-button>

								<el-button
									v-if="switchPaging == 1 && !iscommunity"
									@click="openAuditRecord"
									type="primary"
									size="small"
								>
									提交记录
								</el-button>

								<el-button
									v-if="switchPaging == 1 && !iscommunity"
									@click="historyExport = true"
									type="primary"
									size="small"
								>
									导出记录
								</el-button>
							</div>
						</div>
					</template>
				</el-dropdown>
			</template>

			<template #expand>
				<FormComp
					ref="searchFormRef"
					v-action:enter="formSubmit"
					inline
					label-width="80"
					label-position="right"
					submitText="查询"
					:form="formSearchClone"
					:showButton="true"
					:showResetButton="false"
					:loading="loading"
					itemWidth="66%"
					:conditionList="conditionList"
					@onChange="formChange"
					@onSubmit="formSubmit"
					@onClean="formClean"
					@enter="formSubmit"
					class="search-form"
					style="padding: 10px 0 0 0; width: calc(100% - 32px)"
				>
					<template #condition> </template>

					<template #_cascade="scope">
						<div style="width: 100%">
							<Cascade
								:options="cascadeOptions"
								:keys="['name', 'name']"
								:is-clear="isClearSearch"
								@change="formChange($event, scope.item.field)"
								@focus="searchFormRef.focus()"
								@blur="searchFormRef.blur()"
							></Cascade>
						</div>
					</template>
				</FormComp>
			</template>

			<div class="description" v-if="switchPaging == 1">
				<div class="left">
					<el-icon>
						<WarningFilled />
					</el-icon>
					<span>填报说明:</span>
				</div>
				<div class="right" id="updateDescription">
					<el-tooltip placement="bottom-start" :show-after="200">
						<template #content>
							<div style="max-width: 600px">
								<pre
									style="
										width: 100%;
										white-space: pre-wrap;
										word-wrap: break-word;
									"
									>{{ ledger?.updateDescription || '无' }}</pre
								>
							</div>
						</template>
						{{ ledger?.updateDescription || '无' }}
					</el-tooltip>

					<a
						href="javascript:;"
						class="more"
						v-if="showMoreButtonVisible"
						@click="viewMoreInfoModalIsVisible = true"
						>更多</a
					>
				</div>
			</div>

			<template v-if="switchPaging == 1">
				<BaseTableComp
					class="tableComp"
					row-key="Id"
					ref="tableRef"
					:offsetHeight="tableOffsetHeight"
					:colData="colData"
					:checkbox="true"
					:data="tableData"
					:buttons="buttons"
					:visibleSetting="false"
					:hideHeader="true"
					:currentPage="pageation.currentPage"
					:pageSize="pageation.pageSize"
					:pageSizeArray="pageation.pageSizeArray"
					:total="pageation.total"
					:loading="loading"
					@clickButton="onTableClickButton"
					@size-change="onPageationChange($event, 'size')"
					@current-change="onPageationChange($event, 'current')"
					@selection-change="handleSelectionChange"
					@sortableChange="onSortableChange"
					@handle-filter="handleFilter"
					@cell-click="onCellDblClick"
				>
					<template #[filed.field]="{rowData}" v-for="filed in TilingColData">
						<!-- <template v-if="filed.raw?.type === 'datetime' && filed.raw?.displayForm">
							<p v-if="filed.raw?.displayForm === 1">
								{{ dayjs(rowData[filed.field]).format('YYYY年MM月DD日') }}
							</p>
							<p v-if="filed.raw?.displayForm === 2">
								{{ dayjs(rowData[filed.field]).format('YYYY年MM月') }}
							</p>
							<p v-if="filed.raw?.displayForm === 3">
								{{ dayjs(rowData[filed.field]).format('YYYY年') }}
							</p>
							<p v-if="filed.raw?.displayForm === 4">
								{{ getQuarterString(rowData[filed.field]) }}
							</p>
						</template> -->
						<template
							v-if="filed.raw?.type === 'images' || filed.raw?.type === 'attachments'"
						>
							<el-link @click="download(rowData, filed)">
								{{
									rowData[filed.field] === '[]' ||
									rowData[filed.field] === '' ||
									rowData[filed.field] === undefined ||
									rowData[filed.field] === null
										? '-'
										: JSON.parse(rowData[filed.field])[0].name
								}}
							</el-link>
						</template>
						<template v-else>
							<!-- {{ filed.field }} -->
							<template v-if="filed.field === 'DataStatus'">
								<div style="display: flex; align-items: center">
									<el-tooltip
										v-if="
											rowData.LockUserId &&
											rowData.LockUserId !== useUserStore().getUserInfo.id
										"
										effect="dark"
										placement="top"
										:raw-content="true"
										:content="rowData._lockuser"
									>
										<el-icon class="mg-right-5">
											<WarningFilled />
										</el-icon>
									</el-tooltip>

									<span v-if="rowData.DataStatus == 0" style="color: #2185c5"
										>已审核</span
									>
									<span
										v-else-if="rowData.DataStatus == 1"
										:class="[
											rowData.LockUserId == null
												? 'color-ex'
												: rowData.LockUserId ==
												  useUserStore().getUserInfo.id
												? 'color-ex'
												: 'color-locked',
										]"
										>{{
											rowData.LockUserId == null
												? '待提交'
												: rowData.LockUserId ==
												  useUserStore().getUserInfo.id
												? '待提交'
												: '已锁定'
										}}</span
									>
									<span
										v-else-if="rowData.DataStatus == 2"
										:class="[
											rowData.LockUserId == null
												? 'color-ex'
												: rowData.LockUserId ==
												  useUserStore().getUserInfo.id
												? 'color-ex'
												: 'color-locked',
										]"
										>{{
											rowData.LockUserId == null
												? '待审核'
												: rowData.LockUserId ==
												  useUserStore().getUserInfo.id
												? '待审核'
												: '已锁定'
										}}</span
									>
									<span
										v-else-if="rowData.DataStatus == 3"
										:class="[
											rowData.LockUserId == null
												? 'color-del'
												: rowData.LockUserId ==
												  useUserStore().getUserInfo.id
												? 'color-del'
												: 'color-locked',
										]"
										>{{
											rowData.LockUserId == null
												? '已删除'
												: rowData.LockUserId ==
												  useUserStore().getUserInfo.id
												? '已删除'
												: '已锁定'
										}}</span
									>
									<span
										v-else-if="rowData.DataStatus == 4"
										:class="[
											rowData.LockUserId == null
												? 'color-lock'
												: rowData.LockUserId ==
												  useUserStore().getUserInfo.id
												? 'color-lock'
												: 'color-locked',
										]"
									>
										修改中
									</span>
									<span
										v-else-if="rowData.DataStatus == 5"
										:class="[
											rowData.LockUserId == null
												? 'color-bohui'
												: rowData.LockUserId ==
												  useUserStore().getUserInfo.id
												? 'color-bohui'
												: 'color-locked',
										]"
										>{{
											rowData.LockUserId == null
												? '已驳回'
												: rowData.LockUserId ==
												  useUserStore().getUserInfo.id
												? '已驳回'
												: '已锁定'
										}}</span
									>
									<span v-else style="color: #7ecefd">已锁定</span>
								</div>
							</template>
							<template v-else>
								{{ rowData[filed.field] ?? '-' }}
							</template>
						</template>
					</template>
				</BaseTableComp>
			</template>
			<template v-else>
				<TableV2
					:height="tableHeight"
					:columns="tableColumns"
					:defaultTableData="defaultTableData"
					:headers="{Urlkey: 'ledger'}"
					:offset-height="-125"
					:enableToolbar="false"
					:enable-create="false"
					:enable-edit="false"
					:enable-delete="false"
					:disabled="true"
					@before-complete="onTableBeforeComplete"
					:req-params="reqParams"
				>
					<template #[filed.bm]="{row}" v-for="filed in TilingTableData">
						<template v-if="filed.type === 'datetime'">
							<p v-if="filed.displayForm === 1">
								{{ dayjs(row[filed.bm]).format('YYYY年MM月DD日') }}
							</p>
							<p v-if="filed.displayForm === 2">
								{{ dayjs(row[filed.bm]).format('YYYY年MM月') }}
							</p>
							<p v-if="filed.displayForm === 3">
								{{ dayjs(row[filed.bm]).format('YYYY年') }}
							</p>
							<p v-if="filed.displayForm === 4">
								{{ getQuarterString(row[filed.bm]) }}
							</p>
						</template>
						<template
							v-else-if="filed.type === 'images' || filed.type === 'attachments'"
						>
							<el-link @click="fxdownload(row, filed)">
								{{
									row[filed.bm] === '[]' ||
									row[filed.bm] === '' ||
									row[filed.bm] === undefined ||
									row[filed.bm] === null
										? '-'
										: JSON.parse(row[filed.bm])[0].name
								}}
							</el-link>
						</template>
					</template>
				</TableV2>
				<Pagination
					:total="pagination.total"
					:current-page="pagination.page"
					:page-size="pagination.size"
					@current-change="onPaginationChange($event, 'page')"
					@size-change="onPaginationChange($event, 'size')"
				>
				</Pagination>
			</template>
		</Block>

		<ValidateCode
			v-model="cancelOpen"
			:title="codeTitle"
			:loading="loading"
			@send="handelSendCode"
			@confirm="verifyCode"
		></ValidateCode>

		<Dialog
			v-model="addOpen"
			:title="formTitle"
			:enableButton="false"
			width="600"
			@close=";(addOpen = false), (showAddLedgerDataModelisVisible = false)"
			@clicl-close=";(addOpen = false), (showAddLedgerDataModelisVisible = false)"
		>
			<collapseForm
				v-if="showAddLedgerDataModelisVisible"
				ref="addFormRef"
				label-position="right"
				label-width="100"
				submitIcon="i-ic-twotone-published-with-changes"
				:formGroupArray="handleData"
				:submitText="'保存'"
				:ledgerAuxiliaryFillingList="ledgerAuxiliaryFillingList"
				:fields="tableFiled"
				:formisDepartmentEditField="formisDepartmentEditField"
				:form="formArray"
				:rules="formRules"
				:showButton="true"
				:loading="addLoading"
				@onChange="formChanges"
				@onSubmit="formSubmitAdd"
				@remoteMethod="remoteMethod"
				@onBlur="onBlur"
				@onSearch="onAuxiliaryFillingSearch"
				@onIdentificationClick="onIdentificationClick"
				@onTemporarySave="onTemporarySave"
				:showTemprorary="formTitle === '新增'"
			>
			</collapseForm>
		</Dialog>

		<Dialog
			v-model="importOpen"
			confirmText="上传"
			@clicl-close="importOpen = false"
			@close="importOpen = false"
			@click-confirm="onUpload"
		>
			<template #header>
				导入数据
				<el-tooltip effect="dark" placement="bottom">
					<span class="fs-12 df aic mg-left-10">
						<el-icon size="12" class="mg-right-5">
							<WarningFilled />
						</el-icon>
						导入须知
					</span>
					<template #content>
						<pre>
1.业务表设置了唯一标识，会校验唯一标识字段是否已存在，已存在则更新该行数据，不存在则新增一行数据;业务表未设置唯一标识，则直接新增数据
2.导入文件后缀名为xs或xlsx，文件大小勿超过50M
3.数据请勿放在合并的单元格中
4.单元格填入内容为多选项的，各选项之前用英文逗号(,)隔开
5.导入模板中标红的列为必填项，必填才能导入成功
						</pre
						>
					</template>
				</el-tooltip>
			</template>

			<div
				style="
					color: var(--z-main);
					display: flex;
					align-items: center;
					margin-bottom: 10px;
				"
			>
				<el-button type="primary" @click="onDownloadExcelTemplae" class="df aic">
					<i class="icon i-ic-outline-download"></i>
					导入模板下载
				</el-button>
			</div>

			<el-steps
				class="steps"
				style="padding: 10px 0; max-width: 100%"
				:active="3"
				align-center
			>
				<el-step title="下载模版" />
				<el-step title="填写表格" />
				<el-step title="上传表格" />
			</el-steps>

			<el-upload
				class="upload"
				drag
				:limit="1"
				accept=".xlsx"
				ref="uploadRef"
				:auto-upload="false"
				:action="uploadUrl"
				:data="uploadData"
				:headers="{
					Authorization: 'Bearer ' + useUserStore().getToken,
				}"
				:before-upload="onBeforeUpload"
				:on-success="onUploadSuccess"
				:on-error="onUploadError"
				:on-change="onUploadChange"
			>
				<el-icon class="el-icon--upload" style="color: var(--z-main)"
					><upload-filled
				/></el-icon>
				<div class="el-upload__text">
					点击或将文件拖拽到这里上传
					<small style="display: block; color: #ccc" class="pd-top-20"
						>导入模式为业务表中已有数据更新,业务表中不包含数据新增,仅支持后缀名为xlsx文件</small
					>
				</div>
			</el-upload>
		</Dialog>

		<Dialog
			v-model="viewMoreInfoModalIsVisible"
			title="填报说明"
			width="600"
			:enable-confirm="false"
			@close="viewMoreInfoModalIsVisible = false"
			@click-close="viewMoreInfoModalIsVisible = false"
		>
			<div p-10px text="14px" style="white-space: pre-wrap">
				{{ ledger?.updateDescription }}
			</div>
		</Dialog>

		<Dialog
			v-model="showAuxiliaryFillingDataModal"
			title="辅助填报"
			width="600"
			:enable-confirm="getSingleData?.data && getSingleData?.data.length !== 0"
			@click-confirm="onAuxiliaryFillingCOnfirm"
			@close="showAuxiliaryFillingDataModal = false"
			@click-close="showAuxiliaryFillingDataModal = false"
		>
			<div w-full h-full>
				<el-checkbox-group
					v-model="auxiliaryFillingDataSelected"
					v-if="getSingleData?.data && getSingleData?.data.length !== 0"
					max="1"
				>
					<el-checkbox
						w-full
						v-for="item of getSingleData?.data"
						:label="
							getSingleData?.preview
								.map((x) => item[x])
								.toString()
								.split(',')
								.join(' ')
						"
						:value="item"
					></el-checkbox>
				</el-checkbox-group>

				<div v-else>暂未查询到数据</div>
			</div>
		</Dialog>

		<!-- <Dialog
			v-model="imagePreviewModalIsVisible"
			title="预览"
			width="45%"
			:enable-confirm="false"
			@close="imagePreviewModalIsVisible = false"
			@click-close="imagePreviewModalIsVisible = false"
		>
			<el-image :src="imageUrl" style="width: 100%; height: 100%" lazy fit="contain" />
		</Dialog> -->

		<ViewImage :src="imageUrl"></ViewImage>

		<ImportHistory
			v-model="historyOpen"
			width="900"
			title="导入历史"
			confirm-text="前往审核"
			:interval="2000"
			:ledgerId="(route.query.ledgerId as string)"
			@close="historyOpen = false"
			@click-close="historyOpen = false"
			@first-item-completed="onImportHistoryFirstItemCompleted"
			@open-audit="openAudit"
		></ImportHistory>

		<ExportHistory
			v-model="historyExport"
			width="900"
			:ledgerId="(route.query.ledgerId as string)"
			title="导出记录"
			@close="historyExport = false"
			@click-close="historyExport = false"
		>
		</ExportHistory>

		<ExportData
			v-if="exportOpen"
			v-model="exportOpen"
			width="900"
			title="数据导出"
			:tableGroup="tableGroup"
			:tableFiled="exportTableFiled"
			:ledger="ledger"
			:exportCount="
				handClickExportBtn === 'exportSelet' ? allSeletePageData.length : pageation.total
			"
			@close="exportOpen = false"
			@click-close="exportOpen = false"
			@getExportTableFiled="getExportTableFiled"
		></ExportData>

		<!-- 提交审核 -->
		<SubmitAudit
			ref="submitAuditRef"
			width="1000"
			title="提交审核"
			v-model="auditOpen"
			v-model:count="auditDataNumber"
			:visible="auditOpen"
			:tableAuditList="tableAuditList"
			@changeAuditVisible="changeAuditVisible"
			@uploadLedgenTbaleListData="uploadLedgenTbaleListData"
		>
		</SubmitAudit>

		<submitModel
			v-model="submitModalVisible"
			:visible="submitModalVisible"
			:type="openType"
			:submitData="allSeletePageData"
			:whereFields="searchFields"
			width="1000"
			title="提交审核"
			@change-audit-visible="changeSubmitAuditVisible"
			@uploadLedgenTbaleListData="uploadLedgenTbaleListData"
		></submitModel>

		<!-- 审核记录 -->
		<auditRecord
			v-model="auditRecordModal"
			title="审核记录"
			width="1100"
			:LedgerId="(route.query.ledgerId as string)"
			:col="colData"
			:AuditedBatchId="auditedBatchId"
			@close="auditRecordModal = false"
			@click-close="auditRecordModal = false"
		></auditRecord>

		<!-- / -->
		<auditRecordBatch
			v-model="auditRecordBatchModal"
			:visible="auditRecordBatchModal"
			title="提交记录"
			width="1100"
			:LedgerId="(route.query.ledgerId as string)"
			:col="colData"
			@handle-click-item="handleClickItem"
			@close="auditRecordBatchModal = false"
		></auditRecordBatch>

		<fillProgress
			v-model="fillProgressModal"
			title="填报进度"
			width="1100"
			:remindInterval="ledger?.reminderConfig?.interval"
			@close="fillProgressModal = false"
			@click-close="fillProgressModal = false"
		></fillProgress>

		<dataComparisonOne
			title="数据比对"
			width="900px"
			v-model="isDataComparisonOne"
			:dataComparisonOneData="currentRowComparison"
			@close="isDataComparisonOne = false"
			@click-close="onDateTimesCancel"
			@clickConfirmOne="onDateTimesCount"
			@applyNewData="onSuccessData"
		>
		</dataComparisonOne>
		<dataComparisonAll
			title="全量比对"
			width="900px"
			v-model="isDataComparisonAll"
			:dataComparisonAllData="currentRowComparisonAll"
			@close="isDataComparisonAll = false"
			@click-close="onDateAllCancel"
			@clickConfirmAll="onDateAllCount"
			@applyNewDataAll="onApplyNewDataAll"
		>
		</dataComparisonAll>

		<fill-screen-data
			title=""
			width="900"
			@setFreateAnalysis="setFreateAnalysis"
			v-model="showScreenData"
			@close="showScreenData = false"
			@click-close="showScreenData = false"
			:columnsList="columnsList"
			v-if="showScreenData"
			:groupByModelData="groupByModelData"
			:selectByModelData="selectByModelData"
			:onLineAnalysisConfigName="onLineAnalysisConfigName"
			:name="screenDataName"
			:analysisDescription="analysisDescription"
			:filterData="filterData"
		></fill-screen-data>

		<ExportDataDeclaration
			v-model="showDeclaration"
			v-model:result="declarationResult"
			@click-confirm="onClickDeclaration"
		></ExportDataDeclaration>
	</div>
</template>
<style lang="scss" scoped>
.ellipsis {
	max-width: 200px;
	overflow: hidden;
	text-overflow: ellipsis;
}
.right {
	align-items: center;
	display: flex;
	width: calc(100%);

	small {
		align-items: center;
		border-left: 1px solid var(--z-bg-secondary);
		display: flex;
		font-size: 12px;
		padding-left: 10px;
	}
	.active1 {
		border-bottom: 2px solid transparent;
		.ellipsis {
			max-width: 200px;
			overflow: hidden;
			text-overflow: ellipsis;
		}
		.ellipsis1 {
			max-width: 66px;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}
	.active {
		border-bottom: 2px solid var(--el-color-primary);
		color: var(--el-color-primary);
	}
}
.search {
	display: flex;
	align-items: center;
	flex-flow: wrap;

	i {
		transition: all 0.15s linear;

		&.expand {
			rotate: 180deg;
		}
	}

	button {
		margin-top: 5px;
		margin-bottom: 5px;
	}

	.label {
		margin-right: 10px;
	}

	.search-input {
		margin-right: 10px;
		width: 200px;
	}
}

.table-header {
	align-items: center;
	display: flex;
}

.table-title {
	font-size: 14px;
	font-weight: 500;
	line-height: 1;
	text-align: left;
	width: 100%;

	small {
		color: #333;
		font-weight: 400;
	}
}

.search-high {
	height: 0;
	margin-top: -10px;
	padding-top: 10px;
	overflow: hidden;
	transition: all 0.15s ease-in-out;

	&.expand {
	}

	:deep(.el-scrollbar__wrap) {
		max-height: 450px;
	}

	:deep(.el-form) {
		padding: 0 7px 15px 7px;

		.el-form-item {
			margin-right: 10px;
		}

		.el-form-item__label {
			padding: 0;
		}
	}
}

.el-upload__tip {
	display: flex;

	.left {
		margin-right: 10px;
	}

	.left,
	.right {
		border-radius: 5px;
		border: var(--z-border);
		flex: 1;
		padding: 10px;

		h2 {
			border-bottom: var(--z-border);
			font-size: 14px;
			font-weight: 500;
			padding-bottom: 5px;
			margin-bottom: 5px;
		}

		p {
			font-size: 13px;
			line-height: 2;
		}
	}
}

.upload {
	position: relative;
}

:deep(.el-upload-list) {
	padding: 0 10px;
	width: 100%;
	position: absolute;
	left: 0;
	bottom: 10px;
}

:deep(.el-upload-list__item) {
	transition-duration: 0s;
	padding: 0 10px;
}

:deep(.el-upload-list:hover .el-upload-list__item-file-name) {
	color: rgb(23, 100, 206);
}

.top-comp {
	height: unset;
}

.description {
	align-items: flex-start;
	display: flex;
	font-size: 12px;
	line-height: 1;
	margin-bottom: 10px;
	white-space: nowrap;

	.left {
		align-items: center;
		color: var(--z-main);
		display: flex;

		i {
			margin-right: 5px;
		}
	}

	.right {
		font-size: 12px;
		flex: 1;
		padding-right: 20px;
		height: 100%;
		padding-left: 10px;
		overflow: hidden;
		position: relative;

		:deep(span) {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			max-width: 100%;
		}

		.more {
			position: absolute;
			right: 2px;
			top: 20px;
			color: #000;
		}
	}
}

.color-ex {
	color: #e6a23c;
}

.color-del {
	color: #ff7f66;
}
.color-lock {
	color: gray;
}
.color-bohui {
	color: red;
}

.color-locked {
	color: #7ecefd;
}

.steps {
	:deep(.el-step__icon) {
		background-color: var(--z-main);
		color: #fff;
	}
}
.search-form {
	:deep(.el-form-item) {
		margin-right: 0;

		.el-select {
			width: 100% !important;
		}

		&:not(.full) {
			width: 25%;
		}
	}
}

.more-operations {
	padding: 15px;
	width: 300px;
	.mo-box {
		border-bottom: 1px solid var(--z-line);
		display: flex;
		flex-wrap: wrap;
		padding-bottom: 10px;
		margin-bottom: 20px;

		&:last-child {
			border-bottom: none;
			margin-bottom: 0;
		}

		> span {
			font-size: 14px;
			font-weight: 500;
			padding: 0 0 10px 0;
			width: 100%;
		}

		:deep(button) {
			margin-left: 0;
			margin-right: 10px;
			margin-bottom: 10px;

			&:nth-child(5n) {
				margin-right: 0;
			}

			span {
				color: var(--z-nav-font-color) !important;
			}
		}
	}
}
</style>

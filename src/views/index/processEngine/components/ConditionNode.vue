<script setup lang="ts">
import {computed, nextTick, onMounted, ref, watch} from 'vue'
import {FlowNodeTypes} from '@/define/Workflow'
import {useGuid} from '@/hooks/useGuid'
import {useUserStore} from '@/stores/useUserStore'
import {GetConditionDict} from '@/api/WorkflowApi'
import {GetRegion, GetDepartment, GetDepartmentSimple} from '@/api/OrganizeApi'
import {ElMessage} from 'element-plus'

enum ConditionIocNames {
	Region = '操作人所属区域',
	RegionContain = '操作人所属区域(包含)',
	LargeDepartment = '操作人所属部门',
	RegionGrade = '操作人所属区域等级',
	Role = '操作人角色',
}

enum Level {
	City = 1,
	District = 2,
	Street = 3,
	Community = 4,
}

enum TransferType {
	Region = '选择区域',
	RegionContain = '选择区域(包含)',
	Department = '选择部门',
}

const emits = defineEmits(['clickConfirm'])
const props = defineProps({
	node: {type: Object, default: () => ({})},
	parentNode: {type: Object, default: () => ({})},
	parentNodes: {type: Array, default: () => []},
})

const userInfo = useUserStore().getUserInfo
const userDepartment = useUserStore().getCurrentDepartment

const formProps = ref([
	{
		prop: 'isDefault',
		label: '设置为其他条件都不符合的默认分支',
		type: 'switch',
		labelWidth: 245,
	},
	{
		prop: 'unitId',
		label: '节点名称',
		type: 'select',
		options: [],
	},
	{
		prop: 'iocName',
		label: '条件名称',
		type: 'select',
		options: Object.keys(ConditionIocNames).map((key) => ({
			label: ConditionIocNames[key as keyof typeof ConditionIocNames],
			value: key,
		})),
	},
	{
		prop: '_transfer',
		label: '',
		formShow: false,
	},
	{
		prop: 'value',
		label: '对应值',
		type: 'select',
		multiple: true,
		options: [],
	},
])

const openTransfer = ref(false)
const transferTitle = ref('')
const transferData = ref([])
const transferValue = ref([])
const currentTransferData: any = ref([])
const transferPagination = ref({
	total: 0,
	size: 10,
	page: 1,
})
const level = ref(0)
const levelOptions = ref([])
const cascade = ref({})
const historyLastArray: any = ref([])

const reportOptions = computed(() =>
	props.parentNodes
		// 临时处理, 只要开始节点
		.filter((item: any) => item.type === FlowNodeTypes.Input)
		.map((item: any) => ({label: item.label, value: item.id}))
)

const formRef = ref()
const formRules = {
	unitId: [{required: true, message: '请选择节点名称', trigger: 'change'}],
	iocName: [{required: true, message: '请选择条件名称', trigger: 'change'}],
	value: [{required: true, message: '请选择对应值', trigger: 'change'}],
}
const conditionForm: any = ref({})
const flowNode: any = ref(null)

const cascadeProps = ref([])
const historyIds = ref([])

const onClickConfirmDrawer = () => {
	formRef.value.validate((valid: boolean) => {
		if (!valid) return

		const value: any = {}
		const label =
			ConditionIocNames[conditionForm.value.iocName as keyof typeof ConditionIocNames]

		if (
			label === ConditionIocNames.Region ||
			label === ConditionIocNames.LargeDepartment ||
			label === ConditionIocNames.RegionContain
		) {
			if (Object.keys(currentTransferData.value).length === 0) {
				conditionForm.value.value.forEach((id: any) => {
					const item = formProps.value[4].options?.find((f: any) => f.value === id)
					if (item) {
						value[id] = item.label
					}
				})
			} else {
				currentTransferData.value.forEach((item: any) => {
					value[item.key] = item.label
				})
			}
		} else {
			const formValueOptions = formProps.value.find((f: any) => f.prop === 'value')?.options

			conditionForm.value.value?.forEach((id: any) => {
				const option = formValueOptions?.find((f: any) => f.value === id)
				if (option) {
					value[id] = option.label
				}
			})
		}

		if (
			props.parentNode.conditionNodes.some(
				(s: any) => s?.config?.isDefault && props.node.id !== s.id
			) &&
			props.node.type === FlowNodeTypes.Condition &&
			conditionForm.value.isDefault
		) {
			ElMessage.warning('同级条件已经存在默认分支, 请先取消默认分支')
			conditionForm.value.isDefault = false
			setFormPropsShow(false)
			return
		}

		Object.assign(flowNode.value, {
			config: {
				code: flowNode.value.code || useGuid(),
				name: flowNode.value.label,
				iocName: conditionForm.value.iocName,
				unitId: conditionForm.value.unitId,
				type: 4,
				level: 1,
				compareType: 1,
				value,
				isDefault:
					conditionForm.value.isDefault === null ? false : conditionForm.value.isDefault,
			},
		})
		currentTransferData.value = {}
		emits('clickConfirm', valid)
	})
}

const setConfig = (node: any) => {
	if (node.type !== FlowNodeTypes.Condition) return

	if (node.config) {
		conditionForm.value = {
			iocName: node.config.iocName,
			unitId: node.config.unitId,
			value: Object.keys(node.config?.value),
			isDefault: node.config.isDefault === null ? false : node.config.isDefault,
		}
	}

	nextTick(() => {
		!node.config && formRef.value.clear()
		console.log('Set Config: ', node)

		const configValue = Object.keys(node.config?.value ?? {}).map((key) => ({
			label: node.config?.value[key],
			value: key,
			raw: {key, label: node.config?.value[key]},
		}))

		formProps.value[1].options = reportOptions.value

		const curIocName = ConditionIocNames[node.config?.iocName as keyof typeof ConditionIocNames]
		let showButton = true

		if (curIocName === ConditionIocNames.Region) {
			transferTitle.value = TransferType.Region
		} else if (curIocName === ConditionIocNames.RegionContain) {
			transferTitle.value = TransferType.RegionContain
		} else if (curIocName === ConditionIocNames.LargeDepartment) {
			transferTitle.value = TransferType.Department
		} else {
			transferTitle.value = ''
			showButton = false
		}

		setFormPropsShow(node.config?.isDefault)

		formProps.value[3].formShow = showButton

		if (curIocName === ConditionIocNames.Role || curIocName === ConditionIocNames.RegionGrade) {
			GetConditionDict(node.config?.iocName).then((res) => {
				formProps.value[4].options = res.data.map((item: any) => ({
					label: item.Name,
					value: item.Id,
				}))
			})
		} else {
			formProps.value[4].options = configValue || []
		}

		flowNode.value = node
		// 临时处理, 选择开始节点
		setTimeout(() => formRef.value.setValue('unitId', reportOptions.value[0].value), 0)
	})
}

const setFormPropsShow = (bool: Boolean) => {
	formProps.value.forEach((f: any) => {
		if (f.prop !== 'isDefault') {
			f.formShow = !bool
		}

		if (f.prop === '_transfer') {
			if (
				!bool &&
				(conditionForm.value.iocName === 'Region' ||
					conditionForm.value.iocName === 'LargeDepartment')
			) {
				f.formShow = true
			} else {
				f.formShow = false
			}
		}
	})
}

const onFormChange = (val: any, item: any) => {
	let label = null
	const transfer = formProps.value.find((item) => item.prop === '_transfer')
	const value = formProps.value.find((item) => item.prop === 'value')

	if (item.prop === 'isDefault') {
		setFormPropsShow(val)
	}

	if (item.prop === 'iocName') {
		label = ConditionIocNames[val as keyof typeof ConditionIocNames]
		formRef.value.setValue('value', [])
		value!.options = []

		if (label === ConditionIocNames.Role || label === ConditionIocNames.RegionGrade) {
			transfer!.formShow = false
			GetConditionDict(val).then((res) => {
				value!.options = res.data.map((item: any) => ({label: item.Name, value: item.Id}))
			})
		} else if (
			label === ConditionIocNames.Region ||
			label === ConditionIocNames.RegionContain ||
			label === ConditionIocNames.LargeDepartment
		) {
			let title = ''
			if (label === ConditionIocNames.Region) {
				title = TransferType.Region
			} else if (label === ConditionIocNames.RegionContain) {
				title = TransferType.RegionContain
			} else {
				title = TransferType.Department
			}
			transferTitle.value = title
			transfer!.formShow = true
		}
	}

	if (item.prop === 'value') {
		// label = ConditionIocNames[conditionForm.value.iocName as keyof typeof ConditionIocNames]
		// if (label === ConditionIocNames.Region || label === ConditionIocNames.LargeDepartment) {
		// 	const value: any = {}
		// 	val.forEach((id: any) => {
		// 		const option = item.options.find((f: any) => f.value === id)
		// 		if (option) {
		// 			value[id] = option.label
		// 		}
		// 	})
		// 	console.log('Set Config Value: ', val, value)
		// 	Object.assign(flowNode.value, {
		// 		config: {
		// 			...flowNode.value.config,
		// 			value,
		// 		},
		// 	})
		// 	console.log('Set Config Value: ', flowNode.value)
		// }
	}
}

const onLevelChange = (val: any) => {
	let temp: any = []

	const defautlConfig = {
		type: 'select',
		method: 'GET',
		cascadeUrl: `/api/platform/region/parent/${userDepartment.cityRegion.id}`,
		cascadeParams: {},
		beforeInitOptions: (val: any, next: any, item: any) => {
			next.cascadeUrl = `/api/platform/region/parent/${val}`
		},
		options: [],
	}

	cascade.value = {}
	transferData.value = []

	if (
		transferTitle.value === TransferType.Region ||
		transferTitle.value === TransferType.RegionContain
	) {
		if (val === Level.City) {
			// 目前固定市级
			transferData.value = [
				{
					key: userDepartment?.cityRegion.id,
					label: userDepartment?.cityRegion.name,
					disabled: false,
					raw: JSON.parse(JSON.stringify(userDepartment?.cityRegion)),
				},
			] as any
		}

		if (val === Level.District) {
			onCascadeChange({district: userDepartment?.cityRegion.id})
		}

		if (val === Level.Street) {
			temp = [
				{
					...defautlConfig,
					prop: 'district',
					placeholder: '请选择区县',
					multiple: true,
					collapseTags: true,
				},
			]
		}

		if (val === Level.Community) {
			temp = [
				{...defautlConfig, prop: 'district', placeholder: '请选择区县'},
				{
					...defautlConfig,
					prop: 'street',
					placeholder: '请选择街道',
					multiple: true,
					collapseTags: true,
				},
			]
		}
	} else if (transferTitle.value === TransferType.Department) {
		temp = [
			{
				...defautlConfig,
				prop: 'city',
				placeholder: '请选择城市',
				// 目前固定市级
				options: [
					{
						value: userDepartment.cityRegion.id,
						label: userDepartment.cityRegion.name,
						raw: JSON.parse(JSON.stringify(userDepartment.cityRegion)),
					},
				],
			},
			{...defautlConfig, prop: 'district', placeholder: '请选择区县'},
			{...defautlConfig, prop: 'street', placeholder: '请选择街道'},
			{
				...defautlConfig,
				prop: 'community',
				placeholder: '请选择社区',
				multiple: true,
				collapseTags: true,
			},
		]
	}

	cascadeProps.value = temp
}

const onCascadeChange = async (val: any) => {
	const last: any = Object.values(val).pop()
	let isMultiple = false

	if (
		transferTitle.value === TransferType.Region ||
		transferTitle.value === TransferType.RegionContain
	) {
		if (last && last[0]) {
			const promise = []
			if (typeof last === 'string') {
				promise.push(GetRegion(last))
			} else if (Array.isArray(last)) {
				isMultiple = true

				last.forEach((item) => {
					if (!historyLastArray.value.some((i: any) => i === item)) {
						promise.push(GetRegion(item))
					}
				})

				historyLastArray.value = last
			}

			const result = await Promise.all(promise)
			const temp: any = result.map((item: any) => item.data.items).flat()

			if (isMultiple) {
				const newData: any = temp.map((item: any) => ({
					key: item.id,
					label: item.name,
					disabled: false,
					raw: JSON.parse(JSON.stringify(item)),
				}))
				transferData.value = transferData.value
					.concat(newData)
					.filter((item: any) => last.join().includes(item.raw.parentId))
			} else {
				transferData.value = temp.map((item: any) => ({
					key: item.id,
					label: item.name,
					disabled: false,
					raw: JSON.parse(JSON.stringify(item)),
				}))
			}
		} else {
			transferData.value = []
			historyLastArray.value = []
		}
	} else if (transferTitle.value === TransferType.Department) {
		let regionIds: any = []

		if (val.city) {
			regionIds = [userDepartment.cityRegion.id]
		}

		if (val.district) {
			regionIds = [val.district]
		}

		if (val.street) {
			regionIds = [val.street]
		}

		if (val.community && val.community.length > 0) {
			regionIds = val.community
		}

		if (regionIds.length === 0) {
			transferData.value = []
			return
		}

		historyIds.value = regionIds
		transferPagination.value.page = 1
		GetDepartmentList(regionIds)
	}
}

const onTransferChange = (val: any, direction: any, movedKeys: any, raws: any) => {
	console.log('Transfer change', val, direction, movedKeys, raws)
	currentTransferData.value = raws
}

const onClickConditionButton = () => {
	onLevelChange(
		transferTitle.value === TransferType.Region ||
			transferTitle.value === TransferType.RegionContain
			? level.value
			: Level.Community
	)
	openTransfer.value = true
}

const GetDepartmentList = (regionIds: any) => {
	GetDepartmentSimple({
		filter: '',
		skipCount: transferPagination.value.size * (transferPagination.value.page - 1),
		maxResultCount: transferPagination.value.size,
		regionIds,
	}).then((res) => {
		const temp: any = []
		res.data.items.forEach((item: any) => {
			temp.push({
				key: item.id,
				label: item.name,
				disabled: false,
				raw: JSON.parse(JSON.stringify(item)),
			})
		})
		transferData.value = temp
		transferPagination.value.total = res.data.totalCount
	})
}

const onDialogOpen = () => {
	transferValue.value = []
	transferPagination.value.page = 1
	transferPagination.value.size = 10
	transferPagination.value.total = 0
}

const onClickConfirmTransfer = () => {
	console.log('Transfer value', transferValue.value)

	if (transferValue.value.length === 0) {
		ElMessage.warning('请选择对应值')
		return
	}

	const val = formProps.value.find((f: any) => f.prop === 'value')
	val!.options = currentTransferData.value.map((item: any) => ({
		label: item.label,
		value: item.key,
		raw: item,
	}))
	formRef.value.setValue('value', transferValue.value)
	openTransfer.value = false
}

const onPaginationChange = (val: any, type: any) => {
	if (type === 'size') {
		transferPagination.value.size = val
	} else {
		transferPagination.value.page = val
	}
	GetDepartmentList(historyIds.value)
}

const initOptions = () => {
	const levelTemp: any = []

	if (
		!userInfo?.city ||
		(userInfo.city && !userInfo.district && !userInfo.street && !userInfo.community)
	) {
		level.value = Level.City
		levelTemp.push({label: '市级', value: Level.City})
	}

	if (!userInfo?.district) {
		level.value = Level.District
		levelTemp.push({label: '区县', value: Level.District})
	}

	if (!userInfo?.street) {
		level.value = Level.Street
		levelTemp.push({label: '街道', value: Level.Street})
	}

	if (!userInfo?.community) {
		level.value = Level.Community
		levelTemp.push({label: '社区', value: Level.Community})
	}

	levelOptions.value = levelTemp
}

onMounted(() => {
	initOptions()
})

watch(
	() => props.node,
	(val) => val && setConfig(val)
)
</script>
<template>
	<div class="flow-audit-node">
		<Drawer
			v-bind="$attrs"
			class="flow-audit-node-drawer"
			:enable-close="false"
			@click-confirm="onClickConfirmDrawer"
		>
			<div class="tips">
				<i class="icon i-ic-baseline-tips-and-updates" mr-3px></i>
				以下条件符合则执行该分支
			</div>
			<Form
				ref="formRef"
				v-model="conditionForm"
				:data="formProps"
				:rules="conditionForm.isDefault ? [] : formRules"
				:enable-button="false"
				label-width="80"
				@change="onFormChange"
			>
				<template #form-_transfer>
					<el-button type="primary" @click="onClickConditionButton">
						{{ transferTitle }}
					</el-button>
				</template>
			</Form>
		</Drawer>

		<Dialog
			v-model="openTransfer"
			:title="transferTitle"
			:destroy-on-close="true"
			@open="onDialogOpen"
			@close=";(transferData = []), (cascade = {})"
			@click-confirm="onClickConfirmTransfer"
			width="1000"
		>
			<Transfer
				v-model="transferValue"
				:data="transferData"
				:enbable-collect="false"
				class="condition-transfer-component"
				@transfer-change="onTransferChange"
			>
				<template #collect-left>
					<template
						v-if="
							transferTitle === TransferType.Region ||
							transferTitle === TransferType.RegionContain
						"
					>
						<span class="level-title">选择层级:</span>
						<el-select
							v-model="level"
							style="width: 140px"
							mr-10px
							@change="onLevelChange"
						>
							<el-option
								v-for="item of levelOptions as any"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</template>
					<span
						v-if="level !== Level.District && level !== Level.City"
						class="level-title"
						ml-10px
					>
						选择区域:
					</span>
					<Cascade
						v-model="cascade"
						:key="level"
						:options="cascadeProps"
						@change="onCascadeChange"
						class="condition-cascade-component"
					></Cascade>
				</template>

				<!-- <template #footer>
					<div class="transfer-page" v-if="transferTitle === TransferType.Department">
						<Pagination
							:total="transferPagination.total"
							:current-page="transferPagination.page"
							justify-content="center"
							@size-change="onPaginationChange($event, 'size')"
							@current-change="onPaginationChange($event, 'page')"
						></Pagination>
					</div>
				</template> -->
			</Transfer>
		</Dialog>
	</div>
</template>
<style scoped lang="scss">
.condition-items {
	.item {
		align-items: center;
		display: flex;
		margin-bottom: 15px;
		&:last-child {
			margin-bottom: 0;
		}

		> * {
			margin-right: 15px;
		}

		.i-ic-outline-delete-forever {
			cursor: pointer;
		}
	}
}
.tips {
	align-items: center;
	color: var(--z-warning);
	display: flex;
	padding-bottom: torem(10px);
	font-weight: 500;
	i {
		color: var(--z-warning);
	}
}
</style>
<style lang="scss">
.condition-transfer-component {
	.level-title {
		margin-right: 10px;
		white-space: nowrap;
	}
	.transfer-page .pageination-component {
		justify-content: center !important;
	}
}
.condition-cascade-component {
	margin-right: 10px;
	.el-form-item {
		margin-bottom: 0 !important;
	}
}
</style>

<script setup lang="ts">
import {ref, computed, watch, nextTick, inject, onMounted, reactive} from 'vue'
import {useUserStore} from '@/stores/useUserStore'
import {FlowNodeTypes} from '@/define/Workflow'
import {USER_ROLES_ENUM} from '@/define/organization.define'
import {ElMessage} from 'element-plus'
import SelectRegionOrDepartment from '@/components/common/SelectRegionOrDepartment.vue'
import axios from 'axios'

enum DepartmentTypes {
	Same = 0, //填报人同部门,
	Upper = 1, //填报人上一级部门,
	UpperTwo = 2, //填报人上二级部门,
	UpperThree = 3, //填报人上三级部门,
	UpperFour = 4, //填报人上四级部门,
	UpperFive = 5, //填报人上五级部门,
}

const user = useUserStore()
const emits = defineEmits(['clickConfirm'])
const props = defineProps({
	node: {type: Object, default: () => ({})},
	parentNode: {type: Object, default: () => ({})},
	parentNodes: {type: Array, default: () => []},
	formData: {type: Object, default: {}},
	initData: {type: Object, default: {}},
})

const flowNode: any = ref(null)
const tableRef = ref()
const columns = [
	{prop: 'department', label: '部门'},
	{prop: 'roles', label: '用户角色/用户姓名'},
]
const treeRef = ref()
const reviewForm: any = ref({isCountersign: true})
const rejectForm: any = ref({})
const form0Ref = ref()
const showAddReview = ref(false)
const showAddTable = ref(false)
const showSuperAdminDialog = ref(false)
const chooseRole = ref(0)
const autoSelectRoleForm: any = ref({department: 0})
const transferDepartments = ref([])
const transferDepartmentRole = ref('')
const transferRightRaws: any = ref([])
const transferRightHistory: any = ref([])
const addReviewForm: any = ref({})
const form0: any = ref({type: '2'})

const superAdmin = computed(() => {
	return (
		user.getUserInfo?.baseRoles.indexOf('ledger-super-admin') > -1 &&
		props.formData.category === '业务表导出流程'
	)
})

const departmentOptions = [
	{label: '填报人同部门', value: DepartmentTypes.Same},
	{label: '填报人上一级部门', value: DepartmentTypes.Upper},
	{label: '填报人上二级部门', value: DepartmentTypes.UpperTwo},
	{label: '填报人上三级部门', value: DepartmentTypes.UpperThree},
	{label: '填报人上四级部门', value: DepartmentTypes.UpperFour},
	{label: '填报人上五级部门', value: DepartmentTypes.UpperFive},
]
const roleOptions = [
	{label: '数据管理岗', value: USER_ROLES_ENUM.DATA_LEADER},
	{label: USER_ROLES_ENUM.DEPARTMENT_LEADER, value: USER_ROLES_ENUM.DEPARTMENT_LEADER},
	{label: USER_ROLES_ENUM.WORK_STAFF, value: USER_ROLES_ENUM.WORK_STAFF},
	{label: USER_ROLES_ENUM.MAIN_LEDGER, value: USER_ROLES_ENUM.MAIN_LEDGER},
	{label: USER_ROLES_ENUM.PERSON_CHARGE, value: USER_ROLES_ENUM.PERSON_CHARGE},
]
const addReviewFormProps = [
	{
		prop: 'department',
		label: '附加条件',
		type: 'select',
		options: departmentOptions,
	},
	{
		prop: 'roles',
		label: '用户角色',
		type: 'checkbox',
		options: roleOptions,
	},
]

let historyFlowNode = {}

const rejectOptions = computed(() =>
	props.parentNodes
		.filter(
			(item: any) =>
				item.type !== FlowNodeTypes.Gateway && item.type !== FlowNodeTypes.Condition
		)
		.map((item: any) => ({label: item.label, value: item.id}))
)

const getDepartment = (row: any) => {
	const {originDepartMentData, selectDepartmentGroup} = props.initData
	let label = '-'

	if (chooseRole.value === 1) {
		label = props.initData.originDepartMentData.find(
			(item: any) => item.value === row.department
		)?.parent
			? `${
					props.initData.originDepartMentData.find(
						(item: any) => item.value === row.department
					)?.parent?.name
			  }/${
					props.initData.originDepartMentData.find(
						(item: any) => item.value === row.department
					)?.label
			  }`
			: props.initData.originDepartMentData.find((item: any) => item.value === row.department)
					?.label
	} else {
		if (!transferRightHistory.value) {
			label = selectDepartmentGroup.find((f: any) => f.id === row.department)?.name || ''
		} else {
			label =
				transferRightHistory.value.find((f: any) => f.key === row.department)?.label || ''
		}

		if (!label) {
			label = selectDepartmentGroup.find((f: any) => f.id === row.department)?.name || ''
		}

		if (!label) {
			label = originDepartMentData.find((f: any) => f.value === row.department)?.name || ''
		}
	}

	return label
}

const onClickTableButton = ({row, btn, index}: any) => {
	if (btn.type === 'danger') {
		tableRef.value.remove(row.id, 'id')
		flowNode.value.auditUsers = flowNode.value.auditUsers.filter(
			(item: any) => item.id !== row.id
		)
	}
}

const onClickAddReview = () => {
	const isCustom = chooseRole.value === 0

	if (isCustom) {
		if (transferDepartments.value.length === 0) {
			ElMessage.warning('请选择部门')
			return
		}

		if (!transferDepartmentRole.value) {
			ElMessage.warning('请选择角色')
			return
		}

		const tableData = tableRef.value.getTableData()

		const isRepeated = transferDepartments.value.some((departmentId: any) =>
			tableData.some(
				(item: any) =>
					item.department === departmentId && transferDepartmentRole.value === item.roles
			)
		)

		if (isRepeated) {
			ElMessage.warning(`请勿重复添加相同部门相同角色的审核人`)
		} else {
			transferDepartments.value.forEach((departmentId: any) => {
				tableRef.value.push(
					{roles: transferDepartmentRole.value, department: departmentId},
					'id'
				)
			})
			transferRightHistory.value = [
				...transferRightHistory.value,
				...transferRightRaws.value.map((item: any) => ({key: item.key, label: item.label})),
			]
			console.log(transferRightHistory.value)
			// 业务表导出审核流程
			Object.assign(flowNode.value, {
				auditUsers: tableRef.value.getTableData().map((row: any, idx: number) => ({
					id: row.id,
					type: 2,
					value: row.roles,
					additionalCondition: 1,
					additionalConditionType: 1,
					additionalConditionValue: row.department,
					additionalConditionLabel: transferRightHistory.value.find(
						(item: any) => item.key === row.department
					)?.label,
				})),
			})
		}
	} else {
		if (
			addReviewForm.value.department === undefined ||
			addReviewForm.value.department === null
		) {
			ElMessage.warning('请选择附加条件')
			return
		}

		if (!addReviewForm.value.roles || addReviewForm.value.roles.length === 0) {
			ElMessage.warning('请选择用户角色')
			return
		}

		const roles = addReviewForm.value.roles
		roles.forEach((role: any) => {
			const tableData = tableRef.value.getTableData()

			if (
				tableData.some(
					(item: any) =>
						item.department === addReviewForm.value.department &&
						(item.roles === role || item.roles.includes(role))
				)
			) {
				ElMessage.warning(
					`"${
						departmentOptions[addReviewForm.value.department].label
					}" 已存在角色为 "${role}" 的审核人, 请勿重复添加`
				)
			} else {
				tableRef.value.push({...addReviewForm.value, roles: role}, 'id')
			}
		})

		// 业务表流程
		Object.assign(flowNode.value, {
			auditUsers: tableRef.value.getTableData().map((row: any) => ({
				id: row.id,
				type: 2,
				// value: row.roles.join(','),
				value: row.roles,
				additionalCondition: 1,
				additionalConditionValue: row.department,
			})),
		})

		addReviewForm.value = {department: undefined, roles: []}
	}
	transferDepartments.value = []
	showAddReview.value = false
}

const onClickSupper = () => {
	const isCustom = chooseRole.value === 0

	if (isCustom) {
		if (transferDepartments.value.length === 0) {
			ElMessage.warning('请选择部门')
			return
		}

		if (!transferDepartmentRole.value) {
			ElMessage.warning('请选择角色')
			return
		}

		const tableData = tableRef.value.getTableData()

		const isRepeated = transferDepartments.value.some((departmentId: any) =>
			tableData.some(
				(item: any) =>
					item.department === departmentId && transferDepartmentRole.value === item.roles
			)
		)

		if (isRepeated) {
			ElMessage.warning(`请勿重复添加相同部门相同角色的审核人`)
		} else {
			transferDepartments.value.forEach((departmentId: any) => {
				tableRef.value.push(
					{roles: transferDepartmentRole.value, department: departmentId},
					'id'
				)
			})

			transferRightHistory.value = [
				...transferRightHistory.value,
				...transferRightRaws.value.map((item: any) => ({key: item.key, label: item.label})),
			]

			// 业务表导出审核流程
			Object.assign(flowNode.value, {
				auditUsers: tableRef.value.getTableData().map((row: any, idx: number) => ({
					id: row.id,
					type: 2,
					value: row.roles,
					additionalCondition: 1,
					additionalConditionType: 1,
					additionalConditionValue: row.department,
					additionalConditionLabel: transferRightHistory.value.find(
						(item: any) => item.key === row.department
					)?.label,
				})),
			})
		}
	} else {
		tableRef.value.push(
			{roles: autoSelectRoleForm.value.role, department: '业务表发布部门'},
			'id'
		)
		// 业务表导出审核流程
		Object.assign(flowNode.value, {
			auditUsers: tableRef.value.getTableData().map((row: any, idx: number) => ({
				id: row.id,
				type: 2,
				value: row.roles,
				additionalCondition: 4,
				additionalConditionType: 2,
				additionalConditionLabel: '业务表发布部门',
			})),
		})
	}

	transferDepartments.value = []
	showSuperAdminDialog.value = false
}

const onClicKAddTable = () => {
	if (props.formData.category === '临时报表流程' && !form0.value.type) {
		return ElMessage.warning('请选择类型')
	}
	console.log(form0.value)
	console.log(props.initData.originDepartMentData)

	tableRef.value.push([])

	if (form0.value.type == 2) {
		const roles = form0.value.value
		roles.forEach((role: any) => {
			const tableData = tableRef.value.getTableData()

			if (
				tableData.some(
					(item: any) =>
						item.department === form0.value.additionalConditionValue &&
						(item.roles === role || item.roles.includes(role))
				)
			) {
				const Indexs = props.initData.originDepartMentData.value.findIndex(
					(item: any) => item.id === form0.value.additionalConditionValue
				)
				ElMessage.warning(
					`"${props.initData.originDepartMentData.value[Indexs].label}" 已存在角色为 "${role}" 的审核人, 请勿重复添加`
				)
			} else {
				tableRef.value.push(
					{...form0.value, department: form0.value.additionalConditionValue, roles: role},
					'id'
				)
			}
		})
		console.log(tableRef.value.getTableData())
		Object.assign(flowNode.value, {
			auditUsers: tableRef.value.getTableData().map((row: any) => ({
				id: row.id,
				type: row.type,
				// value: row.roles.join(','),
				valueLabel: row.type == 3 ? row.valueLabel : null,
				value: row.type == 2 ? row.roles : row.value,
				additionalCondition: 2,
				additionalConditionValue: row.department,
				additionalConditionType: 1,
				additionalConditionValueLabel: props.initData.originDepartMentData.find(
					(item: any) => item.value === row.department
				)?.parent
					? `${
							props.initData.originDepartMentData.find(
								(item: any) => item.value === row.department
							)?.parent?.name
					  }/${
							props.initData.originDepartMentData.find(
								(item: any) => item.value === row.department
							)?.label
					  }`
					: props.initData.originDepartMentData.find(
							(item: any) => item.value === row.department
					  )?.label,
			})),
		})
		isSelect.value = true
	} else if (form0.value.type == 4) {
		const roles = form0.value.value
		roles.forEach((role: any) => {
			const tableData = tableRef.value.getTableData()

			if (
				tableData.some(
					(item: any) =>
						item.department === form0.value.additionalConditionValue &&
						(item.roles === role || item.roles.includes(role))
				)
			) {
				const Indexs = props.initData.originDepartMentData.value.findIndex(
					(item: any) => item.id === form0.value.additionalConditionValue
				)
				ElMessage.warning(
					`"${props.initData.originDepartMentData.value[Indexs].label}" 已存在角色为 "${role}" 的审核人, 请勿重复添加`
				)
			} else {
				tableRef.value.push(
					{...form0.value, department: form0.value.additionalConditionValue, roles: role},
					'id'
				)
			}
		})
		Object.assign(flowNode.value, {
			auditUsers: tableRef.value.getTableData().map((row: any) => ({
				id: row.id,
				type: row.type,
				// value: row.roles.join(','),
				valueLabel: row.type == 3 ? row.valueLabel : null,
				value: row.roles,
				additionalCondition: 2,
				additionalConditionValue: row.department,
				additionalConditionType: 1,
				additionalConditionValueLabel: props.initData.originDepartMentData.find(
					(item: any) => item.value === row.department
				)?.parent
					? `${
							props.initData.originDepartMentData.find(
								(item: any) => item.value === row.department
							)?.parent?.name
					  }/${
							props.initData.originDepartMentData.find(
								(item: any) => item.value === row.department
							)?.label
					  }`
					: props.initData.originDepartMentData.find(
							(item: any) => item.value === row.department
					  )?.label,
			})),
		})
	} else {
		const roles = form0.value.value
		const uersNew = userLists.value.filter((item: any) => roles.includes(item.value))

		console.log(uersNew)
		console.log(userLists.value)
		console.log(tableRef.value.getTableData())
		uersNew.forEach((role: any) => {
			const tableData = tableRef.value.getTableData()

			if (
				tableData.some(
					(item: any) =>
						item.department === form0.value.additionalConditionValue &&
						item.value.includes(role.value)
				)
			) {
				const Indexs = props.initData.originDepartMentData.value.findIndex(
					(item: any) => item.id === form0.value.additionalConditionValue
				)
				ElMessage.warning(
					`"${props.initData.originDepartMentData.value[Indexs].label}" 已存在角色为 "${role.label}" 的审核人, 请勿重复添加`
				)
			} else {
				tableRef.value.push(
					{
						...form0.value,
						department: form0.value.additionalConditionValue,
						roles: role.label,
						value: role.value,
						valueLabel: role.label,
						additionalConditionValueLabel: '',
					},
					'id'
				)
			}
		})
		Object.assign(flowNode.value, {
			auditUsers: tableRef.value.getTableData().map((row: any) => ({
				id: row.id,
				type: row.type,
				// value: row.roles.join(','),
				valueLabel: row.type == 3 ? row.valueLabel : null,
				value: row.type == 2 ? row.roles : row.value,
				additionalCondition: 2,
				additionalConditionType: 1,
				additionalConditionValue: row.department,
				additionalConditionValueLabel: props.initData.originDepartMentData.find(
					(item: any) => item.value === row.department
				)?.parent
					? `${
							props.initData.originDepartMentData.find(
								(item: any) => item.value === row.department
							)?.parent?.name
					  }/${
							props.initData.originDepartMentData.find(
								(item: any) => item.value === row.department
							)?.label
					  }`
					: props.initData.originDepartMentData.find(
							(item: any) => item.value === row.department
					  )?.label,
			})),
		})
	}
	form0.value = {type: '2'}
	userLists.value = []
	showAddTable.value = false
}

const onClickConfirmDrawer = () => {
	const isCountersign = reviewForm.value.isCountersign
	const {rejectAppointUnitViewId} = rejectForm.value

	Object.assign(flowNode.value, {
		isCountersign,
		countersignType: isCountersign ? 1 : 0,
		countersignAllType: isCountersign ? 100 : 0,
		countersignAgain: isCountersign ? 1 : 0,

		rejectType: 4,
		// rejectAppointUnitViewId,
	})

	emits('clickConfirm', true)
}

const props2 = {
	label: 'label',
	value: 'value',
	isLeaf: 'isLeaf',
}

const setConfig = (node: any) => {
	if (node.type !== FlowNodeTypes.Review) return

	const temp: any = []
	transferRightHistory.value.length = 0
	console.log('setConfig', node)

	if (node.auditUsers) {
		node.auditUsers.forEach((item: any) => {
			let obj: any = {}
			let val = item.value
			let roles = item.valueLabel
			if (item.type == 2 || item.type == 4) {
				roles = item.value
			}
			obj = {
				id: item.id,
				department:
					item.additionalConditionValue == 0
						? item.additionalConditionValue
						: item.additionalConditionValue === null
						? '业务表发布部门'
						: item.additionalConditionValue,
				// roles: item.value.split(','),
				roles,
				type: item.type,
				value: val === '数据领导' ? '数据管理岗' : val,
				additionalConditionType: item.additionalConditionType,
				additionalConditionLabel: item.additionalConditionLabel,
				valueLabel: item.valueLabel,
			}
			temp.push(obj)

			if (superAdmin.value) {
				if (obj.additionalConditionType === 1) {
					// 指定部门
					chooseRole.value = 0
					transferRightHistory.value.push({
						key: item.additionalConditionValue,
						label: item.additionalConditionLabel,
					})
				} else if (obj.additionalConditionType === 2) {
					// 自动获取部门
					chooseRole.value = 1
				}
			} else {
				if (obj.additionalConditionType === 1) {
					// 指定部门
					chooseRole.value = 0
					transferRightHistory.value.push({
						key: item.additionalConditionValue,
						label: item.additionalConditionLabel || item.additionalConditionValueLabel,
					})
				} else {
					// 自动获取部门
					chooseRole.value = 1
				}
			}
		})
	}
	reviewForm.value.isCountersign = node.isCountersign ?? true
	rejectForm.value.rejectAppointUnitViewId = node.rejectAppointUnitViewId
	nextTick(() => {
		tableRef.value.push(temp)
		flowNode.value = node
		console.log(tableRef.value.getTableData())
		historyFlowNode = JSON.parse(JSON.stringify(node))
	})
}

watch(
	() => props.node,
	(val) => val && setConfig(val)
)

onMounted(async () => {
	// await getNextOrganizationTree()
})

const loading = ref(false)
const currentCheckUsersDepartment = ref()

// const filterMethod = (query: string) => {
// 	if (query) {
// 		loading.value = true
// 		axios
// 			.request({
// 				method: 'get',
// 				url: `/api/platform/department/department-bind-users?filter=${query}`,
// 				headers: {
// 					Urlkey: 'iframeCode',
// 				},
// 			})
// 			.then((res: any) => {
// 				loading.value = false
// 				const {data} = res
// 				const arr = data.items
// 					.map((v:any) => ({
// 						...v,
// 						label: v.name,
// 						name: v.department?.parent?.name + '-' + v.department?.name + '-' + v.name,
// 						value: v?.department?.id + '/' + v.id,
// 						departmentId: v?.department?.id,
// 						isLeaf: true,
// 						disabled: false,
// 					}))
// 					.filter(
// 						(user: any) =>
// 							(user.staffRole.includes(STAFFROLEARRAY[2]) ||
// 								user.staffRole.includes(STAFFROLEARRAY[0]) ||
// 								user.staffRole.includes(STAFFROLEARRAY[3])) &&
// 							user.id !== JSON.parse(localStorage.getItem('currentUserInfo') as string).id
// 					)
// 				selectDepartmentGroup.value = useArrayToTree(
// 					arr,
// 					'id',
// 					'parentId',
// 					'name',
// 					true,
// 					'children'
// 				)

// 				// selectDepartmentGroup.value = arr
// 				// console.log(111, res)
// 			})
// 	} else {
// 		getDepartmentChildren()
// 	}
// }

const userLists: any = ref([])
const loadNode = (node: any) => {
	console.log(node)

	console.log(node.id)
	let names = node?.parent ? `${node?.parent?.name}/${node.name}` : node.name
	axios
		.request({
			method: 'get',
			url: `/api/platform/departmentInternal/${node.id}/bind-users`,
			headers: {
				Urlkey: 'iframeCode',
			},
		})
		.then((users: any) => {
			const userList =
				users.data.items
					// .filter(
					// 	(user: any) =>
					// 		(user.staffRole.includes(STAFFROLEARRAY[2]) ||
					// 			user.staffRole.includes(STAFFROLEARRAY[0]) ||
					// 			user.staffRole.includes(STAFFROLEARRAY[3])) &&
					// 		user.id !== JSON.parse(localStorage.getItem('currentUserInfo') as string).id
					// )
					.map((res: any) => ({
						label: `${res.name}`,
						value: res.id,
						departmentId: res?.department?.id,
						isLeaf: true,
						disabled: res?.department?.id ? false : true,
					})) ?? []
			console.log(userList)
			userLists.value = userList
			// resolve(node.data.children.concat(userList))
		})
}
const onOpenshowAddReview = () => {
	console.log(props)
	if (superAdmin.value) {
		showSuperAdminDialog.value = true
		transferDepartments.value = []
		autoSelectRoleForm.value = {department: 0, roles: ''}
	} else {
		if (props.formData.category === '临时报表流程') {
			showAddTable.value = true
		} else {
			showAddReview.value = true
		}
	}
}
const onChangeRadio = (val: any) => {
	form0.value.value = null
	form0.value.additionalConditionValue = null
	// tableRef.value.push([])
	userLists.value = []
}
const handleCurrentChange = (node: any) => {
	userLists.value = []
	form0.value.value = null
	isSelect.value = false
	loadNode(node)
	currentCheckUsersDepartment.value = node.departmentId
}
const isSelect = ref(true)
const conditionTypeList = [
	{
		value: '数据管理岗',
		label: '数据管理岗',
	},
	{
		value: '工作人员',
		label: '工作人员',
	},
	{
		value: '分管领导',
		label: '分管领导',
	},
]
</script>
<template>
	<div class="flow-audit-node">
		<Drawer
			v-bind="$attrs"
			class="flow-audit-node-drawer"
			:enable-close="false"
			@click-confirm="onClickConfirmDrawer"
		>
			<Block
				title="添加审核人"
				:enable-expand="false"
				:enable-back-button="false"
				:enable-expand-content="false"
				:enable-close-button="false"
				:border="false"
			>
				<div class="reviewer-btns">
					<el-button size="small" type="primary" @click="onOpenshowAddReview">
						审核人
					</el-button>

					<el-button
						size="small"
						type="danger"
						@click="tableRef.push([]), (flowNode.auditUsers = [])"
					>
						清空
					</el-button>
				</div>

				<TableV2
					ref="tableRef"
					:columns="columns"
					:auto-load="false"
					:auto-height="true"
					:enable-toolbar="false"
					:enable-create="false"
					:enable-edit="false"
					:enable-delete="false"
					:enable-index="false"
					:buttons="[
						{
							label: '删除',
							popconfirm: '确认删除吗?',
							type: 'danger',
							icon: 'i-ic-baseline-delete-forever',
						},
					]"
					@click-button="onClickTableButton"
				>
					<template #department="{row}">
						<template v-if="!superAdmin">
							{{ getDepartment(row) }}
						</template>
						<template v-else>
							{{
								chooseRole === 0
									? transferRightHistory.find(
											(f: any) => f.key === row.department
									  )?.label
									: row.department || row.additionalConditionLabel
							}}
						</template>
					</template>

					<template #roles="{row}">
						{{ row.roles === '数据领导' ? '数据管理岗' : row.roles }}
					</template>
				</TableV2>
			</Block>
			<Block
				title="审核方式"
				:enable-expand="false"
				:enable-back-button="false"
				:enable-expand-content="false"
				:enable-close-button="false"
				:border="false"
			>
				<Form
					v-model="reviewForm"
					label-width="0"
					:enable-button="false"
					:data="[
						{
							prop: 'isCountersign',
							label: '',
							type: 'radio',
							options: [
								{label: '全部审核人都需审核', value: true},
								{label: '任一审核人审核即可', value: false},
							],
						},
					]"
					class="review-form"
				></Form>
			</Block>

			<!-- <Block title="审核不通过" :enable-expand="false" :enable-back-button="false" :border="false">
				<Form
					v-model="rejectForm"
					label-width="120"
					:enable-button="false"
					:data="[
						{
							prop: 'rejectAppointUnitViewId',
							label: '审核不通过驳回到',
							type: 'select',
							options: rejectOptions,
						},
					]"
				></Form>
			</Block> -->
		</Drawer>

		<Dialog
			:destroy-on-close="true"
			title="添加审核人"
			v-model="showAddReview"
			@close="showAddReview = false"
			@click-confirm="onClickAddReview"
		>
			<FormItem
				v-model="chooseRole"
				:items="[
					{
						prop: 'chooseRole',
						type: 'radio',
						options: [
							{label: '手动指定审核人部门角色', value: 0},
							{label: '自动查询获取审核人部门角色', value: 1},
						],
					},
				]"
				@change="tableRef.clear()"
				style="margin-bottom: 5px"
			></FormItem>
			<SelectRegionOrDepartment
				v-if="chooseRole === 0"
				v-model="transferDepartments"
				v-model:role="transferDepartmentRole"
				v-model:right-raws="transferRightRaws"
				type="选择部门"
			></SelectRegionOrDepartment>
			<div v-if="chooseRole === 1">
				<div class="tips">
					<i class="icon i-ic-baseline-tips-and-updates" mr-3px></i>
					审核人指的是业务表授权中授予“审核”权限的用户
				</div>
				<Form
					v-model="addReviewForm"
					:data="addReviewFormProps"
					:enable-button="false"
					label-width="70"
				></Form>
			</div>
		</Dialog>

		<Dialog
			title="添加审核人"
			v-model="showAddTable"
			@close=";(showAddTable = false), (isSelect = true)"
			width="800"
			@click-confirm="onClicKAddTable"
		>
			<el-form ref="form0Ref" label-position="left" label-width="70px" :model="form0">
				<el-form-item>
					<el-radio-group v-model="form0.type" @change="onChangeRadio">
						<el-radio value="2">指定角色</el-radio>
						<el-radio value="3">指定用户</el-radio>
						<el-radio value="4" v-if="superAdmin">指定部门角色</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="指定" v-if="form0.type == 2">
					<el-col :span="8">
						<el-tree-select
							ref="treeRef"
							w-full
							collapse-tags
							collapse-tags-tooltip
							v-model="form0.additionalConditionValue"
							:data="props.initData.selectDepartmentGroup"
							filterable
							:props="props2"
							@current-change="handleCurrentChange"
						></el-tree-select>
					</el-col>
					<el-col :span="2" class="text-center">
						<span class="text-gray-500 pd-left-5">的</span>
					</el-col>
					<el-col :span="8">
						<el-select multiple v-model="form0.value" :disabled="isSelect" clearable>
							<el-option
								v-for="condition of conditionTypeList"
								:value="condition.value"
								:label="condition.label"
							/>
						</el-select>
					</el-col>
					<el-col :span="4" class="text-center">
						<span class="text-gray-500 pd-left-5">作为审核人</span>
					</el-col>
				</el-form-item>
				<el-form-item label="指定" v-if="form0.type == 3">
					<el-col :span="8">
						<el-tree-select
							ref="treeRef"
							w-full
							collapse-tags
							collapse-tags-tooltip
							v-model="form0.additionalConditionValue"
							:data="props.initData.selectDepartmentGroup"
							filterable
							:props="props2"
							@current-change="handleCurrentChange"
						></el-tree-select>
					</el-col>
					<el-col :span="2" class="text-center">
						<span class="text-gray-500 pd-left-5">的</span>
					</el-col>
					<el-col :span="8">
						<el-select multiple v-model="form0.value" clearable>
							<el-option
								v-for="condition of userLists"
								:value="condition.value"
								:label="condition.label"
							/>
						</el-select>
					</el-col>
					<el-col :span="4" class="text-center">
						<span class="text-gray-500 pd-left-5">作为审核人</span>
					</el-col>
				</el-form-item>
				<el-form-item label="指定" v-if="form0.type == 4">
					<el-col :span="8">
						<el-select v-model="form0.additionalConditionValue" clearable>
							<el-option
								v-for="condition of departmentOptions"
								:value="condition.value"
								:label="condition.label"
							/>
						</el-select>
					</el-col>
					<el-col :span="2" class="text-center">
						<span class="text-gray-500 pd-left-5">的</span>
					</el-col>
					<el-col :span="8">
						<el-select multiple v-model="form0.value" clearable>
							<el-option
								v-for="condition of conditionTypeList"
								:value="condition.value"
								:label="condition.label"
							/>
						</el-select>
					</el-col>
					<el-col :span="4" class="text-center">
						<span class="text-gray-500 pd-left-5">作为审核人</span>
					</el-col>
				</el-form-item>
			</el-form>
		</Dialog>

		<Dialog
			v-model="showSuperAdminDialog"
			title="选择部门"
			:destroy-on-close="true"
			@click-confirm="onClickSupper"
		>
			<FormItem
				v-model="chooseRole"
				:items="[
					{
						prop: 'chooseRole',
						type: 'radio',
						options: [
							{label: '手动指定审核人部门角色', value: 0},
							{label: '自动查询获取审核人部门角色', value: 1},
						],
					},
				]"
				@change="tableRef.clear()"
			></FormItem>
			<SelectRegionOrDepartment
				v-if="chooseRole === 0"
				v-model="transferDepartments"
				v-model:role="transferDepartmentRole"
				v-model:right-raws="transferRightRaws"
				type="选择部门"
			></SelectRegionOrDepartment>
			<Form
				v-if="chooseRole === 1"
				v-model="autoSelectRoleForm"
				:props="[
					{
						prop: 'department',
						label: '审核人所在部门',
						type: 'radio',
						options: [{label: '业务表发布部门', value: 0}],
						labelWidth: 110,
					},
					{
						prop: 'role',
						label: '审核人所属角色',
						type: 'select',
						options: roleOptions.filter(
							(item:any) =>
								item.value !== USER_ROLES_ENUM.MAIN_LEDGER &&
								item.value !== USER_ROLES_ENUM.PERSON_CHARGE
						),
						labelWidth: 110,
					},
				]"
				:enable-button="false"
			></Form>
		</Dialog>
	</div>
</template>
<style scoped lang="scss">
.reviewer-btns {
	display: flex;
	justify-content: space-between;
	padding-bottom: torem(10px);
}

:deep(.review-form) {
	.form-items {
		padding: 0 !important;
	}
}
.tips {
	align-items: center;
	color: var(--z-warning);
	display: flex;
	padding-bottom: torem(20px);
	font-weight: 500;

	i {
		color: var(--z-warning);
	}
}
</style>

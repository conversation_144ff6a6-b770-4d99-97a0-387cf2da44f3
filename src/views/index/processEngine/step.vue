<script setup lang="ts" name="step">
import {nextTick, onActivated, onDeactivated, ref, toRaw} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {SaveWorkflow, UpdateWorkflowById, GetWorkflowById} from '@/api/WorkflowApi'
import {FlowLRNodeTypes, FlowNodeTypes, FlowType} from '@/define/Workflow'
import {ElMessage, ElMessageBox} from 'element-plus'
import Step2 from './components/Step2.vue'
import {useGuid} from '@/hooks/useGuid'
import {deleteLabel, updateLabelTitle} from '@/hooks/useLabels'

const route = useRoute()
const router = useRouter()

const formProps = [
	{
		prop: 'name',
		label: '流程名称',
		type: 'text',
		placeholder: '最多20字说明所属业务(示例: 民政报表业务收发流程)',
		attrs: {
			maxlength: 20,
		},
	},
	{
		prop: 'category',
		label: '流程类型',
		type: 'select',
		placeholder: '请选择类型',
		options: [
			{label: FlowType.Initiate, value: FlowType.Initiate},
			{label: FlowType.Fill, value: FlowType.Fill},
			{label: FlowType.Export, value: FlowType.Export},
		],
		disabled: true, //!!route.query.id,
	},
	{
		prop: 'description',
		label: '流程描述',
		type: 'textarea',
		placeholder: '最多100字描述流程的作用和流程的业务场景',
		maxlength: 100,
	},
]

const formRef = ref()
const form: any = ref({})
const formRules = {name: [{required: true, message: '请输入流程名称', trigger: 'blur'}]}

const stepActive = ref(1)
const chooseTemplateIndex = ref(0)

const isFullScreen = ref(false)
const queryId = ref('')

const stepRef = ref()
const nodeConfig = ref({})
const flowDetail: any = ref({})
const flowButton: any = ref('')

const handleNodeConfig = (node: any, parentNode: any = null, depth = 1, condition: any = null) => {
	if (!node) return []

	let type: any

	switch (node.type) {
		case FlowNodeTypes.Input:
			type = FlowLRNodeTypes.StartEvent
			break
		case FlowNodeTypes.Output:
			type = FlowLRNodeTypes.EndEvent
			break
		case FlowNodeTypes.Gateway:
			type = FlowLRNodeTypes.GatewayXor
			break
		case FlowNodeTypes.Review:
			type = FlowLRNodeTypes.UserTask
			break
		case FlowNodeTypes.Report:
			type = FlowLRNodeTypes.Custom
			break
		default:
			type = FlowLRNodeTypes.Default
			break
	}

	const currentValue: any = [
		// 节点
		{
			viewId: node.id,
			name: `${node.label}`,
			type,
			// 审核节点
			auditUsers: toRaw(node.auditUsers) ?? null,
			isCountersign: node.isCountersign,
			countersignAgain: node.countersignAgain ?? 0,
			countersignAllType: node.countersignAllType ?? 0,
			countersignType: node.countersignType ?? 0,
			rejectType: node.rejectType ?? 0,
			rejectAppointUnitViewId: node.rejectAppointUnitViewId ?? null,

			ico:
				FlowNodeTypes.Report === node.type && FlowType.Initiate === form.value.category
					? 'LedgerFilling'
					: null,
		},
	]

	if (FlowNodeTypes.Review === node.type) {
		currentValue[0]['noAuditor'] = 2
	}

	if (parentNode) {
		// 线条
		const guid = useGuid()

		currentValue.push({
			viewId: guid,
			name: `线条${depth - 1}`,
			fromViewId: parentNode.id,
			toViewId: node.id,
			type: FlowLRNodeTypes.MyLine,
			lineConditions: condition ? [condition?.code] : null,
		})
	}

	const end = stepRef.value.flowRef.getWorkflowEndNode()
	let childNodeValues: any = []

	if (node.childNode && node.childNode.length > 0) {
		childNodeValues = node.childNode.flatMap((child: any) =>
			handleNodeConfig(child, node, depth + 1)
		)
	} else if (
		node.type !== FlowNodeTypes.Gateway &&
		(node.childNode || node.childNode?.length === 0 || node.childNode === null)
	) {
		// 非条件节点下无子节点
		childNodeValues.push({
			viewId: useGuid(),
			name: `线条${depth}`,
			fromViewId: node.id,
			toViewId: end.id,
			type: FlowLRNodeTypes.MyLine,
		})
	}

	if (node.conditionNodes && node.conditionNodes.length > 0) {
		// 条件节点没有配置
		if (node.conditionNodes.some((condition: any) => !condition.config)) {
			return null
		}

		// 排他网关
		node.conditionNodes.forEach((condition: any) => {
			// 排他网关配置
			if (condition.config) {
				if (currentValue[0]['conditions']) {
					currentValue[0]['conditions'].push(toRaw(condition.config))
				} else {
					currentValue[0]['conditions'] = [toRaw(condition.config)]
				}
			}

			if (condition.childNode.length > 0) {
				childNodeValues = childNodeValues.concat(
					condition.childNode.flatMap((child: any) =>
						handleNodeConfig(child, node, depth + 1, condition?.config)
					)
				)
			} else {
				// 条件下无子节点
				childNodeValues.push({
					viewId: useGuid(),
					name: `线条${depth}`,
					fromViewId: node.id,
					toViewId: end.id,
					type: FlowLRNodeTypes.MyLine,
					lineConditions: condition?.config ? [condition.config.code] : null,
				})
			}
		})
	}

	return currentValue.concat(childNodeValues)
}

const onStep = (number: number) => {
	formRef.value.validate((valid: boolean) => {
		if (valid) {
			if (stepActive.value < 3 || stepActive.value >= 1) {
				stepActive.value += number
			}

			if (number === 1) {
				setTimeout(() => stepRef.value.flowRef.getInstance().fitView(), 1)
			}

			onFullScreenChanged(isFullScreen.value)
		}
	})
}

const onFormItemChange = (val: any, item: any) => {
	if (val === FlowType.Initiate) {
		chooseTemplateIndex.value = 0
		flowButton.value = `${FlowNodeTypes.Condition},${FlowNodeTypes.Review}`
	}

	if (val === FlowType.Fill) {
		chooseTemplateIndex.value = 3

		flowButton.value = `${FlowNodeTypes.Condition},${FlowNodeTypes.Review}` //,${FlowNodeTypes.Report}
	}
}

const onToggleDefaultWorkflow = (index: number) => {
	ElMessageBox.confirm('确定切换流程模板吗？使用后，现有已设置流程将被替换', '警告', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(() => {
		console.log('选择默认流程:', index)
		chooseTemplateIndex.value = index
	})
}

const onSaveWorkflow = () => {
	console.log('保存流程 节点:', nodeConfig.value)

	const wfData = handleNodeConfig(nodeConfig.value)
	console.log(111, wfData)
	if (wfData.some((x: any) => x === null)) {
		ElMessage.warning('请配置条件节点')
		return
	}

	const noAuditUser = wfData.find(
		(x: any) => x.type === FlowLRNodeTypes.UserTask && !x.auditUsers
	)
	if (noAuditUser) {
		ElMessage.warning(`请配置${noAuditUser.name}节点的审核人员`)
		return
	}

	const endNode = stepRef.value.flowRef.getWorkflowEndNode()
	wfData.push({
		viewId: endNode.id,
		name: endNode.label,
		type: FlowLRNodeTypes.EndEvent,
	})
	wfData.forEach((item: any) => {
		if (item.auditUsers && item.auditUsers.length > 0) {
			item.auditUsers.forEach((it2: any) => {
				if (it2.value === '数据管理岗') {
					it2.value = '数据领导'
				}
			})
		}
	})
	const data = {
		code: useGuid(),
		...form.value,
		scheme: {
			extend: JSON.stringify(nodeConfig.value),
			wfData,
		},
	}
	console.log('保存流程 数据:', data)

	if (queryId.value) {
		const {code, scheme} = flowDetail.value

		data.code = code
		data.scheme.id = scheme.id

		UpdateWorkflowById(queryId.value, data)
			.then((res: any) => {
				console.log('更新流程:', res)
				ElMessage.success('更新成功')
				deleteLabel({
					path: router.currentRoute.value.fullPath,
				})
			})
			.catch((err: any) => {
				window.errMsg(err, '更新流程')
			})
	} else {
		SaveWorkflow(data)
			.then((res: any) => {
				console.log('保存流程:', res)
				ElMessage.success('保存成功')
				deleteLabel({
					path: router.currentRoute.value.fullPath,
				})
			})
			.catch((err: any) => {
				window.errMsg(err, '保存流程')
			})
	}
}

const onFullScreenChanged = (bool: boolean) => {
	nextTick(() => {
		if (stepActive.value === 2) {
			stepRef.value.size(bool)
		}
		isFullScreen.value = bool
	})
}

const init = () => {
	stepActive.value = 1
	chooseTemplateIndex.value = 0
	queryId.value = route.query.id as string

	form.value = {
		name: '',
		category: route.query?.category || FlowType.Initiate,
		description: '',
	}

	const next = () => {
		flowButton.value =
			form.value.category === FlowType.Initiate
				? //业务表
				  `${FlowNodeTypes.Condition},${FlowNodeTypes.Review}`
				: // 填报
				  `${FlowNodeTypes.Condition},${FlowNodeTypes.Review}` // ${FlowNodeTypes.Report}

		updateLabelTitle({
			path: router.currentRoute.value.fullPath,
			title: queryId.value ? `编辑-流程设计-${form.value.name}` : '新增-流程设计',
		})
	}

	if (queryId.value) {
		GetWorkflowById(queryId.value).then((res: any) => {
			const {data} = res
			const {extend} = data.scheme

			form.value = {
				name: data.name,
				category: data.category,
				description: data.description,
			}

			flowDetail.value = data
			nodeConfig.value = JSON.parse(extend)

			chooseTemplateIndex.value = -1

			next()
		})
	} else {
		next()
	}
}

onActivated(() => {
	console.log('进入流程')
	init()
})

onDeactivated(() => {
	console.log('离开流程')
	chooseTemplateIndex.value = -1
})
</script>
<template>
	<div class="flow-step">
		<Block
			:title="`${queryId ? '编辑' : '创建'}流程`"
			:enable-fixed-height="true"
			:enable-full-screen="false"
			:enable-expand-content="false"
			@full-screen-changed="onFullScreenChanged"
			class="flow-step"
		>
			<template #topRight>
				<div class="buttons">
					<el-button
						size="small"
						v-if="stepActive > 1"
						type="primary"
						@click="onStep(-1)"
					>
						上一步
					</el-button>
					<el-button size="small" v-if="stepActive < 2" type="primary" @click="onStep(1)">
						下一步
					</el-button>
					<el-button size="small" v-else type="primary" @click="onSaveWorkflow"
						>保存</el-button
					>
				</div>
			</template>

			<template #topCenter>
				<div class="steps">
					<span :class="{active: stepActive === 1}">1</span>
					<span>基础设置</span>
					<span :class="{active: stepActive === 2}">2</span>
					<span>流程设计</span>
				</div>
			</template>

			<div class="workflow-helper" v-if="stepActive === 2">
				<h2>流程助手</h2>
				<p>一键启用精选流程模板，提升效率</p>
				<ul class="shadow-24">
					<!-- 发起 -->
					<template v-if="form.category === FlowType.Initiate">
						<li @click="onToggleDefaultWorkflow(0)">
							<h3>业务表填报流程</h3>
							<p>业务表数据填报仅部门内部审核的流程</p>
						</li>
						<li @click="onToggleDefaultWorkflow(1)">
							<h3>业务表填报多级审核流程</h3>
							<p>针对于需要多部门多级审核流程</p>
						</li>
						<li @click="onToggleDefaultWorkflow(2)">
							<h3>业务表按授权顺序审核流程</h3>
							<p>业务表审核顺序与授权顺序相反</p>
						</li>
					</template>
					<template v-if="form.category === FlowType.Fill">
						<!-- 填报 -->
						<li @click="onToggleDefaultWorkflow(3)">
							<h3>填报数据由分管领导审核后提交</h3>
							<p>针对于需要分管领导审核的报表流程</p>
						</li>
						<li @click="onToggleDefaultWorkflow(4)">
							<h3>填报数据由数据管理岗审核后提交</h3>
							<p>针对于需要数据管理岗审核的报表流程</p>
						</li>
						<li @click="onToggleDefaultWorkflow(5)">
							<h3>填报数据多级审核</h3>
							<p>针对于填报数据需要多级审核的场景</p>
						</li>
					</template>
				</ul>
			</div>

			<Form
				ref="formRef"
				v-show="stepActive === 1"
				v-model="form"
				:data="formProps"
				:enable-button="false"
				:rules="formRules"
				label-width="80px"
				style="margin: auto; width: 80%"
				@change="onFormItemChange"
			>
			</Form>

			<Step2
				ref="stepRef"
				v-model="nodeConfig"
				v-show="stepActive === 2"
				:formData="form"
				:template-index="chooseTemplateIndex"
				:showButton="flowButton"
			></Step2>
		</Block>
	</div>
</template>
<route>
	{
		meta: {
			childTitle: '',
		},
	}
</route>
<style scoped lang="scss">
.flow-step {
	background-color: #fff;
	height: 100%;
	position: relative;

	.steps {
		align-items: center;
		display: flex;
		justify-content: flex-start;
		width: 100%;

		span:nth-child(odd) {
			border-radius: 100%;
			background-color: var(--z-bg-secondary);
			color: var(--z-font-color);
			height: 20px;
			line-height: 20px;
			margin-left: 35px;
			margin-right: 3px;
			text-align: center;
			transform: scale(0.8);
			width: 20px;
			&.active {
				background-color: var(--z-main);
				color: var(--z-bg-secondary);
			}
		}
	}

	.workflow-helper {
		border-radius: 5px;
		background-color: var(--z-bg-secondary);
		cursor: pointer;
		left: 25px;
		position: absolute;
		padding: 10px;
		transition: all 0.15s linear;
		top: 65px;
		width: 230px;
		z-index: 1;

		&:hover {
			background-color: var(--z-main);
			h2 {
				color: #fff;
				+ p {
					color: #fff;
				}
			}

			ul {
				border: 1px solid var(--z-line);
				height: 196px;
				opacity: 1;
			}
		}

		h2 {
			font-size: 14px;
			transition: all 0.15s linear;
			padding-bottom: 10px;
		}

		h3 {
			font-size: 13px;
			padding-bottom: 10px;
		}

		p {
			font-size: 12px;
			opacity: 0.3;
			transition: all 0.15s linear;
		}

		ul {
			border: 1px solid transparent;
			border-radius: 5px;
			background-color: #fff;
			display: flex;
			flex-direction: column;
			height: 0;
			justify-content: space-between;
			left: 0;
			opacity: 0;
			overflow: hidden;
			position: absolute;
			top: 101%;
			transition: all 0.15s linear;
			width: 100%;
			z-index: -1;

			li {
				border-bottom: 1px solid var(--z-line);
				display: flex;
				flex-direction: column;
				justify-content: center;
				padding: 10px;
				transition: all 0.15s linear;
				flex: 1;

				&:hover {
					background-color: var(--z-bg-secondary);
				}

				&:last-child {
					border-bottom: none;
				}
			}
		}
	}

	.buttons {
		align-items: center;
		display: flex;
		padding: 20px 0;
		justify-content: center;
	}
}
</style>

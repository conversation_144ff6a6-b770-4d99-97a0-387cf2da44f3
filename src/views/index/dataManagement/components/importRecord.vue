<script setup lang="ts" name="template">
import { ref, reactive, defineProps } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useSleep } from "@/hooks/useSleep";
import { ACTION_KEY, useLocalStorage } from "@/hooks/useLocalStorage";
import { ElMessageBox, ElMessage, dayjs } from "element-plus";
import { time } from "echarts";
const storage: any = useLocalStorage();
const router = useRouter();
const route = useRoute();
// 搜索表单
const searchFormProp = ref([
  { label: "业务表名称", prop: "name", type: "text", placeholder: "请输入业务表名称" },
]);
const searchForm = ref({ name: "" });
const props = defineProps({
  selectedRows: { type: Array, default: () => [] },
});
// 加载状态
const loading = ref(false);
const tableData = ref([
  {
    name: "户厕改造业务表", // 业务表名称
    version: "1.0.0", // 版本号
    updateTime: dayjs().format("YYYY-MM-DD HH:mm:ss"), // 数据更新周期
    createUser: "陈航", // 数据更新周期
  },
  {
    name: "失业人口登记表", // 业务表名称
    version: "1.0.0", // 版本号
    updateTime: dayjs().format("YYYY-MM-DD HH:mm:ss"), // 数据更新周期
    createUser: "刘树林", // 数据更新周期
  },
]);
// 表格
const tableRef = ref();
const tableHeight = ref(0);
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 75; // Block 内容高度 - 分页高度
};
const currentRow = ref(null);
const buttons = [
  { label: "查看", type: "primary", code: "view" },
  { label: "回滚", type: "primary", code: "rollback" },
  { label: "对比", type: "primary", code: "contrast" },
]; // 操作按钮
const columns = [
  { prop: "version", label: "版本号" },
  { prop: "name", label: "业务表名称" },
  { prop: "updateTime", label: "版本更新时间" },
  { prop: "createUser", label: "创建人" },
]; // 表头

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
});

// 列表请求参数
const reqParams = reactive({
  name: "",
});
const statisticsList = ref([]);
const selectionChange = (selection: []) => {
  statisticsList.value = selection;
};
const tableDataView = ref([
  {
    belongVillage: "中山路社区",
    name: "李海",
    gender: "男",
    birthDate: "1990-01-01",
    idCard: "500**************78",
    education: "本科",
    nowAddress: "重庆市-永川区",
    phone: "138****8000",
    isFreeJobTraining: "是",
    isJobInfo: "是",
    jobStatus: "已就业",
    dataSource: "用户新增",
    updateTime: "2022-01-01",
  },
  {
    belongVillage: "中山路社区",
    name: "张海燕",
    gender: "女",
    birthDate: "1990-01-01",
    idCard: "510**************78",
    education: "本科",
    nowAddress: "重庆市-永川区",
    phone: "138****8000",
    isFreeJobTraining: "是",
    isJobInfo: "是",
    jobStatus: "待就业",
    dataSource: "用户新增",
    updateTime: "2022-01-01",
  },
]);
const tableDataHistory = ref([
  {
    belongVillage: "中山路社区",
    name: "李海",
    gender: "男",
    birthDate: "1990-01-01",
    idCard: "500**************78",
    education: "本科",
    nowAddress: "重庆市-永川区",
    phone: "138****8000",
    isFreeJobTraining: "是",
    isJobInfo: "是",
    jobStatus: "已就业",
    dataSource: "用户新增",
    updateTime: "2022-01-01",
  },
  {
    belongVillage: "中山路社区",
    name: "张海燕",
    gender: "女",
    birthDate: "1990-01-01",
    idCard: "510**************78",
    education: "本科",
    nowAddress: "重庆市-永川区",
    phone: "138****8000",
    isFreeJobTraining: "是",
    isJobInfo: "是",
    jobStatus: "待就业",
    dataSource: "用户新增",
    updateTime: "2022-01-01",
  },
]);

// 查询
const onSearch = () => {
  pagination.page = 1;
  //   reqParams.skipCount = 0;
  //   reqParams.maxResultCount = pagination.size;
  // 其他查询参数
  reqParams.name = searchForm.value.name;
  tableData.value.filter((item: any) => {
    return item.name.includes(reqParams.name);
  });
};
const viewDialogVisible = ref(false);
const contrastDialogVisible = ref(false);
// 表格操作点击事件, row 当前行数据
const onTableClickButton = ({ row, btn }: any) => {
  if (btn.code == "view") {
    viewDialogVisible.value = true;
  }
  if (btn.code == "rollback") {
    ElMessageBox.confirm("确定要回滚吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        ElMessage.success("回滚成功");
      })
      .catch(() => {});
  }
  if (btn.code == "contrast") {
    contrastDialogVisible.value = true;
  }
};

// 分页事件
const onPaginationChange = (val: any, type: any) => {
  //   if (type == "page") {
  //     pagination.page = val;
  //     reqParams.skipCount = (val - 1) * pagination.size;
  //   } else {
  //     pagination.size = val;
  //     reqParams.maxResultCount = pagination.size;
  //   }
};
const onClickLink = (type: number) => {
  loading.value = true;
  if (type == 1) {
    tableData.value.forEach((item: any) => {
      statisticsList.value.forEach((item2: any) => {
        if (item.name == item2.name) {
          item.status = "已缓存";
        }
      });
    });
    storage.save("taskLinkData", tableData.value);
    setTimeout(() => {
      loading.value = false;
      ElMessage.success("关联成功");
    }, 1000);
  } else if (type == 2) {
    storage.save("taskCacheData", tableData.value);
    setTimeout(() => {
      loading.value = false;
      ElMessage.success("写入成功");
    }, 1000);
  } else if (type == 3) {
    tableData.value.forEach((item: any) => {
      statisticsList.value.forEach((item2: any) => {
        if (item.name == item2.name) {
          item.status = "未缓存";
        }
      });
    });
    storage.save("taskCacheData", tableData.value);
    setTimeout(() => {
      loading.value = false;
      ElMessage.success("清除成功");
    }, 1000);
  }
};

const handleLink = (row: any) => {
  loading.value = true;
  let flag = row.status;
  row.status = row.status !== "未缓存" ? row.status : "已缓存";
  storage.save("taskCacheData", tableData.value);
  setTimeout(() => {
    loading.value = false;
    ElMessage.success(flag === "未缓存" ? "读取成功" : "写入成功");
    storage.save("taskCacheData", tableData.value);
  }, 1000);
};

const statisticsList1 = ref([]);
const selectedRows = ref([]);

const onBack = () => {
  router.back();
};
onMounted(() => {
  tableData.value = storage.get("taskCacheData")
    ? storage.get("taskCacheData")
    : [
        {
          name: "户厕改造业务表", // 业务表名称
          departmentName: "永川区-供销社", // 发布部门
          belongBlock: "党的建设", // 所属板块
          reminderInterval: "每周", // 数据更新周期
          cacheTime: dayjs().format("YYYY-MM-DD HH:mm:ss"), // 截止时间
          cacheExpire: "未过期", // 数据更新周期
          status: "已缓存", // 状态
        },
        {
          name: "失业人口登记表", // 业务表名称
          departmentName: "永川区-财政局", // 发布部门
          belongBlock: "经济发展", // 所属板块
          reminderInterval: "每日", // 数据更新周期
          cacheTime: dayjs().format("YYYY-MM-DD HH:mm:ss"), // 截止时间
          cacheExpire: "已过期", // 数据更新周期
          status: "未缓存", // 状态
        },
      ];
});
</script>
<template>
  <Dialog v-bind="$attrs" title="导入记录">
    <div class="template">
    <Block
      title="导入记录"
      :enable-fixed-height="true"
      :enable-expand-content="true"
      :enable-back-button="false"
      @height-changed="onBlockHeightChanged"
    >
      <template #topRight>
        <el-button @click="onBack"> 返回 </el-button>
      </template>
      <template #expand>
        <!-- 搜索 -->
        <div class="search">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="2"
            :label-width="120"
            :enable-reset="false"
            confirm-text="查询"
            button-vertical="flowing"
            @submit="onSearch"
          ></Form>
        </div>
      </template>
      <!-- 列表 -->
      <TableV2
        ref="tableRef"
        v-model="tableData"
        :columns="columns"
        :req-params="reqParams"
        :enable-toolbar="false"
        :enable-own-button="false"
        :enable-selection="true"
        :height="tableHeight"
        :buttons="buttons"
        :loading="loading"
        @click-button="onTableClickButton"
        @completed="
          () => {
            pagination.total = tableRef.getTotal();
          }
        "
        @selection-change="selectionChange"
      >
      </TableV2>
      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.size"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      ></Pagination>
      <Dialog v-model="viewDialogVisible" title="导入记录" width="1400">
        <el-table :data="tableDataView">
          <el-table-column type="index" label="序号" width="60"></el-table-column>
          <el-table-column
            prop="belongVillage"
            label="所属村社"
            width="130"
          ></el-table-column>
          <el-table-column prop="name" label="姓名"></el-table-column>
          <el-table-column prop="gender" label="性别"></el-table-column>
          <el-table-column
            prop="birthDate"
            label="出生日期"
            width="150"
          ></el-table-column>
          <el-table-column prop="idCard" label="身份证号" width="150"></el-table-column>
          <el-table-column prop="education" label="学历"></el-table-column>
          <el-table-column
            prop="nowAddress"
            label="现在家庭住址"
            width="200"
          ></el-table-column>
          <el-table-column prop="phone" label="联系电话" width="150"></el-table-column>
          <el-table-column
            prop="isFreeJobTraining"
            label="是否提供免费就业培训"
          ></el-table-column>
          <el-table-column prop="isJobInfo" label="是否提供招聘信息"></el-table-column>
          <el-table-column prop="jobStatus" label="就业状态"></el-table-column>
          <el-table-column prop="dataSource" label="数据来源"></el-table-column>
          <el-table-column
            prop="updateTime"
            label="更新时间"
            width="150"
          ></el-table-column>
        </el-table>
      </Dialog>
      <Dialog v-model="contrastDialogVisible" title="版本对比" width="1400">
        <div style="display: flex; justify-content: space-between">
          <div style="width: 650px">
            当前版本:
            <el-table :data="tableDataView" fit="false">
              <el-table-column type="index" label="序号" width="60"></el-table-column>
              <el-table-column
                prop="belongVillage"
                label="所属村社"
                width="130"
              ></el-table-column>
              <el-table-column prop="name" label="姓名"></el-table-column>
              <el-table-column prop="gender" label="性别"></el-table-column>
              <el-table-column
                prop="birthDate"
                label="出生日期"
                width="150"
              ></el-table-column>
              <el-table-column
                prop="idCard"
                label="身份证号"
                width="150"
              ></el-table-column>
              <el-table-column prop="education" label="学历"></el-table-column>
              <el-table-column
                prop="nowAddress"
                label="现在家庭住址"
                width="200"
              ></el-table-column>
              <el-table-column
                prop="phone"
                label="联系电话"
                width="150"
              ></el-table-column>
              <el-table-column
                prop="isFreeJobTraining"
                label="是否提供免费就业培训"
              ></el-table-column>
              <el-table-column
                prop="isJobInfo"
                label="是否提供招聘信息"
              ></el-table-column>
              <el-table-column prop="jobStatus" label="就业状态"></el-table-column>
              <el-table-column prop="dataSource" label="数据来源"></el-table-column>
              <el-table-column
                prop="updateTime"
                label="更新时间"
                width="150"
              ></el-table-column>
            </el-table>
          </div>
          <div style="width: 650px">
            历史版本:
            <el-table :data="tableDataHistory" fit="false">
              <el-table-column type="index" label="序号" width="60"></el-table-column>
              <el-table-column
                prop="belongVillage"
                label="所属村社"
                width="130"
              ></el-table-column>
              <el-table-column prop="name" label="姓名"></el-table-column>
              <el-table-column prop="gender" label="性别"></el-table-column>
              <el-table-column
                prop="birthDate"
                label="出生日期"
                width="150"
              ></el-table-column>
              <el-table-column
                prop="idCard"
                label="身份证号"
                width="150"
              ></el-table-column>
              <el-table-column prop="education" label="学历"></el-table-column>
              <el-table-column
                prop="nowAddress"
                label="现在家庭住址"
                width="200"
              ></el-table-column>
              <el-table-column
                prop="phone"
                label="联系电话"
                width="150"
              ></el-table-column>
              <el-table-column
                prop="isFreeJobTraining"
                label="是否提供免费就业培训"
              ></el-table-column>
              <el-table-column
                prop="isJobInfo"
                label="是否提供招聘信息"
              ></el-table-column>
              <el-table-column prop="jobStatus" label="就业状态"></el-table-column>
              <el-table-column prop="dataSource" label="数据来源"></el-table-column>
              <el-table-column
                prop="updateTime"
                label="更新时间"
                width="150"
              ></el-table-column>
            </el-table>
          </div>
        </div>
      </Dialog>
    </Block>
  </div>
  </Dialog>
 
</template>
<route>
{
meta: {
title: '导入记录',
},
}
</route>
<style scoped lang="scss"></style>

<script lang="ts" setup name="tasklink">
import { onMounted, ref, reactive, computed } from "vue";
import { useRouter } from "vue-router";
import { allRegionNames, myRegionNames } from "@/api/LedgerApi";
import importRecord from "./components/importRecord.vue";
import {
  // 删除任务绑定台账和台账配置
  deleteLedgerApi,
  // 确认关联
  makeSurePlanProgressConfigApi,
} from "@/api/taskLinkApi";
import { ElMessage, ElMessageBox } from "element-plus";


const router = useRouter();

// 子任务弹窗列表请求参数
const reqParamsSon = reactive({
  TaskName: "", // 任务名称
  Status: "", // 电信任务状态：1-待关联，2-已关联
  parentId: "",

  skipCount: 0,
  maxResultCount: 10,
});
// 获取村社列表
const regionNamesOptions = ref<any>([]);
function getRegionNames() {
  allRegionNames().then((res) => {
    if (res.data.length > 0) {
      regionNamesOptions.value = res.data;
      // regionNamesOptions.value.unshift({
      // 	id: 1,
      // 	name: '本镇街',
      // })
    }
  });
}
// 搜索
const formItems = ref([
  // {
  // 	prop: 'RegionId',
  // 	type: 'text',
  // 	placeholder: '请输入任务名称',
  // 	formShow:
  // 		JSON.parse(localStorage.getItem('currentUserInfo') as string).baseRoles.includes(
  // 			'管理员'
  // 		) &&
  // 		JSON.parse(localStorage.getItem('currentDepartmentInfo') as any).community == null &&
  // 		JSON.parse(localStorage.getItem('currentDepartmentInfo') as any).street == null &&
  // 		JSON.parse(localStorage.getItem('currentDepartmentInfo') as any).district == null,
  // },
  {
    prop: "TelecomCode",
    type: "text",
    placeholder: "请输入任务名称",
    formShow:
      JSON.parse(localStorage.getItem("currentUserInfo") as string).staffRole.includes(
        "区县台账运维员"
      ) ||
      (JSON.parse(localStorage.getItem("currentUserInfo") as string).baseRoles.includes(
        "管理员"
      ) &&
        JSON.parse(localStorage.getItem("currentDepartmentInfo") as any).community ==
          null &&
        JSON.parse(localStorage.getItem("currentDepartmentInfo") as any).street == null &&
        JSON.parse(localStorage.getItem("currentDepartmentInfo") as any).district !==
          null),
  },
  {
    prop: "TaskName",
    type: "text",
    placeholder: "请输入任务名称",
  },
  {
    prop: "Runway",
    type: "select",
    placeholder: "请选择任务所属板块",
    options: [
      // 1-待关联，2-已关联
      { label: "党的建设", value: "党的建设" },
      { label: "经济发展", value: "经济发展" },
      { label: "民生服务", value: "民生服务" },
      { label: "平安法治", value: "平安法治" },
    ],
  },
  {
    prop: "date",
    type: "daterange",
  },

  // {
  // 	prop: 'Status',
  // 	type: 'select',
  // 	placeholder: '请选择任务状态',
  // 	options: [
  // 		// 1-待关联，2-已关联
  // 		{label: '待关联', value: 1},
  // 		{label: '已关联', value: 2},
  // 	],
  // },
]);
const searchForm = ref<any>({});
const importRecordVisible = ref(false);

enum RunwayType {
  "所有任务", // 不传
  "党的建设",
  "经济发展",
  "民生服务",
  "平安法治",
}

let numList = ref<any>({
  所有任务: 0, // 不传
  党的建设: 0,
  经济发展: 0,
  民生服务: 0,
  平安法治: 0,
});

const activeName = ref(RunwayType[0]);

const reqParams = reactive({
  Runway: "", // 党的建设、经济发展、民生服务、平安法治
  TaskName: "", // 任务名称
  StartTime: "", // 开始时间
  EndTime: "",
  Status: "", // 电信任务状态：1-待关联，2-已关联
  telecomCode: "",
  regionId: "",
  skipCount: 0,
  maxResultCount: 10,
});

// 搜索
const onSearch = (type?: string) => {
  if (type === "search") {
    reqParams.TaskName = searchForm.value?.TaskName;
    reqParams.StartTime = searchForm.value?.date?.[0];
    reqParams.EndTime = searchForm.value?.date?.[1];
    reqParams.Status = searchForm.value?.Status;
    reqParams.Runway = searchForm.value?.Runway;
    reqParams.telecomCode = searchForm.value?.TelecomCode;
    reqParams.regionId = searchForm.value?.RegionId;
  }

  if (type === "clear") {
    reqParams.TaskName = "";
    reqParams.StartTime = "";
    reqParams.EndTime = "";
    reqParams.Status = "";
    reqParams.Runway = "";
    reqParams.telecomCode = "";
    reqParams.regionId = "";
    searchForm.value = {};
  }
};

// 表格
const tableRef = ref();
const tableHeight = ref(0);
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 75; // Block 内容高度 - 分页高度
};

// 任务状态 1-待关联，2-已关联
const FlowTaskStatus = [
  { label: "待关联", value: 1, color: "#ff8515" },
  { label: "已关联", value: 2, color: "#78bf4a" },
];

const tableColumns = ref([
  { prop: "taskName", label: "任务名称", width: 200 },
  { prop: "belongBlock", label: "所属板块" }, //
  { prop: "ledgerRunway", label: "核心业务", width: 200 }, //
  { prop: "pushType", label: "任务来源", width: 200 },
  { prop: "streetTown", label: "所属街镇", width: 300 },
  { prop: "leaders", label: "牵头领导" },
  { prop: "date", label: "起止日期", width: 300 },
  { prop: "chargePersons", label: "责任人", width: 140 }, //chargePersons[] .chargePersonUsername
  // string (1任意责任人完成即可 0全部责任人均需完成)
  { prop: "taskCompleteCon", label: "完成条件", width: 250 },
  { prop: "targetName", label: "关联目标", width: 300 },
  { prop: "ledgerName", label: "关联业务表", width: 200 },
  { prop: "childCount", label: "子任务" },

  { prop: "status", label: "关联状态", fixed: "right" }, // 1-待关联，2-已关联
  { prop: "progress", label: "进度", fixed: "right" },
]);

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
});

// 顶部tab切换
const onClickTabs = (val: any) => {
  console.log("onClickTabs----", val);
  activeName.value = val;
  val === "所有任务" ? (reqParams.Runway = "") : (reqParams.Runway = val);

  if (val !== "所有任务" && tableColumns.value[1].label === "所属板块") {
    tableColumns.value.splice(1, 1);
  } else if (val === "所有任务") {
    tableColumns.value.splice(1, 0, { prop: "belongBlock", label: "所属板块" });
  }
};

const onTableCompleted = () => {
  pagination.total = tableRef.value.getTotal();

  console.log("onTableCompleted", pagination.total);
};

function handleDetail(row: any) {
  console.log("handleDetail", row);
  if (row.status === 1) {
    onTableButtonClick({ btn: { code: "link" }, row });
  } else if (row.status === 2) {
    onTableButtonClick({ btn: { code: "detail" }, row });
  }
}
// 点击表格的操作按钮
const onTableButtonClick = ({ btn, row, index }: any) => {
  console.log("点击表格的操作按钮---", btn.code, row, index);
  if (btn.code === "showSon") {
    reqParamsSon.parentId = row.id;

    return (dialogSon.value = true);
  }

  let path;
  if (btn.code === "detail") {
    // if (row.ledgerId) row.ledgerId ? (path = '/dataManagement/detail') : '/dataManagement/link' // 测试
    path = "/dataManagement/detail";
  } else if (btn.code === "link") {
    path = "/dataManagement/link";
  }
  router.push({
    path,
    query: {
      id: row.id,
      type: btn.code,
    },
  });
};
onMounted(() => {
  // getNum()
  getRegionNames();
});

const getNum = async () => {
  // const res0 = await getListApi({Runway: ''})
  // numList.value['所有任务'] = res0.data.totalCount
  // const res1 = await getListApi({Runway: '党的建设'})
  // numList.value['党的建设'] = res1.data.totalCount
  // const res2 = await getListApi({Runway: '经济发展'})
  // numList.value['经济发展'] = res2.data.totalCount
  // const res3 = await getListApi({Runway: '民生服务'})
  // numList.value['民生服务'] = res3.data.totalCount
  // const res4 = await getListApi({Runway: '平安法治'})
  // numList.value['平安法治'] = res4.data.totalCount
};

const onPaginationChange = (val: any, type: any) => {
  if (type == "page") {
    pagination.page = val;
    reqParams.skipCount = (val - 1) * pagination.size;
  } else {
    pagination.size = val;
    reqParams.maxResultCount = pagination.size;
  }
};

// 子任务列表弹窗
let dialogSon: any = ref(false);
const tableHeightSon = ref(0);

const heightChanged = (height: any) => {
  tableHeightSon.value = height - 75; // Block 内容高度 - 分页高度
};
let currentUserInfoRole = JSON.parse(localStorage.getItem("currentUserInfo") as any);
let currentDepartmentInfo = JSON.parse(
  localStorage.getItem("currentDepartmentInfo") as any
);
// 搜索表单star
const searchFormSon = ref<any>({});

const formItemsSon = ref([
  {
    prop: "TaskName",
    type: "text",
    placeholder: "请输入任务名称",
  },
  // {
  // 	prop: 'Status',
  // 	type: 'select',
  // 	placeholder: '请选择任务状态',
  // 	options: [
  // 		// 1-待关联，2-已关联
  // 		{label: '待关联', value: 1},
  // 		{label: '已关联', value: 2},
  // 	],
  // },
]);

const onSearchSon = (type?: string) => {
  if (type === "search") {
    reqParamsSon.TaskName = searchFormSon.value?.TaskName;
    reqParamsSon.Status = searchFormSon.value?.Status;
  }

  if (type === "clear") {
    reqParamsSon.TaskName = "";
    reqParamsSon.Status = "";
  }
};

// 搜索表单end

// 子任务表格
const tableRefSon = ref();

const tableColumnsSon = ref([
  { prop: "taskName", label: "任务名称", width: 200 },
  { prop: "endTime", label: "完成截止时间", width: 200 }, //
  { prop: "ledgerName", label: "关联业务表", width: 200 },
  { prop: "status", label: "关联状态" }, // 1-待关联，2-已关联
  { prop: "progress", label: "进度" },
]);

const tableButtonsSon = ref([
  {
    code: "detail",
    label: "查看",
    show: `row.status ===2`,
  },
  {
    code: "link",
    label: "关联业务表",
    type: "primary",
    show: `row.status ===1`,
  },
]);

// const pagination = reactive({
// 	page: 1,
// 	size: 10,
// 	total: 0,
// }) f5271b5aa81b41c78ef241d58d0230007

const onTableCompletedSon = () => {
  paginationSon.total = tableRefSon.value.getTotal();
};
// 点击表格的操作按钮
const onTableButtonClickSon = ({ btn, row, index }: any) => {
  let path = "";
  if (btn.code === "detail") {
    path = "/dataManagement/detailSon";
  } else if (btn.code === "link") {
    path = "/dataManagement/linkSon";
  }
  dialogSon.value = false;
  router.push({
    path,
    query: {
      id: row.id,
      type: btn.code,
    },
  });
};
const paginationSon = reactive({
  page: 1,
  size: 10,
  total: 0,
});

const onPaginationChangeSon = (val: any, type: any) => {
  if (type == "page") {
    paginationSon.page = val;
    reqParamsSon.skipCount = (val - 1) * paginationSon.size;
  } else {
    paginationSon.size = val;
    reqParamsSon.maxResultCount = paginationSon.size;
  }
};

// 更改配置
const openConfig = (id: string) => {
  ElMessageBox.confirm("更改配置后已计算的任务进度将会重新计算，是否更改？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      router.push({
        path: "/dataManagement/link",
        query: {
          id,
        },
      });
    })
    .catch(() => {});
};
const openConfigSon = (id: string) => {
  ElMessageBox.confirm("更改配置后已计算的任务进度将会重新计算，是否更改？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      dialogSon.value = false;
      router.push({
        path: "/dataManagement/linkSon",
        query: {
          id,
        },
      });
    })
    .catch(() => {});
};

// 取消关联
const openCancel = (id: string) => {
  ElMessageBox.confirm(
    "取消关联后已计算的任务进度将会清空，且状态变为待关联，是否取消？",
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(async () => {
      // 绑定删除任务绑定台账和台账配置台账 taskId,
      await deleteLedgerApi(id);
      await makeSurePlanProgressConfigApi(id, {
        status: 1, // 1 取消关联 2 关联 -- id是页面详情的id
      });
      // 更新数据
      tableRef.value.reload();
      ElMessage({
        type: "success",
        message: "操作成功",
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "操作失败",
      });
    });
};
const historyOpen = ref(false)
const openCancelSon = (id: string) => {
  ElMessageBox.confirm(
    "取消关联后已计算的任务进度将会清空，且状态变为待关联，是否取消？",
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(async () => {
      // 绑定删除任务绑定台账和台账配置台账 taskId,
      await deleteLedgerApi(id);
      await makeSurePlanProgressConfigApi(id, {
        status: 1, // 1 取消关联 2 关联 -- id是页面详情的id
      });
      // 更新数据
      tableRefSon.value.reload();
      ElMessage({
        type: "success",
        message: "操作成功",
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "操作失败",
      });
    });
};
const sercrdAll = ref<any>([]);
const isAdmin = computed(() => {
  return (
    currentUserInfoRole.baseRoles.includes("管理员") ||
    currentUserInfoRole.staffRole.includes("区县台账运维员")
  );
});

const onFilterFieldSelectChange = (val: any) => {
  if (val) {
    myRegionNames({}, { guid: val }).then((res: any) => {
      sercrdAll.value = res.data;
    });
  }
  searchForm.value.TelecomCode = "";
};
</script>
<template>
  <div class="tasklink">
    <Block
      title="任务关联进度"
      :enable-fixed-height="true"
      :enable-expand-content="true"
      :enable-back-button="false"
      @heightChanged="onBlockHeightChanged"
    >
      <template #title>
        <p
          style="
            align-items: center;
            border-left: 5px solid var(--z-nav-hover);
            display: flex;
            font-size: 16px;
            font-weight: 500;
            line-height: 1;
            padding-left: 0.625rem;
            margin: 0 0.3125rem;
          "
        >
          数据管理
        </p>
        <p style="display: flex; align-items: center; color: #ff9b08">
          <el-icon size="16" color="#ff9b08">
            <Warning />
          </el-icon>
          进度更新后30分钟推送至基层智治平台展示
        </p>
      </template>
      <template #topRight>
        <el-button type="primary" @click="导入数据;" size="small" style="margin-right: 10px">新增</el-button>
        <el-dropdown>
          <el-button size="small" type="primary">更多操作</el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="importRecordVisible=true">导入记录</el-dropdown-item>
			  <el-dropdown-item>变更记录</el-dropdown-item>
			  <el-dropdown-item>导出数据</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>

      <template #expand>
        <div class="search">
          <Form
            v-model="searchForm"
            :props="formItems"
            :enable-reset="false"
            label-width="0"
            column-count="5"
            confirm-text="查询"
            @submit="onSearch('search')"
            @clear="onSearch('clear')"
          >
            <template
              #form-RegionId
              v-if="
                currentUserInfoRole.baseRoles.includes('管理员') &&
                currentDepartmentInfo.community == null &&
                currentDepartmentInfo.street == null &&
                currentDepartmentInfo.district == null
              "
            >
              <div style="display: flex; width: 100%">
                <el-select
                  clearable
                  style="width: 50%; margin-right: 10px"
                  v-model="searchForm.RegionId"
                  @keyup.enter="onSearch('search')"
                  @change="onFilterFieldSelectChange"
                >
                  <el-option
                    v-for="item in regionNamesOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
                <el-select
                  v-model="searchForm.TelecomCode"
                  style="width: 50%"
                  @keyup.enter="onSearch('search')"
                  clearable
                >
                  <el-option
                    v-for="condition of sercrdAll"
                    :value="condition.telecomCode"
                    :label="condition.name"
                  />
                </el-select>
              </div>
            </template>
            <template
              #form-TelecomCode
              v-if="
                currentUserInfoRole.staffRole.includes('区县台账运维员') ||
                (currentUserInfoRole.baseRoles.includes('管理员') &&
                  currentDepartmentInfo.community == null &&
                  currentDepartmentInfo.street == null &&
                  currentDepartmentInfo.district !== null)
              "
            >
              <div style="width: 100%">
                <el-select
                  clearable
                  @keyup.enter="onSearch('search')"
                  v-model="searchForm.TelecomCode"
                >
                  <el-option
                    v-for="item in regionNamesOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.telecomCode"
                  />
                </el-select>
              </div>
            </template>
          </Form>
        </div>
      </template>

      <TableV2
        ref="tableRef"
        url="/api/ledger/telecom-task"
        :headers="{ Urlkey: 'base' }"
        :req-params="reqParams"
        :columns="tableColumns"
        :enable-edit="false"
        :enable-delete="false"
        :enable-selection="false"
        :enable-latest-data="false"
        :height="tableHeight"
        :enable-toolbar="false"
        :form-label-width="100"
        @completed="onTableCompleted"
        @click-button="onTableButtonClick"
      >
        <template #taskName="{ row }">
          <span class="link-click" @click="isAdmin ? '' : handleDetail(row)">{{
            row.taskName
          }}</span>
        </template>
        <template #pushType="{ row }">
          <span>{{ row.pushType == 0 ? "基层智治" : "三级平台" }}</span>
        </template>
        <template #streetTown="{ row }">
          <span
            >{{ row.county ? row.county : "" }}-{{ row.street ? row.street : "" }}</span
          >
        </template>
        <!-- 所属板块 -->
        <template #belongBlock="{ row }">
          <div v-if="row.businessName && row.businessName.indexOf('/') > 0">
            <span>{{ row.businessName.split("/")[0] }}</span>
          </div>
          <div v-else>-</div>
        </template>

        <!-- 核心业务 -->
        <template #ledgerRunway="{ row }">
          <div v-if="row.businessName && row.businessName.indexOf('/') > 0">
            <span>{{ row.businessName.split("/")[1] }}</span>
            <span v-if="row.businessName.split('/')[2]"
              >/{{ row.businessName.split("/")[2] }}</span
            >
          </div>
          <div v-else>-</div>
        </template>

        <!-- 起止时间 -->
        <template #date="{ row }">
          <div v-if="row.startTime">
            <span>{{ row.startTime }}~{{ row.endTime }}</span>
          </div>
        </template>

        <!-- 责任人 -->
        <template #chargePersons="{ row }">
          <div v-if="row.chargePersons.length">
            <!-- chargePersonUsername -->
            <!-- <span v-for="(item, idx) in row.chargePersons">{{ item.chargePersonUsername }}、</span> -->
            {{row.chargePersons.map((item: any) => item.chargePersonName).toString()}}
          </div>
          <div v-else></div>
        </template>

        <!-- 完成条件  任务完成条件(1任意责任人完成即可 0全部责任人均需完成) -->
        <template #taskCompleteCon="{ row }">
          <span v-if="row.taskCompleteCon === '1'">任意责任人完成即可</span>
          <span v-else-if="row.taskCompleteCon === '0'">全部责任人均需完成</span>
        </template>

        <!-- {prop: 'childCount', label: '子任务'}, -->
        <template #childCount="{ row }">
          <span
            class="link-click"
            @click="onTableButtonClick({ btn: { code: 'showSon' }, row })"
            >{{ row.childCount }}</span
          >
        </template>

        <!-- 关联状态 -->
        <template #status="{ row }">
          <div flex="~" items-center>
            <span
              mr-5px
              inline-block
              w-10px
              h-10px
              rounded-full
              :style="{
                backgroundColor: FlowTaskStatus.filter((v) => v.value === row.status)[0]
                  .color,
              }"
            ></span>
            <span
              :style="{
                color: FlowTaskStatus.filter((v) => v.value === row.status)[0].color,
              }"
            >
              {{ FlowTaskStatus.filter((v) => v.value === row.status)[0].label }}
            </span>
          </div>
        </template>

        <template #progress="{ row }">
          <span>{{ row.progress }}%</span>
        </template>

        <template #buttons="{ row }">
          <el-button
            v-if="row.status === 1"
            type="primary"
            size="small"
            :disabled="
              !(
                currentUserInfoRole.baseRoles.includes('管理员') ||
                currentUserInfoRole.staffRole.includes('区县台账运维员')
              )
            "
            @click="onTableButtonClick({ btn: { code: 'link' }, row })"
          >
            引用业务表
          </el-button>
          <el-button
            type="primary"
            size="small"
            @click="onTableButtonClick({ btn: { code: 'detail' }, row })"
          >
            查看
          </el-button>
          <el-button
            v-if="row.status === 2"
            type="primary"
            size="small"
            :disabled="
              currentUserInfoRole.baseRoles.includes('管理员') ||
              currentUserInfoRole.staffRole.includes('区县台账运维员')
            "
            @click="
              currentUserInfoRole.baseRoles.includes('管理员') ||
              currentUserInfoRole.staffRole.includes('区县台账运维员')
                ? ''
                : openCancel(row.id)
            "
          >
            取消引用
          </el-button>

          <!-- <el-dropdown v-if="row.status === 2" style="margin-left: 12px">
						<el-button
							size="small"
							:disabled="
								currentUserInfoRole.baseRoles.includes('管理员') ||
								currentUserInfoRole.staffRole.includes('区县台账运维员')
							"
						>
							更多
							<el-icon class="el-icon--right">
								<arrow-down />
							</el-icon>
						</el-button>

						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item>
									<span
										@click="
											currentUserInfoRole.baseRoles.includes('管理员') ||
											currentUserInfoRole.staffRole.includes('区县台账运维员')
												? ''
												: openConfig(row.id)
										"
										>更改配置</span
									>
								</el-dropdown-item>

								<el-dropdown-item>
									<span
										@click="
											currentUserInfoRole.baseRoles.includes('管理员') ||
											currentUserInfoRole.staffRole.includes('区县台账运维员')
												? ''
												: openCancel(row.id)
										"
										>取消关联</span
									>
								</el-dropdown-item>
							</el-dropdown-menu>
						</template>
	</el-dropdown> -->
        </template>
      </TableV2>
      <Pagination
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.size"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      ></Pagination>
    </Block>

    <!-- 子任务列表弹窗 -->
    <Dialog
      title="子任务"
      :enableButton="false"
      :width="'70%'"
      v-model="dialogSon"
      @heightChanged="heightChanged"
      @click-close="dialogSon = false"
    >
      <!-- 搜索表单 -->
      <div class="search">
        <Form
          v-model="searchFormSon"
          :data="formItemsSon"
          :enable-reset="false"
          label-width="0"
          column-count="4"
          button-vertical="flowing"
          confirm-text="查询"
          @submit="onSearchSon('search')"
          @clear="onSearchSon('clear')"
        >
        </Form>
      </div>

      <TableV2
        ref="tableRefSon"
        url="/api/ledger/telecom-task"
        :headers="{ Urlkey: 'base' }"
        :req-params="reqParamsSon"
        :columns="tableColumnsSon"
        :enable-edit="false"
        :enable-delete="false"
        :enable-selection="false"
        :enable-latest-data="false"
        :height="tableHeightSon"
        :enable-toolbar="false"
        :form-label-width="100"
        :auto-height="true"
        @completed="onTableCompletedSon"
        @click-button="onTableButtonClickSon"
      >
        <template #taskName="{ row }">
          <span
            class="link-click"
            @click="onTableButtonClickSon({ btn: { code: 'detail' }, row })"
            >{{ row.taskName }}</span
          >
        </template>
        <!-- 责任人 -->
        <template #chargePersons="{ row }">
          <div v-if="row.chargePersons.length">
            <!-- chargePersonUsername -->
            <!-- <span v-for="(item, idx) in row.chargePersons">{{ item.chargePersonUsername }}、</span> -->
            {{ row.chargePersons.map((item) => item.chargePersonName).toString() }}
          </div>
          <div v-else></div>
        </template>

        <template #date="{ row }">
          <div v-if="row.startTime">
            <span>{{ row.startTime }}~{{ row.endTime }}</span>
          </div>
          <!-- <div v-else>--</div> -->
        </template>

        <template #status="{ row }">
          <div flex="~" items-center>
            <span
              mr-5px
              inline-block
              w-10px
              h-10px
              rounded-full
              :style="{
                backgroundColor: FlowTaskStatus.filter((v) => v.value === row.status)[0]
                  .color,
              }"
            ></span>
            <span
              :style="{
                color: FlowTaskStatus.filter((v) => v.value === row.status)[0].color,
              }"
            >
              {{ FlowTaskStatus.filter((v) => v.value === row.status)[0].label }}
            </span>
          </div>
        </template>

        <template #progress="{ row }">
          <span>{{ row.progress }}%</span>
        </template>

        <template #buttons="{ row }">
          <el-button
            v-if="row.status === 1"
            type="primary"
            size="small"
            @click="onTableButtonClickSon({ btn: { code: 'link' }, row })"
          >
            关联业务表
          </el-button>

          <el-button
            v-if="row.status === 2"
            type="primary"
            size="small"
            @click="onTableButtonClickSon({ btn: { code: 'detail' }, row })"
          >
            查看
          </el-button>

          <el-dropdown v-if="row.status === 2" style="margin-left: 12px">
            <el-button size="small">
              更多
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </el-button>

            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>
                  <span @click="openConfigSon(row.id)">更改配置</span>
                </el-dropdown-item>

                <el-dropdown-item>
                  <span @click="openCancelSon(row.id)">取消关联</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </TableV2>
      <Pagination
        :total="paginationSon.total"
        :current-page="paginationSon.page"
        :page-size="paginationSon.size"
        @current-change="onPaginationChangeSon($event, 'page')"
        @size-change="onPaginationChangeSon($event, 'size')"
      ></Pagination>
    </Dialog>
	<importRecord v-model="importRecordVisible" :title="导入记录"></importRecord>

	
  </div>
</template>
<route>
	{
		meta: {
			title: '数据管理',
		},
	}
</route>

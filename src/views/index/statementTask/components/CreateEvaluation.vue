<!-- 评价指标 -->
<template>
	<div>
		<Dialog
			width="1100"
			:loading="loading"
			height="500"
			v-bind="$attrs"
			title="评价指标"
			size="50%"
			:with-header="false"
		>
			<div class="search">
				<div>评价指标名称:</div>
				<el-input
					style="width: 160px; margin: 0 12px"
					clearable
					v-model="reportName"
				></el-input>
				<el-button type="primary" @click="handleSearch">搜索</el-button>
			</div>
			<!-- 操作按钮 -->
			<div class="btn-box">
				<el-button type="primary" @click="goAdd">新增</el-button>
				<el-button
					type="primary"
					:disabled="!allCreateEvaluation.length"
					@click="dealAllData"
					>删除全部</el-button
				>
			</div>
			<!-- 数据表格 -->
			<div style="padding: 0 12px">
				<el-table
					stripe
					v-loading="loading"
					default-expand-all
					ref="baseTable"
					:border="true"
					:data="tableData"
					:show-overflow-tooltip="true"
					:scrollbar-always-on="true"
					style="width: 100%; height: 284px"
				>
					<el-table-column label="序号" width="60" fixed="left" align="center">
						<template #default="scope">
							{{ state.pageSize * (state.currentPage - 1) + scope.$index + 1 }}
						</template>
					</el-table-column>
					<el-table-column
						:prop="item.prop"
						align="center"
						v-for="item in tableFields"
						:label="item.label"
						:key="item.prop"
						:width="item.width"
					>
						<template #default="{row}">
							<span v-if="item.prop === 'fillingCycle'">{{
								periodList.find((item) => item.value === row.fillingCycle)?.label
							}}</span>
							<span v-else>{{ row[item.prop] }}</span>
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" min-width="120">
						<template #default="{row}">
							<el-button
								link
								type="primary"
								size="small"
								@click="actionData(row, '1')"
								>查看</el-button
							>
							<el-button
								link
								type="primary"
								size="small"
								@click="actionData(row, '2')"
								>编辑</el-button
							>
							<el-button
								link
								type="primary"
								size="small"
								@click="delReportData(1, row.id)"
								>删除</el-button
							>
						</template>
					</el-table-column>
				</el-table>
			</div>

			<div style="display: flex; justify-content: flex-end; padding: 12px">
				<el-pagination
					background
					:page-sizes="[100, 200, 300, 400]"
					layout="total, sizes, prev, pager, next, jumper"
					:total="allCreateEvaluation.length"
					v-model:current-page="state.currentPage"
					:page-size="state.pageSize"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</div>
		</Dialog>
		<!-- 新增报告水印-->
		<Dialog
			v-model="state.showAddDialog"
			:loading="loading"
			:title="state.addType == 'add' ? '新增评价指标' : '编辑评价指标'"
			width="850"
			height="600"
			@clickConfirm="addConfirm"
			@close="() => (state.showAddDialog = false)"
		>
			<div style="padding-left: 20px">
				<div style="margin: 20px 0; display: flex; align-items: center">
					<div style="width: 110px; text-align: left">评价指标名称：</div>
					<el-input
						style="width: 220px"
						v-model="state.reportForm.reportName"
						:readonly="isReadOnly"
						clearable
						placeholder="请输入"
					></el-input>
				</div>
				<div style="margin: 20px 0; display: flex; align-items: center">
					<div style="width: 110px; text-align: left">填报周期：</div>
					<el-select
						:disabled="isReadOnly"
						style="width: 220px"
						w-full
						v-model="state.reportForm.fillingCycle"
					>
						<el-option
							v-for="item in periodList"
							:label="item.label"
							:value="item.value"
							>{{ item.label }}
						</el-option>
					</el-select>
				</div>

				<div class="btn-box">
					<span>绑定任务：</span>
					<el-button
						type="primary"
						:disabled="!taskSelection.length || isReadOnly"
						@click="batchAction('task', '2')"
						>批量关联</el-button
					>
					<el-button
						type="primary"
						:disabled="!taskSelection.length || isReadOnly"
						@click="batchAction('task', '1')"
						>批量取消关联</el-button
					>
				</div>
			</div>
			<div style="padding-left: 80px">
				<div class="search">
					<div>任务名称:</div>
					<el-input style="width: 160px; margin: 0 12px" v-model="taskName"></el-input>
					<el-button type="primary" clearable @click="searchTask">搜索</el-button>
				</div>
				<el-table
					stripe
					v-loading="loading"
					default-expand-all
					ref="baseTable"
					:border="true"
					:data="state.taskList"
					:show-overflow-tooltip="true"
					:scrollbar-always-on="true"
					style="width: 100%; height: 200px"
					@selection-change="taskSelectionChange"
				>
					<el-table-column type="selection" width="55" />
					<el-table-column
						:prop="item.prop"
						align="center"
						v-for="item in bindTaskFields"
						:label="item.label"
						:key="item.prop"
						:width="item.width"
					>
						<template #default="{row}">
							<span v-if="item.prop === 'status'">{{
								row[item.prop] == 1 ? '未关联' : '已关联'
							}}</span>
							<span v-else>{{ row[item.prop] }}</span>
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" min-width="120">
						<template #default="{row}">
							<el-button
								v-if="row.status == 2"
								link
								type="primary"
								size="small"
								@click="bindActionData('task', row.id, '1')"
							>
								取消关联
							</el-button>
							<el-button
								link
								v-if="row.status == 1"
								:disabled="isReadOnly"
								type="primary"
								size="small"
								@click="bindActionData('task', row.id, '2')"
								>关联</el-button
							>
						</template>
					</el-table-column>
				</el-table>
			</div>
		</Dialog>
	</div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, defineExpose, computed} from 'vue'
import {useUserStore} from '@/stores/useUserStore'
import {periodEnum, week} from '@/define/statement.define'
import {ElMessage, ElMessageBox, dayjs} from 'element-plus'
const userInfo = useUserStore().getUserInfo
const props = defineProps<{
	selectionList: Array<any>
}>()
const periodList = ref([
	{label: '一次性', value: periodEnum.once, dateRange: null},
	{label: '每日一次', value: periodEnum.day, dateRange: null},
	{label: '每周一次', value: periodEnum.week, dateRange: null},
	{label: '每半月一次', value: periodEnum.halfMonth, dateRange: null},
	{label: '每月一次', value: periodEnum.month, dateRange: dayjs().daysInMonth()},
	{label: '每季度一次', value: periodEnum.quarter, dateRange: 90},
	{label: '每半年一次', value: periodEnum.halfYear, dateRange: null},
	{label: '每年一次', value: periodEnum.year, dateRange: 365},
])
// 查看
const isReadOnly = ref(false)
const editId = ref(0)

// 表格每一项类型
interface DataItem {
	reportName: string
	createTime: string
	id?: number | string
}
// 表格每一项类型
interface bindDataItem {
	id: number
	reportName: string
	createTime?: string
	isText?: string
	status: string
}
const loading = ref(false)
// 表格数据
let tableData = reactive<DataItem[]>([])
// 所有报告报告水印数据
let allCreateEvaluation = reactive<DataItem[]>([])

const state = reactive({
	showAddDialog: false,
	seeDialog: false,
	drawer: false,
	reportForm: {
		reportName: '',
		fillingCycle: '',
		id: '',
	},
	currentPage: 1,
	pageSize: 10,
	addType: 'add',
	taskList: [] as any[],
	isTableList: [] as any[],
})

const addConfirm = () => {
	if (!state.reportForm.reportName || !state.reportForm.fillingCycle) {
		return ElMessage.error('请完善数据')
	}
	loading.value = true
	// 新增数据
	if (state.addType === 'add') {
		const obj = {
			reportName: state.reportForm.reportName,
			createTime: getCurrentTimeFormatted(),
			fillingCycle: state.reportForm.fillingCycle,
			createUser: userInfo.name,
			id: allCreateEvaluation.length + 1,
		}
		localStorage.setItem('allCreateEvaluation', JSON.stringify([...allCreateEvaluation, obj]))
	}
	// 更新数据
	else {
		allCreateEvaluation = allCreateEvaluation.map((item) =>
			item.id === editId.value
				? Object.assign({}, item, {...state.reportForm, id: editId.value})
				: item
		)
		localStorage.setItem('allCreateEvaluation', JSON.stringify([...allCreateEvaluation]))
	}
	setTimeout(() => {
		state.showAddDialog = false
		loading.value = false
		updateTaskTableData()
	}, 1000)
}
const typeOptions = [
	{
		label: '待审核',
		value: '1',
	},
	{
		label: '已取消',
		value: '2',
	},
	{
		label: '通过',
		value: '3',
	},
	{
		label: '驳回',
		value: '4',
	},
]

const updateBindTableDataItem = (key: string, id: number, updatedData: Partial<bindDataItem>) => {
	if (key == 'task') {
		// 更新 bindTableData，注意使用 state.taskList 作为数据源
		state.taskList = state.taskList.map((item) =>
			item.id === id ? Object.assign({}, item, updatedData) : item
		)
	} else {
		// 更新 bindTableData，注意使用 state.taskList 作为数据源
		state.isTableList = state.taskList.map((item) =>
			item.id === id ? Object.assign({}, item, updatedData) : item
		)
	}
}
// 查看 | 编辑
const actionData = (row: any, type: string, openType?: number) => {
	isReadOnly.value = type == '2' ? false : true
	state.addType = 'edit'
	editId.value = row.id
	for (const key in state.reportForm) {
		state.reportForm[key] = row[key]
	}
	setInitData()
	state.showAddDialog = true
}
const bindActionData = (key: string, id: number, type: string) => {
	ElMessageBox.confirm(`确定${type == '2' ? '关联' : '取消关联'}吗？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			updateBindTableDataItem(key, id, {status: type})
			setTimeout(() => {
				ElMessage.success('操作成功')
			}, 500)
		})
		.catch(() => {})
}

const getCurrentTimeFormatted = () => {
	const now = new Date()
	const year = now.getFullYear()
	const month = String(now.getMonth() + 1).padStart(2, '0') // 月份从0开始
	const day = String(now.getDate()).padStart(2, '0')
	const hours = String(now.getHours()).padStart(2, '0')
	const minutes = String(now.getMinutes()).padStart(2, '0')
	return `${year}-${month}-${day} ${hours}:${minutes}`
}
const tableFields = [
	{
		prop: 'reportName',
		label: '评价指标名称',
		width: 160,
	},
	{
		prop: 'fillingCycle',
		label: '填报周期',
		width: 160,
	},
	{
		prop: 'createTime',
		label: '创建时间',
		width: 160,
	},
	{
		prop: 'createUser',
		label: '创建人',
		width: 160,
	},
]
const allBindTask = ref<bindDataItem[]>([])

const bindTaskFields = [
	{
		prop: 'taskName',
		label: '任务名称',
		width: 160,
	},
	{
		prop: 'status',
		label: '是否关联',
		width: 160,
	},
]

const reportName = ref('')
const handleSearch = () => {
	if (!reportName.value) {
		tableData.splice(0, tableData.length, ...allCreateEvaluation)
	} else {
		const filteredData = allCreateEvaluation.filter(
			(item) => item.reportName.indexOf(reportName.value) != -1
		)
		tableData.splice(0, tableData.length, ...filteredData)
	}
}
const taskName = ref('')
const searchTask = () => {
	if (!taskName.value) {
		state.taskList.splice(0, state.taskList.length, ...allBindTask.value)
	} else {
		const filteredData = allBindTask.value.filter((item) => {
			return item.taskName.indexOf(taskName.value) != -1
		})

		state.taskList.splice(0, state.taskList.length, ...filteredData)
	}
}

// 展示新增
const goAdd = () => {
	isReadOnly.value = false
	state.showAddDialog = true
	state.reportForm = {
		reportName: '',
		fillingCycle: '',
		id: '',
	}

	setInitData()
}
const setInitData = () => {
	state.taskList = []
	state.isTableList = []
	allBindTask.value = []
	props.selectionList.forEach((item) => {
		state.taskList.push({
			taskName: item.name,
			id: item.id,
			status: '1',
		})
		if (item.reportTableTemplateNames.length) {
			item.reportTableTemplateNames.forEach((tabItem: string) => {
				state.isTableList.push({
					tableName: tabItem,
					id: state.isTableList.length + 1,
					status: '1',
				})
			})
		}
	})
	setTimeout(() => {
		allBindTask.value = JSON.parse(JSON.stringify(state.taskList))
	}, 300)
}
state.addType = 'add'

const open = () => {
	state.drawer = true
}
defineExpose({
	open,
})

// 更新数据
const updateTaskTableData = () => {
	state.currentPage = 1
	allCreateEvaluation.splice(0, allCreateEvaluation.length) // 清空原数组
	const newData = JSON.parse(localStorage.getItem('allCreateEvaluation')) || []
	allCreateEvaluation.push(...newData)

	if (newData && newData.length) {
		tableData.splice(0, tableData.length, ...newData.slice(0, state.pageSize))
	} else {
		tableData.splice(0, tableData.length)
	}
}
// 组件内部定义类型
const FlowNodeTypes = {
	Input: 'input',
	Output: 'output',
	Report: 'report',
	Review: 'review',
	Gateway: 'gateway',
	Condition: 'condition',
}

const seeDialog = () => {
	state.seeDialog = true
}

// 每页数量改变时触发
const handleSizeChange = (size: number) => {
	state.pageSize = size
	updateTaskTableDataByPagination()
}

// 当前页码改变时触发
const handleCurrentChange = (page: number) => {
	state.currentPage = page
	updateTaskTableDataByPagination()
}

// 根据分页参数更新表格数据
const updateTaskTableDataByPagination = () => {
	const {currentPage, pageSize} = state
	const start = (currentPage - 1) * pageSize
	const end = start + pageSize

	// 截取当前页的数据
	const currentData = allCreateEvaluation.slice(start, end)
	tableData.splice(0, tableData.length, ...currentData)
}

const taskSelection = ref([])
const taskSelectionChange = (selection: any) => {
	taskSelection.value = selection
}
const tableSelection = ref([])
const tableSelectionChange = (selection: any) => {
	tableSelection.value = selection
}

// 批量关联 | 批量取消关联
const batchAction = (key: string, type: string) => {
	ElMessageBox.confirm('确定批量操作数据吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const dataId = state.taskList.map((item: any) => item.id)
			dataId.forEach((item) => {
				updateBindTableDataItem(key, item, {status: type})
			})

			setTimeout(() => {
				ElMessage.success('操作成功')
				updateTaskTableData()
			}, 500)
		})
		.catch(() => {})
}

const delReportData = (type: number, rowId?: number) => {
	ElMessageBox.confirm('确定删除数据吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			if (type == 1) {
				let data = allCreateEvaluation.filter((item) => item.id != rowId) || []
				tableData.splice(0, tableData.length, ...data)
				localStorage.setItem('allCreateEvaluation', JSON.stringify(data))
			} else {
				// 批量删除
				const ids = selectionReportList.value.map((item: any) => item.id)
				let data = allCreateEvaluation.filter((item) => !ids.includes(item.id)) || []
				tableData.splice(0, tableData.length, ...data)
				localStorage.setItem('allCreateEvaluation', JSON.stringify(data))
			}

			setTimeout(() => {
				ElMessage.success('删除成功')
				updateTaskTableData()
			}, 500)
		})
		.catch(() => {})
}
const cancelBind = () => {
	ElMessageBox.confirm('确定取消关联数据吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			setTimeout(() => {
				ElMessage.success('取消关联成功')
				updateTaskTableData()
			}, 500)
		})
		.catch(() => {})
}
const dealAllData = () => {
	ElMessageBox.confirm('确定删除全部数据吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			// 清空所有数据源和当前页数据
			tableData.splice(0, tableData.length) // 正确清空 tableData
			allCreateEvaluation.splice(0, allCreateEvaluation.length) // 清空原始数据源

			// 清除本地存储
			localStorage.removeItem('allCreateEvaluation')
		})
		.catch(() => {})
}
onMounted(() => {
	updateTaskTableData()
	state.taskList = []
	state.isTableList = []
})
</script>

<style scoped lang="scss">
:deep(.el-drawer__body) {
	padding: 12px 0 12px 0 !important;
}

.header-title {
	font-size: 16px;
	font-weight: 500;
	padding-bottom: 16px;
	padding: 12px 12px 16px 12px;
	border-bottom: 1px solid #e9e9e9;
}
.search {
	align-items: center;
	border-bottom: var(--z-border);
	display: flex;
	padding-bottom: 12px;
	white-space: nowrap;

	span {
		font-size: 13px;
		i {
			margin-top: -1px;
		}
	}
}
.btn-box {
	display: flex;
	padding: 20px 0px;
	align-items: center;
}
</style>

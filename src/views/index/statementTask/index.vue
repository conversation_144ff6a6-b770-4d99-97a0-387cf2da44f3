<script setup lang="ts" name="statementtask">
import queryString from "querystring";
import { ref, inject, computed, onActivated, h } from "vue";
import { useRouter, useRoute } from "vue-router";
import { Axios } from "axios";
import { useTaskManageStore } from "@/stores/taskManageStore";
import {
  ElNotification,
  ElMessageBox,
  ElCheckbox,
  CheckboxValueType,
  ElMessage,
} from "element-plus";
import {
  periodList,
  statusTrans,
  StatusSelect,
  ReportsFlowStatus,
  ReportsFlowStatusType,
  ReportsFlowStatusEnum,
  reportStatusTrans,
  GetBatch,
  surplusDate,
  PlanTaskStatus,
} from "@/define/statement.define";
import { useUserStore } from "@/stores/useUserStore";
import util from "@/plugin/util";
import { STAFFROLEARRAY } from "@/define/organization.define";
import { CirclePlusFilled, Bell, ElementPlus, Promotion } from "@element-plus/icons-vue";
import {
  ExecutionReportTask,
  ReportRevokeInnerWrite,
  DeleteReportFlowTask,
  PushReportNotice,
  GetPlanTaskProgress,
  urging,
} from "@/api/ReportApi";
import { exportExcel } from './exportExcel'
import { useLocalStorage, ACTION_KEY } from "@/hooks/useLocalStorage";
import { useSleep } from "@/hooks/useSleep";
import CopyReport from "./components/CopyReport.vue";
import PermissionConfig from "./components/PermissionConfig.vue";
import BatchUrge from "./components/BatchUrge.vue";
import BatchEditReport from "./components/BatchEditReport.vue";
import BatchStopReport from "./components/BatchStopReport.vue";
import BatchResetReport from "./components/BatchResetReport.vue";
import BatchDeleteFlow from "./components/BatchDeleteFlow.vue";
import BatchBindFlow from "./components/BatchBindFlow.vue";
import BatchUnbindFlow from "./components/BatchUnbindFlow.vue";
import BatchFlowDes from "./components/BatchFlowDes.vue";
import ReminderNotification from "./components/ReminderNotification.vue";
import ReportWatermark from "@/views/index/statementTask/components/ReportWatermark.vue";
import ProcessUsageReport from "./components/ProcessUsageReport.vue";
import RiskStrategy from "./components/RiskStrategy.vue";
import OperatingManual from "./components/OperatingManual.vue";
import CreateEvaluation from "./components/CreateEvaluation.vue";
import DeleteTask from "./components/DeleteTask.vue";
import PerformanceOptimizationReport from "./components/PerformanceOptimizationReport.vue";
import OptimizationReportWatermark from "./components/OptimizationReportWatermark.vue";

import PerformanceTesting from './components/PerformanceTesting.vue'
import EmergencyPlan from './components/EmergencyPlan.vue'
import TableVerificationRules from './components/TableVerificationRules.vue'
enum LTABS {
  ICREATE = "我的创建",
  ITURN = "我的转发",
  QUALITYREPORT = "任务质量报告",
  VISUALIZEDTABLE = "可视化表格",
}

const storage: any = useLocalStorage();
const route = useRoute();
const router = useRouter();
const UserStore = useUserStore();
const taskManageStore = useTaskManageStore();
const axios = inject<Axios>("#axios");
const cureerntText = ref("任务创建");
const pageParams = ref({
  MaxResultCount: 10,
  SkipCount: 0,
});
const searchParams = ref<any>({
  Name: null,
  FillingPeriodType: undefined,
  PlanTaskStatus: undefined,
  isSelf: true,
  fillingRange: undefined,
});
const fwList = [
  {
    label: "1",
    value: "1",
  },
  {
    label: "2",
    value: "2",
  },
];
const loading = ref(false);
const tabList = ref([
  {
    name: "任务管理",
    isActive: true,
    index: 1,
  },
  // {
  // 	name: '提交记录',
  // 	isActive: false,
  // 	index: 2,
  // },
]);
const currentIndex = ref(1);
const changeTab = (index: number) => {
  taskManageStore.taskList = [];
  pageParams.value = {
    MaxResultCount: 10,
    SkipCount: 0,
  };

  searchParams.value = {
    Name: null,
    FillingPeriodType: undefined,
    PlanTaskStatus: undefined,
    fillingRange: undefined,
    isSelf: true,
  };

  currentIndex.value = index;
  tabList.value.forEach((item: { name: string; isActive: boolean; index: number }) => {
    if (index === item.index) {
      item.isActive = true;
    } else {
      item.isActive = false;
    }
  });
  // 请求数据
  switch (index) {
    case 1:
      // 请求计划任务列表
      getTaskList(11);
      // 切换button列表显示
      buttons.value = [
        {
          type: "primary",
          code: "urge",
          title: "催办",
          more: true,
          icon: "<i i-majesticons-clock-line></i>",
          verify:
            "row.status === 2 || row.status === 3 || row.status === 6 || row.status === 7",
        },
        {
          type: "primary",
          code: "submitAudit",
          title: "执行",
          more: false,
          icon: "<i i-majesticons-send-line></i>",
          verify: `row.status === ${ReportsFlowStatusEnum.PendingExecution} || row.status === ${ReportsFlowStatusEnum.Rejected} || row.status === ${ReportsFlowStatusEnum.Revoke}`,
        },
        {
          type: "primary",
          code: "detail",
          title: "详情",
          more: false,
          icon: "<i i-majesticons-eye-line></i>",
          verify: "true",
        },
        {
          type: "primary",
          code: "edit",
          title: "编辑",
          more: true,
          icon: "<i i-majesticons-pencil-alt-line></i>",
          verify: `"${UserStore.userInfo.id}" ==  row.creatorId && row.status === 1 || "${UserStore.userInfo.id}" ==  row.creatorId && row.status === 4`,
        },

        {
          type: "primary",
          code: "terminate",
          title: "终止",
          more: true,
          icon: "<i i-majesticons-stop-circle-line></i>",
          verify: `"${UserStore.userInfo.id}" == row.creatorId && (row.status === 2 || row.status === 3 || row.status === 6 || row.status === 7)`,
        },
        {
          type: "danger",
          code: "del",
          more: true,
          title: "删除",
          icon: "<i i-majesticons-minus-circle-line></i>",
          verify: "true",
        },
      ];
      // 表格数据展示
      colData.value = [
        {
          title: "任务名称",
          field: "name",
        },
        {
          title: "填报范围",
          field: "fillingRange",
        },
        // {
        // 	title: '填报周期',
        // 	field: 'fillingPeriodType',
        // 	width: '120',
        // },
        {
          title: "本周期截止时间",
          field: "endDate",
        },
        {
          title: "本周期填报情况",
          field: "departmentReportingStatus",
        },
        {
          title: "任务状态",
          field: "status",
        },
        // {
        // 	title: '创建时间',
        // 	field: 'creationTime',
        // },
        // {
        // 	title: '是否启用',
        // 	field: 'enabled',
        // },
      ];
      break;
    case 2:
      // 请求任务归档列表 filterMode=4
      getReportTask(7);
      buttons.value = [
        {
          type: "primary",
          code: "detail",
          title: "详情",
          icon: "<i i-majesticons-eye-line></i>",
          verify: "true",
        },
      ];
      colData.value = [
        {
          title: "任务名称",
          field: "name",
        },
        // {
        // 	title: '填报周期',
        // 	field: 'fillingPeriodType',
        // 	width: '150',
        // },
        {
          title: "填报部门",
          field: "areaOrganizationUnit",
        },
        // {
        // 	title: '所属批次',
        // 	field: 'batch',
        // },
        {
          title: "任务状态",
          field: "reportTaskStatus",
        },
      ];
      break;
    default:
      break;
  }
};

const columns = {
  [LTABS.ICREATE]: [
    {
      title: "任务名称",
      field: "name",
    },
    {
      title: "填报范围",
      field: "fillingRange",
    },
    {
      title: "填报周期",
      field: "fillingPeriodType",
      width: "100",
    },
    {
      title: "本周期截止时间",
      field: "endDate",
    },
    {
      title: "本周期填报情况",
      field: "departmentReportingStatus",
    },
    {
      title: "任务状态",
      field: "status",
      width: 100,
    },

    // {
    // 	title: '创建时间',
    // 	field: 'creationTime',
    // },
    // {
    // 	title: '是否启用',
    // 	field: 'enabled',
    // },
  ],
  [LTABS.ITURN]: [
    { title: "任务名称", field: "reportTaskName" },
    { title: "创建人", field: "reportTaskCreatorName" },
    { title: "创建人部门", field: "createdBigDepartment" },
    { title: "创建人科室", field: "createdDepartment" },
    { title: "创建时间", field: "creationTime" },
    { title: "填报时间", field: "fillerSubmitTime" },
    { title: "截止时间", field: "endTime" },
    { title: "状态", field: "status" },
  ],
};

const tableButtons = {
  [LTABS.ICREATE]: [
    // {
    // 	type: 'primary',
    // 	code: 'urge',
    // 	more: true,
    // 	title: '催办',
    // 	icon: '<i i-majesticons-clock-line></i>',
    // 	verify: 'row.status === 2 || row.status === 3 || row.status === 6 || row.status === 7',
    // },
    {
      type: "primary",
      code: "reset",
      more: true,
      title: "重置",
      verify: "true",
    },
    {
      type: "primary",
      code: "bindFlow",
      more: true,
      title: "绑定任务",
      verify: "true",
    },
    {
      type: "primary",
      code: "flowDes",
      more: true,
      title: "流程说明",
      verify: "true",
    },
    {
      type: "primary",
      code: "submitAudit",
      more: false,
      title: "执行",
      icon: "<i i-majesticons-send-line></i>",
      verify: `row.status === ${ReportsFlowStatusEnum.PendingExecution} || row.status === ${ReportsFlowStatusEnum.Rejected} || row.status === ${ReportsFlowStatusEnum.Revoke}`,
    },
    {
      type: "primary",
      code: "detail",
      more: false,
      title: "详情",
      verify: "true",
    },
    {
      type: "primary",
      more: true,
      code: "edit",
      title: "编辑",
      verify: `"${UserStore.userInfo.id}" ==  row.creatorId && row.status === 1 || row.status === 11 || "${UserStore.userInfo.id}" ==  row.creatorId && (row.status === 14 || row.status === 4)`,
    },

    {
      type: "danger",
      code: "terminate",
      more: true,
      title: "终止",
      verify: `"${UserStore.userInfo.id}" == row.creatorId && (row.status === 2 || row.status === 3 || row.status === 6 || row.status === 7)`,
    },
    {
      type: "primary",
      code: "revoke",
      more: true,
      title: "撤回",
      verify: `row.status === ${ReportsFlowStatusEnum.PendingReview}`,
    },
    // {
    // 	type: 'danger',
    // 	more: true,
    // 	code: 'urging',
    // 	title: '催办',
    // 	icon: '<i i-majesticons-minus-circle-line></i>',
    // 	verify: 'row.status == 3 || row.status == 13',
    // },
    { type: "primary", code: "copy", more: true, title: "复制", verify: "true" },
    {
      type: "danger",
      more: true,
      code: "del",
      title: "删除",
      verify: "row.status !== 3",
    },
  ],
  [LTABS.ITURN]: [
    {
      type: "primary",
      code: "turnDetail",
      title: "详情",
      verify: "true",
    },
    // {
    // 	type: 'warning',
    // 	code: 'turnExpediting',
    // 	title: '催办',
    // 	verify: 'true',
    // },
    {
      type: "danger",
      code: "turnDelete",
      title: "删除",
      verify: "row._raw?.stop",
    },
    {
      type: "primary",
      code: "turnRevocation",
      title: "撤回",
      more: true,
      verify: "true",
    },
    // {
    // 	type: 'danger',
    // 	more: true,
    // 	code: 'urging',
    // 	title: '催办',
    // 	icon: '<i i-majesticons-minus-circle-line></i>',
    // 	// verify: 'row.status == 4 && !row.endTime',
    // },
  ],
};

const colData = ref<any>(columns[LTABS.ICREATE]);
const buttons = ref<any>(tableButtons[LTABS.ICREATE]);

const tableData = computed(() => taskManageStore.taskList);
const totalCount = computed(() => taskManageStore.totalCount);

const messageModalNotify = ref({
  audit: false,
  terminate: false,
  messageDing: false,
  NotifyDing: false,
  phoneDing: false,
});
const notifyType = ref<number>();

const currentActiveName = ref(LTABS.ICREATE);
const btnCode = ref();
const visibleDelete=ref(false);
const planTaskId=ref('');
const selectValue=ref('任务删除权限申请流程');
const selectList=ref([
  {
    label:'任务删除权限申请流程',
    value:'任务删除权限申请流程'
  },
  {
    label:'cyl',
    value:'cyl'
  },
  {
    label:'工作人员',
    value:'工作人员'
  },
  {
    label:'测试-流程-重复提交',
    value:'测试-流程-重复提交'
  },
  {
    label:'测试创建123',
    value:'测试创建123'
  },
  {
    label:'市级业务表审核不含本部门',
    value:'市级业务表审核不含本部门'
  },
  {
    label:'bgaz',
    value:'bgaz'
  },
  {
    label:'测试消息',
    value:'测试消息'
  },
  {
    label:'多级审核',
    value:'多级审核'
  },
  {
    label:'污染',
    value:'污染'
  },
]);
const handleDeleteConfirm=()=>{
   axios
    ?.post("/api/filling/StaffPlanTaskHide/create", {
      staffId: UserStore.userInfo?.id,
      planTaskId: planTaskId.value,
    })
    .then((res) => {
      handleDeleteCancel()
      getTaskList(11);
      ElMessage.success("删除成功");
    });
}
const handleDeleteCancel=()=>{
  visibleDelete.value=false
}
// 表格按钮点击行为
const clickButton = (btn: {
  btn: {
    code: any;
    title?: string;
    verify?: string;
  };
  scope: any;
}) => {
  let urgingMessageTypes: any = [];
  urgingList.value.forEach((item: any) => {
    if (item == "0") {
      urgingMessageTypes.push(0);
    } else {
      urgingMessageTypes.push(Number(item));
    }
  });
  switch (btn.btn.code) {
    case "flowDes":
      selectionList.value = [btn.scope];
      isBatchFlowDes.value = true;
      batchFlowModel.value = 1;
      break;
    case "bindFlow":
      selectionList.value = [btn.scope];
      isBatchBindFlow.value = true;
      break;
    case "reset":
      selectionList.value = [btn.scope];
      isBatchResetReport.value = true;
      break;
    case "copy":
      isCopyReport.value = true;
      break;
    case "submitAudit":
      // 执行
      ExecutionReportTask({ planTaskId: btn.scope.id }).then((res: any) => {
        ElMessage.success("已执行任务");
        getTaskList(11);
      });
      // ElMessageBox({
      // 	title: '执行',
      // 	message: () =>
      // 		h(ElCheckbox, {
      // 			modelValue: messageModalNotify.value.audit,
      // 			'onUpdate:modelValue': (val: any) => {
      // 				messageModalNotify.value.audit = val
      // 			},
      // 			label: '通知审核人！',
      // 		}),
      // })
      // 	.then(() => {

      // 		axios
      // 			?.post(
      // 				`/api/filling/plan-task/set-active?id=${btn.scope.id}&enabled=true&notice=${messageModalNotify.value.audit}`
      // 			)
      // 			.then((res) => {
      // 				getTaskList()
      // 				ElNotification.success({
      // 					title: '通知',
      // 					message: '已提交领导审核',
      // 				})
      // 			})
      // 	})
      // 	.catch((action) => {
      // 		messageModalNotify.value.audit = false
      // 		// console.log(action)
      // 	})

      break;
    case "detail":
      router.push({
        path: "/statementTask/detail",
        query: { id: btn.scope.id, type: "detail" },
      });

      // router.push({
      // 	path: '/statementTask/task-detail',
      // 	query: {
      // 		reportTaskId: btn.scope.id,
      // 	},
      // })
      break;
    case "edit":
      router.push({ path: "/statementTask/create", query: { id: btn.scope.id } });
      break;
    case "export":
      break;
    case "del":
        planTaskId.value=btn.scope.id
        visibleDelete.value=true
      // ElMessageBox.confirm("请确认是否删除？删除后不可恢复！", "删除")
      //   .then((type) => {
      //     if (type === "confirm") {
           
      //       // 软删除-仅对当前登陆用户不可见
      //       axios
      //         ?.post("/api/filling/StaffPlanTaskHide/create", {
      //           staffId: UserStore.userInfo?.id,
      //           planTaskId: btn.scope.id,
      //         })
      //         .then((res) => {
      //           getTaskList(11);
      //           ElMessage.success("删除成功");
      //         });
      //       // axios?.delete(`/api/filling/plan-task/${btn.scope.id}`).then((res) => {
      //       // 	getTaskList()
      //       // 	ElNotification.success({
      //       // 		title: '通知',
      //       // 		message: '删除成功',
      //       // 		duration: 2000,
      //       // 	})
      //       // })
      //     }
      //   })
      //   .catch((err) => {
      //     console.log(err);
      //   });
      break;
    case "urge":
      // 催办

      ElMessageBox({
        title: "任务催办",
        showCancelButton: true,
        message: `将发送消息提示${btn.scope.status === 3 ? "填报人" : "审核人"}进行${btn.scope.status === 3 ? "填报" : "审核"
          }操作`,
        // () =>
        // 	h('div', {style: 'margin-left:10px'}, [
        // 		h(
        // 			'p',
        // 			{},
        // 			`将发送消息提示${btn.scope.status === 3 ? '填报人' : '审核人'}进行${
        // 				btn.scope.status === 3 ? '填报' : '审核'
        // 			}操作`
        // 		),
        // 		h(
        // 			'div',
        // 			{
        // 				style:
        // 					'display:flex;align-items:center;padding-right:60px;flex:1;justify-content: space-between;',
        // 			},
        // 			[
        // 				h('div', {}, [
        // 					h(ElCheckbox, {
        // 						modelValue: messageModalNotify.value.NotifyDing,
        // 						'onUpdate:modelValue': (val: any) => {
        // 							messageModalNotify.value.NotifyDing = val
        // 							messageModalNotify.value.messageDing = false
        // 							messageModalNotify.value.phoneDing = false
        // 							notifyType.value = val ? 1 : undefined
        // 						},
        // 					}),
        // 					h('span', {style: 'display:inline-block;margin-left:5px'}, '消息通知DING'),
        // 				]),
        // 				h('div', {}, [
        // 					h(ElCheckbox, {
        // 						modelValue: messageModalNotify.value.messageDing,
        // 						'onUpdate:modelValue': (val: any) => {
        // 							messageModalNotify.value.messageDing = val
        // 							messageModalNotify.value.NotifyDing = false
        // 							messageModalNotify.value.phoneDing = false
        // 							notifyType.value = val ? 2 : undefined
        // 						},
        // 					}),
        // 					h('span', {style: 'display:inline-block;margin-left:5px'}, '短信DING'),
        // 				]),
        // 				h('div', {}, [
        // 					h(ElCheckbox, {
        // 						modelValue: messageModalNotify.value.phoneDing,
        // 						'onUpdate:modelValue': (val: any) => {
        // 							messageModalNotify.value.phoneDing = val
        // 							messageModalNotify.value.messageDing = false
        // 							messageModalNotify.value.NotifyDing = false
        // 							notifyType.value = val ? 3 : undefined
        // 						},
        // 					}),
        // 					h('span', {style: 'display:inline-block;margin-left:5px'}, '电话DING'),
        // 				]),
        // 			]
        // 		),
        // 	]),

        // ,
      })
        .then(() => {
          axios
            ?.post(
              `/api/filling/message-notice/urge?planTaskId=${btn.scope.id}${notifyType.value ? `&dingMessageType=${notifyType.value}` : ''
              }`
            )
            .then((res) => {
              // if (res.status === 200 || res.status === 204) {
              ElMessage.success('已通知相关人员')
              // }
            })
        })
        .catch((action) => {
          // console.log(action)
        })
      break
    case 'terminate':
      // 终止
      ElMessageBox({
        title: '确认要终止该项任务吗？',
        showCancelButton: true,
        message: () =>
          h('div', { style: 'margin-left:10px' }, [
            h('p', { style: 'color:red' }, '终止后，填报人将无法填报。'),
            h('div', { style: 'display:flex;align-items:center' }, [
              h(ElCheckbox, {
                modelValue: messageModalNotify.value.terminate,
                'onUpdate:modelValue': (val: any) => {
                  messageModalNotify.value.terminate = val
                },
              }),
              h(
                'span',
                { style: 'display:inline-block;margin-left:5px' },
                '渝快政发送通知填报人及审核人！'
              ),
            ]),
          ]),
      })
        .then(() => {
          axios
            ?.put(
              `/api/filling/plan-task/stope?id=${btn.scope.id}&notice=${messageModalNotify.value.terminate}`
            )
            .then((res) => {
              ElMessage.success('已终止该任务')
              getTaskList(11)
              messageModalNotify.value.terminate = false
            })
        })
        .catch((action) => {
          messageModalNotify.value.audit = false
          // console.log(action)
        })
      break
    case 'revoke':
      // 撤回
      let arrId = urgingMessageTypes.map((item: any) => {
        return `types=${item}`
      })
      ElMessageBox({
        title: '提示',
        showCancelButton: true,
        message: () =>
          h('div', { style: 'margin-left:10px' }, [
            h('p', { style: 'font-size:16px' }, '您正在撤回您的创建任务。'),
            h(
              'p',
              { style: 'margin-top:10px;color:#9b9b9b;font-size:14px;' },
              '你正在执行任务撤回操作，原任务的审批记录将被清除，请确认该操作。'
            ),
          ]),
      }).then(() => {
        axios
          ?.get(
            `/api/filling/plan-task/revoke-planTask/${btn.scope.id}?${arrId.join('&')}`
          )
          .then((res) => {
            ElMessage.success('已撤回该任务')
            getTaskList(11)
          })
      })
      break
    // ====================转发任务====================
    case 'turnRevocation':
      // 撤回
      let arr = urgingMessageTypes.map((item: any) => {
        return item
      })
      ElMessageBox({
        title: '提示',
        showCancelButton: true,
        message: () =>
          h('div', { style: 'margin-left:10px' }, [
            h('p', { style: 'font-size:16px' }, '您正在撤回您的转发任务。'),
            h(
              'p',
              { style: 'margin-top:10px;color:#9b9b9b;font-size:14px;' },
              '你正在进行任务撤回操作，原任务已收集数据将被清除，请确认该操作。'
            ),
          ]),
      }).then(() => {
        let parmas = {
          id: btn.scope._raw.id,
          // reportTaskId: btn.scope._raw.reportTask.id,
          // areaOrganizationUnitId: btn.scope._raw.areaOrganizationUnitId,
          types: arr,
        }
        ReportRevokeInnerWrite(parmas)
          .then(() => {
            ElMessage.success('已撤回任务')
            getReportTask(16)
          })
          .catch((err) => {
            window.errMsg(err, '撤回')
          })
      })

      break
    case 'turnDetail':
      router.push({
        path: '/statementTask/report-task-detail',
        query: {
          reportTaskId: btn.scope.id,
          areaOrganizationUnitId: btn.scope._raw.areaOrganizationUnitId,
          id: btn.scope._raw.id,
          type: 'detail',
          from: 'myturn',
        },
      })
      break
    case 'turnDelete':
      DeleteReportFlowTask(btn.scope.id, btn.scope._raw.areaOrganizationUnitId)
        .then(() => {
          ElMessage.success('已删除任务')
          getReportTask(16)
        })
        .catch((err) => {
          window.errMsg(err, '删除')
        })
      break
    case 'turnExpediting':
      PushReportNotice({
        reportTaskId: btn.scope.id,
        areaOrganizationUnitId: btn.scope._raw.areaOrganizationUnitId,
        dingMessageType: 1,
      })
        .then(() => {
          ElMessage.success('已催办')
        })
        .catch((err) => {
          window.errMsg(err, '催办')
        })
      break
    case 'urging':
      isUrging.value = true
      urgingId.value = btn.scope.id
      break
    default:
      break
  }
}
const isUrging = ref(false)
const urgingList = ref(['0', '1'])
const urgingId = ref('')
const urgeingLoading = ref(false)
// 催办确定
const onUrging = () => {
  if (!urgingList.value.length) return ElMessage.error("请选择通知方式");
  urgeingLoading.value = true;
  let urgingMessageTypes: any = [];
  urgingList.value.forEach((item: any) => {
    if (item == "0") {
      urgingMessageTypes.push(0);
    } else {
      urgingMessageTypes.push(Number(item));
    }
  });
  if ((btnCode.value = "revoke")) {
    let arr = urgingMessageTypes.map((item: any) => {
      return `types=${item}`;
    });
    axios
      ?.get(`/api/filling/plan-task/revoke-planTask/${urgingId.value}?${arr.join("&")}`)
      .then((res) => {
        ElMessage.success("已撤回该任务");
        urgeingLoading.value = false;
        isUrging.value = false;
        getTaskList(11);
      });
  }

  if (!btnCode.value) {
    urging(urgingId.value, {
      urgingMessageTypes: urgingMessageTypes,
      reportTaskType: currentIndex.value == 1 ? 0 : 1,
    }).then((res) => {
      console.log(res);
      ElMessage.success("催办成功");
      urgeingLoading.value = false;
      isUrging.value = false;
    });
  }
};
// 催办取消
const closeUrging = () => {
  urgingList.value = ["0", "1"];
  btnCode.value = "";
  isUrging.value = false;
  urgeingLoading.value = false;
};
// 是否启用
const statusChange = (events: boolean, id: string) => {
  axios
    ?.post(`/api/filling/plan-task/set-active?id=${id}&enabled=${events}`)
    .then((res) => {
      getTaskList(11);
      ElMessage.success(`${events ? "已启用" : "已禁用"}`);
    });
};
const sizeChange = (val: number) => {
  pageSize.value = val;
  pageParams.value.MaxResultCount = val;
  pageParams.value.SkipCount = 0;
  currentIndex.value === 1 ? getTaskList(11) : getReportTask(16);
};
const currentPage = ref(1);
const pageSize = ref(10);
const currentChange = (val: number) => {
  currentPage.value = val;
  pageParams.value.SkipCount = pageParams.value.MaxResultCount * (val - 1);
  currentIndex.value === 1 ? getTaskList(11) : getReportTask(16);
};

// 跳转到创建任务页面
const createTask = () => {
  router.push("/statementTask/create");
};

const getPlanTaskProgress = () => {
  loading.value = true;
  GetPlanTaskProgress(taskManageStore.taskList.map((item: any) => item.id))
    .then((res: any) => {
      res.data.forEach((item: any) => {
        const task = taskManageStore.taskList.find((x: any) => x.id === item.planTaskId);
        if (task) {
          task.departmentReportingStatus = item.reportTaskStatus;
        }
      });
    })
    .catch((err: any) => {
      window.errMsg(err, "获取进度");
    })
    .finally(() => {
      loading.value = false;
    });
};

// 获取计划任务列表, 我创建的
const getTaskList = async (filterMode: number = 10) => {
  const params = util.extractNonEmptyValuesFromSearchParams({
    FilterMode: filterMode,
    ...searchParams.value,
  });
  loading.value = true;
  axios
    ?.get(
      `/api/filling/plan-task?${queryString.stringify(
        pageParams.value
      )}&${queryString.stringify(params)}`
    )
    .then((res: any) => {
      const { data } = res;
      const deleteAll = storage.get(ACTION_KEY.BatchDeleteReport);
      const revokeAll = storage.get(ACTION_KEY.BatchRevokeReport);
      const resetAll = storage.get(ACTION_KEY.BatchResetReport);
      const stoprows = storage.get(ACTION_KEY.BatchStopReport);

      if (deleteAll) {
        loading.value = false;
        taskManageStore.taskList = [];
        return;
      }

      if (revokeAll) {
        data.items.forEach((item: any) => {
          if (typeof revokeAll === "string" && revokeAll === "all") {
            item.status = 11;
          }
        });
      }

      if (stoprows) {
        data.items.forEach((item: any) => {
          if (typeof stoprows === "string" && stoprows === "all") {
            item.status = 8;
          } else if (stoprows.includes(item.id)) {
            item.status = 8;
          }
        });
      }

      if (resetAll) {
        data.items.forEach((item: any) => {
          if (typeof resetAll === "string" && resetAll === "all") {
            item.status = 14;
          } else if (resetAll.includes(item.id)) {
            item.status = 14;
          }
        });
      }

      taskManageStore.taskList = data.items.map((data: any) => ({
        ...data,
        departmentReportingStatus: "",
        remindDays: surplusDate(data.fillingPeriodType, data.newToDays, data.endDate),
      }));
      taskManageStore.$patch({ totalCount: data.totalCount });
      getPlanTaskProgress();
      loading.value = false;
    })
    .catch((error: any) => {
      if (error.response?.status === 500) {
        ElNotification.error("当前操作“报表创建”人员较多，请5分钟后再试");
      }
    });
};

// 获取报表任务列表
const getReportTask = async (FilterMode: number) => {
  const params = {
    Name: searchParams.value.Name,
    FillingPeriodType: searchParams.value.FillingPeriodType,
    Status: searchParams.value.PlanTaskStatus,
    fillingRange: searchParams.value.fillingRange,
  };
  loading.value = true;
  await axios
    ?.get(
      `/api/filling/report-task-ou?Sorting=submitTime Desc&FilterMode=${FilterMode}&${queryString.stringify(
        pageParams.value
      )}&${queryString.stringify(params)}`
    )
    .then((res: any) => {
      const { data } = res;
      taskManageStore.taskList = data.items.map((x: any) => ({
        id: x.reportTask.id,
        reportTaskName: x.reportTask?.name,
        reportTaskCreatorName: x.reportTask?.creatorName,
        creationTime: x.creationTime,
        status: x.status,
        createdBigDepartment: x.createdBigDepartment,
        createdDepartment: x.createdDepartment,
        fillerSubmitTime: x.fillerSubmitTime,
        endTime: x?.reportTask.endDate,
        currentServiceTime: x?.reportTask.currentServiceTime,
        _raw: JSON.parse(JSON.stringify(x)),
      }));
      taskManageStore.$patch({ totalCount: data.totalCount });
      loading.value = false;
    })
    .catch((error: any) => {
      if (error.response?.status === 500) {
        ElNotification.error("当前操作人员较多，请5分钟后再试");
      }
    });
};

async function checkBoxChange(val: CheckboxValueType) {
  searchParams.value.isSelf = val;
  await getTaskList(11);
}

const selectedCount = ref(0);
const checked = ref(true);

const tableOffsetHeight = ref(-165);
const tableRef = ref();
const expendSearch = (height: number, expand: boolean) => {
  tableOffsetHeight.value = -(height + 165);
  tableRef.value.resize();
};
const resetSearch = () => {
  // searchParams.value = {
  // 	Name: null,
  // 	FillingPeriodType: undefined,
  // 	PlanTaskStatus: undefined,
  // 	isSelf: true,
  // }

  Object.keys(searchParams.value).forEach((key) => {
    searchParams.value[key] = null;
    searchParams.value.isSelf = true;
  });

  if (currentIndex.value === 1) {
    getTaskList(11);
  } else {
    getReportTask(16);
  }
};
const selectionList: any = ref([]);
const selectionChange = (selection: any) => {
  selectionList.value = selection;
};
const onClickTabs = (val: LTABS) => {
  if (val === LTABS.QUALITYREPORT) {
    router.push("/taskQualityReport");
    useSleep(16).then(() => {
      currentActiveName.value = LTABS.ICREATE;
    });
    return;
  }
  if (val === LTABS.VISUALIZEDTABLE) {
    router.push("/visualizedTable");
    useSleep(16).then(() => {
      currentActiveName.value = LTABS.ICREATE;
    });
    return;
  }

  if (loading.value) {
    return;
  }

  taskManageStore.taskList = [];
  searchParams.value = {
    Name: null,
    FillingPeriodType: undefined,
    PlanTaskStatus: undefined,
    fillingRange: undefined,
    isSelf: true,
  };

  currentPage.value = 1;
  pageSize.value = 10;

  if (val === LTABS.ICREATE) {
    currentIndex.value = 1;
    buttons.value = tableButtons[LTABS.ICREATE];
    colData.value = columns[LTABS.ICREATE];
    getTaskList(11);
  } else if (val === LTABS.ITURN) {
    currentIndex.value = 2;
    buttons.value = tableButtons[LTABS.ITURN];
    colData.value = columns[LTABS.ITURN];
    getReportTask(16);
  }
  cureerntText.value = currentIndex.value == 1 ? "任务创建" : "";
  selectionList.value = [];
};

const handleClickbell = () => {
  if (selectionList.value.length === 0) {
    return ElMessage.warning("请选择要催办的任务");
  }
  isBatchUrge.value = true;
};
const getTimeLeft = (scoped: any) => {
  if (!scoped.rowData.endTime || !scoped.rowData.currentServiceTime) return "";
  const start: any = new Date(scoped.rowData.currentServiceTime);
  const end: any = new Date(scoped.rowData.endTime);
  const diffTime = Math.abs(end - start);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  let type = "";
  if (diffDays <= 1) {
    type = "danger";
  } else if (diffDays <= 3) {
    type = "warning";
  } else {
    type = "primary";
  }

  return {
    type,
    diffDays,
  };
};

const isCopyReport = ref(false);
const isPermissionConfig = ref(false);
const isBatchUrge = ref(false);
const isBatchEditReport = ref(false);
const isBatchStopReport = ref(false);
const isBatchRevokeReport = ref(false);
const isBatchResetReport = ref(false);
const isBatchDeleteFlow = ref(false);
const isBatchBindFlow = ref(false);
const isBatchUnbindFlow = ref(false);
const isBatchFlowDes = ref(false);
const batchFlowModel = ref(0);
const isReminderNotification = ref(false);
const isRiskStrategy = ref(false);
const isDeleteTask = ref(false)
const isPerformanceTesting = ref(false)
const resetBatchGetTaskList = () => {
  useSleep(1100).then(() => {
    selectionList.value = [];
    tableRef.value.clearSelection();
    getTaskList(11);
  });
};

const resetBatch = () => {
  useSleep(16).then(() => {
    selectionList.value = [];
    tableRef.value.clearSelection();
  });
};

const handleClickDeleteAll = () => {
  if (currentActiveName.value == LTABS.ICREATE) {
    ElMessage.success("批量删除成功");
    storage.save(ACTION_KEY.BatchDeleteReport, "all");
    getTaskList(11);
  } else if (currentActiveName.value == LTABS.ITURN) {
  }
};

const handleClickDeleteSelect = async () => {
  if (selectionList.value.length === 0) {
    return ElMessage.warning("请选择要删除的任务");
  }

  loading.value = true;
  const promises = [];

  for (let i = 0; i < selectionList.value.length; i++) {
    promises.push(
      currentActiveName.value == LTABS.ICREATE
        ? axios?.post("/api/filling/StaffPlanTaskHide/create", {
          staffId: UserStore.userInfo?.id,
          planTaskId: selectionList.value[i].id,
        })
        : DeleteReportFlowTask(
          selectionList.value[i].id,
          selectionList.value[i]._raw.areaOrganizationUnitId
        )
    );
  }

  Promise.all(promises)
    .then(() => {
      ElMessage.success("批量删除成功");
      currentActiveName.value == LTABS.ICREATE ? getTaskList(11) : getReportTask(16);
    })
    .catch((err) => {
      window.errMsg(err, "删除");
    })
    .finally(() => {
      loading.value = false;
      selectionList.value = [];
      tableRef.value.clearSelection();
    });
};

const handleClickStop = (type: string) => {
  if (selectionList.value.length === 0 && type === "select") {
    return ElMessage.warning("请选择要终止的任务");
  }

  isBatchStopReport.value = true;
};

const handleClickRevoke = (type: string) => {
  if (selectionList.value.length === 0 && type === "select") {
    return ElMessage.warning("请选择要撤回的任务");
  }
  isBatchRevokeReport.value = true;
};

const onBatchRevokeReport = () => {
  let urgingMessageTypes: any = [];
  urgingList.value.forEach((item: any) => {
    if (item == "0") {
      urgingMessageTypes.push(0);
    } else {
      urgingMessageTypes.push(Number(item));
    }
  });
  let arrId = urgingMessageTypes.map((item: any) => {
    return `types=${item}`;
  });
  const ids = selectionList.value.map((item: any) => {
    return item.id;
  });

  if (ids.length === 0) {
    isBatchRevokeReport.value = false;
    storage.save(ACTION_KEY.BatchRevokeReport, "all");
    getTaskList(11);
    return ElMessage.warning("全部撤回成功");
  }

  const promises = [];
  for (let i = 0; i < ids.length; i++) {
    promises.push(
      axios?.get(`/api/filling/plan-task/revoke-planTask/${ids[i]}?${arrId.join("&")}`)
    );
  }
  loading.value = true;
  Promise.all(promises)
    .then(() => {
      ElMessage.success("已撤回该任务");
      getTaskList(11);
    })
    .catch((err) => {
      window.errMsg(err, "撤回");
    })
    .finally(() => {
      loading.value = false;
      isBatchRevokeReport.value = false;
      selectionList.value = [];
      tableRef.value.clearSelection();
    });
};

const handleClickReset = (type: string) => {
  if (selectionList.value.length === 0 && type === "select") {
    return ElMessage.warning("请选择要重置的任务");
  }
  isBatchResetReport.value = true;
};

const handleClickDeleteProcess = (type: string) => {
  if (selectionList.value.length === 0 && type === "select") {
    return ElMessage.warning("请选择要删除的任务流程");
  }
  isBatchDeleteFlow.value = true;
};

const handleClickBind = (type: string) => {
  if (selectionList.value.length === 0 && type === "select") {
    return ElMessage.warning("请选择要绑定的任务流程");
  }
  isBatchBindFlow.value = true;
};

const handleClickUnbindProcess = (type: string) => {
  if (selectionList.value.length === 0 && type === "select") {
    return ElMessage.warning("请选择要解绑的任务流程");
  }
  isBatchUnbindFlow.value = true;
};

const handleDeleteFlowDes = (type: number) => {
  if (selectionList.value.length === 0 && (type === 0 || type === 1 || type === 2)) {
    return ElMessage.warning(`请选择要${type === 1 ? "修改" : "删除"}的任务流程`);
  }
  batchFlowModel.value = type;
  isBatchFlowDes.value = true;
};

const handleClickReminderNotification = () => {
  if (selectionList.value.length === 0) {
    return ElMessage.warning("请选择要提醒的任务");
  }
  isReminderNotification.value = true;
};
const isProcessUsageReportShow = ref(false);
const handleProcessUsageReportShow = () => {
  isProcessUsageReportShow.value = true
}
const isReportWatermark = ref(false)
const handleIsReportWatermark = () => {
  isReportWatermark.value = true;
};
const isPerformanceOptimizationReport = ref(false);
const handlePerformanceOptimizationReportShow = () => {
  isPerformanceOptimizationReport.value = true;
};
const handlePerformanceOptimizationReportClose = () => {
  isPerformanceOptimizationReport.value = false;
};
const isOptimizationReportWatermark = ref(false);
const handleOptimizationReportWatermarkShow = () => {
  isOptimizationReportWatermark.value = true;
};
const handleOptimizationReportWatermarkClose = () => {
  isOptimizationReportWatermark.value = false;
};
//任务终止风险策略
const handleClickRiskStrategy = () => {
  if (selectionList.value.length === 0) {
    return ElMessage.warning('请选择要提醒的任务')
  }
  isRiskStrategy.value = true
}
//创建方式性能检测
const handlePerformanceTesting = () => {
  isPerformanceTesting.value = true
}
//任务删除
const handleDeleteTask = () => {
  isDeleteTask.value = true;
};
const handleProcessUsageReportClose = () => {
  isProcessUsageReportShow.value = false;

  onActivated(async () => {
    const from = route.query.from;

    if (from === "turn") {
      currentActiveName.value = LTABS.ITURN;
    }

    taskManageStore.taskList = [];
    // taskManageStore.$patch({taskList:null})
    // setTimeout(async () => {
    // 如果是tab第一项 则请求计划任务列表
    // if (currentIndex.value === 1) await getTaskList()
    // 如果是tab第二项 则请求报表任务列表 7代表提交记录
    // else await getReportTask(7)
    // }, 500)

    if (currentActiveName.value === LTABS.ICREATE) {
      colData.value = columns[LTABS.ICREATE];
      buttons.value = tableButtons[LTABS.ICREATE];
      await getTaskList(11);
    } else if (currentActiveName.value === LTABS.ITURN) {
      colData.value = columns[LTABS.ITURN];
      buttons.value = tableButtons[LTABS.ITURN];
      await getReportTask(16);
    }
  });
};
const operatingManualRef = ref(null);
// 操作手册
const showOperatingManual = () => {
  if (!selectionList.value.length) {
    ElMessage.warning("请选择数据");
    return false;
  }
  if (operatingManualRef && operatingManualRef.value) {
    operatingManualRef.value.open();
  }
};
// 评价指标
const showCreateEvalutaion = ref(false);
// 应急预案
const showPlan = ref(false);

// onMounted(() => {
// 	getTaskList(11)
// })
// 导出
const handleClickExport = (isAll: boolean) => {
  if (isAll) {
    exportExcel(tableData.value || [], columns[LTABS.ICREATE])
  } else {
    if (selectionList.value.length === 0) {
      ElMessage.warning('请选择数据')
      return false
    }
    exportExcel(selectionList.value, columns[LTABS.ICREATE])
  }
}
</script>
<template>
  <div class="statement">
    <Block title="" :enable-expand-content="true" :enableBackButton="false" :enableFixedHeight="true"
      @content-expand="expendSearch">
      <template #title>
        <el-tabs v-model="currentActiveName" class="tabs-ui" :before-leave="() => !loading" @tab-change="onClickTabs">
          <el-tab-pane :label="LTABS.ICREATE" :name="LTABS.ICREATE"></el-tab-pane>
          <el-tab-pane :label="LTABS.ITURN" :name="LTABS.ITURN"></el-tab-pane>
          <el-tab-pane :label="LTABS.QUALITYREPORT" :name="LTABS.QUALITYREPORT"></el-tab-pane>
          <el-tab-pane :label="LTABS.VISUALIZEDTABLE" :name="LTABS.VISUALIZEDTABLE"></el-tab-pane>
        </el-tabs>
      </template>
      <template #topRight>
        <div style="width: 100%; height: 100%; display: flex; align-items: center">
          <!-- <el-checkbox
						v-if="
							(UserStore.userInfo?.staffRole.includes(STAFFROLEARRAY[1]) ||
								UserStore.userInfo?.staffRole.includes(STAFFROLEARRAY[4])) &&
							currentIndex === 1
						"
						v-model="checked"
						label="仅显示本人经办任务"
						size="small"
						@change="checkBoxChange"
						class="mg-left-10"
					></el-checkbox> -->

          <template v-if="currentActiveName === LTABS.ICREATE">
            <el-dropdown class="mg-left-10">
              <el-button size="small" type="danger">
                批量删除<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleClickDeleteAll">删除全部
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleClickDeleteSelect">删除选择
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-dropdown class="mg-left-10">
              <el-button size="small" type="danger">
                批量撤回<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleClickRevoke('all')">撤回全部
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleClickRevoke('select')">撤回选择
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-dropdown class="mg-left-10">
              <el-button size="small" type="danger">
                批量终止<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleClickStop('all')">终止全部
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleClickStop('select')">终止选择
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-dropdown class="mg-left-10">
              <el-button size="small" type="danger">
                批量重置<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleClickReset('all')">重置全部
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleClickReset('select')">重置选择
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-dropdown class="mg-left-10">
              <el-button size="small" type="danger">
                批量删除任务流程<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleClickDeleteProcess('all')">删除全部流程任务
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleClickDeleteProcess('select')">删除选择流程任务
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-dropdown class="mg-left-10 mg-right-10">
              <el-button size="small" type="primary">
                批量表格任务<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleClickBind('all')">全部绑定任务流程
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleClickBind('select')">绑定选择任务流程
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleClickUnbindProcess('all')">全部解绑任务流程
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleClickUnbindProcess('select')">解绑选择任务流程
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-button type="primary" size="small" @click="isPermissionConfig = true">
              权限配置
            </el-button>
            <el-button type="primary" size="small" @click="handleDeleteTask">
              任务删除
            </el-button>
          </template>

          <el-button type="primary" size="small" @click="createTask" class="mg-left-10">
            创建任务
          </el-button>
          <el-button type="primary" size="small" @click="showOperatingManual" :disabled="!selectionList.length"
            class="mg-left-10">
            操作手册
          </el-button>
          <el-button type="primary" size="small" :disabled="!selectionList.length" @click="showCreateEvalutaion = true"
            class="mg-left-10">
            评价指标
          </el-button>
          <el-button type="primary" size="small" @click="showPlan = true" class="mg-left-10">
            任务终止应急预案
          </el-button>
        </div>
      </template>
      <template #expand>
        <div class="search-box" v-action:enter="() => (currentIndex === 1 ? getTaskList(11) : getReportTask(16))
          ">
          <el-input size="default" v-model="searchParams.Name" placeholder="请输入任务名称" class="value">
          </el-input>

          <el-select clearable filterable v-model="searchParams.FillingPeriodType" placeholder="请选择填报周期" size="default"
            class="value">
            <el-option v-for="item in periodList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-input size="default" v-model="searchParams.fillingRange" placeholder="请输入填报范围" clearable class="value">
          </el-input>

          <el-select v-if="currentIndex === 1" clearable v-model="searchParams.PlanTaskStatus" placeholder="请选择任务状态"
            size="default" @change="() => (currentIndex === 1 ? getTaskList(11) : getReportTask(16))" class="value">
            <el-option v-for="item in ReportsFlowStatus.filter(
              (x) => x.type === 'plan' && x.enable
            )" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-if="currentIndex === 2" clearable v-model="searchParams.PlanTaskStatus" placeholder="请选择任务状态"
            size="default" @change="() => (currentIndex === 1 ? getTaskList(11) : getReportTask(16))" class="value">
            <el-option v-for="item in reportStatusTrans.filter((x: any) => x.enable)" :key="item.value"
              :label="item.name" :value="item.value" />
          </el-select>

          <el-button size="default" type="default" @click="resetSearch">
            <i class="i-ic-outline-cleaning-services" mr-3px></i>
            清空
          </el-button>
          <el-button :loading="loading" size="default" type="primary"
            @click="currentIndex === 1 ? getTaskList(11) : getReportTask(16)">
            查询</el-button>
        </div>
      </template>
      <BaseTableComp ref="tableRef" :colData="colData" :data="tableData" :checkbox="true" :buttons="buttons"
        :selected-count="selectedCount" :total="totalCount" :currentIndex="currentIndex" :cureerntText="cureerntText"
        :offsetHeight="tableOffsetHeight" :visible-header="true" :visible-page="true" :visible-search="false"
        :visibleIndex="false" :visible-export="false" :visible-setting="false" :current-page="currentPage"
        :page-size="pageSize" :loading="loading" @selectionChange="selectionChange" @click-button="clickButton"
        @size-change="sizeChange" @current-change="currentChange">
        <template #header>
          <el-button type="primary" size="small" @click="handleClickbell">
            全部催办
          </el-button>
          <el-button type="primary" size="small" @click="isBatchEditReport = true">
            批量编辑
          </el-button>

          <el-dropdown class="mg-left-10 mg-right-10">
            <el-button size="small" type="primary">
              批量流程说明<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleDeleteFlowDes(0)">批量新增流程说明
                </el-dropdown-item>
                <el-dropdown-item @click="handleDeleteFlowDes(1)">批量修改流程说明
                </el-dropdown-item>
                <el-dropdown-item @click="handleDeleteFlowDes(2)">批量删除流程说明
                </el-dropdown-item>
                <el-dropdown-item @click="handleDeleteFlowDes(3)">删除全部说明
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button size="small" type="primary" @click="handleClickReminderNotification">
            提醒通知
          </el-button>
          <el-button size="small" type="primary" @click="handleProcessUsageReportShow">
            流程使用报告
          </el-button>
          <el-button size="small" type="primary" @click="handleIsReportWatermark">
            报告水印
          </el-button>
          <el-button size="small" type="primary" @click="handlePerformanceOptimizationReportShow">
            性能优化报告
          </el-button>
          <el-button size="small" type="primary" @click="handleOptimizationReportWatermarkShow">
            优化报告水印
          </el-button>


          <el-button size="small" type="primary" @click="handleClickRiskStrategy">
            任务终止风险策略
          </el-button>

          <el-dropdown class="mg-left-10 mg-right-10">
            <el-button size="small" type="primary"> 导出可视化表格 </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleClickExport(true)">
                  导出全部
                </el-dropdown-item>
                <el-dropdown-item @click="handleClickExport(false)">导出选择
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button size="small" type="primary" @click="handleDeleteTask">
            任务删除
          </el-button>
          <el-button size="small" type="primary" @click="handlePerformanceTesting">
            创建方式性能检测
          </el-button>
        </template>
        <template #name="{ rowData }">
          <span class="link-click" @click="clickButton({ btn: { code: 'detail' }, scope: rowData })">{{ rowData.name
          }}</span>
        </template>
        <template #departments="scope">
          <!-- {{ scope.rowData?.departments }} -->
          {{
            scope.rowData && scope.rowData?.fillingMode === 2
              ? scope.rowData?.staffs !== null
                ? scope.rowData?.staffs.map((v: any) => v.name).toString()
                : '-'
              : scope.rowData?.departments !== null
                ? scope.rowData?.departments
                  .map((v: any) => v?.parent?.name + '-' + v.name)
                  .toString()
                : '-'
          }}
        </template>
        <template #areaOrganizationUnit="scope">
          {{ scope.rowData.areaOrganizationUnit }}
        </template>
        <template #endTime="scoped">
          <div class="df aic">
            {{ scoped.rowData.endTime || "-" }}
            <el-tag v-if="
              new Date(scoped.rowData.endTime).getTime() >
              new Date(scoped.rowData.currentServiceTime).getTime()
            " class="mg-left-10" :type="getTimeLeft(scoped)?.type">
              剩余{{ getTimeLeft(scoped)?.diffDays }}天
            </el-tag>
            <el-tag v-else-if="scoped.rowData.endTime" type="danger" class="mg-left-10">
              已截止
            </el-tag>
          </div>
        </template>
        <template #endDate="scope">
          <template v-if="scope.rowData.status === 9">已完结</template>
          <template v-else-if="scope.rowData.status === 8">已手动终止</template>
          <template v-else-if="scope.rowData.status === 10">时间已截止</template>
          <template v-else>
            <template v-if="scope.rowData.remindDays === false">
              <span text="gray">时间已截止</span>
            </template>
            <template v-else>
              <span v-if="scope.rowData.remindDays > 7" style="color: #20a162" class="df aic">
                <el-icon style="color: #20a162">
                  <WarningFilled />
                </el-icon>
                剩余{{ scope.rowData.remindDays }}天
              </span>
              <span v-if="scope.rowData.remindDays <= 7 && scope.rowData.remindDays > 3" class="df aic"
                style="color: #ffaa18">
                <el-icon style="color: #ffaa18">
                  <WarningFilled />
                </el-icon>
                剩余{{ scope.rowData.remindDays }}天
              </span>
              <span v-if="scope.rowData.remindDays <= 3" class="df aic" style="color: #20a162">
                <el-icon style="color: #a61b29">
                  <WarningFilled />
                </el-icon>
                剩余{{ scope.rowData.remindDays }}天
              </span>
            </template>
          </template>

          <!-- {{ scope.rowData.remindDays }} -->
        </template>
        <template #fillingPeriodType="scope">
          {{ periodList[scope.rowData.fillingPeriodType - 1].label }}
        </template>
        <template #status="scoped">
          <!-- <el-button round size="small" :class="statusTrans[scope.rowData.status - 1]?.class">{{
						statusTrans[scope.rowData.status - 1]?.name
					}}</el-button> -->
          <el-tag :type="ReportsFlowStatusType(
            scoped.rowData.status,
            currentActiveName === LTABS.ICREATE ? 'plan' : 'turn'
          )
            ">
            {{
              ReportsFlowStatus.find(
                (f) =>
                  f.value === scoped.rowData.status &&
                  f.type === (currentActiveName === LTABS.ICREATE ? "plan" : "turn")
              )?.label || "-"
            }}
          </el-tag>
        </template>
        <!-- <template #enabled="scope">
				<el-switch
					v-model="scope.rowData.enabled"
					:disabled="scope.rowData.enabled"
					size="small"
					@change="statusChange($event, scope.rowData.id)"
				/>
			</template> -->
        <template #reportTaskStatus="scope">
          <span class="el-button el-button--small is-round"
            :class="reportStatusTrans[scope.rowData.reportTaskStatus]?.class">
            {{ reportStatusTrans[scope.rowData?.reportTaskStatus]?.name }}
          </span>
        </template>
        <template #reportTaskCreatorName="scope">
          {{ scope.rowData._raw.reportTask?.creator?.name || "-" }}
        </template>
        <template #departmentReportingStatus="scoped">
          <LoadingTransition v-if="loading"></LoadingTransition>
          <template v-else>
            {{ scoped.rowData.departmentReportingStatus || "-" }}
          </template>
        </template>
      </BaseTableComp>
      <Dialog v-model="isUrging" :title="btnCode ? '报表撤回' : '报表催办'" width="600" @clickConfirm="onUrging"
        @close="closeUrging" :loading="urgeingLoading">
        <div>
          <p v-show="!btnCode">将批量通知各未完成填报部门尽快完成填报</p>
          <el-checkbox-group v-model="urgingList" style="margin-top: 10px">
            <el-checkbox label="发送系统内消息" value="0" />
            <el-checkbox label="发送渝快政工作通知" value="1" />
            <el-checkbox label="发送渝快政Ding消息" value="2" />
          </el-checkbox-group>
        </div>
      </Dialog>
    </Block>
    <Dialog
        v-model="visibleDelete"
        title="删除任务"
        width="500px"
        confirm-text="删除"
        @confirm="handleDeleteConfirm"
        @cancel="handleDeleteCancel"
      >
      <div >请确认是否删除？删除后不可恢复！</div>
      <div style="display: flex; align-items: center; margin-top: 20px;">
        <div style="margin-right: 10px;">任务流程：</div>
        <el-select
					clearable
					v-model="selectValue"
					size="default"
					style="width: 200px"
					:filterable="true"
					:placeholder="`请选择`"
				>
					<el-option
						v-for="selectItem of selectList"
						:label="selectItem.label"
						:value="selectItem.value"
					/>
				</el-select>
      </div>
    </Dialog>
    <CopyReport v-model="isCopyReport" title="报表复制" />
    <PermissionConfig v-model="isPermissionConfig" title="权限配置" />
    <BatchUrge v-model="isBatchUrge" title="批量催办" @close="tableRef.clearSelection()" />
    <BatchEditReport v-model="isBatchEditReport" title="批量编辑" />
    <BatchStopReport v-model="isBatchStopReport" :title="selectionList.length > 1 ? '批量终止' : '终止'"
      :selectedRows="selectionList" @click-confirm="resetBatchGetTaskList" @closed="resetBatch" />
    <BatchResetReport v-model="isBatchResetReport" :title="selectionList.length > 1 ? '批量报表重置' : '报表重置'"
      :selectedRows="selectionList" @click-confirm="resetBatchGetTaskList" @closed="resetBatch" />
    <Dialog v-model="isBatchRevokeReport" title="批量撤回" @click-confirm="onBatchRevokeReport">
      <p class="pd-bottom-20">
        你正在撤回全部报表任务，撤回后已审核通过数据可继续查看，未填报数据的部门
        将无法再填报
      </p>
      <p class="df aic">
        <Icons name="Warning" class="mg-right-5" color="#f00" />
        系统将通知相关填报人。
      </p>
    </Dialog>
    <BatchDeleteFlow v-model="isBatchDeleteFlow" :title="selectionList.length > 1 ? '批量删除任务流程' : '删除任务流程'"
      :selectedRows="selectionList" @click-confirm="resetBatchGetTaskList" @closed="resetBatch" />
    <BatchBindFlow v-model="isBatchBindFlow" :title="selectionList.length > 1 ? '批量绑定任务流程' : '绑定任务流程'"
      :selectedRows="selectionList" @click-confirm="resetBatchGetTaskList" @closed="resetBatch" />
    <BatchUnbindFlow v-model="isBatchUnbindFlow" :title="selectionList.length > 1 ? '批量解绑任务流程' : '解绑任务流程'"
      :selectedRows="selectionList" @click-confirm="resetBatchGetTaskList" @closed="resetBatch" />


    <BatchFlowDes
      v-model="isBatchFlowDes"
      :title="selectionList.length > 1 ? '批量流程说明' : '流程说明'"
      :model="batchFlowModel"
      :selectedRows="selectionList"
      @click-confirm="resetBatchGetTaskList"
      @closed="resetBatch"
    />
    <ReminderNotification
      v-model="isReminderNotification"
      :selectedRows="selectionList"
      title="提醒通知"
      width="800px"
    />
    <ReportWatermark
      v-model="isReportWatermark"
      :selectedRows="selectionList"
      

      width="1400px"
    />
    <ProcessUsageReport
      v-model="isProcessUsageReportShow"
      :selectedRows="selectionList"

      width="800px"
    />

    <BatchFlowDes v-model="isBatchFlowDes" :title="selectionList.length > 1 ? '批量流程说明' : '流程说明'" :model="batchFlowModel"
      :selectedRows="selectionList" @click-confirm="resetBatchGetTaskList" @closed="resetBatch" />
    <ReminderNotification v-model="isReminderNotification" :selectedRows="selectionList" title="提醒通知" width="800px" />
    <ReportWatermark v-model="isReportWatermark" :selectedRows="selectionList" width="1400px" />
    <ProcessUsageReport :isProcessUsageReportShow="isProcessUsageReportShow" :selectedRows="selectionList"
      width="800px" />

    <RiskStrategy v-model="isRiskStrategy" :selectedRows="selectionList" title="任务终止风险策略" width="600px" />

    <PerformanceOptimizationReport v-model="isPerformanceOptimizationReport" :selectedRows="selectionList"
      title="性能优化报告" width="800px" />

    <OptimizationReportWatermark v-model="isOptimizationReportWatermark" :selectedRows="selectionList" title="优化报告水印"
      width="800px" />


    <OperatingManual ref="operatingManualRef" :selectionList="selectionList" />
    <CreateEvaluation v-model="showCreateEvalutaion" :selectionList="selectionList" />
    <DeleteTask v-model="isDeleteTask" title="任务删除" width="1000px" />
    <PerformanceTesting v-model="isPerformanceTesting" title="创建方式性能检测" width="1000px" />
    <EmergencyPlan title="任务终止预案" v-model="showPlan" height="160" @close="() => (showPlan = false)" />
  </div>
</template>
<route>
	{
		meta: {
			title: '临时报表创建',
		},
	}
</route>
<style scoped lang="scss">
:deep(.el-checkbox) {
  margin-right: 10px !important;
}

.active {
  border-bottom: 3px solid #2185c5;
  font-weight: bold;
  color: #000;
}

.statement {
  .title-tag {
    width: 110px;
    height: 26px;
    background: #e3ecfa;
    font-size: 14px;
    border-radius: 4px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei, sans-serif;
    font-weight: bold;
    color: #3d7fff;
  }
}

.link {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: no-wrap;
  color: var(--z-main);
}

.search-box {
  display: flex;
  padding: 10px 15px;

  .value {
    margin-right: 10px;
    width: 250px;
  }
}
</style>

<script setup lang="ts" name="typicalscenario">
import { onActivated, ref, inject, onMounted, reactive } from "vue";
import { useRouter, useRoute } from "vue-router";
const axios = inject("#axios") as any;
import { myOnlineLedgers } from "@/api/LedgerApi";
import {
  auditSomeApi, // 批量审核
  // getAuditingApi, // 批量审核
} from "@/api/ReportApi";
import {
  FlowType as WorkFlowType,
  FlowAuditTypes,
  FlowAuditTypeData,
} from "@/define/Workflow";
import { ElMessage, ElMessageBox } from "element-plus";
import { useUserStore } from "@/stores/useUserStore";
const route = useRoute();
const router = useRouter();
import TodoRemindComponent from "../taskPending/component/TodoRemindComponent.vue";
// 表格中的操作列
const buttons: any = [
  {
    code: "process",
    label: "审核",
    type: "primary",
    icon: "i-majesticons-pencil-alt-line",
    verify: "true",
  },
];
const options = ref([
  {
    value: "1",
    label: "上架",
  },
  {
    value: "2",
    label: "下架",
  },
]);
// 查询条件
const runwayForm: any = ref({
  name: "",
  warningOperationLogics: [
    {
      name: "",
      keyValue: "",
      operatorSymbol: null,
      threshold: "",
      remark: "",
    },
  ],
});
// 设置表格高度计算
const tableHeight = ref(0);
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 80;
};
const tableData = ref([]);

const showEcharts = ref();

// 表中的内容
const recordColData = ref([]);
// 表头
const colData: any = ref([
  { label: "任务名称", prop: "title", type: "text" },
  { label: "任务类型", prop: "_category", type: "text" },
  { label: "提交部门", prop: "createUserLargeDepartmentName", type: "text" },
  { label: "提交科室", prop: "createUserDepartmentName", type: "text" },
  { label: "提交人", prop: "createUserName", type: "text" },
  {
    label: "提交时间",
    prop: "businessRelevanceTime",
    type: "datetime",
    sortable: "custom",
  },
  { label: "截止时间", prop: "deadline", type: "datetime" },
]);
//表格与分页关联
// 分页相关参数
const pagination = ref({
  total: 0,
  page: 1,
  size: 10,
});
// 查询
const rules = reactive({
  radio1: [{ required: true, message: "请选择通过", trigger: "change" }],
  textarea: [{ required: true, message: "请输入审核意见", trigger: "blur" }],
});

const onTableClickButton = ({ btn, row, index }: any) => {
  // if (btn.code === "delete") {
  //   ElMessageBox.confirm(`是否要删除 ${row.name} 预警机制?`, "消息确认", {
  //     confirmButtonText: "确定",
  //     cancelButtonText: "取消",
  //     type: "warning",
  //   })
  //     .then(() => {
  //       axios?.delete(`/api/new-feature/warning-mechanism/${row.id}`).then((res: any) => {
  //         ElMessage.success("删除成功！");
  //         tableRef.value.reload();
  //       });
  //     })
  //     .catch(() => {});
  // }
  if (btn.code === "process") {
    router.push({
      path: "/serviceManage/taskInformation",
      query: {
        id: row.id,
        taskId: row.process.id,
        keyword: row.process.keyword2,
        businessId: row.process.businessId,
        batchType: row.process.businessExtend.Type,
        Remark: row.process.businessExtend.Remark,
      },
    });
  }
};
const handleAddTable = () => {
  let obj: any = {
    name: "",
    keyValue: "",
    operatorSymbol: null,
    threshold: "",
    remark: "",
  };
  runwayForm.value.warningOperationLogics.push(obj);
};
const submit = async (formEl: any) => {
  if (!formEl) return;
  let validState;
  await formEl.validate((valid: any, fields: any) => {
    console.log(valid);
    validState = valid;
  });
  if (validState) {
    console.log(runwayForm);
    let data = {
      code: runwayForm.value.radio1,
      des: runwayForm.value.textarea,
      taskIds: allSelectPageData.value,
    };
    auditSomeApi(data);
    processDialog.value = false;
    tableRef.value.clearSelection();
    tableRef.value.reload();
    // let id = runwayForm.value.id;
    // let data = runwayForm.value;
    // if (id) {
    //   console.log(runwayForm.value);
    //   axios?.put(`/api/new-feature/warning-mechanism/${id}`, data).then((res: any) => {
    //     showTodoRemind.value = false;
    //     tableRef.value.reload();
    //   });
    // } else {
    //   console.log(runwayForm.value);
    //   axios?.post("/api/new-feature/warning-mechanism", data).then((res: any) => {
    //     showTodoRemind.value = false;
    //     tableRef.value.reload();
    //   });
    // }
  }
};
// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type === "page") {
    pagination.value.page = val;
    reqParams.skipCount = (val - 1) * pagination.value.size;
  } else {
    pagination.value.page = 1;
    pagination.value.size = val;
    reqParams.maxResultCount = pagination.value.size;
    reqParams.skipCount = 0;
  }
};
const formSearch = ref({});
//表格与分页关联
const reqParams = reactive({
  Name: null,
  skipCount: 0,
  maxResultCount: 10,
  Sorting: "BusinessRelevanceTime desc",
  BusinessType: "Ledger",
  title: "",
  startDate: "",
  endDate: "",
});
const tableRef = ref();
const ledgerOptions = ref();
const LedgersList = async () => {
  const res = await myOnlineLedgers();
  res.data.forEach((item: any) => {
    item.label = item.name;
    item.value = item.id;
  });
  ledgerOptions.value = res.data;
};
const tagOptions = ref();
const tagsList = async () => {
  const res = await axios?.get(`/api/new-feature/tags`);
  tagOptions.value = res.data.items;
};

const allSelectPageData = ref([]);
const handleSelectionChange = (val: any) => {
  let ids = val.map((item: any) => item.id);
  allSelectPageData.value = ids; // 点击的时候保存数据
};
const ruleFormRef = ref();
const dialogVisible = ref();
const handleClose = () => {
  showTodoRemind.value = false;
  runwayForm.value = {
    name: "",
    warningOperationLogics: [
      {
        name: "",
        keyValue: "",
        operatorSymbol: null,
        threshold: "",
        remark: "",
      },
    ],
  };
};
// 待办设置
const showTodoRemind = ref(false);
const hanleTodo = () => {
  showTodoRemind.value = true;
};
//审核
const processDialog = ref(false);
const processTitle = ref("");
const hanleProcess = (title: any) => {
  processTitle.value = title;
  processDialog.value = true;
};
const handleGoto = (url: any) => {
  router.push(url);
};
const recordDialog = ref();
const hanleRecord = () => {
  router.push({
    path: "/taskPending/record",
  });
};
const empty = () => {};
const value = ref();
const checkedBox = ref(true);
const handleCount = () => {
  dialogVisible.value = true;
};
const activeName = ref("0");
const handleClick = (row: any) => {
  console.log(row);
};
const beforeComplete = ({ items, next }: any) => {
  const temp: any = [];
  items.forEach((x: any) => {
    x.title = x.process.title;
    x.createUserName = x.process.createUserName;
    x.createUserLargeDepartmentName = x.process.createUserLargeDepartmentName;
    x.createUserDepartmentName = x.process.createUserDepartmentName;
    x.ledgerName = x.process.businessExtend ? x.process.businessExtend.LedgerName : "";
    x.businessRelevanceTime = x.process.businessRelevanceTime;
    x.creationTime = x.process.creationTime;
    x.deadline = x.process.deadline ?? "-";
    x.state = x.state;

    temp.push(x);
  });
  next(temp);
};
const worktodo = ref();
const onSearch = () => {
  pagination.value.page = 1;
  reqParams.skipCount = 0;
  reqParams.title = formSearch.value.title;
  reqParams.startDate = formSearch.value.date[0];
  reqParams.endDate = formSearch.value.date[1];
  reqParams.maxResultCount = pagination.value.size;
};
const searchForm: any = ref({});
const formItems = ref([
  { prop: "title", type: "text", placeholder: "请输入任务名称" },
  {
    prop: "date",
    type: "datetimerange",
    placeholder: "请选择创建时间",
  },
  // {
  // 	prop: 'IsUrge',
  // 	type: 'select',
  // 	placeholder: '请选择提状态',
  // 	options: [
  // 		{label: '正常', value: '0'},
  // 		{label: '催办', value: '1'},
  // 	],
]);
onMounted(() => {
  LedgersList();
  tagsList();
});
</script>
<template>
  <div class="dataAnalysis">
    <Block
      :enable-fixed-height="true"
      :enable-expand-content="true"
      :enableBackButton="false"
      :title="'任务审核'"
      @heightChanged="onBlockHeightChanged"
    >
      <template #expand>
        <div class="search">
          <Form
            v-model="formSearch"
            :props="formItems"
            :column-count="4"
            :enable-reset="false"
            buttonVertical="flowing"
            label-width="0"
            confirm-text="查询"
            @submit="onSearch"
            @clear="onSearch"
          ></Form>
        </div>
      </template>
      <template #topRight>
        <div class="df aic">
          <!-- <el-checkbox v-model="checkedBox" label="不显示终止任务" size="large" /> -->
          <el-button size="small" class="mg-left-10" @click="hanleRecord"
            >办理记录</el-button
          >
          <el-button size="small" class="mg-left-10" @click="hanleTodo"
            >待办设置</el-button
          >
          <el-button
            size="small"
            type="primary"
            v-show="allSelectPageData.length > 0"
            class="mg-left-10"
            @click="hanleProcess('批量审核')"
            >批量审核</el-button
          >
          <el-button
            type="primary"
            size="small"
            class="mg-left-10"
            @click="handleGoto('/processEngine')"
            >审核流程配置</el-button
          >
        </div>
      </template>

      <TableV2
        ref="tableRef"
        url="/api/workflow/workflowTask/my-unCompleted"
        v-model="tableData"
        :height="tableHeight"
        :columns="colData"
        :headers="{ Urlkey: 'ledger' }"
        :enableToolbar="false"
        :enable-create="false"
        :enable-edit="false"
        :enable-delete="false"
        :enableSelection="true"
        :enableIndex="true"
        :req-params="reqParams"
        @beforeComplete="beforeComplete"
        :buttons="buttons"
        @clickButton="onTableClickButton"
        @selection-change="handleSelectionChange"
        @completed="
          () => {
            pagination.total = tableRef.getTotal();
          }
        "
      >
        <template #_category="{ row }">
          {{ FlowAuditTypeData[row.process?.businessType].name }}
        </template>

        <template #warningCount="{ row }">
          <div @click="handleCount" class="analysisCount">
            {{ row.warningCount }}
          </div>
        </template>
      </TableV2>
      <Pagination
        :total="pagination.total"
        :page-size="pagination.size"
        :current-page="pagination.page"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      >
      </Pagination>
      <Dialog
        v-model="processDialog"
        :title="processTitle"
        width="600"
        :showClose="false"
        @close="handleClose"
        :confirmText="'确定'"
        @clickConfirm="submit(ruleFormRef)"
        @clickCancel="handleClose"
      >
        <el-form ref="ruleFormRef" :model="runwayForm" :rules="rules" label-width="auto">
          <el-form-item label="审核结果:" prop="radio1">
            <el-radio-group v-model="runwayForm.radio1">
              <el-radio value="agree" size="large">通过</el-radio>
              <el-radio value="disagree" size="large">驳回</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核意见:" prop="textarea">
            <el-input
              v-model="runwayForm.textarea"
              :rows="5"
              type="textarea"
              placeholder="请输入审核意见"
            />
          </el-form-item>
        </el-form>
      </Dialog>

      <Dialog
        v-model="recordDialog"
        title="办理记录"
        width="1500"
        @close="recordDialog = false"
        :enable-confirm="false"
        :enable-close="false"
      >
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane label="任务分发" name="0"></el-tab-pane>
          <el-tab-pane label="任务审核" name="1"></el-tab-pane>
          <el-tab-pane label="任务填报" name="2"></el-tab-pane>
        </el-tabs>
        <!-- <TableV2
          ref="record"
          url="/api/new-feature/warning-mechanism"
          v-model="tableData"
          :height="tableHeight"
          :columns="recordColData"
          :headers="{ Urlkey: 'ledger' }"
          :enableToolbar="false"
          :enable-create="false"
          :enable-edit="false"
          :enable-delete="false"
          :enableSelection="true"
          :enableIndex="true"
          :req-params="reqParams"
          :buttons="buttons"
          @clickButton="onTableClickButton"
          @selection-change="handleSelectionChange"
        >
        </TableV2> -->
        <!-- <Pagination
          :total="pagination.total"
          :page-size="pagination.size"
          :current-page="pagination.page"
          @current-change="onPaginationChange($event, 'page')"
          @size-change="onPaginationChange($event, 'size')"
        >
        </Pagination> -->
      </Dialog>
      <TodoRemindComponent
        v-model="showTodoRemind"
        @completed="showTodoRemind = false"
      ></TodoRemindComponent>
    </Block>
  </div>
</template>
<route>
	{
		meta:{
			title:'任务审核'
		}
	}
</route>
<style scoped lang="scss">
::v-deep(.analysisCount) {
  cursor: pointer;
  text-decoration: underline;
  color: #1764ce;
}
.search-box {
  align-items: start;
  display: flex;
  padding: 10px 15px 10px 10px;
}
.search {
  display: flex;
  height: 80px;
}
.todo-tip {
  padding-bottom: 20px;
}
.todo-list {
  border-radius: 5px;
  border: 1px solid var(--z-line);
  padding: 10px 20px;
}
::v-deep(.el-form-item__label) {
  display: block;
}
</style>

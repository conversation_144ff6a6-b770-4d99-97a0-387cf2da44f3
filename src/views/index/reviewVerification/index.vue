<script setup lang="ts" name="reviewverification">
import {FlowAuditTypes, FlowAuditTypeData} from '@/define/Workflow'
import TodoRemindComponent from './component/TodoRemindComponent.vue'
import SecondaryAnalysis from './component/SecondaryAnalysis.vue'
import FeedbackMechanism from './component/FeedbackMechanism.vue'
import QualityAssessment from './component/QualityAssessment.vue'
import ReminderSetting from './component/ReminderSetting.vue'
import EvaluationSystem from './component/EvaluationSystem.vue'
import Progress from './component/Progress.vue'
import Authorize from './component/Authorize.vue'

const router = useRouter()

const formProps = ref([{label: '任务名称', prop: 'title', type: 'text'}])
const form = ref({title: ''})
const showQualityAssessment = ref(false)
const statusArr = [
	{name: '激活', color: '#3D7FFF', state: 1},
	{name: '待激活', color: '#5DC1AA', state: 2},
	{name: '完成', color: '#FD6B69', state: 3},
	{name: '关闭', color: '#EA8B60', state: 4},
	{name: '加签状态', color: '#3D7FFF', state: 1},
	{name: '转移给其他人', color: '#5DC1AA', state: 2},
	{name: '作废', color: '#FD6B69', state: 3},
	{name: '子流程运行中', color: '#EA8B60', state: 4},
]

const statusArr2 = [
	{name: '-', color: '#303030', state: 0},
	{name: '已通过', color: '#4caf50', state: 1},
	{name: '已驳回', color: '#FD6B69', state: 2},
]

const columns = [
	{label: '任务名称', prop: 'title', type: 'text'},
	{label: '任务类型', prop: '_category', type: 'text'},
	{label: '提交部门', prop: 'createUserLargeDepartmentName', type: 'text'},
	{label: '提交科室', prop: 'createUserDepartmentName', type: 'text'},
	{label: '提交人', prop: 'createUserName', type: 'text'},
	{label: '提交时间', prop: 'businessRelevanceTime', type: 'datetime', sortable: 'custom'},
	{label: '截止时间', prop: 'deadline', type: 'datetime', width: 280},
]
const tableRef = ref()
const tableHeight = ref(0)
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})

const reqParams = reactive({
	title: '',
	skipCount: 0,
	maxResultCount: 10,
	// businessType: FlowAuditTypes.ReportTaskDataAudit,
	businessTypeList: [FlowAuditTypes.ReportTaskIssude, FlowAuditTypes.ReportTaskDataAudit],
})

const showTodoSetting = ref(false)
const showTodoRemind = ref(false)
const tableRefRecord = ref()
const recordColumns = [
	{label: '任务名称', prop: 'title', type: 'text'},
	{label: '提交人部门', prop: 'createUserLargeDepartmentName', type: 'text'},
	{label: '提交人科室', prop: 'createUserDepartmentName', type: 'text'},
	{label: '提交时间', prop: 'creationTime', type: 'datetime'},
	{label: '办理时间', prop: 'processTime', type: 'datetime', sortable: 'custom'},
	{label: '审核结果', prop: 'outcome', type: 'switch'},
]
const reqParamsRecord = reactive({
	skipCount: 0,
	maxResultCount: 10,
	Sorting: `processTime desc`,
})
const paginationRecord = reactive({
	page: 1,
	size: 10,
	total: 0,
})

const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.page = val
		reqParams.skipCount = (val - 1) * pagination.size
	} else {
		pagination.size = val
		reqParams.maxResultCount = pagination.size
	}
}

const onPaginationRecordChange = (val: any, type: any) => {
	if (type == 'page') {
		paginationRecord.page = val
		reqParamsRecord.skipCount = (val - 1) * paginationRecord.size
	} else {
		paginationRecord.size = val
		reqParamsRecord.maxResultCount = paginationRecord.size
	}
}

const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}

const beforeTableComplete = ({items, next}: any) => {
	const temp: any = []
	items.forEach((x: any) => {
		x.title = x.process.title
		x.createUserName = x.process.createUserName
		x.createUserLargeDepartmentName = x.process.createUserLargeDepartmentName
		x.createUserDepartmentName = x.process.createUserDepartmentName
		x.ledgerName = x.process.businessExtend ? x.process.businessExtend.LedgerName : ''
		x.businessRelevanceTime = x.process.businessRelevanceTime
		x.creationTime = x.process.creationTime
		x.deadline = x.process.deadline ?? '-'
		x.state = x.state
		temp.push(x)
	})
	next(temp)
}

const beforeRecordComplete = ({items, next}: any) => {
	const temp: any = []
	items.forEach((x: any) => {
		x.title = x.process.title
		x.createUserName = x.process.createUserName
		x.createUserDepartmentName = x.process.createUserDepartmentName
		x.createUserLargeDepartmentName = x.process.createUserLargeDepartmentName
		x.creationTime = x.process.creationTime
		;(x.finishTime = x.process.finishTime ?? '-'), (x.outcome = x.state == 3 ? 1 : 2)
		temp.push(x)
	})
	next(temp)
}

const onSearch = () => {
	pagination.page = 1
	reqParams.skipCount = 0
	reqParams.title = form.value.title
}

const onTableButtonClick = ({btn, row}: any) => {
	if (btn.code === 'view') {
		showTodoRemind.value = false
		if (row.process.businessType === FlowAuditTypes.ReportTaskIssude) {
			// 下发
			router.push({
				path: '/taskPending/detail',
				query: {
					id: row.process.businessId,
					taskId: row.id,
					type: 'detail',
					currentIndex: 1,
				},
			})
		} else if (
			row.process.businessType === FlowAuditTypes.ReportTaskDataAudit ||
			row.process.businessType === FlowAuditTypes.ReportTaskTranspondAudit
			// 数据审核转发
		) {
			router.push({
				path: '/taskPending/report-task-detail',
				query: {
					taskId: row.id,
					reportTaskId: row.process.businessExtend.ReportTaskId,
					areaOrganizationUnitId: row.process.businessExtend.AreaOrganizationUnitId,
					id: row.process.businessExtend.Id,
					type: 'detail',
					currentIndex: 4,
					businessType: row.process.businessType,
				},
			})
		} else if (row.process.businessType === FlowAuditTypes.LedgerDataExport) {
			router.push({
				path: '/taskPending/export-audit',
				query: {
					id: row.process.businessId,
					rid: row.processId,
					tid: row.id,
				},
			})
		} else {
			router.push({
				path: '/taskPending/task-review-details',
				query: {
					id: row.id,
					taskId: row.process.id,
					keyword: row.process.keyword2,
					businessId: row.process.businessId,
					Remark: row.process.businessExtend.Remark,
					showButton: 'false',
				},
			})
		}
	} else if (btn.code === 'toexamine') {
		if (row.process.businessType === FlowAuditTypes.ReportTaskIssude) {
			router.push({
				path: '/taskPending/detail',
				query: {
					id: row.process.businessId,
					taskId: row.id,
					type: 'audit',
					currentIndex: 1,
				},
			})
		} else if (
			row.process.businessType === FlowAuditTypes.ReportTaskDataAudit ||
			row.process.businessType === FlowAuditTypes.ReportTaskTranspondAudit
		) {
			router.push({
				path: '/taskPending/report-task-detail',
				query: {
					taskId: row.id,
					reportTaskId:
						row.process.businessExtend.Id || row.process.businessExtend.ReportTaskId,
					areaOrganizationUnitId:
						row.process.businessExtend.Id ||
						row.process.businessExtend.AreaOrganizationUnitId,
					id: row.process.businessExtend.Id,
					type: 'audit',
					currentIndex: 4,
					businessType: row.process.businessType,
				},
			})
		} else if (row.process.businessType === FlowAuditTypes.LedgerDataExport) {
			router.push({
				path: '/taskPending/export-audit',
				query: {id: row.process.businessId, rid: row.processId, tid: row.id},
			})
		} else {
			router.push({
				path: '/taskPending/task-review-details',
				query: {
					id: row.id,
					taskId: row.process.id,
					keyword: row.process.keyword2,
					businessId: row.process.businessId,
					batchType: row.process.businessExtend.Type,
					Remark: row.process.businessExtend.Remark,
				},
			})
		}
	}
}

const isSecondaryAnalysis = ref(false)
const isFeedbackMechanism = ref(false)
const isReminderSetting = ref(false)
const isEvaluationSystem = ref(false)
const isProgress = ref(false)
const isAuthorize = ref(false)
// 评价体系
const handlEvaluationSystem = () => {
	isEvaluationSystem.value = true
}
//分管领导审核效率分析
const handlSecondaryAnalysis = () => {
	// isSecondaryAnalysis.value = true
	router.push('/leadersChargeManagement')
}
const handlZhuyaoAnalysis = () => {
	router.push('/leadershipEfficiency')
}
// 审核质量反馈机制设置
const handlFeedbackMechanism = () => {
	isFeedbackMechanism.value = true
}
//分管领导意见回复提醒设置
const handlReminderSetting = () => {
	isReminderSetting.value = true
}
// 回复进度设置
const handlProgress = () => {
	isProgress.value = true
}
//授权委托
const handlAuthorize = () => {
	isAuthorize.value = true
}
// 当前时间距离截止时间 时间格式2025-04-29 16:00:00

// 当前时间距离截止时间 时间格式2025-04-29 16:00:00
const setDate = (date: string) => {
	// 处理空值或无效日期
	if (!date || date === '-' || isNaN(Date.parse(date))) {
		return {
			date: '',
			color: '',
		}
	}

	// 解析日期
	const deadline = new Date(date)
	const now = new Date()

	// 计算时间差（毫秒）
	const timeDiff = deadline.getTime() - now.getTime()

	// 计算剩余天数（向上取整）
	const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24))

	// 根据天数范围设置颜色和显示文本
	if (daysDiff <= 7 && daysDiff > 0) {
		let color = ''
		if (daysDiff <= 3) {
			color = 'red' // 小于等于3天显示红色
		} else if (daysDiff <= 7) {
			color = '#f59a24' // 3-7天显示黄色
		}

		// 如果是今天，显示"今天到期"
		if (daysDiff == 0) {
			return {
				date: '今天到期',
				color: '#f59a24',
			}
		}

		return {
			date: `剩余${daysDiff}天`,
			color: color,
		}
	} else if (daysDiff < 0) {
		return {
			date: '',
			color: '',
		}
	}

	// 大于7天不显示
	return {
		date: '',
		color: '',
	}
}
</script>
<template>
	<div class="review-verification">
		<Block
			title="流程化审核校验"
			:enable-fixed-height="true"
			@heightChanged="onBlockHeightChanged"
		>
			<template #topRight>
				<el-button size="small" type="primary" @click="handlEvaluationSystem">
					评价体系
				</el-button>
				<el-button size="small" type="primary" @click="handlSecondaryAnalysis">
					分管领导审核效率分析
				</el-button>
				<el-button size="small" type="primary" @click="handlZhuyaoAnalysis">
					主要领导审核日志分析
				</el-button>
				<el-button size="small" type="primary" @click="handlFeedbackMechanism">
					审核质量反馈机制设置
				</el-button>
				<el-button size="small" type="primary" @click="handlReminderSetting">
					分管领导意见回复提醒设置
				</el-button>
				<el-button size="small" type="primary" @click="handlProgress">
					分管领导意见回复进度设置
				</el-button>
				<el-button size="small" type="primary" @click="showTodoRemind = true">
					办理记录
				</el-button>
				<el-button size="small" type="primary" @click="showTodoSetting = true">
					办理设置
				</el-button>
				<el-button size="small" type="primary" @click="$router.push('/processEngine')">
					审核流程配置
				</el-button>
				<el-button size="small" type="primary" @click="handlAuthorize">
					授权委托
				</el-button>
				<el-button size="small" type="primary" @click="showQualityAssessment = true">
					质量评估指标
				</el-button>
				<TodoRemindComponent
					v-model="showTodoSetting"
					@completed="showTodoSetting = false"
				></TodoRemindComponent>
			</template>
			<template #expand>
				<div class="search">
					<Form
						:props="formProps"
						v-model="form"
						:column-count="2"
						:label-width="74"
						:enable-reset="false"
						confirm-text="查询"
						button-vertical="flowing"
						@submit="onSearch"
					>
					</Form>
				</div>
			</template>
			<TableV2
				ref="tableRef"
				url="/api/workflow/workflowTask/my-unCompleted"
				:columns="columns"
				:req-params="reqParams"
				:height="tableHeight"
				:enable-toolbar="false"
				:enable-selection="true"
				:enable-own-button="false"
				:buttons="[{label: '审核', code: 'toexamine', type: 'primary'}]"
				@click-button="onTableButtonClick"
				@before-complete="beforeTableComplete"
				@completed="
					() => {
						pagination.total = tableRef.getTotal()
					}
				"
			>
				<template #title="{row}">
					{{ row.process?.title }}
				</template>
				<template #state="{row}">
					<span
						:style="{color: statusArr.filter((x:any) => x.state === row.state)[0].color}"
					>
						{{ statusArr.filter((x: any) => x.state === row.state)[0].name }}
					</span>
				</template>
				<template #_category="{row}">
					{{ FlowAuditTypeData[row.process?.businessType].name }}
				</template>
				<template #deadline="{row}">
					<!-- {{ FlowAuditTypeData[row.process?.businessType].name }} -->
					<span>{{ row.process?.deadline }}</span>
					<el-icon
						v-if="setDate(row.process?.deadline).date"
						:style="{color: setDate(row.process?.deadline).color, 'margin-left': '5px'}"
						><WarningFilled
					/></el-icon>
					<span
						:style="{color: setDate(row.process?.deadline).color, 'margin-left': '5px'}"
						v-if="setDate(row.process?.deadline).date"
						>{{ setDate(row.process?.deadline).date }}</span
					>
				</template>
			</TableV2>
			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			></Pagination>
		</Block>
		<EvaluationSystem v-model="isEvaluationSystem" title="评价体系" width="800" />
		<SecondaryAnalysis v-model="isSecondaryAnalysis" title="分管领导效率分析" width="1000" />
		<FeedbackMechanism v-model="isFeedbackMechanism" title="分管领导审核效率分析" width="600" />
		<QualityAssessment
			v-model="showQualityAssessment"
			@handleConfirm="showQualityAssessment = false"
			title="质量评估指标管理"
			width="800"
		/>
		<ReminderSetting v-model="isReminderSetting" title="分管领导意见回复提醒设置" width="800" />
		<Progress v-model="isProgress" title="分管领导意见回复进度设置" width="700" />
		<Authorize v-model="isAuthorize" title="委托权限管理" width="1200" />
		<Dialog
			title="办理记录"
			v-model="showTodoRemind"
			:destroy-on-close="true"
			:enable-confirm="false"
			width="1000"
		>
			<TableV2
				ref="tableRefRecord"
				url="/api/workflow/workflowTask/my-completed"
				:auto-height="true"
				:columns="recordColumns"
				:req-params="reqParamsRecord"
				:enable-toolbar="false"
				:enable-own-button="false"
				:buttons="[{label: '查看', code: 'view', type: 'primary'}]"
				@click-button="onTableButtonClick"
				@before-complete="beforeRecordComplete"
				@completed="
					() => {
						paginationRecord.total = tableRefRecord.getTotal()
					}
				"
			>
				<template #outcome="{row}">
					<span
						:style="{
							color: statusArr2.filter(
								(x) => x.state === (!row.isAgree ? 2 : row.outcome)
							)[0].color,
						}"
					>
						{{
							statusArr2.filter(
								(x) => x.state === (!row.isAgree ? 2 : row.outcome)
							)[0].name
						}}
					</span>
				</template>
			</TableV2>
			<Pagination
				:total="paginationRecord.total"
				:current-page="paginationRecord.page"
				:page-size="paginationRecord.size"
				@current-change="onPaginationRecordChange($event, 'page')"
				@size-change="onPaginationRecordChange($event, 'size')"
			></Pagination>
		</Dialog>
	</div>
</template>
<route>
    {
		meta: {
			title: '流程化审核校验',
		},
	}
</route>
<style scoped lang="scss"></style>

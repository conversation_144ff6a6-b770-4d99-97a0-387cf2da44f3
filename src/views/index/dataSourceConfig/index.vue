<script setup lang="ts" name="DataSourceConfig">
import {ref, reactive, computed, onMounted} from 'vue'
import {ElMessage} from 'element-plus'

import {
	DataSourceConfigItem,
	DATA_SOURCE_CONFIG_MAP,
	getConfigItemName,
} from '@/define/dataSourceConfig'
import {
	getDataSourceConfigs,
	createDataSourceConfig,
	updateDataSourceConfig,
	deleteDataSourceConfig,
} from '@/api/DataSourceConfigApi'
import PreviewDialog from '@/components/common/PreviewDialog.vue'
import {IndexedDBManager} from '@/utils/indexedDB'

// 搜索表单
const searchFormProp = ref([{label: '', prop: 'name', type: 'text'}])
const searchForm = ref({name: ''})

// 加载状态
const loading = ref(false)

// 表格ref
const tableRef = ref()
const tableHeight = ref(0)
const currentRow = ref<DataSourceConfigItem | null>(null)

// 操作按钮
const buttons = [
	{label: '预览', type: 'info' as const, code: 'preview'},
	{label: '编辑', type: 'primary' as const, code: 'edit'},
	{label: '删除', type: 'danger' as const, code: 'delete', popconfirm: '确认删除吗?'},
]

// 表头配置
const columns = [
	{prop: 'index', label: '序号', width: 80},
	{prop: 'nameZh', label: '名称', minWidth: 150},
	{prop: 'language', label: '语言', width: 120}, // 改为单一语言字段
	{prop: 'creator', label: '创建人', width: 120},
	{prop: 'createTime', label: '创建时间', width: 150},
]

// 分页
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})

// 列表请求参数
const reqParams = reactive({
	name: '',
	skipCount: 0,
	maxResultCount: 10,
})

// 表格数据类型（扩展了序号和格式化的创建时间）
interface TableDataItem extends Omit<DataSourceConfigItem, 'createTime'> {
	index: number
	createTime: string
}

// 表格数据
const tableData = ref<TableDataItem[]>([])

// 弹窗表单数据类型
interface DialogFormData {
	configKey?: string
	language?: string
	creator?: string
}

// 弹窗相关
const showDialogForm = ref(false)
const dialogFormRef = ref()
const dialogForm = ref<DialogFormData>({})

// 预览相关
const showPreview = ref(false)
const previewConfigId = ref('')
const previewConfigKey = ref('')
const previewLanguage = ref('中文') // 新增预览语言变量

// 多语言配置相关
const showMultiLangConfigDialog = ref(false)
const showMultiLangDefineDialog = ref(false)
const multiLangConfigForm = ref({
	// 数据格式多语言设置
	chineseDateFormat: '',
	englishDateFormat: '',
	chineseNumberFormat: '',
	numberFormat: '',
	// 数据单位多语言设置
	dataUnits: [
		{
			unitType: '',
			chineseDataUnit: '',
			englishDataUnit: ''
		}
	],
	// 数据精度多语言设置
	chineseDecimalPlaces: '',
	englishDecimalPlaces: '',
	chineseMeaningRules: '',
	englishMeaningRules: '',
	// 四舍五入多语言合理设置
	dataRounding: '',
	chineseSettings: '',
	englishSettings: '',
	// 数据刷新多语言准确校验
	refreshCharacter: '',
	// 数据填充多语言高效配置
	chineseNullValue: '',
	englishNullValue: '',
	chineseAbnormalValue: '',
	englishAbnormalValue: '',
	chineseLongText: '',
	englishLongText: '',
	chineseFormatError: '',
	englishFormatError: ''
})
const multiLangDefineForm = ref({
	// 排序规则多语言定义
	sortRules: [
		{
			sortAttribute: '',
			chineseSortRule: '',
			englishSortRule: ''
		}
	],
	// 分组规则多语言定义
	groupRules: [
		{
			chineseGroupRule: '',
			englishGroupRule: ''
		}
	],
	// 聚合规则多语言定义
	aggregateRules: [
		{
			chineseAggregateRule: '',
			englishAggregateRule: ''
		}
	],
	// 过滤/查询规则多语言定义
	filterRules: [
		{
			ruleType: '',
			chineseFilterRule: '',
			englishFilterRule: ''
		}
	],
	// 数据统计规则多语言定义
	statisticsRules: [
		{
			statisticsType: '',
			chineseStatisticsRule: '',
			englishStatisticsRule: ''
		}
	],
	// 数据可视化规则多语言定义
	visualizationRules: [
		{
			chineseVisualizationRule: '',
			englishVisualizationRule: ''
		}
	]
})

// IndexedDB 管理器
const dbManager = new IndexedDBManager('MultiLanguageConfigDB', 1)

// 弹窗表单属性
const dialogFormProps = computed(() => [
	{
		label: '配置名称',
		prop: 'configKey',
		type: 'select',
		options: Object.entries(DATA_SOURCE_CONFIG_MAP).map(([key, config]) => ({
			label: config.nameZh, // 固定显示中文名称
			value: key,
		})),
	},
	{
		label: '支持语言',
		prop: 'language', // 改为单一字段
		type: 'radio', // 改为单选按钮
		options: [
			{label: '中文', value: '中文'},
			{label: '英文', value: '英文'},
		],
	},
	{label: '创建人', prop: 'creator', type: 'text'},
])

// 表单验证规则
const dialogFormRules = {
	configKey: [{required: true, message: '请选择配置名称', trigger: 'change'}],
	language: [{required: true, message: '请选择支持语言', trigger: 'change'}], // 改为单一字段
	creator: [{required: true, message: '请输入创建人', trigger: 'blur'}],
}

// 多语言配置表单属性
const multiLangConfigFormProps = computed(() => [
	// 数据格式多语言设置
	{
		label: '中文日期格式设置',
		prop: 'chineseDateFormat',
		type: 'text',
		placeholder: '请输入（例如YYYY年MM月DD日）',
	},
	{
		label: '英文日期格式',
		prop: 'englishDateFormat',
		type: 'text',
		placeholder: '请输入（例如MM/DD/YYYY）',
	},
	{
		label: '中文数值格式',
		prop: 'chineseNumberFormat',
		type: 'text',
		placeholder: '请输入',
	},
	{
		label: '数值格式',
		prop: 'numberFormat',
		type: 'text',
		placeholder: '请输入',
	},
	// 数据单位多语言设置
	{
		label: '单位类型',
		prop: 'unitType',
		type: 'select',
		placeholder: '请选择',
		options: [
			{label: '长度', value: '长度'},
			{label: '重量', value: '重量'},
			{label: '体积', value: '体积'},
		],
	},
	{
		label: '中文数据单位',
		prop: 'chineseDataUnit',
		type: 'text',
		placeholder: '请输入（例如：公里/米/厘米）',
	},
	{
		label: '英文数据单位',
		prop: 'englishDataUnit',
		type: 'text',
		placeholder: '请输入（例如：km/m/cm）',
	},
	// 数据精度多语言设置
	{
		label: '中文小数位数',
		prop: 'chineseDecimalPlaces',
		type: 'text',
		placeholder: '请输入',
	},
	{
		label: '英文小数位数',
		prop: 'englishDecimalPlaces',
		type: 'text',
		placeholder: '请输入',
	},
	{
		label: '中文含义规则',
		prop: 'chineseMeaningRules',
		type: 'text',
		placeholder: '请输入',
	},
	{
		label: '英文含义规则',
		prop: 'englishMeaningRules',
		type: 'text',
		placeholder: '请输入',
	},
	// 四舍五入多语言合理设置
	{
		label: '数据四舍五入',
		prop: 'dataRounding',
		type: 'select',
		placeholder: '请选择',
		options: [
			{label: '四舍五入', value: '四舍五入'},
			{label: '向上取整', value: '向上取整'},
			{label: '向下取整', value: '向下取整'},
		],
	},
	{
		label: '中文设置',
		prop: 'chineseSettings',
		type: 'text',
		placeholder: '请输入',
	},
	{
		label: '英文设置',
		prop: 'englishSettings',
		type: 'text',
		placeholder: '请输入',
	},
	// 数据刷新多语言准确校验
	{
		label: '刷新字符',
		prop: 'refreshCharacter',
		type: 'text',
		placeholder: '请输入',
	},
	// 数据填充多语言高效配置
	{
		label: '中文空值',
		prop: 'chineseNullValue',
		type: 'text',
		placeholder: '请输入',
	},
	{
		label: '英文空值',
		prop: 'englishNullValue',
		type: 'text',
		placeholder: '请输入',
	},
	{
		label: '中文异常值',
		prop: 'chineseAbnormalValue',
		type: 'text',
		placeholder: '请输入',
	},
	{
		label: '英文异常值',
		prop: 'englishAbnormalValue',
		type: 'text',
		placeholder: '请输入',
	},
	{
		label: '中文超长文本',
		prop: 'chineseLongText',
		type: 'text',
		placeholder: '请输入',
	},
	{
		label: '英文超长文本',
		prop: 'englishLongText',
		type: 'text',
		placeholder: '请输入',
	},
	{
		label: '中文格式错误',
		prop: 'chineseFormatError',
		type: 'text',
		placeholder: '请输入',
	},
	{
		label: '英文格式错误',
		prop: 'englishFormatError',
		type: 'text',
		placeholder: '请输入',
	},
])

// 多语言定义表单属性
const multiLangDefineFormProps = computed(() => [
	// 排序规则多语言定义
	{
		label: '排序属性',
		prop: 'sortAttribute',
		type: 'text',
		placeholder: '请输入（按时间顺序/按所属部门/按创建时间/创建部门/排序）',
	},
	{
		label: '中文排序规则',
		prop: 'chineseSortRule',
		type: 'select',
		placeholder: '请选择',
		options: [
			{label: '升序', value: '升序'},
			{label: '降序', value: '降序'},
			{label: '拼音', value: '拼音'},
		],
	},
	{
		label: '英文排序规则',
		prop: 'englishSortRule',
		type: 'select',
		placeholder: '请选择',
		options: [
			{label: '升序', value: '升序'},
			{label: '降序', value: '降序'},
			{label: '拼音', value: '拼音'},
		],
	},
	// 分组规则多语言定义
	{
		label: '中文分组规则',
		prop: 'chineseGroupRule',
		type: 'select',
		placeholder: '请选择',
		options: [
			{label: '按所属部门', value: '按所属部门'},
			{label: '创建部门', value: '创建部门'},
			{label: '排序', value: '排序'},
		],
	},
	{
		label: '英文分组规则',
		prop: 'englishGroupRule',
		type: 'select',
		placeholder: '请选择',
		options: [
			{label: '按所属部门', value: '按所属部门'},
			{label: '创建部门', value: '创建部门'},
			{label: '排序', value: '排序'},
		],
	},
	// 聚合规则多语言定义
	{
		label: '中文聚合规则',
		prop: 'chineseAggregateRule',
		type: 'text',
		placeholder: '请输入',
	},
	{
		label: '英文聚合规则',
		prop: 'englishAggregateRule',
		type: 'text',
		placeholder: '请输入',
	},
	// 过滤/查询规则多语言定义
	{
		label: '规则类型',
		prop: 'ruleType',
		type: 'text',
		placeholder: '请输入（过滤/查询）',
	},
	{
		label: '中文规则',
		prop: 'chineseFilterRule',
		type: 'text',
		placeholder: '请输入',
	},
	{
		label: '英文规则',
		prop: 'englishFilterRule',
		type: 'text',
		placeholder: '请输入',
	},
	// 数据统计规则多语言定义
	{
		label: '统计类型',
		prop: 'statisticsType',
		type: 'select',
		placeholder: '请选择',
		options: [
			{label: '按时间统计', value: '按时间统计'},
			{label: '按业务类型统计', value: '按业务类型统计'},
			{label: '按创建部门', value: '按创建部门'},
			{label: '统计', value: '统计'},
		],
	},
	{
		label: '中文规则',
		prop: 'chineseStatisticsRule',
		type: 'text',
		placeholder: '请输入',
	},
	{
		label: '英文规则',
		prop: 'englishStatisticsRule',
		type: 'text',
		placeholder: '请输入',
	},
	// 数据可视化规则多语言定义
	{
		label: '中文规则',
		prop: 'chineseVisualizationRule',
		type: 'text',
		placeholder: '请输入',
	},
	{
		label: '英文规则',
		prop: 'englishVisualizationRule',
		type: 'text',
		placeholder: '请输入',
	},
])

// 初始化
onMounted(async () => {
	loadTableData()
	await initMultiLangDB()
	await loadMultiLangData()
})

// 初始化多语言数据库
const initMultiLangDB = async () => {
	try {
		await dbManager.init()
	} catch (error) {
		console.error('初始化多语言数据库失败:', error)
	}
}

// 加载多语言数据
const loadMultiLangData = async () => {
	try {
		const configData = await dbManager.getSetting('multiLangConfig')
		const defineData = await dbManager.getSetting('multiLangDefine')

		if (configData) {
			multiLangConfigForm.value = { ...multiLangConfigForm.value, ...configData }
		}
		if (defineData) {
			multiLangDefineForm.value = { ...multiLangDefineForm.value, ...defineData }
		}
	} catch (error) {
		console.error('加载多语言数据失败:', error)
	}
}

// 打开多语言配置对话框
const openMultiLangConfigDialog = () => {
	showMultiLangConfigDialog.value = true
}

// 打开多语言定义对话框
const openMultiLangDefineDialog = () => {
	showMultiLangDefineDialog.value = true
}

// 准确性校验
const validateAccuracy = () => {
	ElMessage.success('校验成功')
}

// 保存多语言配置
const saveMultiLangConfig = async () => {
	try {
		console.log('开始保存多语言配置:', multiLangConfigForm.value)

		// 确保数据库已初始化
		await dbManager.init()

		// 创建数据副本，避免Vue响应式代理问题
		const configData = JSON.parse(JSON.stringify(multiLangConfigForm.value))
		await dbManager.saveSetting('multiLangConfig', configData)
		console.log('多语言配置保存成功')
		ElMessage.success('多语言配置保存成功')
		showMultiLangConfigDialog.value = false
	} catch (error: any) {
		console.error('保存多语言配置失败:', error)
		ElMessage.error(`保存失败: ${error?.message || error}`)
	}
}

// 保存多语言定义
const saveMultiLangDefine = async () => {
	try {
		console.log('开始保存多语言定义:', multiLangDefineForm.value)

		// 确保数据库已初始化
		await dbManager.init()

		// 创建数据副本，避免Vue响应式代理问题
		const defineData = JSON.parse(JSON.stringify(multiLangDefineForm.value))
		await dbManager.saveSetting('multiLangDefine', defineData)
		console.log('多语言定义保存成功')
		ElMessage.success('多语言定义保存成功')
		showMultiLangDefineDialog.value = false
	} catch (error: any) {
		console.error('保存多语言定义失败:', error)
		ElMessage.error(`保存失败: ${error?.message || error}`)
	}
}

// 数据单位增删行方法
const addDataUnit = () => {
	multiLangConfigForm.value.dataUnits.push({
		unitType: '',
		chineseDataUnit: '',
		englishDataUnit: ''
	})
}

const removeDataUnit = (index: number) => {
	if (multiLangConfigForm.value.dataUnits.length > 1) {
		multiLangConfigForm.value.dataUnits.splice(index, 1)
	}
}

// 排序规则增删行方法
const addSortRule = () => {
	multiLangDefineForm.value.sortRules.push({
		sortAttribute: '',
		chineseSortRule: '',
		englishSortRule: ''
	})
}

const removeSortRule = (index: number) => {
	if (multiLangDefineForm.value.sortRules.length > 1) {
		multiLangDefineForm.value.sortRules.splice(index, 1)
	}
}

// 分组规则增删行方法
const addGroupRule = () => {
	multiLangDefineForm.value.groupRules.push({
		chineseGroupRule: '',
		englishGroupRule: ''
	})
}

const removeGroupRule = (index: number) => {
	if (multiLangDefineForm.value.groupRules.length > 1) {
		multiLangDefineForm.value.groupRules.splice(index, 1)
	}
}

// 聚合规则增删行方法
const addAggregateRule = () => {
	multiLangDefineForm.value.aggregateRules.push({
		chineseAggregateRule: '',
		englishAggregateRule: ''
	})
}

const removeAggregateRule = (index: number) => {
	if (multiLangDefineForm.value.aggregateRules.length > 1) {
		multiLangDefineForm.value.aggregateRules.splice(index, 1)
	}
}

// 过滤/查询规则增删行方法
const addFilterRule = () => {
	multiLangDefineForm.value.filterRules.push({
		ruleType: '',
		chineseFilterRule: '',
		englishFilterRule: ''
	})
}

const removeFilterRule = (index: number) => {
	if (multiLangDefineForm.value.filterRules.length > 1) {
		multiLangDefineForm.value.filterRules.splice(index, 1)
	}
}

// 数据统计规则增删行方法
const addStatisticsRule = () => {
	multiLangDefineForm.value.statisticsRules.push({
		statisticsType: '',
		chineseStatisticsRule: '',
		englishStatisticsRule: ''
	})
}

const removeStatisticsRule = (index: number) => {
	if (multiLangDefineForm.value.statisticsRules.length > 1) {
		multiLangDefineForm.value.statisticsRules.splice(index, 1)
	}
}

// 数据可视化规则增删行方法
const addVisualizationRule = () => {
	multiLangDefineForm.value.visualizationRules.push({
		chineseVisualizationRule: '',
		englishVisualizationRule: ''
	})
}

const removeVisualizationRule = (index: number) => {
	if (multiLangDefineForm.value.visualizationRules.length > 1) {
		multiLangDefineForm.value.visualizationRules.splice(index, 1)
	}
}

// 加载表格数据
const loadTableData = async () => {
	loading.value = true
	try {
		const response = await getDataSourceConfigs(reqParams)
		tableData.value = response.items.map((item, index) => ({
			...item,
			index: reqParams.skipCount + index + 1,
			createTime: new Date(item.createTime).toLocaleDateString(),
		}))
		pagination.total = response.totalCount
	} catch (error) {
		console.error('加载数据失败:', error)
		ElMessage.error('加载数据失败')
	} finally {
		loading.value = false
	}
}

// 搜索
const onSearch = () => {
	pagination.page = 1
	reqParams.skipCount = 0
	reqParams.maxResultCount = pagination.size
	reqParams.name = searchForm.value.name
	loadTableData()
}

// 表格操作点击事件
const onTableClickButton = ({row, btn}: any) => {
	if (btn.code === 'preview') {
		previewConfigId.value = row.id
		previewConfigKey.value = row.configKey || row.id
		previewLanguage.value = row.language || '中文' // 设置预览语言
		showPreview.value = true
	} else if (btn.code === 'edit') {
		currentRow.value = row
		dialogForm.value = {
			configKey: row.configKey || row.id,
			language: row.language, // 改为单一语言字段
			creator: row.creator,
		}
		showDialogForm.value = true
	} else if (btn.code === 'delete') {
		handleDelete(row.id)
	}
}

// 删除操作
const handleDelete = async (id: string) => {
	try {
		await deleteDataSourceConfig(id)
		ElMessage.success('删除成功')
		loadTableData()
	} catch (error) {
		console.error('删除失败:', error)
		ElMessage.error('删除失败')
	}
}

// 新增
const onClickAdd = () => {
	currentRow.value = null
	dialogForm.value = {
		language: '中文', // 改为单一字段，默认选中中文
	}
	showDialogForm.value = true
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
	if (type === 'page') {
		pagination.page = val
		reqParams.skipCount = (val - 1) * pagination.size
	} else {
		pagination.size = val
		reqParams.maxResultCount = pagination.size
	}
	loadTableData()
}

// 块高度变化事件
const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}

// 检查配置是否已存在
const checkConfigExists = (configKey: string, language: string): boolean => {
	return tableData.value.some(item => {
		// 编辑时排除当前行
		if (currentRow.value && item.id === currentRow.value.id) {
			return false
		}
		return (item.configKey || item.id) === configKey && item.language === language
	})
}

// 弹框表单提交
const onDialogConfirm = () => {
	dialogFormRef.value.validate(async (valid: boolean) => {
		if (valid) {
			const configKey = dialogForm.value.configKey!
			const language = dialogForm.value.language!

			// 检查配置是否已存在
			if (checkConfigExists(configKey, language)) {
				ElMessage.warning('该配置名称和语言的组合已存在，请选择其他配置或语言')
				return
			}

			loading.value = true
			try {
				const configInfo = DATA_SOURCE_CONFIG_MAP[configKey]

				const formData = {
					configKey: configKey,
					nameZh: configInfo.nameZh,
					nameEn: configInfo.nameEn,
					category: configInfo.category,
					language: language, // 改为单一语言字段
					creator: dialogForm.value.creator!,
				}

				if (currentRow.value) {
					await updateDataSourceConfig(currentRow.value.id, formData)
					ElMessage.success('编辑成功')
				} else {
					await createDataSourceConfig(formData)
					ElMessage.success('新增成功')
				}

				showDialogForm.value = false
				loadTableData()
			} catch (error) {
				console.error('保存失败:', error)
				ElMessage.error('保存失败')
			} finally {
				loading.value = false
			}
		}
	})
}
</script>

<template>
	<div class="data-source-config">
		<Block
			title="多语言配置"
			:enable-fixed-height="true"
			@height-changed="onBlockHeightChanged"
		>
			<template #topRight>
				<el-button size="small" type="info" @click="openMultiLangConfigDialog">多语言配置</el-button>
				<el-button size="small" type="warning" @click="openMultiLangDefineDialog" style="margin-left: 8px;">多语言定义</el-button>
				<el-button size="small" type="primary" @click="onClickAdd" style="margin-left: 8px;">新增</el-button>
			</template>

			<template #expand>
				<!-- 搜索 -->
				<div class="search">
					<Form
						:props="searchFormProp"
						v-model="searchForm"
						:column-count="2"
						:label-width="74"
						:enable-reset="false"
						confirm-text="查询"
						button-vertical="flowing"
						@submit="onSearch"
					/>
				</div>
			</template>

			<!-- 列表 -->
			<el-table
				ref="tableRef"
				:data="tableData"
				:height="tableHeight"
				v-loading="loading"
				stripe
				border
			>
				<el-table-column
					v-for="column in columns"
					:key="column.prop"
					:prop="column.prop"
					:label="column.label"
					:width="column.width"
					:min-width="column.minWidth"
				/>
				<el-table-column label="操作" width="200" fixed="right">
					<template #default="{row}">
						<el-button
							v-for="btn in buttons"
							:key="btn.code"
							:type="btn.type"
							size="small"
							@click="onTableClickButton({row, btn})"
						>
							{{ btn.label }}
						</el-button>
					</template>
				</el-table-column>
			</el-table>

			<!-- 分页 -->
			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			/>
		</Block>

		<!-- 新增/编辑弹窗 -->
		<Dialog
			v-model="showDialogForm"
			:title="currentRow ? '编辑配置' : '新增配置'"
			:destroy-on-close="true"
			:loading="loading"
			loading-text="保存中"
			@closed=";(currentRow = null), (dialogForm = {})"
			@click-confirm="onDialogConfirm"
		>
			<Form
				ref="dialogFormRef"
				v-model="dialogForm"
				:props="dialogFormProps"
				:rules="dialogFormRules"
				:enable-button="false"
			/>
		</Dialog>

		<!-- 预览弹窗 -->
		<PreviewDialog
			v-model:visible="showPreview"
			:config-id="previewConfigId"
			:config-key="previewConfigKey"
			:language="previewLanguage"
			@close="showPreview = false"
		/>

		<!-- 多语言配置对话框 -->
		<Dialog
			v-model="showMultiLangConfigDialog"
			title="多语言配置"
			:destroy-on-close="true"
			width="1200px"
			@click-confirm="saveMultiLangConfig"
		>
			<div style="max-height: 600px; overflow-y: auto;">
				<el-form :model="multiLangConfigForm" label-width="140px">
					<!-- 数据格式多语言设置 -->
					<el-divider content-position="left">数据格式多语言设置</el-divider>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="中文日期格式设置">
							<el-input v-model="multiLangConfigForm.chineseDateFormat" placeholder="请输入（例如YYYY年MM月DD日）" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="英文日期格式">
							<el-input v-model="multiLangConfigForm.englishDateFormat" placeholder="请输入（例如MM/DD/YYYY）" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="中文数值格式">
							<el-input v-model="multiLangConfigForm.chineseNumberFormat" placeholder="请输入" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="数值格式">
							<el-input v-model="multiLangConfigForm.numberFormat" placeholder="请输入" />
						</el-form-item>
					</el-col>
				</el-row>

				<!-- 数据单位多语言设置 -->
				<el-divider content-position="left">
					数据单位多语言设置
					<el-button type="primary" size="small" @click="addDataUnit" style="margin-left: 10px;">添加</el-button>
				</el-divider>
				<div v-for="(unit, index) in multiLangConfigForm.dataUnits" :key="index" style="margin-bottom: 10px;">
					<el-row :gutter="20">
						<el-col :span="7">
							<el-form-item label="单位类型">
								<el-select v-model="unit.unitType" placeholder="请选择" style="width: 100%">
									<el-option label="长度" value="长度" />
									<el-option label="重量" value="重量" />
									<el-option label="体积" value="体积" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="7">
							<el-form-item label="中文数据单位">
								<el-input v-model="unit.chineseDataUnit" placeholder="请输入（例如：公里/米/厘米）" />
							</el-form-item>
						</el-col>
						<el-col :span="7">
							<el-form-item label="英文数据单位">
								<el-input v-model="unit.englishDataUnit" placeholder="请输入（例如：km/m/cm）" />
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item>
								<el-button type="danger" size="small" @click="removeDataUnit(index)" :disabled="multiLangConfigForm.dataUnits.length === 1">删除</el-button>
							</el-form-item>
						</el-col>
					</el-row>
				</div>

				<!-- 数据精度多语言设置 -->
				<el-divider content-position="left">数据精度多语言设置</el-divider>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="中文小数位数">
							<el-input v-model="multiLangConfigForm.chineseDecimalPlaces" placeholder="请输入" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="英文小数位数">
							<el-input v-model="multiLangConfigForm.englishDecimalPlaces" placeholder="请输入" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="中文含义规则">
							<el-input v-model="multiLangConfigForm.chineseMeaningRules" placeholder="请输入" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="英文含义规则">
							<el-input v-model="multiLangConfigForm.englishMeaningRules" placeholder="请输入" />
						</el-form-item>
					</el-col>
				</el-row>

				<!-- 四舍五入多语言合理设置 -->
				<el-divider content-position="left">四舍五入多语言合理设置</el-divider>
				<el-row :gutter="20">
					<el-col :span="8">
						<el-form-item label="数据四舍五入">
							<el-select v-model="multiLangConfigForm.dataRounding" placeholder="请选择" style="width: 100%">
								<el-option label="四舍五入" value="四舍五入" />
								<el-option label="向上取整" value="向上取整" />
								<el-option label="向下取整" value="向下取整" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="中文设置">
							<el-input v-model="multiLangConfigForm.chineseSettings" placeholder="请输入" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="英文设置">
							<el-input v-model="multiLangConfigForm.englishSettings" placeholder="请输入" />
						</el-form-item>
					</el-col>
				</el-row>

				<!-- 数据刷新多语言准确校验 -->
				<el-divider content-position="left">数据刷新多语言准确校验</el-divider>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="刷新字符">
							<el-input v-model="multiLangConfigForm.refreshCharacter" placeholder="请输入" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item>
							<el-button type="primary" @click="validateAccuracy">准确性校验</el-button>
						</el-form-item>
					</el-col>
				</el-row>

				<!-- 数据填充多语言高效配置 -->
				<el-divider content-position="left">数据填充多语言高效配置</el-divider>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="中文空值">
							<el-input v-model="multiLangConfigForm.chineseNullValue" placeholder="请输入" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="英文空值">
							<el-input v-model="multiLangConfigForm.englishNullValue" placeholder="请输入" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="中文异常值">
							<el-input v-model="multiLangConfigForm.chineseAbnormalValue" placeholder="请输入" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="英文异常值">
							<el-input v-model="multiLangConfigForm.englishAbnormalValue" placeholder="请输入" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="中文超长文本">
							<el-input v-model="multiLangConfigForm.chineseLongText" placeholder="请输入" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="英文超长文本">
							<el-input v-model="multiLangConfigForm.englishLongText" placeholder="请输入" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="中文格式错误">
							<el-input v-model="multiLangConfigForm.chineseFormatError" placeholder="请输入" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="英文格式错误">
							<el-input v-model="multiLangConfigForm.englishFormatError" placeholder="请输入" />
						</el-form-item>
					</el-col>
				</el-row>
				</el-form>
			</div>
			<template #footer>
				<el-button @click="showMultiLangConfigDialog = false">取消</el-button>
				<el-button type="primary" @click="saveMultiLangConfig">确定</el-button>
			</template>
		</Dialog>

		<!-- 多语言定义对话框 -->
		<Dialog
			v-model="showMultiLangDefineDialog"
			title="多语言定义"
			:destroy-on-close="true"
			width="1200px"
			@click-confirm="saveMultiLangDefine"
		>
			<div style="max-height: 600px; overflow-y: auto;">
				<el-form :model="multiLangDefineForm" label-width="140px">
					<!-- 排序规则多语言定义 -->
					<el-divider content-position="left">
					排序规则多语言定义
					<el-button type="primary" size="small" @click="addSortRule" style="margin-left: 10px;">添加</el-button>
				</el-divider>
				<div v-for="(rule, index) in multiLangDefineForm.sortRules" :key="index" style="margin-bottom: 10px;">
					<el-row :gutter="20">
						<el-col :span="8">
							<el-form-item label="排序属性">
								<el-input v-model="rule.sortAttribute" placeholder="请输入（按时间顺序/按所属部门/按创建时间/创建部门/排序）" />
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="中文排序规则">
								<el-select v-model="rule.chineseSortRule" placeholder="请选择" style="width: 100%">
									<el-option label="升序" value="升序" />
									<el-option label="降序" value="降序" />
									<el-option label="拼音" value="拼音" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="英文排序规则">
								<el-select v-model="rule.englishSortRule" placeholder="请选择" style="width: 100%">
									<el-option label="升序" value="升序" />
									<el-option label="降序" value="降序" />
									<el-option label="拼音" value="拼音" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="4">
							<el-form-item>
								<el-button type="danger" size="small" @click="removeSortRule(index)" :disabled="multiLangDefineForm.sortRules.length === 1">删除</el-button>
							</el-form-item>
						</el-col>
					</el-row>
				</div>

				<!-- 分组规则多语言定义 -->
				<el-divider content-position="left">
					分组规则多语言定义
					<el-button type="primary" size="small" @click="addGroupRule" style="margin-left: 10px;">添加</el-button>
				</el-divider>
				<div v-for="(rule, index) in multiLangDefineForm.groupRules" :key="index" style="margin-bottom: 10px;">
					<el-row :gutter="20">
						<el-col :span="10">
							<el-form-item label="中文分组规则">
								<el-select v-model="rule.chineseGroupRule" placeholder="请选择" style="width: 100%">
									<el-option label="按所属部门" value="按所属部门" />
									<el-option label="创建部门" value="创建部门" />
									<el-option label="排序" value="排序" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="10">
							<el-form-item label="英文分组规则">
								<el-select v-model="rule.englishGroupRule" placeholder="请选择" style="width: 100%">
									<el-option label="按所属部门" value="按所属部门" />
									<el-option label="创建部门" value="创建部门" />
									<el-option label="排序" value="排序" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="4">
							<el-form-item>
								<el-button type="danger" size="small" @click="removeGroupRule(index)" :disabled="multiLangDefineForm.groupRules.length === 1">删除</el-button>
							</el-form-item>
						</el-col>
					</el-row>
				</div>

				<!-- 聚合规则多语言定义 -->
				<el-divider content-position="left">
					聚合规则多语言定义
					<el-button type="primary" size="small" @click="addAggregateRule" style="margin-left: 10px;">添加</el-button>
				</el-divider>
				<div v-for="(rule, index) in multiLangDefineForm.aggregateRules" :key="index" style="margin-bottom: 10px;">
					<el-row :gutter="20">
						<el-col :span="10">
							<el-form-item label="中文聚合规则">
								<el-input v-model="rule.chineseAggregateRule" placeholder="请输入" />
							</el-form-item>
						</el-col>
						<el-col :span="10">
							<el-form-item label="英文聚合规则">
								<el-input v-model="rule.englishAggregateRule" placeholder="请输入" />
							</el-form-item>
						</el-col>
						<el-col :span="4">
							<el-form-item>
								<el-button type="danger" size="small" @click="removeAggregateRule(index)" :disabled="multiLangDefineForm.aggregateRules.length === 1">删除</el-button>
							</el-form-item>
						</el-col>
					</el-row>
				</div>

				<!-- 过滤/查询规则多语言定义 -->
				<el-divider content-position="left">
					过滤/查询规则多语言定义
					<el-button type="primary" size="small" @click="addFilterRule" style="margin-left: 10px;">添加</el-button>
				</el-divider>
				<div v-for="(rule, index) in multiLangDefineForm.filterRules" :key="index" style="margin-bottom: 10px;">
					<el-row :gutter="20">
						<el-col :span="7">
							<el-form-item label="规则类型">
								<el-input v-model="rule.ruleType" placeholder="请输入（过滤/查询）" />
							</el-form-item>
						</el-col>
						<el-col :span="7">
							<el-form-item label="中文规则">
								<el-input v-model="rule.chineseFilterRule" placeholder="请输入" />
							</el-form-item>
						</el-col>
						<el-col :span="7">
							<el-form-item label="英文规则">
								<el-input v-model="rule.englishFilterRule" placeholder="请输入" />
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item>
								<el-button type="danger" size="small" @click="removeFilterRule(index)" :disabled="multiLangDefineForm.filterRules.length === 1">删除</el-button>
							</el-form-item>
						</el-col>
					</el-row>
				</div>

				<!-- 数据统计规则多语言定义 -->
				<el-divider content-position="left">
					数据统计规则多语言定义
					<el-button type="primary" size="small" @click="addStatisticsRule" style="margin-left: 10px;">添加</el-button>
				</el-divider>
				<div v-for="(rule, index) in multiLangDefineForm.statisticsRules" :key="index" style="margin-bottom: 10px;">
					<el-row :gutter="20">
						<el-col :span="7">
							<el-form-item label="统计类型">
								<el-select v-model="rule.statisticsType" placeholder="请选择" style="width: 100%">
									<el-option label="按时间统计" value="按时间统计" />
									<el-option label="按业务类型统计" value="按业务类型统计" />
									<el-option label="按创建部门" value="按创建部门" />
									<el-option label="统计" value="统计" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="7">
							<el-form-item label="中文规则">
								<el-input v-model="rule.chineseStatisticsRule" placeholder="请输入" />
							</el-form-item>
						</el-col>
						<el-col :span="7">
							<el-form-item label="英文规则">
								<el-input v-model="rule.englishStatisticsRule" placeholder="请输入" />
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item>
								<el-button type="danger" size="small" @click="removeStatisticsRule(index)" :disabled="multiLangDefineForm.statisticsRules.length === 1">删除</el-button>
							</el-form-item>
						</el-col>
					</el-row>
				</div>

				<!-- 数据可视化规则多语言定义 -->
				<el-divider content-position="left">
					数据可视化规则多语言定义
					<el-button type="primary" size="small" @click="addVisualizationRule" style="margin-left: 10px;">添加</el-button>
				</el-divider>
				<div v-for="(rule, index) in multiLangDefineForm.visualizationRules" :key="index" style="margin-bottom: 10px;">
					<el-row :gutter="20">
						<el-col :span="10">
							<el-form-item label="中文规则">
								<el-input v-model="rule.chineseVisualizationRule" placeholder="请输入" />
							</el-form-item>
						</el-col>
						<el-col :span="10">
							<el-form-item label="英文规则">
								<el-input v-model="rule.englishVisualizationRule" placeholder="请输入" />
							</el-form-item>
						</el-col>
						<el-col :span="4">
							<el-form-item>
								<el-button type="danger" size="small" @click="removeVisualizationRule(index)" :disabled="multiLangDefineForm.visualizationRules.length === 1">删除</el-button>
							</el-form-item>
						</el-col>
					</el-row>
				</div>
				</el-form>
			</div>
			<template #footer>
				<el-button @click="showMultiLangDefineDialog = false">取消</el-button>
				<el-button type="primary" @click="saveMultiLangDefine">确定</el-button>
			</template>
		</Dialog>
	</div>
</template>

<route>
{
  meta: {
    title: '多语言配置'
  }
}
</route>

<style scoped lang="scss">
.data-source-config {
	.search {
		margin-bottom: 16px;
	}
}
</style>

<script setup lang="ts" name="datatags">
import {reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import {PostTags, PutTags, DeleteTags} from '@/api/TagsApi'
import {ElMessage} from 'element-plus'

const router = useRouter()
const loading = ref(false)
const loadingText = ref('')

const tableRef = ref()
const curTableRow: any = ref(null)
const columns = [
	{label: '标签名称', prop: 'tagName'},
	{label: '所属分类', prop: 'tagCategoryName'},
	{label: '标签权限', prop: 'tagPermissions'},
	{label: '标签描述', prop: 'tagDescription'},
	{label: '标签图标', prop: 'tagIcon', tooltip: false},
	{label: '标签状态', prop: 'isEnabled'},
	{label: '更新时间', prop: 'lastModificationTime'},
]

const formProps = ref([{label: '标签名称', prop: 'tagName', type: 'text'}])
const form: any = ref({})

const categoryOptions = [
	{label: '人口统计学', value: '人口统计学'},
	{label: '教育信息', value: '教育信息'},
	{label: '就业信息', value: '就业信息'},
	{label: '健康信息', value: '健康信息'},
	{label: '特殊群体', value: '特殊群体'},
	{label: '其他', value: '其他'},
]
const permissionOptions = [
	{label: '数据管理岗', value: '数据管理岗'},
	{label: '工作人员', value: '工作人员'},
	{label: '分管领导', value: '分管领导'},
	{label: '主要领导', value: '主要领导'},
	{label: '台账运维员', value: '台账运维员'},
]

const formRef = ref()
const formDataProps = ref([
	{label: '标签名称', prop: 'tagName', type: 'text'},
	{label: '所属分类', prop: 'tagCategoryName', type: 'select', options: categoryOptions},
	{
		label: '标签权限',
		prop: 'tagPermissions',
		type: 'select',
		options: permissionOptions,
		multiple: true,
	},
	{label: '标签描述', prop: 'tagDescription', type: 'textarea'},
	{label: '标签图标', prop: 'tagIcon'},
])
const formRules = reactive({
	tagName: [{required: true, message: '请输入标签名称', trigger: 'blur'}],
	tagCategoryName: [{required: true, message: '请输入所属分类', trigger: 'change'}],
	tagPermissions: [{required: true, message: '请输入标签权限', trigger: 'change'}],
})
const formData: any = ref({})

const tableHeight = ref(0)
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})
const reqParams = reactive({
	tagName: '',
	skipCount: 0,
	maxResultCount: 10,
})

const showDialog = ref(false)
const showTagDialog = ref(false)
const isNew = ref(true)

const collapsed = ref(false)

const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.page = val
		reqParams.skipCount = (val - 1) * pagination.size
	} else {
		pagination.size = val
		reqParams.maxResultCount = pagination.size
	}
}

const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}

const onTableButtonClick = ({btn, row, index}: any) => {
	curTableRow.value = row
	if (btn.code === 'edit') {
		formData.value = Object.assign({}, row, {
			tagPermissions: row.tagPermissions?.split(',') ?? [],
		})
		isNew.value = false
		showDialog.value = true
	} else if (btn.code === 'tag') {
		showTagDialog.value = true
	} else if (btn.code === 'delete') {
		DeleteTags(row.id).then(() => {
			ElMessage.success('删除成功！')
			tableRef.value.reload()
		})
	}
}

const onSearch = () => {
	pagination.page = 1
	reqParams.skipCount = 0
	reqParams.tagName = form.value.tagName
}

const onConfirm = () => {
	formRef.value.validate((valid: any) => {
		if (!valid) {
			return
		}
		const reqData = Object.assign({}, formData.value, {
			tagPermissions: formData.value.tagPermissions?.join(',') ?? '',
		})
		loading.value = true
		loadingText.value = '保存中...'
		if (isNew.value) {
			PostTags(reqData).then(() => {
				loading.value = false
				showDialog.value = false
				tableRef.value.reload()
			})
		} else {
			PutTags(curTableRow.value.id, reqData).then(() => {
				loading.value = false
				showDialog.value = false
				tableRef.value.reload()
			})
		}
	})
}

const onBatchDelete = () => {
	const rows = tableRef.value.getSelectionRows()
	if (rows.length === 0) {
		ElMessage.warning('请选择要删除的数据')
		return
	}
	const promises = rows.map((row: any) => DeleteTags(row.id))
	Promise.all(promises).then(() => {
		ElMessage.success('批量删除成功！')
		tableRef.value.reload()
		tableRef.value.clearSelection()
	})
}
</script>
<template>
	<div class="data-tags">
		<!-- <div class="left mg-right-20" :style="{width: collapsed ? '60px' : '300px'}">
			<Block
				title="数据类目"
				:enableExpand="true"
				:enable-fixed-height="true"
				:enable-expand-content="false"
				:enable-back-button="false"
				:enable-expand-button="true"
				:enable-close-button="false"
				:expand-vertical="false"
				collapsedText="展开数据类目"
				@collapsed="($event:any) => (collapsed = $event)"
			>
				<el-tree-v2></el-tree-v2>
			</Block>
		</div> -->
		<div class="right flx" :style="{width: collapsed ? '100%' : 'calc(100% - 300px)'}">
			<Block
				title="数据标签"
				:enable-fixed-height="true"
				@heightChanged="onBlockHeightChanged"
			>
				<template #topRight>
					<el-button size="small" type="danger" @click="onBatchDelete"
						>批量删除</el-button
					>
					<el-button
						size="small"
						type="primary"
						@click=";(formData = {}), (isNew = true), (showDialog = true)"
					>
						新增标签
					</el-button>
				</template>
				<template #expand>
					<div class="search">
						<Form
							:props="formProps"
							v-model="form"
							:column-count="2"
							:label-width="84"
							:enable-reset="false"
							:loading="loading"
							confirm-text="查询"
							button-vertical="flowing"
							@submit="onSearch"
						></Form>
					</div>
				</template>
				<TableV2
					ref="tableRef"
					url="/api/new-feature/tags"
					:columns="columns"
					:req-params="reqParams"
					:enable-toolbar="false"
					:height="tableHeight"
					:enable-own-button="false"
					:enable-selection="true"
					:buttons="[
						{
							code: 'edit',
							label: '编辑',
							type: 'primary',
						},
						{
							code: 'tag',
							label: '标注',
							type: 'primary',
						},
						{
							code: 'delete',
							label: '删除',
							type: 'danger',
							popconfirm: '确定删除吗?',
						},
					]"
					@click-button="onTableButtonClick"
					@completed="
						() => {
							pagination.total = tableRef.getTotal()
						}
					"
				>
					<template #isEnabled="scoped">
						<el-switch v-model="scoped.row.isEnabled"></el-switch>
					</template>
					<template #tagIcon="scoped">
						<div v-if="scoped.row.tagIcon" class="icon-list" style="margin: 0 -10px">
							<div class="icon-item">
								<Icons :name="scoped.row.tagIcon" />
							</div>
						</div>
					</template>
				</TableV2>
				<Pagination
					:total="pagination.total"
					:current-page="pagination.page"
					:page-size="pagination.size"
					@current-change="onPaginationChange($event, 'page')"
					@size-change="onPaginationChange($event, 'size')"
				></Pagination>
			</Block>
		</div>

		<Dialog
			v-model="showDialog"
			:title="isNew ? '新增数据标签' : '编辑数据标签'"
			:loading="loading"
			:loading-text="loadingText"
			@click-confirm="onConfirm"
		>
			<Form
				ref="formRef"
				v-model="formData"
				:props="formDataProps"
				:rules="formRules"
				:column-count="1"
				:label-width="100"
				:enable-button="false"
			>
				<template #form-tagIcon="scoped">
					<div class="df tac icon-list">
						<div
							class="icon-item"
							v-for="name of [
								'Home',
								'Setting',
								'Create',
								'Edit',
								'Delete',
								'Send',
								'Cancel',
								'Reset',
								'Clear',
								'Clear2',
								'Clear3',
								'Eye',
								'Lock',
								'Unlock',
								'Warning',
								'Warning2',
								'User',
								'ArrowRight',
								'ArrowRight2',
								'RotateRight',
								'Sun',
								'Moon',
								'List',
								'File',
								'Upload',
								'Download',
								'Appendix',
								'Notifications',
								'Done',
								'Back',
								'More',
								'Language',
								'Thumbtack',
								'Loading',
								'Expand',
								'Search',
								'CopyFile',
								'Refresh',
								'Stop',
								'Start',
								'Pause',
							]"
							@click="formData.tagIcon = name"
							:class="{active: formData.tagIcon === name}"
						>
							<Icons :name="name" />
						</div>
					</div>
				</template>
			</Form>
		</Dialog>

		<Dialog
			v-model="showTagDialog"
			title="标注"
			@click-confirm="
				() => {
					ElMessage.success('标注成功')
					showTagDialog = false
				}
			"
		>
			<FormItem
				:items="[{prop: 'tagContent', label: '标注内容', type: 'textarea'}]"
			></FormItem>
		</Dialog>
	</div>
</template>
<style scoped lang="scss">
.data-tags {
	display: flex;
	.left {
		transition: width 0.15s;
	}
}
.icon-list {
	display: flex;
	flex-wrap: wrap;
}
.icon-item {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 30px;
	height: 30px;
	border: 1px solid var(--z-line);
	border-radius: 5px;
	margin: 5px;
	cursor: pointer;
	transition: all 0.15s;
	&:hover {
		border-color: var(--z-main);
		background-color: var(--z-main);
		color: #fff;
	}
	&.active {
		border-color: var(--z-main);
		background-color: var(--z-main);
		color: #fff;
	}
}
</style>
<route>
    {
        meta: {
            title: '数据标签',
        },
    }
</route>

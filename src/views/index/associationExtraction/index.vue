<script setup lang="ts" name="dataanalysis">
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { RunwayList,ReminderCycleList } from "@/define/ledger.define";
import {

} from '@/api/LedgerApi'
import { dataManagement, dataRuleManagement, managementbatchdelete, putdatarulemanagement, putdatarulemanagementenable } from '@/api/resultAnalysis'
import { associatedSynList, postdataComparisons, putassociatedSyn, deleteassociatedSyn, deletebatch } from '@/api/associationExtraction'
import {
	getLedgerList,
	getrunwayledgerList,
} from "@/api/LedgerApi";
import { ElMessage, ElMessageBox,ElNotification } from 'element-plus'

const loading = ref(false)
const route = useRoute()
const router = useRouter()
import { USER_ROLES_ENUM } from '@/define/organization.define'
const currentUser = ref(JSON.parse(localStorage.getItem('currentUserInfo') as string))
// 数据管理岗位权限
const role = ref(currentUser.value.staffRole.some((item) => item === USER_ROLES_ENUM.DATA_LEADER))

// 表格中的操作列
const buttons: any = [
	{
		code: 'edit',
		label: '编辑',
		type: 'primary',
		icon: 'i-majesticons-pencil-alt-line',
		verify: 'true',
		disabled: `${!role.value}`,
	},
	// {
	// 	code: 'download',
	// 	label: '下载',
	// 	type: 'info',
	// 	icon: 'i-ic-baseline-download',
	// 	verify: 'true',
	// },
	{
		code: 'delete',
		label: '删除',
		type: 'danger',
		icon: 'i-ic-round-dangerous',
		verify: 'true',
		disabled: `${!role.value}`,
	},
]
const id = ref('')

// 查询条件
const runwayForm: any = ref({
	Filter: '',

})
// 设置表格高度计算
const tableHeight = ref(0)
const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}
// 多选数据
const statisticsList: any = ref([])
const selectedCount = ref(0)
const selectionChange = (selection: []) => {
	selectedCount.value = selection.length
	statisticsList.value = selection
}

// 表中的内容
const tableData = ref([])
// 表头
const colData: any = ref([
	{
		prop: 'name',
		label: '任务名称',
		tooltip: true,
	},
	{
		prop: 'ledgerName',
		label: '提取数据源',
	},
	{
		prop: 'sourceLedgerName',
		label: '需填充业务表',
	},
	{
		prop: 'associatedFieldNumber',
		label: '关联字段数',
	},
	{
		prop: 'resultQuantity',
		label: '提取结果',
	},
	{
		prop: 'isEnable',
		label: '是否启用',
	},
	{
		prop: 'lastModificationTime',
		label: '更新时间',
	},
])
const active = ref(0)
const url = ref('')

//表格与分页关联
const reqParams = ref({
	skipCount: 0,
	maxResultCount: 10,
})
// 分页相关参数
const pagination = ref({
	total: 0,
	page: 1,
	size: 10,
})
// 查询
const getList = () => {
	loading.value = true
	associatedSynList({
		skipCount: (pagination.value.page - 1) * pagination.value.size,
		MaxResultCount: pagination.value.size,
		name: runwayForm.value.Filter,
		associatedSynType: 2
	}).then((res: any) => {
		loading.value = false
		res.data.items.forEach((item: any) => {
			item.ledgerName = item.ledger.name
			item.sourceLedgerName = item.sourceLedger.name
		})
		tableData.value = res.data.items
		pagination.value.total = res.data.totalCount
	})
}
// 批量删除
const ledgerArrDelete = () => {
	if(selectedCount.value == 0){
		ElMessage.warning('请选择数据！')
		return
	}
	let data: any = []
	statisticsList.value.forEach((item: any, index: any) => {
		data.push({
			ledgerId: item.ledgerId,
			sourceLedgerId: item.sourceLedgerId
		})
	})

	ElMessageBox.confirm('是否要批量删除?', '消息确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			deletebatch(data).then(() => {
				ElMessage.success('删除成功！')
				selectedCount.value = 0
				getList()
			})
		})
		.catch(() => { })
}
// 高级查询
const seniorList = () => {
	pagination.value.page = 1
	pagination.value.size = 10
	getList()
}
// 清空
const empty = () => {
	runwayForm.value.Filter = ''
	getList()
}
// 分页事件
const onPaginationChange = (val: any, type: any) => {
	if (type === 'page') {
		pagination.value.page = val
		reqParams.value.skipCount = (val - 1) * pagination.value.size
	} else {
		pagination.value.page = 1
		pagination.value.size = val
		reqParams.value.maxResultCount = pagination.value.size
	}
	getList()
}
const onTableBeforeComplete = ({ items, next }: any) => {
	const temp: any = []
	if (active.value === 1) {
		items.forEach((item: any) => {
			item.details.forEach((detail: any) => {
				temp.push({
					district: item.district,
					departmentName: detail.departmentName,
					ledgerCount: detail.ledgerCount,
					ledgerDataCount: detail.ledgerDataCount,
				})
			})
		})
		next(temp)
	} else if (active.value == 2) {
		items.forEach((item: any) => {
			item.details.forEach((detail: any) => {
				temp.push({
					district: item.district,
					street: detail.street,
					cityLedgerCount: detail.cityLedgerCount,
					cityLedgerDataCount: detail.cityLedgerDataCount,
					districtLedgerCount: detail.districtLedgerCount,
					districtLedgerDataCount: detail.districtLedgerDataCount,
					historyReportCount: detail.historyReportCount,
					ledgerCount: detail.ledgerCount,
					ledgerDataCount: detail.ledgerDataCount,
					userCount: detail.userCount,
				})
			})
		})
		next(temp)
	} else {
		next(items)
	}
}

const name = ref('')

const onToggleSwitch = (row: any) => {
	ElMessage.success(` ${row.isEnable ? '启用' : '禁用'}成功`)
}
const isSynchronous = ref(false);
// 判断编辑或者是添加
const isshowData = ref(false);
const addExtraction = () =>{
	isSynchronous.value = true;
	isshowData.value = false
	name.value = ''
	configs.value = [
		{
			fieldId: "",
			sourceFiledId: "",
		},
	]
	cycle.value = null
	ledgerIdData.value = {}
	sourceLedgerIdData.value = {}
	sourceLedgerId.value = null
	ledgerId.value = null
	ledgerIdList.value = []
	sourceLedgerIdList.value = []
}
// 添加或者编辑同步
const onSynchronous = () => {
	if (name.value == '') {
		return ElMessage.warning("请输入任务名称!");
	}
	if (configs.value.length == 0) {
		return ElMessage.warning("请完成对应关系!");
	}
	if (
		configs.value[configs.value.length - 1].fieldId == "" ||
		configs.value[configs.value.length - 1].sourceFiledId == ""
	) {
		return ElMessage.warning("请完成对应关系配置!");
	}
	if (cycle.value == null) {
		return ElMessage.warning("请选择自动提取周期!");
	}
	const data = {
		name:name.value,
		cycle:cycle.value == 0 ? '不提醒' :cycle.value == 2 ? '每周' :cycle.value == 3 ? '每月' :cycle.value == 6 ? '每半月' :cycle.value == 4 ? '每季度' :cycle.value == 7 ? '每半年' :cycle.value == 5 ? '每年' : null,
		ledgerId: ledgerIdData.value.id,
		AssociatedSynType:2,
		sourceLedgerId: sourceLedgerIdData.value.id,
		isEnable:true,
		configs: configs.value,
	};
	if (isshowData.value) {
		putassociatedSyn(data.ledgerId, data.sourceLedgerId, data).then((res) => {
			ElMessage.success("编辑数据关联提取成功！");
			isSynchronous.value = false;
			isshowData.value = false;
			opendata()
			getList()
		});
	} else {
		postdataComparisons(data).then((res) => {
			ElMessage.success("数据关联提取成功！");
			isSynchronous.value = false;
			opendata()
			getList()
		});
	}
};
// 表格中的操作列
const onTableClickButton = ({ btn, row, index }: any) => {
	if (btn.code === 'edit') {
		console.log(row);
		
		isSynchronous.value = true
		isshowData.value = true
		name.value = row.name
		ledgerId.value = row.ledgerId
		sourceLedgerId.value =  row.sourceLedgerId
		cycle.value = row.cycle == '不提醒' ? 0 :row.cycle =='每周' ? 2 :row.cycle == '每月' ? 3 :row.cycle == '每半月' ? 6 :row.cycle == '每季度' ? 4 :row.cycle == '每半年'  ? 7 :row.cycle == '每年' ? 5 : null
		configs.value = []
		row.configs.forEach((item) =>{
			configs.value.push({
				fieldId:item.fieldId,
				sourceFiledId:item.sourceFiledId
			})
		})
		showgetledgerId(row.ledgerId)
		shwogetsourceLedgerId(row.sourceLedgerId)
		
		
	}
	if (btn.code === 'delete') {
		let data: any = [{
			ledgerId: row.ledgerId,
			sourceLedgerId: row.sourceLedgerId
		}]
		ElMessageBox.confirm(`是否要删除 ${row.name}?`, '消息确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
			.then(() => {
				deletebatch(data).then(() => {
					ElMessage.success('删除成功！')
					getList()
				})
			})
			.catch(() => { })
	}

	if (btn.code === 'download') {
	}
}
// 当选定数据源发生变化时
async function showgetledgerId(e: any) {
	const res = await getLedgerList("", 0, 1, e);
	const { data } = res;
	ledgerIdData.value = data;
	ledgerIdList.value = data.tableInfo.fields;
}
// 当选定数据源发生变化时
async function shwogetsourceLedgerId(e: any) {
	const res = await getLedgerList("", 0, 1, e);
	const { data } = res;
	sourceLedgerIdData.value = data;
	sourceLedgerIdList.value = data.tableInfo.fields;

}
// 自动提取周期
const cycle:any = ref(null)
// 当前业务表数据
const sourceLedgerIdData:any = ref({});
// 当前业务表表头
const sourceLedgerIdList: any = ref([]);
// 需要填充业务表数据
const ledgerIdData: any = ref({});
// 需要填充业务表表头
const ledgerIdList: any = ref([]);
const open = () =>{
	if(!isSynchronous.value){
		opendata()
	}
}
const opendata = ()=>{
	name.value =''
	cycle.value = null
	ledgerId.value = null
	sourceLedgerId.value = null
	configs.value = [
		{
			fieldId: "",
			sourceFiledId: "",
		},
	];
	sourceLedgerIdData.value = {}
	sourceLedgerIdList.value = []
	ledgerIdData.value = {}
	ledgerIdList.value = []
}
// 当选定数据源发生变化时
async function getledgerId(e: any, index: number) {
	const res = await getLedgerList("", 0, 1, e[1]);
	const { data } = res;
	ledgerIdData.value = data;
	ledgerIdList.value = data.tableInfo.fields;
	configs.value = [
		{
			fieldId: "",
			sourceFiledId: "",
		},
	];
}
// 当选定数据源发生变化时
async function getsourceLedgerId(e: any, index: number) {
	const res = await getLedgerList("", 0, 1, e[1]);
	const { data } = res;
	sourceLedgerIdData.value = data;
	sourceLedgerIdList.value = data.tableInfo.fields;
	configs.value = [
		{
			fieldId: "",
			sourceFiledId: "",
		},
	];
}
const sourceLedgerId = ref(null);
const ledgerId = ref(null)
async function getLedgerByRunway() {
  try {
    const res = await getrunwayledgerList();
    sourceLedgerData.value.forEach((v: any) => {
      v.children = [];

      if (res.data) {
        if (v.label === "党的建设") {
          v.children.push(
            ...res.data
              .filter((x) => x.runwayName === "党的建设")[0]
              .ledgerItemDtos.map((x: any) => ({
                label: x.name,
                value: x.id,
                leaf: true,
              }))
          );
        }
        if (v.label === "经济发展") {
          v.children.push(
            ...res.data
              .filter((x) => x.runwayName === "经济发展")[0]
              .ledgerItemDtos.map((x: any) => ({
                label: x.name,
                value: x.id,
                leaf: true,
              }))
          );
        }
        if (v.label === "民生服务") {
          v.children.push(
            ...res.data
              .filter((x) => x.runwayName === "民生服务")[0]
              .ledgerItemDtos.map((x: any) => ({
                label: x.name,
                value: x.id,
                leaf: true,
              }))
          );
        }
        if (v.label === "平安法治") {
          v.children.push(
            ...res.data
              .filter((x) => x.runwayName === "平安法治")[0]
              .ledgerItemDtos.map((x: any) => ({
                label: x.name,
                value: x.id,
                leaf: true,
              }))
          );
        }
      } else {
        v.disabled = true;
      }
    });
  } catch (error: any) {
    if (error.response?.status === 500) {
      ElNotification.error("当前配置业务表人数较多，请5分钟后再试");
    }
  }
}
const sourceLedgerData = ref<any[]>(
	RunwayList.map((x) => ({ label: x.name, value: x.id, children: [] }))
);
const configs = ref([
	{
		fieldId: "",
		sourceFiledId: "",
	},
]);
const deleteConfigs = (index: any) => {
	configs.value.splice(index, 1);
};
const addConfigs = () => {
	if (configs.value.length !== 0) {
		if (
			configs.value[configs.value.length - 1].fieldId == "" ||
			configs.value[configs.value.length - 1].sourceFiledId == ""
		) {
			return ElMessage.warning("请完成上一条配置在进行添加!");
		}
	}
	configs.value.push({
		fieldId: "",
		sourceFiledId: "",
	});
};
const openChangeLedgerTime = ref(false)
const onReminderConfigChange = () => {
	if(cycle.value != 0){
		openChangeLedgerTime.value = true
		remindConfig.value.interval = cycle
	}
}
const remindConfig = ref<any>({
	interval: 0,
	weeklyReminderDayOfWeek: null,
	weeklyDeadlineDayOfWeek: null,
	monthlyReminderDay: 0,
	monthlyDeadlineDay: 0,
	quarterlyReminderDay1: null,
	quarterlyDeadlineDay1: null,
	quarterlyReminderDay2: null,
	quarterlyDeadlineDay2: null,
	quarterlyReminderDay3: null,
	quarterlyDeadlineDay3: null,
	quarterlyReminderDay4: null,
	quarterlyDeadlineDay4: null,
	yearlyReminderDay: null,
	yearlyDeadlineDay: null,
	upHalfMonthReminderTime: 0,
	upHalfMonthDateOnlyTime: 0,
	downHalfMonthReminderTime: 0,
	downHalfMonthDateOnlyTime: 0,
	upHalfYearReminderTime: null,
	upHalfYearDateOnlyTime: null,
	downHalfYearReminderTime: null,
	downHalfYearDateOnlyTime: null,

	weeklyStartDayOfWeek: null,
	monthlyStartDay: 0,
	quarterlyStartDay1: null,
	quarterlyStartDay2: null,
	quarterlyStartDay3: null,
	quarterlyStartDay4: null,
	yearlyStartDay: null,
	upHalfYearStartTime: 0,
	downHalfYearStartTime: 0,
	upHalfMonthStartTime: 0,
	downHalfMonthStartTime: 0,
})
const onConfirmChangeLedgerTime = ()=>{
	openChangeLedgerTime.value = false
}
const onCloseTime = () => {
	openChangeLedgerTime.value = false
}
const intervalOptions = [
	{value: 1, label: '周一'},
	{value: 2, label: '周二'},
	{value: 3, label: '周三'},
	{value: 4, label: '周四'},
	{value: 5, label: '周五'},
	{value: 6, label: '周六'},
	{value: 7, label: '周日'},
]
const onBlur = (type: any) => {
	if (type === 'upHalfMonthReminderTime') {
		if (!remindConfig.value.upHalfMonthReminderTime) {
			remindConfig.value.upHalfMonthReminderTime = 0
		}
	}
	if (type === 'upHalfMonthDateOnlyTime') {
		if (!remindConfig.value.upHalfMonthDateOnlyTime) {
			remindConfig.value.upHalfMonthDateOnlyTime = 0
		}
	}
	if (type === 'downHalfMonthReminderTime') {
		if (!remindConfig.value.downHalfMonthReminderTime) {
			remindConfig.value.downHalfMonthReminderTime = 0
		}
	}
	if (type === 'downHalfMonthDateOnlyTime') {
		if (!remindConfig.value.downHalfMonthDateOnlyTime) {
			remindConfig.value.downHalfMonthDateOnlyTime = 0
		}
	}
	if (type === 'upHalfMonthStartTime') {
		if (!remindConfig.value.upHalfMonthStartTime) {
			remindConfig.value.upHalfMonthStartTime = 0
		}
	}
	if (type === 'downHalfMonthStartTime') {
		if (!remindConfig.value.downHalfMonthStartTime) {
			remindConfig.value.downHalfMonthStartTime = 0
		}
	}
}

onMounted(() => {
	getList()
	getLedgerByRunway()
})
</script>

<route>
	{
		meta: {
			title:'数据关联提取',
			ignoreLabel:false
		}
	}
</route>
<template>
	<div class="dataAnalysis">
		<Dialog
			v-model="openChangeLedgerTime"
			title="设置提醒"
			width="1200"
			@clickConfirm="onConfirmChangeLedgerTime"
			@close="onCloseTime"
			style="overflow: visible"
		>
			<div class="custom-form">
				<div class="item">
					<div class="label timeTitle">业务表更新周期:</div>
					<div class="value">
						{{
							remindConfig.interval == 2
								? '每周更新'
								: remindConfig.interval == 3
								? '每月更新'
								: remindConfig.interval == 4
								? '每季度更新'
								: remindConfig.interval == 5
								? '每年更新'
								: remindConfig.interval == 6
								? '每半月更新'
								: remindConfig.interval == 7
								? '每半年更新'
								: ''
						}}
					</div>
				</div>
				<div class="item flex" v-if="remindConfig.interval == 2">
					<div class="cloum">
						<div class="label">提取提醒时间:</div>
						<div class="value">
							<el-select
								v-model="remindConfig.weeklyReminderDayOfWeek"
								placeholder="请选择日期"
								size="small"
							>
								<el-option
									v-for="item in intervalOptions"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</div>
					</div>
					<div class="cloum">
						<div class="label">提取开始时间:</div>
						<div class="value">
							<el-select
								v-model="remindConfig.weeklyStartDayOfWeek"
								placeholder="请选择日期"
								size="small"
							>
								<el-option
									v-for="item in intervalOptions"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</div>
					</div>
					<div class="cloum">
						<div class="label">提取结束时间:</div>
						<div class="value">
							<el-select
								v-model="remindConfig.weeklyDeadlineDayOfWeek"
								placeholder="请选择日期"
								size="small"
							>
								<template v-for="item in intervalOptions">
									<template
										v-if="item.value > remindConfig.weeklyReminderDayOfWeek"
									>
										<el-option
											:key="item.value"
											:label="item.label"
											:value="item.value"
										/>
									</template>
								</template>
							</el-select>
						</div>
					</div>
				</div>
				<div class="item flex" v-else-if="remindConfig.interval == 3">
					<div class="cloum">
						<div class="label">提取提醒时间:</div>
						<div class="value">
							每月
							<el-input-number
								style="width: 70px"
								v-model="remindConfig.monthlyReminderDay"
								:min="1"
								:max="31"
								size="small"
								controls-position="right"
							/>
							日
						</div>
					</div>
					<div class="cloum">
						<div class="label">提取开始时间:</div>
						<div class="value">
							每月
							<el-input-number
								style="width: 70px"
								v-model="remindConfig.monthlyStartDay"
								:min="1"
								:max="31"
								size="small"
								controls-position="right"
							/>
							日
						</div>
					</div>
					<div class="cloum">
						<div class="label">提取结束时间:</div>
						<div class="value">
							每月
							<el-input-number
								style="width: 70px"
								v-model="remindConfig.monthlyDeadlineDay"
								:min="1"
								:max="31"
								size="small"
								controls-position="right"
							/>
							日
						</div>
					</div>
				</div>
				<div style="width: 100%" v-else-if="remindConfig.interval == 4">
					<div class="item">
						<div class="cloum">
							<div class="label">提取提醒时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">提取开始时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">提取结束时间:</div>
							<div class="value"></div>
						</div>
					</div>
					<div class="item">
						<div class="label">第一季度:</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyReminderDay1"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyStartDay1"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyDeadlineDay1"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="item">
						<div class="label">第二季度:</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyReminderDay2"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyStartDay2"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyDeadlineDay2"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="item">
						<div class="label">第三季度:</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyReminderDay3"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyStartDay3"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyDeadlineDay3"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="item">
						<div class="label">第四季度:</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyReminderDay4"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyStartDay4"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyDeadlineDay4"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
				</div>
				<div class="item flex" v-else-if="remindConfig.interval == 5">
					<div class="cloum">
						<div class="label">提取提醒时间:</div>
						<div class="value">
							<el-date-picker
								style="width: 180px"
								v-model="remindConfig.yearlyReminderDay"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="cloum">
						<div class="label">提取开始时间:</div>
						<div class="value">
							<el-date-picker
								style="width: 180px"
								v-model="remindConfig.yearlyStartDay"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="cloum">
						<div class="label">提取结束时间:</div>
						<div class="value">
							<el-date-picker
								style="width: 180px"
								v-model="remindConfig.yearlyDeadlineDay"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
				</div>
				<div style="width: 100%" v-else-if="remindConfig.interval == 6">
					<div class="item">
						<div class="cloum">
							<div class="label">提取提醒时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">提取开始时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">提取结束时间:</div>
							<div class="value"></div>
						</div>
					</div>
					<div class="item">
						<div class="value">
							上半月
							<el-input-number
								@blur="onBlur('upHalfMonthReminderTime')"
								style="width: 70px"
								v-model="remindConfig.upHalfMonthReminderTime"
								:min="1"
								:max="15"
								size="small"
								controls-position="right"
							/>
							日
						</div>
						<div class="value">
							上半月
							<el-input-number
								@blur="onBlur('upHalfMonthStartTime')"
								style="width: 70px"
								v-model="remindConfig.upHalfMonthStartTime"
								:min="1"
								:max="15"
								size="small"
								controls-position="right"
							/>
							日
						</div>
						<div class="value">
							上半月
							<el-input-number
								@blur="onBlur('upHalfMonthDateOnlyTime')"
								style="width: 70px"
								v-model="remindConfig.upHalfMonthDateOnlyTime"
								:min="1"
								:max="15"
								size="small"
								controls-position="right"
							/>
							日
						</div>
					</div>
					<div class="item">
						<div class="value">
							下半月
							<el-input-number
								@blur="onBlur('downHalfMonthReminderTime')"
								style="width: 70px"
								v-model="remindConfig.downHalfMonthReminderTime"
								:min="16"
								:max="30"
								size="small"
								controls-position="right"
							/>
							日
						</div>
						<div class="value">
							下半月
							<el-input-number
								@blur="onBlur('downHalfMonthStartTime')"
								style="width: 70px"
								v-model="remindConfig.downHalfMonthStartTime"
								:min="16"
								:max="30"
								size="small"
								controls-position="right"
							/>
							日
						</div>
						<div class="value">
							下半月
							<el-input-number
								@blur="onBlur('downHalfMonthDateOnlyTime')"
								style="width: 70px"
								v-model="remindConfig.downHalfMonthDateOnlyTime"
								:min="16"
								:max="30"
								size="small"
								controls-position="right"
							/>
							日
						</div>
					</div>
				</div>
				<div style="width: 100%" v-else-if="remindConfig.interval == 7">
					<div class="item">
						<div class="cloum">
							<div class="label">提取提醒时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">提取开始时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">提取结束时间:</div>
							<div class="value"></div>
						</div>
					</div>
					<div class="item">
						<div class="label">上半年:</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.upHalfYearReminderTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.upHalfYearStartTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.upHalfYearDateOnlyTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="item">
						<div class="label">下半年:</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.downHalfYearReminderTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.downHalfYearStartTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.downHalfYearDateOnlyTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
				</div>
			</div>
		</Dialog>
		<Dialog v-model="isSynchronous" title="数据关联同步配置" width="800" :destory-on-close="true" @clickConfirm="onSynchronous"
			@close="isSynchronous = false" @open="open">
			<div class="synchronous">
				<div class="synchronous-label">
					<div class="label">任务名称:</div>
					<el-input placeholder="请输入比对任务名称" v-model="name"
						style="margin-right: 10px"></el-input>

				</div>
				<div class="synchronous-label">
					<div class="label">提取数据源:</div>
					<div class="value">
						<el-cascader v-model="sourceLedgerId" :show-all-levels="false" filterable :options="sourceLedgerData"
							@change="getsourceLedgerId($event, index)"></el-cascader>
					</div>
				</div>
				<div class="synchronous-label">
					<div class="label">需要填充的业务表:</div>
					<div class="value">
						<el-cascader v-model="ledgerId" :show-all-levels="false" filterable :options="sourceLedgerData"
							@change="getledgerId($event, index)"></el-cascader>
					</div>
				</div>
				<div class="synchronous-label" v-if="ledgerIdList.length && sourceLedgerIdList.length">
					<div class="mark">
						<p>当前业务表</p>
						<span></span>
						<p>需填充业务表</p>
					</div>
				</div>
				<div class="synchronous-label" v-for="(item, index) in configs" :key="index" v-if="ledgerIdList.length && sourceLedgerIdList.length">
					<div class="association">
						<el-select clearable v-model="item.sourceFiledId" placeholder="请选择字段名" style="width: 304px">
							<el-option v-for="item of sourceLedgerIdList" :key="item.name" :label="item.displayName" :value="item.id"
								:disabled="configs.some((f: any) => f.sourceFiledId === item.id)" />
						</el-select>
						<img src="@/assets/image/analysis4.png" alt="" />
						<el-select clearable v-model="item.fieldId" placeholder="请选择字段名" style="width: 304px">
							<el-option v-for="item of ledgerIdList" :key="item.name" :label="item.displayName" :value="item.id"
								:disabled="configs.some((f: any) => f.fieldId === item.id)" />
						</el-select>
						<el-icon style="margin-left: 10px; color: red; cursor: pointer" @click="deleteConfigs(index)">
							<Delete />
						</el-icon>
					</div>
				</div>
				<div class="synchronous-label" v-if="ledgerIdList.length&& sourceLedgerIdList.length">
					<p class="button" @click="addConfigs">添加对应关系</p>
				</div>
				<div class="synchronous-label">
					<div class="label">自动提取周期:</div>
					<div class="value">
						<el-select
								v-model="cycle"
								placeholder="请选择自动提取周期"
								@change="onReminderConfigChange"
							>
								<el-option
									v-for="item of ReminderCycleList.filter(
										(v) => v.label !== '全部'
									)"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
					</div>
				</div>

				
			</div>
		</Dialog>
		<Block :enable-fixed-height="true" :enable-expand-content="true" :enableBackButton="false" :title="'数据关联提取'"
			@heightChanged="onBlockHeightChanged">
			<template #topRight>
				<el-button type="primary" size="small" @click="addExtraction" style="margin-left: 10px">
					新增数据关联提取
				</el-button>
				<el-button type="danger" size="small" @click="ledgerArrDelete"
					style="margin-left: 10px">
					批量删除
				</el-button>
			</template>
			<template #expand>
				<div class="search-box" v-action:enter="seniorList">
					<el-input placeholder="请输入分析名称" v-model="runwayForm.Filter"
						style="width: 250px; margin-right: 10px"></el-input>
					<el-button size="default" type="default" @click="empty">
						<i class="i-ic-baseline-cleaning-services" style="margin-right: 3px"></i>
						清空
					</el-button>
					<el-button size="default" type="primary" @click="seniorList">
						<i class="i-ic-baseline-send" style="margin-right: 3px"></i>
						查询</el-button>
				</div>
			</template>
			<TableV2 ref="tableRef" :loading="loading" :height="tableHeight" :columns="colData" :defaultTableData="tableData"
				:headers="{ Urlkey: 'ledger' }" :enableToolbar="false" :enable-create="false" :enable-edit="false"
				:enable-delete="false" :enableSelection="true" :enableIndex="false" @before-complete="onTableBeforeComplete"
				:req-params="reqParams" :buttons="buttons" @clickButton="onTableClickButton"
				@selection-change="selectionChange">
				<template #isEnable="scope">
					<el-switch v-model="scope.row.isEnable" class="ml-2"
						style="--el-switch-on-color: #409eff; --el-switch-off-color: #ff4949" @change="onToggleSwitch(scope.row)" />
				</template>
			</TableV2>
			<Pagination :total="pagination.total" :page-size="pagination.size" :current-page="pagination.page"
				@current-change="onPaginationChange($event, 'page')" @size-change="onPaginationChange($event, 'size')">
			</Pagination>
		</Block>
	</div>
</template>
<style scoped lang="scss">
.search-box {
	align-items: start;
	display: flex;
	padding: 10px 15px;
}

.setFiltering {
	&-main {
		display: flex;
		align-items: center;
		margin-bottom: 16px;

		&-title {
			min-width: 100px;
			display: flex;
			align-items: center;

			margin-right: 10px;
		}

		&-span {
			display: flex;
			justify-content: space-between;
			align-items: center;

			& span {
				flex-grow: 1;
			}
		}
	}

	.screenData-name {
		padding-bottom: 10px;
		display: flex;
		align-items: flex-start;

		& p {
			text-align: end;
			min-width: 100px;
			text-wrap: nowrap;
			font-weight: 400;
		}
	}

	&-footer {
		// display: flex;
		align-items: center;
		justify-content: center;

		&-main {
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 14px;
			line-height: 22px;
			width: calc(100%);
			cursor: pointer;
			color: rgb(23, 100, 206);
			border: 1px dashed rgb(23, 100, 206);
		}
	}
}

.synchronous {
	padding: 10px 20px;
	margin-top: 10px;

	&-label {
		display: flex;
		align-items: center;
		margin-top: 10px;

		& .label {
			text-align: end;
			min-width: 120px;
			margin-right: 10px;
			&::before {
				content: "*";
				color: red;
			}
		}
		& .value {
			flex: 1;
		}

		& .mark {
			width: 100%;
			display: flex;

			& span {
				display: inline-block;
				width: 114px;
			}

			& p {
				width: 304px;
			}
		}
	}

	.association {
		display: flex;
		align-items: center;

		& img {
			margin: 0px 10px;
			width: 94px;
		}
	}

	.button {
		cursor: pointer;
		border: 1px solid #dcdfe6;
		color: #fdfffd;
		background: rgb(23, 100, 206);
		width: 100%;
		padding: 6px 0px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}
.custom-form {
	.timeTitle {
		width: 115px;
	}

	.item {
		display: flex;
		justify-content: space-between;
		.cloum {
			width: 30%;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}

		.flexend {
			display: flex;
			justify-content: end;
		}
	}
}
</style>

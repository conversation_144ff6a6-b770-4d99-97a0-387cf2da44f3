// IndexedDB 工具类
export class IndexedDBManager {
  private dbName: string
  private version: number
  protected db: IDBDatabase | null = null

  constructor(dbName: string, version: number = 1) {
    this.dbName = dbName
    this.version = version
  }

  // 初始化数据库
  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version)

      request.onerror = () => {
        reject(new Error('Failed to open IndexedDB'))
      }

      request.onsuccess = () => {
        this.db = request.result
        resolve()
      }

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result

        // 创建联系人存储
        if (!db.objectStoreNames.contains('contacts')) {
          const contactStore = db.createObjectStore('contacts', { keyPath: 'id' })
          contactStore.createIndex('name', 'name', { unique: false })
        }

        // 创建消息存储
        if (!db.objectStoreNames.contains('messages')) {
          const messageStore = db.createObjectStore('messages', { keyPath: 'id' })
          messageStore.createIndex('contactId', 'contactId', { unique: false })
          messageStore.createIndex('time', 'time', { unique: false })
        }

        // 创建设置存储
        if (!db.objectStoreNames.contains('settings')) {
          db.createObjectStore('settings', { keyPath: 'key' })
        }

        // 创建备份配置存储
        if (!db.objectStoreNames.contains('backupConfigs')) {
          const backupStore = db.createObjectStore('backupConfigs', { keyPath: 'id' })
          backupStore.createIndex('createdAt', 'createdAt', { unique: false })
        }

        // 创建区域管理存储
        if (!db.objectStoreNames.contains('regions')) {
          const regionStore = db.createObjectStore('regions', { keyPath: 'id' })
          regionStore.createIndex('regionName', 'regionName', { unique: false })
          regionStore.createIndex('regionType', 'regionType', { unique: false })
          regionStore.createIndex('createdAt', 'createdAt', { unique: false })
        }

        // 创建权限管理存储
        if (!db.objectStoreNames.contains('permissions')) {
          const permissionStore = db.createObjectStore('permissions', { keyPath: 'id' })
          permissionStore.createIndex('regionId', 'regionId', { unique: false })
          permissionStore.createIndex('authorizedUser', 'authorizedUser', { unique: false })
          permissionStore.createIndex('createdAt', 'createdAt', { unique: false })
        }

        // 创建共享设置存储
        if (!db.objectStoreNames.contains('shareSettings')) {
          const shareStore = db.createObjectStore('shareSettings', { keyPath: 'id' })
          shareStore.createIndex('regionId', 'regionId', { unique: false })
          shareStore.createIndex('sharedUser', 'sharedUser', { unique: false })
          shareStore.createIndex('createdAt', 'createdAt', { unique: false })
        }

        // 创建锁定设置存储
        if (!db.objectStoreNames.contains('lockSettings')) {
          const lockStore = db.createObjectStore('lockSettings', { keyPath: 'id' })
          lockStore.createIndex('regionId', 'regionId', { unique: false })
          lockStore.createIndex('createdAt', 'createdAt', { unique: false })
        }

        // 创建临时权限存储
        if (!db.objectStoreNames.contains('tempPermissions')) {
          const tempPermStore = db.createObjectStore('tempPermissions', { keyPath: 'id' })
          tempPermStore.createIndex('regionId', 'regionId', { unique: false })
          tempPermStore.createIndex('authorizedUser', 'authorizedUser', { unique: false })
          tempPermStore.createIndex('createdAt', 'createdAt', { unique: false })
        }
      }
    })
  }

  // 保存联系人
  async saveContacts(contacts: any[]): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['contacts'], 'readwrite')
      const store = transaction.objectStore('contacts')

      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)

      // 清空现有数据
      const clearRequest = store.clear()
      clearRequest.onsuccess = () => {
        // 保存新数据，确保是纯JavaScript对象
        for (const contact of contacts) {
          const plainContact = {
            id: contact.id,
            name: contact.name,
            lastMessage: contact.lastMessage,
            lastTime: contact.lastTime,
            unreadCount: contact.unreadCount,
            isOnline: contact.isOnline
          }
          store.add(plainContact)
        }
      }
      clearRequest.onerror = () => reject(clearRequest.error)
    })
  }

  // 获取联系人
  async getContacts(): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['contacts'], 'readonly')
    const store = transaction.objectStore('contacts')
    const request = store.getAll()

    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  // 保存消息
  async saveMessages(messages: any[]): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['messages'], 'readwrite')
      const store = transaction.objectStore('messages')

      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)

      // 清空现有数据
      const clearRequest = store.clear()
      clearRequest.onsuccess = () => {
        // 保存新数据，确保是纯JavaScript对象
        for (const message of messages) {
          const plainMessage = {
            id: message.id,
            contactId: message.contactId,
            content: message.content,
            time: message.time,
            isOwn: message.isOwn
          }
          store.add(plainMessage)
        }
      }
      clearRequest.onerror = () => reject(clearRequest.error)
    })
  }

  // 获取消息
  async getMessages(): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['messages'], 'readonly')
    const store = transaction.objectStore('messages')
    const request = store.getAll()

    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  // 添加单条消息
  async addMessage(message: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    // 确保消息对象是纯JavaScript对象，避免Vue响应式代理问题
    const plainMessage = {
      id: message.id,
      contactId: message.contactId,
      content: message.content,
      time: message.time,
      isOwn: message.isOwn
    }

    const transaction = this.db.transaction(['messages'], 'readwrite')
    const store = transaction.objectStore('messages')
    store.add(plainMessage)

    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)
    })
  }

  // 更新联系人
  async updateContact(contact: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    // 确保联系人对象是纯JavaScript对象，避免Vue响应式代理问题
    const plainContact = {
      id: contact.id,
      name: contact.name,
      lastMessage: contact.lastMessage,
      lastTime: contact.lastTime,
      unreadCount: contact.unreadCount,
      isOnline: contact.isOnline
    }

    const transaction = this.db.transaction(['contacts'], 'readwrite')
    const store = transaction.objectStore('contacts')
    store.put(plainContact)

    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)
    })
  }

  // 保存设置
  async saveSetting(key: string, value: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['settings'], 'readwrite')
    const store = transaction.objectStore('settings')
    store.put({ key, value })

    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)
    })
  }

  // 获取设置
  async getSetting(key: string): Promise<any> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['settings'], 'readonly')
    const store = transaction.objectStore('settings')
    const request = store.get(key)

    return new Promise((resolve, reject) => {
      request.onsuccess = () => {
        resolve(request.result ? request.result.value : null)
      }
      request.onerror = () => reject(request.error)
    })
  }



  // 保存备份配置
  async saveBackupConfig(config: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    // 确保配置对象是纯JavaScript对象
    const plainConfig = {
      id: config.id,
      startDate: config.startDate,
      fullBackupFrequency: config.fullBackupFrequency,
      incrementalBackupFrequency: config.incrementalBackupFrequency,
      dataRetentionPolicy: config.dataRetentionPolicy,
      createdAt: config.createdAt,
      updatedAt: config.updatedAt
    }

    const transaction = this.db.transaction(['backupConfigs'], 'readwrite')
    const store = transaction.objectStore('backupConfigs')
    store.put(plainConfig)

    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)
    })
  }

  // 获取备份配置
  async getBackupConfig(id: string): Promise<any> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['backupConfigs'], 'readonly')
    const store = transaction.objectStore('backupConfigs')
    const request = store.get(id)

    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  // 获取所有备份配置
  async getAllBackupConfigs(): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['backupConfigs'], 'readonly')
    const store = transaction.objectStore('backupConfigs')
    const request = store.getAll()

    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  // 删除备份配置
  async deleteBackupConfig(id: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['backupConfigs'], 'readwrite')
    const store = transaction.objectStore('backupConfigs')
    store.delete(id)

    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)
    })
  }

  // 保存区域
  async saveRegion(region: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    // 确保区域对象是纯JavaScript对象
    const plainRegion = {
      id: region.id,
      regionName: region.regionName,
      regionType: region.regionType,
      regionDescription: region.regionDescription,
      remark: region.remark,
      status: region.status,
      creator: region.creator,
      createdAt: region.createdAt,
      updatedAt: region.updatedAt
    }

    const transaction = this.db.transaction(['regions'], 'readwrite')
    const store = transaction.objectStore('regions')
    store.put(plainRegion)

    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)
    })
  }

  // 获取区域
  async getRegion(id: string): Promise<any> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['regions'], 'readonly')
    const store = transaction.objectStore('regions')
    const request = store.get(id)

    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  // 获取所有区域
  async getAllRegions(): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['regions'], 'readonly')
    const store = transaction.objectStore('regions')
    const request = store.getAll()

    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  // 删除区域
  async deleteRegion(id: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['regions'], 'readwrite')
    const store = transaction.objectStore('regions')
    store.delete(id)

    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)
    })
  }

  // 权限管理相关方法
  // 保存权限
  async savePermission(permission: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['permissions'], 'readwrite')
    const store = transaction.objectStore('permissions')

    // 创建数据副本，避免Vue响应式代理问题
    const permissionData = JSON.parse(JSON.stringify(permission))
    store.put(permissionData)

    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)
    })
  }

  // 获取单个权限
  async getPermission(id: string): Promise<any> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['permissions'], 'readonly')
    const store = transaction.objectStore('permissions')
    const request = store.get(id)

    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  // 获取所有权限
  async getAllPermissions(): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['permissions'], 'readonly')
    const store = transaction.objectStore('permissions')
    const request = store.getAll()

    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result || [])
      request.onerror = () => reject(request.error)
    })
  }

  // 根据区域ID获取权限
  async getPermissionsByRegionId(regionId: string): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['permissions'], 'readonly')
    const store = transaction.objectStore('permissions')
    const index = store.index('regionId')
    const request = index.getAll(regionId)

    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result || [])
      request.onerror = () => reject(request.error)
    })
  }

  // 删除权限
  async deletePermission(id: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['permissions'], 'readwrite')
    const store = transaction.objectStore('permissions')
    store.delete(id)

    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)
    })
  }

  // 共享设置相关方法
  // 保存共享设置
  async saveShareSetting(shareSetting: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['shareSettings'], 'readwrite')
    const store = transaction.objectStore('shareSettings')

    // 创建数据副本，避免Vue响应式代理问题
    const shareData = JSON.parse(JSON.stringify(shareSetting))
    store.put(shareData)

    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)
    })
  }

  // 获取单个共享设置
  async getShareSetting(id: string): Promise<any> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['shareSettings'], 'readonly')
    const store = transaction.objectStore('shareSettings')
    const request = store.get(id)

    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  // 获取所有共享设置
  async getAllShareSettings(): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['shareSettings'], 'readonly')
    const store = transaction.objectStore('shareSettings')
    const request = store.getAll()

    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result || [])
      request.onerror = () => reject(request.error)
    })
  }

  // 根据区域ID获取共享设置
  async getShareSettingsByRegionId(regionId: string): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['shareSettings'], 'readonly')
    const store = transaction.objectStore('shareSettings')
    const index = store.index('regionId')
    const request = index.getAll(regionId)

    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result || [])
      request.onerror = () => reject(request.error)
    })
  }

  // 删除共享设置
  async deleteShareSetting(id: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['shareSettings'], 'readwrite')
    const store = transaction.objectStore('shareSettings')
    store.delete(id)

    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)
    })
  }

  // 锁定设置相关方法
  // 保存锁定设置
  async saveLockSetting(lockSetting: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['lockSettings'], 'readwrite')
    const store = transaction.objectStore('lockSettings')

    // 创建数据副本，避免Vue响应式代理问题
    const lockData = JSON.parse(JSON.stringify(lockSetting))
    store.put(lockData)

    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)
    })
  }

  // 根据区域ID获取锁定设置
  async getLockSettingsByRegionId(regionId: string): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['lockSettings'], 'readonly')
    const store = transaction.objectStore('lockSettings')
    const index = store.index('regionId')
    const request = index.getAll(regionId)

    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result || [])
      request.onerror = () => reject(request.error)
    })
  }

  // 删除锁定设置
  async deleteLockSetting(id: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['lockSettings'], 'readwrite')
    const store = transaction.objectStore('lockSettings')
    store.delete(id)

    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)
    })
  }

  // 临时权限相关方法
  // 保存临时权限
  async saveTempPermission(tempPermission: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['tempPermissions'], 'readwrite')
    const store = transaction.objectStore('tempPermissions')

    // 创建数据副本，避免Vue响应式代理问题
    const tempPermData = JSON.parse(JSON.stringify(tempPermission))
    store.put(tempPermData)

    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)
    })
  }

  // 根据区域ID获取临时权限
  async getTempPermissionsByRegionId(regionId: string): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['tempPermissions'], 'readonly')
    const store = transaction.objectStore('tempPermissions')
    const index = store.index('regionId')
    const request = index.getAll(regionId)

    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result || [])
      request.onerror = () => reject(request.error)
    })
  }

  // 删除临时权限
  async deleteTempPermission(id: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(['tempPermissions'], 'readwrite')
    const store = transaction.objectStore('tempPermissions')
    store.delete(id)

    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)
    })
  }

  // 关闭数据库
  close(): void {
    if (this.db) {
      this.db.close()
      this.db = null
    }
  }
}

// 即时通信专用的IndexedDB管理器
export class MessagingDBManager extends IndexedDBManager {
  constructor() {
    super('MessagingDB', 1)
  }

  // 初始化默认数据
  async initDefaultData(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    return new Promise((resolve, reject) => {
      // 先检查是否已有数据
      const checkTransaction = this.db!.transaction(['contacts', 'messages'], 'readonly')
      const contactStore = checkTransaction.objectStore('contacts')
      const messageStore = checkTransaction.objectStore('messages')

      const contactCountRequest = contactStore.count()
      const messageCountRequest = messageStore.count()

      let contactCount = 0
      let messageCount = 0
      let completed = 0

      const checkComplete = () => {
        completed++
        if (completed === 2) {
          // 如果没有数据，则添加默认数据
          if (contactCount === 0 || messageCount === 0) {
            this.addDefaultData().then(resolve).catch(reject)
          } else {
            resolve()
          }
        }
      }

      contactCountRequest.onsuccess = () => {
        contactCount = contactCountRequest.result
        checkComplete()
      }

      messageCountRequest.onsuccess = () => {
        messageCount = messageCountRequest.result
        checkComplete()
      }

      checkTransaction.onerror = () => reject(checkTransaction.error)
    })
  }

  // 添加默认数据
  private async addDefaultData(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['contacts', 'messages'], 'readwrite')
      const contactStore = transaction.objectStore('contacts')
      const messageStore = transaction.objectStore('messages')

      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(transaction.error)

      // 添加默认联系人
      const defaultContacts = [
        {
          id: 'contact_1',
          name: '杨绍东',
          lastMessage: '我已经提交了业务数据表',
          lastTime: '10:23',
          unreadCount: 1,
          isOnline: true
        },
        {
          id: 'contact_2',
          name: '王明华',
          lastMessage: '明天的会议准备好了吗',
          lastTime: '10:23',
          unreadCount: 1,
          isOnline: true
        },
        {
          id: 'contact_3',
          name: '李华',
          lastMessage: '我已经提交了业务数据表',
          lastTime: '10:23',
          unreadCount: 0,
          isOnline: false
        },
        {
          id: 'contact_4',
          name: '张建国',
          lastMessage: '项目进度如何？',
          lastTime: '09:45',
          unreadCount: 0,
          isOnline: true
        },
        {
          id: 'contact_5',
          name: '陈美玲',
          lastMessage: '测试报告已完成',
          lastTime: '昨天',
          unreadCount: 2,
          isOnline: false
        }
      ]

      // 生成当前日期的时间字符串
      const formatLocalTime = (date: Date): string => {
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        const seconds = String(date.getSeconds()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      }

      const today = new Date()
      const todayStr = formatLocalTime(today).substring(0, 10) // 获取今天的日期部分

      // 添加默认消息
      const defaultMessages = [
        {
          id: 'msg_1',
          contactId: 'contact_1',
          content: '我已经提交了业务数据表',
          time: `${todayStr} 10:30:35`,
          isOwn: false
        },
        {
          id: 'msg_2',
          contactId: 'contact_1',
          content: '好的，我看一下',
          time: `${todayStr} 10:31:20`,
          isOwn: true
        },
        {
          id: 'msg_3',
          contactId: 'contact_2',
          content: '明天的会议准备好了吗',
          time: `${todayStr} 10:20:15`,
          isOwn: false
        },
        {
          id: 'msg_4',
          contactId: 'contact_2',
          content: '已经准备好了，会议材料都整理完毕',
          time: `${todayStr} 10:22:30`,
          isOwn: true
        },
        {
          id: 'msg_5',
          contactId: 'contact_4',
          content: '项目进度如何？',
          time: `${todayStr} 09:45:10`,
          isOwn: false
        },
        {
          id: 'msg_6',
          contactId: 'contact_4',
          content: '目前进度正常，预计下周完成',
          time: `${todayStr} 09:47:25`,
          isOwn: true
        }
      ]

      // 添加联系人
      for (const contact of defaultContacts) {
        contactStore.add(contact)
      }

      // 添加消息
      for (const message of defaultMessages) {
        messageStore.add(message)
      }
    })
  }
}

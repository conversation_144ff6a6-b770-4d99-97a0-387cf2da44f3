self.addEventListener(
	'message',
	function (e) {
		console.log(e, 'm')
		let fn = async () => {
			const {url, ids, token} = e.data
			if (!url || !ids || ids.length === 0 || e.origin) {
				self.postMessage({isComplete: true})
				return
			}
			// ?RegionId=${data.id}
			for (let i = 0; i < ids.length; i++) {
				const id = ids[i]
				const __url = url.replace('{id}', id)
				await fetch(`${__url}`, {
					method: 'GET',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: token,
					},
				})
					.then((res) => res.json())
					.then((data) => {
						data.items.forEach((v: any) => (v.parentId = id))
						self.postMessage({id, data, index: i, isComplete: i === ids.length - 1})
					})
			}
		}
		fn()
	},
	false
)

import {saveAs} from 'file-saver'
import {request as defHttp} from '@/api'
import qs from 'querystring'
const Urlkey = 'ledger'
const iframe_code = 'iframeCode'

/**
 * 获取业务表类型
 */
export const getLedgerType = async (params?: any) => {
	// return defHttp.request({
	// 	url: `/api/ledger-service/ledger-type`,
	// 	method: 'GET',
	// 	params,
	//
	// })

	const res = await defHttp.request({
		url: `/api/ledger-service/ledger-type/allledgertype`,
		method: 'GET',
		params,
	})
	// console.log(res)
	// res.data.items = res.data
	// res.data.totalCount = res.data.length
	return res
}

/**
 * 业务表管理创建业务表
 * @param params 业务表参数
 * @returns
 */
export const createLedger = (params: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger`,
		method: 'POST',
		data: {
			...params,
		},
	})
}

export const dataComparisonLedgerIdFull = (ledgerId: any, data: any) => {
	return defHttp.request({
		url: `/api/ledger/dataComparison/${ledgerId}/full`,
		method: 'POST',
		data,
	})
}

export const dataComparisonRecordList = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/dataComparisonRecord`,
		method: 'GET',
		params,
	})
}

export const ratingGetpagelist = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/five-star-rating/getpagelist`,
		method: 'GET',
		params,
	})
}
export const ledgerDepartmentDataStatisticsList = (params: any) => {
	if (params.departmentLevels === null) {
		params.departmentLevels = []
	}

	// /api/ledger/ledgerDepartmentDataStatistics  1.15--dzj更改接口
	if (params.departmentLevels === null) {
		params.departmentLevels = []
	}
	return defHttp.request({
		url: `api/ledger/ledgerDepartmentDataStatistics/list-new`,
		method: 'GET',
		params,
	})
}
export const ledgerDepartmentFiles = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/statistics-fill-export`,
		method: 'POST',
		data,

		responseType: 'blob',
	})
}
export function getChildRegion(parentId: string) {
	return defHttp.request({
		url: `/api/platform/region/parent/${parentId}`,
		method: 'GET',
	})
}
export const getDistrictRegions = () => {
	return defHttp.request({
		url: `/api/platform/departmentInternal/district-region-names`,
		method: 'POST',
	})
}
export const ledgerDepartmentFilesDetail = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/statistics-detail-export`,
		method: 'POST',
		data,

		responseType: 'blob',
	})
}

export const ledgerDepartmentDataStatisticsDetailList = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartmentDataStatistics/detail-list`,
		method: 'GET',
		params,
	})
}
export const ledgergradeparentid = (params: any) => {
	return defHttp.request({
		url: `/api/platform/region/region-by-grade-parentid`,
		method: 'GET',
		params,
	})
}
export const dataComparisonLedgerIdFullData = (ledgerId: any, comparisonRecordId: any) => {
	return defHttp.request({
		url: `/api/ledger/dataComparison/${ledgerId}/${comparisonRecordId}/apply-full-data`,
		method: 'PUT',
	})
}

export const dataComparisonRecordbenchmarkInfo = (ledgerId: any) => {
	return defHttp.request({
		url: `/api/ledger/dataComparison/${ledgerId}/benchmark-info`,
		method: 'GET',
	})
}
export const ledgerDepartmentDataStatistics = (ledgerId: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartmentDataStatistics/${ledgerId}`,
		method: 'GET',
	})
}
export const ledgerDepartmentDataStatisticsUrgeUser = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartmentDataStatistics/urge-many-ledger`,
		method: 'POST',
		data,
	})
}
export const ledgerDepartmentDataStatisticsUrgeDepartment = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartmentDataStatistics/urge-many-department`,
		method: 'POST',
		data,
	})
}

export const workflowSchemeInfoCode = (code: any) => {
	return defHttp.request({
		url: `/api/workflow/workflowSchemeInfo/by-code/${code}`,
		method: 'GET',
	})
}
/**
 * 根据业务表id获取业务表详情
 * @param id 业务表id
 * @returns
 */
export const getDetailByLedgerId = (id: string) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/${id}`,
		method: 'GET',
	})
}

/**
 * 根据业务表id获取业务表详情精简版
 * @param id
 * @returns
 */
export const GetLedgerDetailById = (id: string) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/lite-table/${id}`,
		method: 'GET',
	})
}

/**
 * 创建新业务表模板
 * @param id 业务表id
 * @returns
 */
export const getTLedgerTemplate = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerTemplate`,
		method: 'POST',
		data,
	})
}

/**
 * 根据id更新业务表管理的业务表
 * @param id 业务表id
 * @param params 业务表参数
 * @returns
 */
export const updateLedgerById = (id: string, params: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/${id}`,
		method: 'PUT',
		data: {
			...params,
		},
	})
}

/**
 * 根据ID更新业务表管理的业务表在线状态
 * @param id 业务表id
 * @param online 上线/下线
 * @returns
 */
export const updateLedgerOnline = (id: string, online: boolean, startImmediately?: boolean) => {
	let params
	if (startImmediately) {
		params = {
			isOnline: online,
			startImmediately,
		}
	} else {
		params = {
			isOnline: online,
		}
	}
	return defHttp.request({
		url: `/api/ledger-service/ledger/${id}/online-status`,
		method: 'PUT',
		params,
	})
}

/**
 * 根据ID更新业务表管理的业务表名称
 * @param id 业务表id
 * @param name 业务表名称
 * @returns
 */
export const updateLedgerName = (id: string, name: string) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/${id}/name`,
		method: 'PUT',
		params: {
			name,
		},
	})
}

/**
 * 根据业务表ID获取业务表excel模版
 * @param id 业务表id
 */
export const downloadLedgerTemp = (id: string) => {
	const reslut = defHttp.request({
		url: `/api/ledger-service/ledger/${id}/import-excel-template-file`,
		method: 'GET',
		headers: {
			Urlkey,
			responseType: 'arraybuffer',
		},
	})

	reslut.then((res: any) => {
		console.log('downloadLedgerTemp', res.data)
		const fileBytes = res.data.fileBytes

		let b64toFile = (b64Data: any, filename: any, contentType: any) => {
			let sliceSize = 512
			let byteCharacters = window.atob(b64Data)

			let byteArrays = []

			for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
				let slice = byteCharacters.slice(offset, offset + sliceSize)
				let byteNumbers = new Array(slice.length)

				for (let i = 0; i < slice.length; i++) {
					byteNumbers[i] = slice.charCodeAt(i)
				}
				let byteArray = new Uint8Array(byteNumbers)
				byteArrays.push(byteArray)
			}

			let file = new File(byteArrays, filename, {type: contentType})
			return file
		}

		saveAs(
			b64toFile(
				fileBytes,
				`${+new Date()}.xlsx`,
				'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
			),
			`${+new Date()}.xlsx`
		)
	})
}

/**
 * 获取数据源数据集
 * @returns
 */
export const getTableDataSet = () => {
	return defHttp.request({
		url: `/api/ledger-service/table-data-set`,
		method: 'GET',
	})
}
/**
 * 获取区县运维员数据
 * @returns
 */
export const getDepartmentRoleName = (params: any) => {
	return defHttp.request({
		url: `/api/platform/user-department-role/district-operator`,
		method: 'GET',
		params,
	})
}

/**
 * 获取区县运维员数据
 * @returns
 */
export const getDepartmentRoleNameActive = (data: any) => {
	return defHttp.request({
		url: `/api/platform/user-department-role/change-user-active`,
		method: 'put',
		data,
	})
}

/**
 * 获取数据源数据集
 * @param id 数据源数据集id
 * @returns
 */
export const getTableDataSetById = (id: string) => {
	return defHttp.request({
		url: `/api/ledger-service/table-data-set/${id}`,
		method: 'GET',
	})
}

/**
 * 获取业务表管理列表或根据id获取业务表管理详情 (管理员)
 * @param id 业务表id
 * @param skip 跳过条数
 * @param size 获取条数
 * @returns
 */
export const getLedgerList = (
	runway: string = '',
	skip: number = 0,
	size: number = 10,
	id?: string
) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger${id ? '/' + id : ''}`,
		method: 'GET',
		params: {
			runway,
			SkipCount: skip,
			MaxResultCount: size,
		},
	})
}

export const getOriginLedgerList = (
	runway: string = '',
	skip: number = 0,
	size: number = 10,
	IsOnlyContainsUniqueField: boolean = true
) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger`,
		method: 'GET',
		params: {
			runway,
			SkipCount: skip,
			MaxResultCount: size,
			IsOnlyContainsUniqueField,
			ignoreRunways: '市级共性台账',
		},
	})
}

export const getrunwayledgerList = () => {
	return defHttp.request({
		url: '/api/ledger-service/ledger/runway-ledger-list',
		method: 'GET',
	})
}
/**
 * 根据runway获取对应的业务表列表(用户)
 * @param skip
 * @param size
 */
export const getLedgerListByUser = (params?: any) => {
	const arr = new URLSearchParams()
	params.ledgerIds?.forEach((item: any) => arr.append('ledgerIds', item))
	return defHttp.request({
		url: `/api/ledger/ledger-user/my-ledgers?${arr.toString() ? arr.toString() : ''}`,
		method: 'GET',
		params,
	})
}

/**
 * 根据runway获取对应的业务表列表(用户)
 * @param skip
 * @param size
 */
export const getLedgerListByUserlittle = (params?: any) => {
	const arr = new URLSearchParams()
	params.ledgerIds?.forEach((item: any) => arr.append('ledgerIds', item))
	return defHttp.request({
		url: `/api/ledger/ledger-user/my-ledgers-little?${arr.toString() ? arr.toString() : ''}`,
		method: 'GET',
		params,
	})
}

export const getLedgerListByCommunity = (params?: any) => {
	const arr = new URLSearchParams()
	params.ledgerIds?.forEach((item: any) => arr.append('ledgerIds', item))
	return defHttp.request({
		url: `/api/ledger/ledger-user/my-ledgers?${arr.toString() ? arr.toString() : ''}`,
		method: 'GET',
		params,
	})
}

export const GetLedgerListOnlyNameByUser = (params?: any) => {
	const arr = new URLSearchParams()
	params.ledgerIds?.forEach((item: any) => arr.append('ledgerIds', item))
	return defHttp.request({
		url: `/api/ledger/ledger-user/my-ledgers-only-name?${arr.toString() ? arr.toString() : ''}`,
		method: 'GET',
		params,
	})
}

/**
 * 根据业务表id和用户id获取业务表详情
 * @param ledgerId 业务表id
 * @param uid 用户id
 * @returns
 */
export const getLedgerInfoByUser = (ledgerId: string, uid: string) => {
	console.log(ledgerId, uid, 777)
	return defHttp.request({
		url: `/api/ledger/ledger-user/${ledgerId}/${uid}/permission`,
		method: 'GET',
	})
}

/**
 * 根据业务表id和用户id设置业务表置顶
 * @param uid 用户id
 * @param ledgerId 业务表id
 * @param isTop 是否置顶
 * @returns
 */
export const setLedgerTopByUser = (uid: string, ledgerId: string, isTop: boolean) => {
	return defHttp.request({
		url: `/api/ledger/ledger-user/${ledgerId}/${uid}/top`,
		method: 'POST',
		params: {isTop},
	})
}

/**
 * 获取业务表管理列表跑道统计
 * @param arr 跑道 name 集合
 * @returns
 */
export const getRunwaysCount = (arr: string[]) => {
	const params = arr.map((item) => `runways=${item}`).join('&')
	return defHttp.request({
		url: `/api/ledger-service/ledger/runway-ledger-count?${params}`,
		method: 'GET',
	})
}

/**
 * 根据业务表id下载业务表excel模版
 * @param id 业务表id
 * @returns
 */
export const downloadExcelTemplateByLedgerId = (id: string) => {
	return defHttp.request({
		url: `/api/ledger-service/dynamic-table/export-template`,
		method: 'POST',
		data: {
			tableInfoId: id,
		},
		headers: {
			Urlkey,
		},
		responseType: 'blob',
	})
}

/**
 * Excel上传成功后通知后台执行导入
 * @param data
 * @returns
 */
export const executeImportNotify = (data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/dynamic-table/import`,
		method: 'POST',
		data,
	})
}

/**
 * 获取业务表导入记录
 * @param params
 * @returns
 */
export const getLedgerImportRecord = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger-service/table-data-import-record`,
		method: 'GET',
		params,
	})
}

/**
 * 保存业务表筛选、统计、计算规则
 * @param ledgerId 业务表id
 * @param uid 当前登陆用户id
 * @param data 规则数据
 * @returns
 */
export const saveLedgerConfig = (ledgerId: string, uid: string, data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledger-user/${ledgerId}/${uid}/table-query-config`,
		method: 'PUT',
		data,
	})
}

/**
 * 删除业务表筛选、统计、计算规则
 * @returns
 */
export const deleteLedgerConfig = (ledgerId: string, uid: string, params: any) => {
	return defHttp.request({
		url: `/api/ledger/ledger-user/${ledgerId}/${uid}/table-query-config`,
		method: 'DELETE',
		params,
	})
}

/**
 * 业务表用户绑定列表
 * @param params
 */
export const ledgerUser = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledger-user`,
		method: 'GET',
		params,
	})
}

/**
 * 将多个用户授权到业务表
 * @param ledgerId  业务表id
 */
export const ledgerService = (ledgerId: string, params: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/${ledgerId}/grant-user`,
		method: 'POST',
		data: params,
	})
}

/**
 * 批量授权业务表和用户
 * @param params
 */
export const BatchAuthLedgersAndUsers = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerPermissionsAuthorizationMode/batch-authorized-the-department`,
		method: 'POST',
		data: params,
	})
}

/**
 * 批量取消本部门授权
 * @param params
 * @returns
 */
export const batchDeleteAuthLedgersAndUsers = (params: any) => {
	return defHttp.request({
		url: '/api/ledger/ledgerPermissionsAuthorizationMode/batch-cancel-authorized-the-department',
		method: 'POST',
		data: params,
	})
}
/**
 * 根据业务表用户Id集合获取业务表用户集合
 * @param   params [{
 * userId,
 * ledgerId
 * }]
 */
export const usersByIds = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/ledger-user/ledger-users-by-ids`,
		method: 'POST',
		data: params,
	})
}

/* 获取业务表筛选、统计、计算规则配置
 * @param ledgerId 业务表id
 * @param uid 用户id
 * @param params {TargetId,QueryType}
 * @returns
 */
export const getLedgerConfig = (ledgerId: string, uid: string, params: any) => {
	return defHttp.request({
		url: `/api/ledger/ledger-user/${ledgerId}/${uid}/table-query-configs`,
		method: 'GET',
		params,
	})
}

/**
 * 根据业务表id获取业务表配置相关的填充数据
 * @param ledgerId 业务表id
 * @param params {QueryConfigId,SkipCount,MaxResultCount}
 * @returns
 */
export const getLedgerTableListByLedgerConfig = (ledgerId: string, params: any) => {
	return defHttp.batch(
		`/api/ledger-service/ledger-data/${ledgerId}/list-by-query-config`,
		5000,
		params,
		{Urlkey}
	)
}

/**
 * 获取业务表分页数据
 * @param data
 * @returns
 */
export const getLedgerTableListByLedgerId = (data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/list`,
		method: 'POST',
		data,
	})
}

/**
 * 所见即所得
 * @param data
 * @returns
 */
export const newGetLedgerTableListByLedgerId = (data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/page`,
		method: 'POST',
		data,
	})
}

/**
 * 获取字段数量
 */
export const getLedgerStatisticsByField = (key: string, type: number, data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/statistics/${key}/${type}`,
		method: 'POST',
		data,
	})
}

/**
 * 根据业务表id创建新的业务表数据
 * @param ledgerId 业务表id
 * @param data 新增数据
 * @returns
 */
export const getLedgerTableAddByLedgerId = (ledgerId: string, data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${ledgerId}/data`,
		method: 'POST',
		data,
	})
}

/**
 * 更新业务表数据
 * @param ledgerId 业务表id
 * @param ledgerDataId 业务表详情数据id
 * @param data
 * @returns
 */
export const updateLedgerTableDataById = (ledgerId: string, ledgerDataId: string, data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${ledgerId}/${ledgerDataId}/data`,
		method: 'PUT',
		data,
	})
}

/**
 * 获取业务表分页数据批量(未筛选、统计、计算的填充数据)
 * @param data
 * @returns
 */
export const getLedgerTableList = (data: any) => {
	return defHttp.batchData(
		{
			url: `/api/ledger-service/ledger-data/list`,
			method: 'POST',
			data,
		},
		5000
	)
}

/**
 * 获取业务表数据详情(我的业务表卡片-详情)
 * @param ledgerId 业务表id
 * @param ledgerDataId 业务表数据id
 * @param params
 * @returns
 */
export const getLedgerDataInfoById = (ledgerId: string, ledgerDataId: string, params: any) => {
	return defHttp
		.request({
			url: `/api/ledger-service/ledger-data/${ledgerId}/${ledgerDataId}/data`,
			params,
			method: 'GET',
		})
		.catch((err: any) => {
			if (err.response.data.error.message === '脱敏验证码已失效,请重新获取') {
				console.log('脱敏验证码已失效,请重新获取')
				localStorage.removeItem('ValidateCode')
			}
		})
}

/**
 * 删除业务表详情数据
 * @param ledgerId 业务表id
 * @param ledgerDataId 业务表数据id
 * @returns
 */
export const deleteLegerDataById = (ledgerId: string, ledgerDataId: string) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${ledgerId}/${ledgerDataId}/data`,
		method: 'DELETE',
	})
}

/**
 * 获取业务表数据详情的历史记录
 * @param params
 * @returns
 */
export const getLedgerDataLog = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataUpdateLog`,
		method: 'GET',
		params,
	})
}

/**
 * 获取业务表列表或根据id获取业务表详情
 * @param params
 */
export const getLedgerLists = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger`,
		method: 'GET',
		params,
	})
}
// 获取区县运维员台账列表
export const getDialogLedgerLists = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/ledger-county-data`,
		method: 'GET',
		params,
	})
}
export const getLedgerByDepartmentLists = (beAuthDepartmentId: string, params?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/by-beAuthDepartmentId/${beAuthDepartmentId}`,
		method: 'GET',
		params,
	})
}

// 获取用户的授权业务表
export const getLedgerByUserId = (beAuthUserId: string, params?: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/by-beAuthUserId/${beAuthUserId}`,
		method: 'GET',
		params,
	})
}
export const getLedgerByLedgerTypeId = (id?: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-type/${id}/ledger-names`,
		method: 'GET',
	})
}
/**
 * 根据跑道获取业务表类型
 * @param params
 */
export const getRunwayLedgerType = async (params?: any) => {
	// return defHttp.request({
	// 	url: `/api/ledger-service/ledger/ledgerType`,
	// 	method: 'GET',
	// 	params,
	//
	// })
	const res = await defHttp.request({
		url: `/api/ledger-service/ledger-type/allledgertype`,
		method: 'GET',
	})
	// console.log(res)
	// res.data.items = res.data
	// res.data.totalCount = res.data.length
	return res
}

export const getTableFiledValidateRule = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger/tableFiledValidateRule/cache`,
		method: 'GET',
		params,
	})
}

export const getMyLedgerType = async (param?: any) => {
	const params = {
		MaxResultCount: 1000,
		SkipCount: 0,
	}
	const res = await defHttp.request({
		url: `/api/ledger/ledger-user/my-ledgers-departments`,
		method: 'GET',
		params,
	})
	console.log(res)
	res.data.items = res.data
	res.data.totalCount = res.data.length
	return res
}

export const getLedgerListsP = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/my-management`,
		method: 'GET',
		params,
	})
}



/**
 * 创建业务表类型
 */
export const createLedgerType = (params: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-type`,
		method: 'POST',
		data: {
			...params,
		},
	})
}

/**
 * 创建业务表类型
 */
export const delLedgerType = (id: string) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-type/${id}`,
		method: 'DELETE',
	})
}

/**
 * 更新业务表提醒配置
 * @param id 业务表id
 * @param params
 * @returns
 */
export const updateLedgerDataConfig = (id: string, params: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/${id}/remind-config`,
		method: 'PUT',
		data: {
			...params,
		},
	})
}
/**
 * 更新业务表类型
 * @param id 业务表id
 * @param typeId 业务表typeid
 * @returns
 */
export const updateLedgerDataType = (id: string, typeId: string, runway?: string) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/${id}/type/${typeId}/${runway}`,
		method: 'PUT',
	})
}

/**
 * 发送脱敏验证码
 * @param id
 * @returns
 */
export const sendCode = (id: string, params: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/${id}/desensitize-code`,
		params,
		method: 'POST',
	})
}

/**
 * 数据分析发送脱敏验证码
 * @param id
 * @returns
 */
export const sendDesensitizeCode = (id: string, params: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/${id}/desensitize-code`,
		params,
		method: 'POST',
	})
}
/**
 * 数据分析校验有效性
 * @param id
 * @returns
 */
export const sendDesensitizeValidateCode = (id: string, params: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/${id}/desensitize-code/validate`,
		params,
		method: 'GET',
	})
}
// 数据分析图表校验有效性
export const sendchartCode = (id: string, params: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/${id}/dataAnalysis-chart`,
		params,
		method: 'GET',
	})
}

/**
 * 验证脱敏验证码有效性
 * @param id
 * @param params
 * @returns
 */
export const sendValidateCode = (id: string, params: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/${id}/desensitize-code/validate`,
		method: 'GET',
		params,
	})
}

export const dolwnloadReginDeatil = (params: any) => {
	const url = `/api/filling/report-task-ou/exportReportTasks-by-region-deatil`
	return defHttp.request({
		url,
		method: 'GET',
		params,
		responseType: 'blob',
	})
}

/**
 * 触发业务表提醒
 * @param ledgerId  业务表id
 * @returns
 */
export const ledgerReminder = (ledgerId: string) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/${ledgerId}/trigger-reminder`,
		method: 'POST',
	})
}

/**
 * 根据业务表id导出业务表数据
 * @param ledgerId 业务表id
 * @param params
 * @returns
 */
export const batchExportLedgerData = (ledgerId: string, params?: any, data?: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${ledgerId}/export-v2/all`,
		method: 'POST',
		params,
		data,
		headers: {
			Urlkey,
		},
		responseType: 'blob',
	})
}

/**
 * 根据业务表id导出业务表选中的数据, 异步
 * @param ledgerId
 * @param params
 * @param data
 * @returns
 */
export const ExportLedgerDataAsync = (ledgerId: string, params?: any, data?: any) => {
	return defHttp.request({
		url: `/api/ledger-service/Ledger-data/${ledgerId}/export-async`,
		method: 'POST',
		params,
		data,
		headers: {
			Urlkey,
		},
		responseType: 'blob',
	})
}

/**
 * 根据业务表id导出业务表全部, 异步
 * @param ledgerId
 * @param params
 * @param data
 * @returns
 */
export const ExportLedgerDataAllAsync = (ledgerId: string, params?: any, data?: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${ledgerId}/export-all-async`,
		method: 'POST',
		params,
		data,
		headers: {
			Urlkey,
		},
		responseType: 'blob',
	})
}

/**
 * 获取业务表统计
 * @param params
 */
export const ledgerServiceList = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/list-by-dynamic-query`,
		method: 'POST',
		data: {
			...params,
		},
		headers: {
			Urlkey,
		},
	})
}
/**
 * 环境整治统计业务表数据
 * @param params
 */
export const improvementServiceList = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/environmental-improvement/statistics`,
		method: 'GET',
		headers: {
			Urlkey,
		},
	})
}

/**
 * 根据业务表id和用户id获取权限
 * @param LedgerId 业务表id
 * @param UserId 用户id
 * @returns
 */
export const getUserLedgerTableRole = (LedgerId: string, UserId: any) => {
	return defHttp.request({
		url: `api/ledger/ledger-user/${LedgerId}/${UserId}/permission`,
		method: 'GET',
		headers: {
			Urlkey,
		},
	})
}
/**
 * 根据业务表id和用户id设置是否已读
 * @param uid 用户id
 * @param ledgerId 业务表id
 * @param isRead 是否已读
 * @returns
 */
export const setLedgerSetReadByUser = (uid: string, ledgerId: string, isRead: boolean) => {
	return defHttp.request({
		url: `/api/ledger/ledger-user/${ledgerId}/${uid}/setRead`,
		method: 'POST',
		params: {isRead},
	})
}
/* 根据业务表id导出业务表选中的数据
 * @param ledgerId 业务表id
 * @param params
 * @returns
 */
export const batchExportLedgerData2 = (ledgerId: string, params?: any, data?: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${ledgerId}/export-v2`,
		method: 'POST',
		params,
		data,
		headers: {
			Urlkey,
		},
		responseType: 'blob',
	})
}

/**
 * 获取表格数据导出记录列表
 * @param params 业务表参数
 * @returns
 */
export const getTableDataExportRecord = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger-service/table-data-export-record`,
		method: 'GET',
		params,
		headers: {
			Urlkey,
		},
	})
}

/**
 * API数据源
 * @param params
 * @param data
 * @param id
 * @param method
 * @returns
 */
export const getLedgerAPIData = (params?: any, data?: any, id?: string, method: string = 'GET') => {
	const url = id ? `/api/ledger/api-data-set/${id}/search` : `/api/ledger/api-data-set`
	return defHttp.request({
		url,
		method,
		params,
		data,
		headers: {
			Urlkey,
		},
	})
}

export const getIgnoreRecord = (params?: any, method: string = 'PUT') => {
	const url = `/api/filling/plan-task/planTask-ignoreRecord`
	return defHttp.request({
		url,
		method,

		params,
	})
}

export const getTableLederList = (params?: any, method: string = 'GET') => {
	const url = `/api/ledger-service/ledger-data/statistics/daily`
	return defHttp.request({
		url,
		method,

		params,
	})
}

export const getStatisticsExport = (params?: any, method: string = 'GET') => {
	const url = `/api/ledger-service/ledger-data/statistics/daily/export`
	return defHttp.request({
		url,
		method,

		responseType: 'blob',

		params,
	})
}

export const getReportList = (params?: any, data?: any, method: string = 'GET') => {
	const url = `/api/filling/report-task-ou/repotTask-regions`
	return defHttp.request({
		url,
		method,
		params,
		data,
	})
}

export const getRegion = (parentId?: any, method: string = 'GET') => {
	const url = `/api/platform/region/parent/${parentId}`
	return defHttp.request({
		url,
		method,
		headers: {
			Urlkey: 'iframeCode',
		},
	})
}

export const getPlanTask = (params?: any, method: string = 'GET') => {
	const url = `/api/filling/plan-task`
	return defHttp.request({
		url,
		method,
		params,
	})
}
export const getStatisticsStatementList = (params: any) => {
	return defHttp.request({
		url: '/api/filling/plan-task/planTask-by-regionId',
		method: 'GET',
		params,
	})
}
export const getRegionAll = (params?: any, method: string = 'GET') => {
	const url = `/api/platform/region/all`
	return defHttp.request({
		url,
		method,
		params,
		headers: {
			Urlkey: 'iframeCode',
		},
	})
}
export const getReportListIds = (params?: any, data?: any, method: string = 'GET') => {
	const url = `/api/filling/report-task-ou/reportTasks-by-region`
	return defHttp.request({
		url,
		method,
		params,
		data,
	})
}

export const searchLedgerAPIData = (data?: any) => {
	return defHttp.request({
		url: `/api/ledger/api-data-set/search`,
		method: 'POST',
		data,
		headers: {
			Urlkey,
		},
	})
}

/**
 * 查看跨部门授权列表
 * @param params 业务表参数
 * @returns
 */
export const ledgerDepartment = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment`,
		method: 'GET',
		params,
	})
}

/**
 * 跨部门批量授权
 * @param params
 * @returns
 */
export const addDepartmentEmpower = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/add-department-empower`,
		method: 'POST',
		data: {
			...params,
		},
	})
}

/* 修改业务表类型
 * @param id 业务表类型ID
 * @param params 业务表参数
 * @returns
 */
export const putLedgerType = (params?: any, id?: string) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-type/${id}`,
		method: 'PUT',
		data: {
			...params,
		},
	})
}
/**
 * 根据业务表id创建新的业务表数据
 * @param ledgerId 业务表id
 * @param data 新增数据
 * @returns
 */
export const getLedgerTableApplyAddByLedgerId = (ledgerId: string, data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${ledgerId}/apply-data`,
		method: 'POST',
		data,
	})
}
/**
 * 获取待审核的业务表分页数据
 * @param ledgerId 业务表id
 * @param skipCount 新增数据
 * @param maxResultCount 新增数据
 * @param operationTypes 新增数据
 * @returns
 */
export const getPendingAuditTableList = (
	ledgerId: any,
	skipCount: any,
	maxResultCount: any,
	operationTypes?: any
) => {
	const operationTypesParams = operationTypes
		.map((type: any) => `OperationTypes=${type}`)
		.join('&')
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${ledgerId}/my-pending-audit-data-list?skipCount=${skipCount}&maxResultCount=${maxResultCount}&${operationTypesParams}`,
		method: 'GET',
		headers: {
			Urlkey,
		},
	})
}

/**
 * 本部门批量授权
 * @param params
 * @returns
 */
export const addUserEmpower = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/add-user-empower`,
		method: 'POST',
		data: {
			...params,
		},
	})
}

/**
 * 跨部门取消授权
 * @param params
 * @returns
 */
export const deleteEmpower = (params?: any, LedgerId?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/${LedgerId}/delete-empower`,
		method: 'POST',
		data: {
			...params,
		},
	})
}

/**
 * 批量设置业务表跨部门数据权限
 * @param params
 * @returns
 */
export const dataEmpower = (params?: any, LedgerId?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/${LedgerId}/data-empower`,
		method: 'POST',
		data: {
			...params,
		},
	})
}

/**
 * 获取业务表数据权限字段
 * @param id 业务表id
 * @param departmentId 部门id
 * @returns
 */
export const ledgerServiceFields = (id?: any, departmentId?: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/${id}/${departmentId}/fields`,
		method: 'GET',
	})
}

/**
 * 保存业务表统计数据
 * @param id 业务表统计id
 * @param data 业务表统计参数
 * @returns
 */
export const saveLedgerStatistics = (data: any, id?: string) => {
	return defHttp.request({
		url: `/api/ledger/ledgerStatistics${id ? '/' + id : ''}`,
		method: id ? 'PUT' : 'POST',
		data,
	})
}

/**
 * 获取业务表统计列表
 * @param params
 * @id 业务表统计id
 * @returns
 */
export const getLedgerStatistics = (params?: any, id?: string, isList: boolean = false) => {
	return defHttp.request({
		url: `/api/ledger/ledgerStatistics${id ? '/' + id + (isList ? '/statistics' : '') : ''}`,
		method: 'GET',
		params,
	})
}

/**
 * 删除业务表统计 by ID
 * @param params
 * @id 业务表统计id
 * @returns
 */
export const deleteLedgerStatisticsById = (id: string) => {
	return defHttp.request({
		url: `/api/ledger/ledgerStatistics${id ? '/' + id : ''}`,
		method: 'DELETE',
	})
}

/**
 * 下载业务表统计
 * @param id
 */
export const downloadLedgerStatistics = (id: string) => {
	const reslut = defHttp.request({
		url: `/api/ledger/ledgerStatistics/${id}/export`,
		method: 'POST',
		headers: {
			Urlkey,
		},
		responseType: 'blob',
	})

	reslut.then((res: any) => {
		const data = res.data
		const blob = new Blob([data])
		saveAs(blob, `${+new Date()}.xlsx`)
	})
}

/**
 * 创建审核批次（申请审核业务表数据）
 * @param data
 * @returns
 */
export const commitLedgerAuditBatch = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledger-audit-batch`,
		method: 'POST',
		data,
	})
}
/**
 * 提交所有数据
 * @param
 * @returns
 */
export const submitAllDatas = (data: any) => {
	return defHttp.request({
		url: '/api/ledger/ledger-audit-batch/submit-all-audited-datas',
		method: 'POST',
		data,
	})
}

export const GetLedgerAuditBatchById = (id: string) => {
	return defHttp.request({
		url: `/api/ledger/ledger-audit-batch/${id}`,
		method: 'GET',
	})
}

export const submitAllData = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledger-audit-batch/shortcut-submit-audited-datas`,
		method: 'POST',
		data,
	})
}
// 获取导出数据的附件大小
export const getAllSize = (ledgerId: string, attachmentType: number) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${ledgerId}/attachment-size?attachmentType=${attachmentType}`,
		method: 'GET',
	})
}
/**
 * 获取表格数据导出记录列表
 * @param params 业务表参数
 * @returns
 */
export const getLegderAuditTableList = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledger-audit-batch/my-audit-batchs`,
		method: 'GET',
		params,
		headers: {
			Urlkey,
		},
	})
}

/**
 * 获取指定审核批次下的业务表分页数据
 * @param batchId 业务表id
 * @param skipCount
 * @param maxResultCount
 * @param operationTypes 类型
 * @param LedgerAuditStatus
 * @returns
 */
export const getLegderAuditTableListDataList = (
	batchId: any,
	skipCount: any,
	maxResultCount: any,
	operationTypes?: any,
	LedgerAuditStatus?: any
) => {
	const operationTypesParams = operationTypes
		.map((type: any) => `OperationTypes=${type}`)
		.join('&')
	const LedgerAuditStatusParams = LedgerAuditStatus.map(
		(type: any) => `LedgerAuditStatus=${type}`
	).join('&')
	return defHttp.request({
		url: `/api/ledger-service/ledgerAuditedData/${batchId}/audit-data-list?skipCount=${skipCount}&maxResultCount=${maxResultCount}&${operationTypesParams}&${LedgerAuditStatusParams}`,
		method: 'GET',
		headers: {
			Urlkey,
		},
	})
}
/**
 * 审核业务表数据
 * @param batchId
 * @param data
 * @returns
 */
export const auditLedgerDataList = (batchId: any, data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledger-audit-batch/${batchId}/audit-ledger-data`,
		method: 'POST',
		data,
	})
}

export const auditAllLedgerData = (batchId: string, data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledger-audit-batch/${batchId}/audit-all-ledger-data`,
		method: 'POST',
		data,
	})
}

/**
 * 	多级审核业务表数据
 * @param flowId
 * @returns
 */
export const PushLedgerAuditBatchFlow = (flowId: string, data) => {
	return defHttp.request({
		url: `/api/ledger/ledgerAuditBatchFlow/${flowId}/audit`,
		method: 'POST',
		data,
	})
}

/**
 * 获取多级审核节点
 * @param flowId
 * @returns
 */
export const GetLedgerAuditFlows = (flowId: string) => {
	return defHttp.request({
		url: `/api/ledger/ledger-audit-batch-flow-node/nodes-by-flow-id/${flowId}`,
		method: 'GET',
	})
}

/**
 * 保存业务表筛选、统计、计算规则
 * @param LedgerId 业务表id
 * @param DataId 数据id
 * @param data
 * @returns
 */
export const updateAuditLegderTableData = (LedgerId: string, DataId: string, data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${LedgerId}/${DataId}/apply-data`,
		method: 'PUT',
		data,
	})
}
/**
 * 申请删除业务表数据
 * @param LedgerId 业务表id
 * @param DataId 数据id
 * @returns
 */
export const AuditDeleteLedgerData = (LedgerId: string, DataId: string) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${LedgerId}/${DataId}/apply-data`,
		method: 'DELETE',
	})
}

/**
 * 获取业务表数据详情(我的业务表卡片-详情)
 * @param ledgerId 业务表id
 * @param DataId 业务表数据id
 * @returns
 */
export const getAuditLedgerDataInfoById = (LedgerId: string, DataId: string) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${LedgerId}/${DataId}/my-pending-audit-data`,
		method: 'GET',
	})
}

/**
 * 新建业务表模板
 * @param ledgerId 业务表id
 * @param data
 * @returns
 */
export const addLedgerTemplate = (LedgerId: string, data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerTemplate/${LedgerId}`,
		data,
		method: 'POST',
	})
}

/**
 * 获取业务表模板列表
 * @param params 业务表参数
 * @returns
 */
export const getLedgerTemplateTableList = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerTemplate`,
		method: 'GET',
		params,
	})
}
export const getLedgerTemplate = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerTemplate`,
		method: 'GET',
		params,
	})
}
/**
 * 获取业务表模板详情
 * @param id 业务表id
 * @returns
 */
export const getLedgerTemplateTableListData = (id?: string) => {
	return defHttp.request({
		url: `/api/ledger/ledgerTemplate/${id}`,
		method: 'GET',
	})
}

/**
 * 编辑业务表模板
 * @param id 模板id
 * @param data
 * @returns
 */
export const updateLedgerTemplatebyId = (id?: string, data?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerTemplate/${id}`,
		data,
		method: 'PUT',
	})
}

/**
 * 删除业务表模板
 * @param id 模板id
 * @param data
 * @returns
 */
export const deleteLedgerTemplateData = (id: string) => {
	return defHttp.request({
		url: `/api/ledger/ledgerTemplate/${id}`,
		method: 'DELETE',
	})
}

/**
 * 获取审核人
 * @param ledgerId 业务表id
 * @param BigDepartmentId //大部门id
 * @returns
 */
export const getLedgerAuditMan = (ledgerId: string) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/${ledgerId}/auditors`,
		method: 'GET',
	})
}

/**
 * 获取多级审核流程
 * @param flowId
 * @returns
 */
export const GetLedgerMultiLevelAudit = (flowId: string) => {
	return defHttp.request({
		url: `/api/ledger/ledger-audit-batch-flow-node/nodes-by-flow-id/${flowId}`,
		method: 'GET',
	})
}

/**
 * 获取大部门ID
 * @returns
 */
export const getBigDepartmentId = (params?: any) => {
	return defHttp.request({
		url: `/api/platform/departmentInternal/department-extend-by-id`,
		method: 'GET',
		params,
		headers: {Urlkey: 'iframeCode'},
	})
}

/**
 * 获取业务表模板转换id后的数据格式
 * @returns
 */
export const getledgerTemplateConversionId = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerTemplate/ConversionId`,
		method: 'GET',
		params,
	})
}

/**
 * @description 批量删除业务表数据
 * @param ledgerId string
 * @param DataIds string[]
 * @returns
 */
export const batchDeleteLedgerData = (ledgerId: string, DataIds: string[]) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${ledgerId}/delete-data`,
		method: 'DELETE',
		data: {ledgerId, DataIds},
	})
}

/**
 * 申请删除全部业务表数据
 * @param ledgerId string
 * @param data
 * @returns
 */

export const deleteAllLedgerData = (ledgerId: string, data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${ledgerId}/apply-all-data`,
		method: 'DELETE',
		data,
	})
}

/**
 * 删除所有业务表数据 后台是否完成
 * @param ledgerId string
 * @returns boolean
 */
export const isDeleteAllProcess = (ledgerId: string) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${ledgerId}/apply-all-data/processing`,
		method: 'DELETE',
	})
}

interface AuditRecordDOT {
	LedgerId: string
	Status?: number[]
	AuditedBatchId?: string
	MaxResultCount?: number
	SkipCount?: number
}
export const getAuditRecord = (params: AuditRecordDOT) => {
	return defHttp.request({
		url: `/api/ledger-service/ledgerAuditedData/my-audited`,
		method: 'GET',
		params,
	})
}

// 获取业务表审核批次

export const getAuditRecordBatch = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/ledger-audit-batch/my-submit-audit-batchs`,
		method: 'GET',
		params,
	})
}

/**
 * 创建业务表辅助填充
 * @param data
 * @returns
 */
export const createLedgerAuxiliaryFilling = (data: any) => {
	return defHttp.request({
		url: '/api/ledger/ledgerAuxiliaryFilling',
		method: 'POST',
		data,
		headers: {Urlkey: 'SR'},
	})
}

/**
 * 获取业务表辅助填报列表
 * @param params
 * @returns
 */
export const getLedgerAuxiliaryFilling = (params: any) => {
	return defHttp.request({
		url: '/api/ledger/ledgerAuxiliaryFilling',
		method: 'GET',
		params,
	})
}

/**
 * 数据源辅助填报列表数据
 * @param params
 * @returns
 */
export const getDataSetAuxiliaryFilling = (params) => {
	return defHttp.request({
		url: '/api/ledger/ledgerDataSetAuxiliaryFilling',
		method: 'GET',
		params,
	})
}
/**
 * 更新辅助填报数据
 * @param ledgerId
 * @param sourceLedgerId
 * @param data
 * @returns
 */
export const updateLedgerAuxiliaryFilling = (
	ledgerId: string,
	sourceLedgerId: string,
	data: any
) => {
	return defHttp.request({
		url: `/api/ledger/ledgerAuxiliaryFilling/${ledgerId}/${sourceLedgerId}`,
		method: 'PUT',
		data,
	})
}

/**
 * 删除辅助填报数据
 * @param ledgerId
 * @param sourceLedgerId
 * @returns
 */
export const removeLedgerAuxiliaryFilling = (ledgerId: string, sourceLedgerId: string) => {
	return defHttp.request({
		url: `/api/ledger/ledgerAuxiliaryFilling/${ledgerId}/${sourceLedgerId}`,
		method: 'DELETE',
	})
}
/**
 * 获取单个业务表数据
 * @param data
 * @returns
 */
export const getSingleLedgerData = (data: any) => {
	return defHttp.request({
		url: '/api/ledger-service/ledger-data/single',
		method: 'POST',
		data,
	})
}
/**
 * 获取数据源辅助填报类型
 * @returns
 */
// export const getApiAuxiliaryFillingData = () => {
// 	return defHttp.request({
// 		url: '/api/ledger/apiAuxiliaryFilling',
// 		method: 'GET',
//
// 	})
// }

export const getApiAuxiliaryFillingData = (apiId: string) => {
	return defHttp.request({
		url: `/api/ledger/apiAuxiliaryFilling/rule/${apiId}`,
		method: 'GET',
	})
}

export const getApiDataComparison = (ledgerId: string, data: any) => {
	return defHttp.request({
		url: `/api/ledger/dataComparison/${ledgerId}/single`,
		method: 'POST',
		data,
	})
}
export const getApiDataComparisonApplyData = (ledgerId: string, dataId: string, data: any) => {
	return defHttp.request({
		url: `/api/ledger/dataComparison/${ledgerId}/${dataId}/apply-data`,
		method: 'PUT',
		data,
	})
}

/**
 * 数据集填报
 * @param data
 * @returns
 */
export const CreateDataSetAuxiliaryFillingData = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataSetAuxiliaryFilling`,
		method: 'POST',
		data,
	})
}

export const updateDataSetAuxiliaryFillingData = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataSetAuxiliaryFilling/${id}`,
		method: 'PUT',
		data,
	})
}
/**
 * 创建数据源业务表辅助填报
 * @param data
 * @returns
 */
export const CreateApiAuxiliaryFillingData = (data: any) => {
	return defHttp.request({
		url: '/api/ledger/apiAuxiliaryFilling/createRule',
		method: 'POST',
		data,
	})
}

/**
 * 获取数据源辅助填报数据
 * @param params
 * @returns
 */
export const getApiAuxiliaryFillingList = (ledgerId: string) => {
	return defHttp.request({
		url: `/api/ledger/apiAuxiliaryFilling/rulelist?ledgerId=${ledgerId}`,
		method: 'GET',
	})
}
/**
 * 获取数据源的列表
 * @params params
 * @returns
 */

export const getApiList = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/apiAuxiliaryFilling/apiList`,
		method: 'GET',
		data,
	})
}

/**
 * 	删除数据源辅助填报
 * @param ledgerId
 * @returns
 */
export const removeApiAuxiliaryFillingData = (ledgerId: string) => {
	return defHttp.request({
		url: `/api/ledger/apiAuxiliaryFilling/removeRule?ledgerId=${ledgerId}`,
		method: 'GET',
	})
}
export const removeDataSetAuxiliaryFillingData = (ledgerId: string) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataSetAuxiliaryFilling/${ledgerId}`,
		method: 'DELETE',
	})
}
// export const updateLedgerAuxiliaryFilling = (id:string,data:any)=>{
// 	return defHttp.request({

// 	})
// }
/**
 * 查询业务表跨部门授权中的操作权限
 */
export const getLedgerDepartmentPermissions = (LedgerId: any, DepartmentId: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/${LedgerId}/${DepartmentId}/department-permissions`,
		method: 'GET',
	})
}

/**
 * 获取单个业务表数据
 * @param data
 * @returns
 */
export const ledgerDepartmentPermissions = (LedgerId: any, data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/${LedgerId}/permissions`,
		method: 'POST',
		data,
	})
}

/**
 * 获取单个业务表数据
 * @param data
 * @returns
 */
export const getWorkflowSchemeInfo = (params: any) => {
	return defHttp.request({
		url: `/api/workflow/workflowSchemeInfo`,
		method: 'GET',
		params,
	})
}

/**
 * 编辑单个业务表数据
 * @param data
 * @returns
 */
export const ledgerAuditedDataPUT = (LedgerId: any, AuditedDataId: any, params: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledgerAuditedData/${AuditedDataId}/${LedgerId}`,
		method: 'PUT',
		data: {
			...params,
		},
	})
}

/**
 * 他人编辑单个业务表数据
 * @param data
 * @returns
 */
export const ledgerAuditedDataPUTOther = (LedgerId: any, AuditedDataId: any, params: any) => {
	return defHttp.request({
		// /api/ledger-service/ledgerAuditedData/{ledgerId}/{id}/update-by-other-user
		// url: `/api/ledger-service/ledgerAuditedData/${AuditedDataId}/${LedgerId}`,
		url: `/api/ledger-service/ledgerAuditedData/${LedgerId}/${AuditedDataId}/update-by-other-user`,

		method: 'POST',
		data: {
			...params,
		},
	})
}
/**
 * 获取单个业务表数据
 * @param data
 * @returns
 */
export const ledgerAuditedDataGet = (LedgerId: any, AuditedDataId: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledgerAuditedData/${AuditedDataId}/${LedgerId}`,
		method: 'GET',
	})
}

/**
 * 删除单个审核
 * @param data
 * @returns
 */
export const ledgerAuditedDataDelete = (LedgerId: any, AuditedDataId: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledgerAuditedData/${AuditedDataId}/${LedgerId}`,
		method: 'DELETE',
	})
}

/**
 * 删除多个审核
 * @param data
 * @returns
 */
export const ledgerAuditedDataDeletemore = (LedgerId: any, data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledgerAuditedData/${LedgerId}/many`,
		data,
		method: 'DELETE',
	})
}

/**
 * 删除全部审核
 * @param data
 * @returns
 */
export const ledgerAuditedDataDeleteAll = (LedgerId: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledgerAuditedData/${LedgerId}/all`,
		method: 'DELETE',
	})
}

/**
 * 是否审核批次提交中
 * @param ledgerId
 * @returns
 */
export const isBatchSubmitting = (ledgerId: string) => {
	return defHttp.request({
		url: `/api/ledger/ledger-audit-batch/${ledgerId}/is-audit-batch-submitting`,
		method: 'GET',
	})
}

/**
 * 判断是否还在审核当中
 * @param batchId
 * @returns
 */
export const isBatchAuditing = (batchId: string) => {
	return defHttp.request({
		url: `/api/ledger/ledger-audit-batch/${batchId}/is-ledger-data-auditing `,
		method: 'GET',
	})
}

export const getDownLoadData = (data: any) => {
	return defHttp.request({
		// url: '/api/ledger-service/ledgerFile/download/0',
		url: '/api/ledger-service/Ledger-data/download/file',
		method: 'POST',
		data,

		responseType: 'blob',
	})
}

// 根据登录用户删除自己创建的未上线业务表
export const deleteOfflineLedger = (params: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/delete-offlineLedger`,
		params,
		method: 'GET',
	})
}

/**
 * 根据业务表id设置收藏业务表
 * @param params
 */
export const getLedgerCollection = (LedgerId?: any, isCollection?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledger-user/${LedgerId}/collection?isCollection=${isCollection}`,
		method: 'GET',
	})
}

/**
 * 业务表用户收藏列表
 * @param params
 */
export const getLedgerCollectionList = (data?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledger-user/collection`,
		method: 'POST',
		data,
	})
}
/**
 * 效验批量本部门授权和复用权限时所选业务表的操作权限是否一致
 * @param params
 */
export const getLedgeralidationOperate = (data?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/validation-operate`,
		method: 'POST',
		data,
	})
}

/**
 * 根据业务表id和复用业务表id设置授权部门和授权人员
 * @param params
 */
export const getLedgerUserCollection = (data: any, CollectionLedgerId: any) => {
	return defHttp.request({
		url: `/api/ledger/ledger-user/${CollectionLedgerId}/collection`,
		method: 'POST',
		data,
	})
}

/**
 * 根据业务表id和复用业务表id设置授权部门和授权人员
 * @param params
 */
export const ledgerManagerrunwayCount = (params: any) => {
	// q去掉市级共性业务表筛选
	const runway = `?Runways=市级共性业务表&Runways=党的建设&Runways=经济发展&Runways=民生服务&Runways=平安法治`
	// const runway = `?Runways=党的建设&Runways=经济发展&Runways=民生服务&Runways=平安法治`

	return defHttp.request({
		url: `/api/ledger-service/ledger/my-management/runway-count${runway}`,
		method: 'GET',
		params,
	})
}

/**
 * 获取各部门填报进度
 * @param ledgerId string
 * @returns
 */
export const getEachDepartmentPrrogress = (ledgerId: string, taskItemId: string, params: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerAuditBatchStatistics/${ledgerId}/${taskItemId}/reporting-progress/each-department`,
		method: 'GET',
		params,
	})
}
/**
 * 获取当前部门填报进度
 * @param ledgerId string
 * @returns
 */
export const getCurrentDepartmentProgress = (ledgerId: string, taskItemId: string, params: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerAuditBatchStatistics/${ledgerId}/${taskItemId}/reporting-progress/current-department`,
		method: 'GET',
		params,
	})
}

/**
 * 部门通知
 * @param ledgerId string
 * @returns
 */
export const departmentNotify = (
	ledgerId: string,
	taskItemId: string,
	params: {StartTime: string; EndTime: string; ledgerId?: string}
) => {
	params = {
		...params,
		ledgerId,
	}
	return defHttp.request({
		url: `/api/ledger/ledgerAuditBatchStatistics/${ledgerId}/${taskItemId}/unfilled-department/notify`,
		method: 'GET',
		params,
	})
}

/**
 * 用户通知
 * @param ledgerId string
 * @returns
 */
export const userNotify = (
	ledgerId: string,
	taskItemId: string,
	params: {StartTime: string; EndTime: string; ledgerId?: string}
) => {
	params = {
		...params,
		ledgerId,
	}
	return defHttp.request({
		url: `/api/ledger/ledgerAuditBatchStatistics/${ledgerId}/${taskItemId}/unfilled-user/notify`,
		method: 'GET',
		params,
	})
}

export const getDataLedger = (id) => {
	return defHttp.request({
		url: `/api/platform/departmentInternal/department-extend-users?departmentId=${id}`,
		method: 'GET',
		headers: {Urlkey: 'iframeCode'},
	})
}

/**
 * 市级部门业务表批量授权
 * @param data
 * @returns
 */
export const AddCountyDepartmentEmpoWer = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/add-countydepartment-empower`,
		method: 'POST',
		data,
	})
}

/**
 * 获取业务表管理的发布部门列表
 * @returns
 */
export const getMyManagementType = () => {
	return defHttp.request({
		url: '/api/ledger-service/ledger/my-management-type',
		method: 'GET',
	})
}

/**
 * 	获取我的业务表的发布部门列表
 * @param params
 * @returns
 */
export const getMyLedgerTypes = (params?: any) => {
	return defHttp.request({
		url: '/api/ledger/ledger-user/my-ledgers-types',
		method: 'GET',
		params,
	})
}

/**
 * 获取业务表授权的发布部门列表
 * @returns
 */
export const getEmpowerLedgerType = () => {
	return defHttp.request({
		url: '/api/ledger-service/ledger/empower-ledger-type',
		method: 'GET',
	})
}

/*
 * 授权部门是否响应
 * @param id string
 * @param departmentId  string
 * @returns
 */
export const responseAuthDepartment = (id: string, departmentId: string) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/${id}/${departmentId}/response`,
		method: 'GET',
	})
}

/**
 * 发送响应提醒
 * @returns
 */
export const sendResponseMessage = (id: string, departmentId: string) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/${id}/${departmentId}/sendremind`,
		method: 'GET',
	})
}
/**
 * 获取业务表数据分析列表
 * @param params
 * @returns
 */
export const ledgerDataAnalysis = (params?: any) => {
	return defHttp.request({
		url: '/api/ledger/ledgerDataAnalysis',
		method: 'GET',
		params,
	})
}
/**
 * 创建业务表数据分析
 * @param data
 * @returns
 */
export const postledgerDataAnalysis = (data?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis`,
		method: 'POST',
		data,
	})
}
/**
 * 修改业务表数据分析
 * @param data
 * @returns
 */
export const putledgerDataAnalysis = (data?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/${data.ledgerDataAnalysisId}`,
		method: 'PUT',
		data,
	})
}

/**
 * 查看业务表数据分析详细
 * @param data
 * @returns
 */
export const getledgerDataAnalysis = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/${params.id}`,
		method: 'GET',
		// params,
	})
}

/**
 * 查看业务表数据分析具体分页数据
 * @param data
 * @returns
 */
export const getdataAnalysis = (params?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/${params.id}/dataAnalysis`,
		method: 'GET',
		params,
	})
}

/**
 * 批量删除业务表统计
 * @param data
 * @returns
 */
export const ledgerDataAnalysisBatchDelete = (data: any, params?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/batch-delete`,
		method: 'POST',
		data,
	})
}

/**
 * 获取所属区县”、“所属街镇”、“所属村社”
 * @param data
 * @returns
 */
export const myRegionNames = (data: any, params?: any) => {
	return defHttp.request({
		url: `/api/platform/department/my-region-names?guid=${params.guid}`,
		method: 'POST',
		data,
	})
}

export const allRegionNames = (data = {}) => {
	return defHttp.request({
		url: `/api/platform/department/my-region-names`,
		method: 'POST',
		data,
	})
}

/**
 * 查看业务表数据分析最终分页数据
 * @param data
 * @returns
 */
export const dataAnalysis = (id: string, params?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/${id}/dataAnalysis`,
		method: 'GET',
		params,
	})
}
/**
 * 查看业务表数据分析中间分页数据
 * @param data
 * @returns
 */
export const extractDataAnalysis = (id: string, params?: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/${id}/extract-dataAnalysis`,
		method: 'GET',
		params,
	})
}
/**
 * 创建在线分析配置
 * @param data
 * @returns
 */
export const addOnLineAnalysisConfig = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/${data.ledgerDataAnalysisId}/onLine-analysis-config`,
		method: 'POST',
		data,
	})
}
/**
 * 修改在线分析配置
 * @param data
 * @returns
 */
export const putOnLineAnalysisConfig = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/${data.id}/onLine-analysis-config`,
		method: 'PUT',
		data,
	})
}
/**
 * 删除在线分析配置
 * @param data
 * @returns
 */
export const deleteOnLineAnalysisConfig = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/${data.id}/delete/onLine-analysis-config`,
		method: 'DELETE',
		data,
	})
}

/**
 * 查看业务表是否存在业务表数据分析详细
 * @param data
 * @returns
 */
export const isExistLedgerDataAnalysis = (ledgerId: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/${ledgerId}/isExist-ledgerDataAnalysis`,
		method: 'GET',
		// params,
	})
}
/**
 * 获取当前账号下面的所有业务表
 * @param data
 * @returns
 */
export const myOnlineLedgers = () => {
	return defHttp.request({
		url: `/api/ledger/ledger-user/my-online-ledgers`,
		method: 'GET',
		// params,
	})
}
/**
 * 获取当前账号下面的业务表中数字类型的字段
 * @param data
 * @returns
 */
 export const myFieldsLedgers = (id:string,type:number) => {
	return defHttp.request({
		url: `/api/ledger-service/plan-progress-config/ledger-table-fields?id=${id}&computeMode=${type}`,
		method: 'GET',
	})
}
/**
 * 获取当前账号下面的预警数据
 * @param data
 * @returns
 */
 export const myWarningLedgers = (id:string) => {
	return defHttp.request({
		url: `/api/new-feature/warning-mechanism/${id}/get-warning-ledger-data`,
		method: 'GET',
	})
}
/**
 * 获取台账数据历史记录(目前用于验收功能-预管机制中查询台账修改的历史值记录)
 * @param data
 * @returns
 */
 export const myHistoryLedgers = (ledgerId:string) => {
	return defHttp.request({
		url: `/api/ledger-service/ledgerAuditedData/${ledgerId}/get-ledger-history`,
		method: 'GET',
	})
}

/**
 * 检查当前是否可以无数据提交审核
 * @param data
 * @returns
 */
export const CanNoDataAudit = (ledgerId: string) => {
	return defHttp.request({
		url: `/api/ledger/ledger-audit-batch/${ledgerId}/is-can-submit-audited-no-data-update`,
		method: 'GET',
	})
}

export const PushNoDataAudit = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledger-audit-batch/submit-audited-no-data-update`,
		method: 'POST',
		data,
	})
}
export const getStatisticsData = (url: string, params?: any) => {
	return defHttp.request({
		url,
		method: 'GET',
		params,
	})
}

export const deleteRelations = (params: any) => {
	return defHttp.request({
		url: '/api/ledger/ledger-user/delete-relations-send-message',
		method: 'POST',
		params,
	})
}

export const GetLedgerIdByFlowTaskId = (flowTaskId: string) => {
	return defHttp.request({
		url: `/api/ledger/telecom-task/${flowTaskId}/bind-ledger-id`,
		method: 'GET',
	})
}

export const GetRunway = (params: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-runway-manages`,
		method: 'GET',

		params,
	})
}

// 获取子跑道列表
export const getRunwayList = (params: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-runway-manages/get-runway-List`,
		method: 'GET',
		headers: {Urlkey},
		params,
	})
}
/**
 * 数据集
 */

export const getDataSetList = () => {
	const params = {
		skipCount: 0,
		maxResultCount: 1000,
	}
	return defHttp.request({
		url: '/api/ledger-service/table-data-set',
		method: 'GET',
		params,
	})
}
/**
 * 获取数据集详情
 * @param id
 * @returns
 */
export const getDataSetInfoById = (id: string) => {
	return defHttp.request({
		url: `/api/ledger-service/table-data-set/${id}`,
		method: 'GET',
	})
}

export const ratingConsolidation = (id: string) => {
	return defHttp.request({
		url: `/api/ledger/five-star-rating/${id}/consolidation-rate`,
		method: 'GET',
	})
}

export const ratingAccuracy = (id: string) => {
	return defHttp.request({
		url: `/api/ledger/five-star-rating/${id}/data-accuracy-rate`,
		method: 'GET',
	})
}

export const ratingUpdateMaintain = (id: string) => {
	return defHttp.request({
		url: `/api/ledger/five-star-rating/${id}/maintain-update-rate`,
		method: 'GET',
	})
}

export const ratingUpdateContribution = (id: string) => {
	return defHttp.request({
		url: `/api/ledger/five-star-rating/${id}/contribution-rate`,
		method: 'GET',
	})
}

export const ratingDeductpoints = (id: string, params: any) => {
	return defHttp.request({
		url: `/api/ledger/five-star-rating/${id}/deductpoints`,
		method: 'GET',

		params,
	})
}

export const deratingDeductpoints = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/five-star-rating/deductpoints`,
		method: 'DELETE',

		params,
	})
}

export const postRatingDeductpoints = (data: any) => {
	return defHttp.request({
		url: '/api/ledger/five-star-rating/deductpoints',
		method: 'POST',
		data,
		headers: {Urlkey, 'Content-Type': 'multipart/form-data'},
	})
}

export const postRatingOnevotereject = (data: any) => {
	return defHttp.request({
		url: '/api/ledger/five-star-rating/onevotereject',
		method: 'POST',
		data,
		headers: {Urlkey, 'Content-Type': 'multipart/form-data'},
	})
}

export const ratingOnevotereject = (id: string) => {
	return defHttp.request({
		url: `/api/ledger/five-star-rating/${id}/onevotereject`,
		method: 'GET',
	})
}

export const ratinghistoryevaluate = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/five-star-rating/historyevaluate`,
		method: 'GET',

		params,
	})
}

export const ratinghistoryevaluateList = (historyEvaluateId: any, params: any) => {
	return defHttp.request({
		url: `/api/ledger/five-star-rating/${historyEvaluateId}/historyevaluate`,
		method: 'GET',

		params,
	})
}
export const postRatingDownload = (data: any) => {
	return defHttp.request({
		url: '/api/ledger/five-star-rating/download',
		method: 'POST',
		data,
	})
}

export const ratinghistoryevaluateHistoryList = (starEvaluateRecordId: any, params: any) => {
	return defHttp.request({
		url: `/api/ledger/five-star-rating/${starEvaluateRecordId}/deductpoints-record`,
		method: 'GET',

		params,
	})
}

export const ratinghistoryevaluateHistoryonevoterejectList = (starEvaluateRecordId: any) => {
	return defHttp.request({
		url: `/api/ledger/five-star-rating/${starEvaluateRecordId}/onevotereject-record`,
		method: 'GET',
	})
}

export const saveEvaluate = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/five-star-rating/save-star-evaluate`,
		method: 'POST',

		data,
	})
}

export const getRunwayNmuber = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/ledger-user/ledger-count-by-runway`,
		method: 'GET',

		params,
	})
}

export const getRunwayStatue = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/set-fill-statue`,
		method: 'PUT',

		data,
	})
}

export const getRunwayStatueList = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/ledger-user/my-ledgers-by-fill-statue`,
		method: 'GET',

		params,
	})
}

/**
 * 获取提交审核流程进度
 * @param businessType
 * @param schemeCode
 * @returns
 */
export const GetFlowAuditors = (
	schemeCode: string,
	params: any,
	businessType: string = 'LedgerFilling'
) => {
	return defHttp.request({
		url: `/api/workflow/workflowSchemeInfo/${businessType}/${schemeCode}/first-batch/auditors`,
		method: 'GET',

		params,
	})
}

export const GetProcessList = (id: string) => {
	return defHttp.request({
		url: `/api/workflow/workflowProcess/${id}/greed-node-auditors/turn`,
		method: 'GET',
	})
}
export const GetProcessListBysinessId = (id: string) => {
	return defHttp.request({
		url: `/api/workflow/workflowProcess/by-businessId/${id}/greed-node-auditors/turn`,
		method: 'GET',
	})
}

// 获取未发起流程之前的流程列表
export const GetUnStartProcessList = (id: string, params: any) => {
	return defHttp.request({
		url: '/api/workflow/workflowSchemeInfo/greed-node-auditors',
		method: 'GET',
		params: {SchemeCode: id, ...params},
	})
}
/**
 * 获取绑定的业务表
 * @param params
 * @returns
 */
export const GetMyBindLedgers = (arr: Array<any>) => {
	const params = new URLSearchParams()
	arr.forEach((value) => params.append('ledgerIds', value))
	return defHttp.request({
		url: `/api/ledger/ledger-user/my-bind-ledgers?${params.toString()}`,
		method: 'GET',
	})
}

// dzj 2024-09-24 修改内容--五星评价

class Attachment {
	'objectId': string
	'name': string
	'path': string
	'extension': string
	'size': 0
}
/**
 * 修改编辑业务表整合率明细
 * @param id string
 * @param data
 * @returns
 */
export const updateConsolidationRate = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/ledger/five-star-rating/${id}/consolidation-rate`,
		method: 'PUT',
		data,
		headers: {Urlkey, 'Content-Type': 'multipart/form-data'},
	})
}

/**
 * 修改编辑数据准确率明细
 * @param id
 * @param data
 * @returns
 */
export const updateDataRate = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/ledger/five-star-rating/${id}/data-accuracy-rate`,
		method: 'PUT',
		data,
		headers: {Urlkey, 'Content-Type': 'multipart/form-data'},
	})
}

/**
 * 	修改编辑更新维护率
 * @param id
 * @param data
 * @returns
 */
export const editUpdateRate = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/ledger/five-star-rating/${id}/maintain-update-rate`,
		method: 'PUT',
		data,
		headers: {Urlkey, 'Content-Type': 'multipart/form-data'},
	})
}

/**
 * 编辑迭代升级率
 * @param id
 * @param data
 * @returns
 */
export const updateContrubutionRate = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/ledger/five-star-rating/${id}/contribution-rate`,
		method: 'PUT',
		data,
		headers: {Urlkey, 'Content-Type': 'multipart/form-data'},
	})
}

/**
 * 发布五星评价
 * @param ids
 */
export const publishFiveStar = (ids: string[]) => {
	return defHttp.request({
		url: '/api/ledger/five-star-rating/publish-star-evaluate',
		method: 'PUT',
		data: ids,
	})
}

/**
 * 取消发布五星评价
 * @param ids
 * @returns
 */
export const cancelPublishFiveStar = (ids: string[]) => {
	return defHttp.request({
		url: '/api/ledger/five-star-rating/unPublish-star-evaluate',
		method: 'PUT',
		data: ids,
	})
}

export const cancelOneVote = (id: string) => {
	return defHttp.request({
		url: '/api/ledger/five-star-rating/onevotereject?starEvaluateId=' + id,
		method: 'DELETE',
	})
}
/**
 * 获取当前用户下面的所有区县、街道、社区
 * @param params
 * @returns
 */
export const regionList = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/region-list`,
		method: 'GET',

		params,
	})
}
/**
 * 创建台账数据分析结果智能图表
 * @param data
 * @returns
 */
export const addCharts = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/${data.ledgerDataAnalysisId}/dataAnalysis-chart`,
		method: 'POST',
		data,
	})
}

/**
 * 查看台账数据分析结果图表数据
 * @param data
 * @returns
 */
export const getCharts = (ledgerDataAnalysisId: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/${ledgerDataAnalysisId}/dataAnalysis-chart`,
		method: 'GET',
		// params,
	})
}

/**
 * 修改台账数据分析结果智能图表
 * @param data
 * @returns
 */
export const putCharts = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/${data.id}/dataAnalysis-chart`,
		method: 'PUT',
		data,
	})
}

/**
 * 删除台账数据分析结果智能图表
 * @param data
 * @returns
 */
export const deleteCharts = (id: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDataAnalysis/${id}/dataAnalysis-chart`,
		method: 'DELETE',
		// data,
	})
}

// 绑定台账 taskId  params: ledgerId
export const BindLedgerByTaskId = (taskId: string, params: any) => {
	return defHttp.request({
		url: `/api/ledger/telecom-task/${taskId}/ledger`,
		method: 'PUT',
		params,
	})
}
// 绑定删除任务绑定台账和台账配置台账 taskId
export const DeleteBindLedgerByTaskId = (taskId: string) => {
	return defHttp.request({
		url: `/api/ledger/telecom-task/${taskId}/delete-config`,
		method: 'GET',
	})
}

// 获取本级区域授权列表
export const getRegionAuthList = (regionId: string) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/${regionId}/departments`,
		method: 'GET',
	})
}

// 获取下级区域部门授权列表
export const getNextRegionAuthList = (regionId: string) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/${regionId}/child/departments`,
		method: 'GET',
	})
}

export const getindicatorList = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/indicator-cockpit`,
		method: 'GET',

		params,
	})
}

export const getindicatorCockpitList = (ant?: any) => {
	return defHttp.request({
		url: `/api/ledger/indicator-cockpit/runway/${ant}`,
		method: 'GET',
	})
}

export const getindicatorCockpitComputing = (indicatorCockpitId?: any) => {
	return defHttp.request({
		url: `/api/ledger-service/indicator-cockpit-config/${indicatorCockpitId}/get-indicator-cockpit-computing`,
		method: 'GET',
	})
}

export const addIndicatorConfig = (data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/indicator-cockpit-config`,
		method: 'POST',
		data,
	})
}

export const getindicatorData = (id: any) => {
	return defHttp.request({
		url: `/api/ledger/indicator-cockpit/data/${id}`,
		method: 'GET',
	})
}

export const getFillingNumApi = (params: any) => {
	return defHttp.request({
		url: `/api/filling/report-table/get-reportTableRowNumber`,
		method: 'GET',
		params,
	})
}

export const PutIndicatorConfig = (params: any, data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/indicator-cockpit-config`,
		method: 'PUT',
		data,

		params,
	})
}

export const getinDicatorCockpitData = (id: any) => {
	return defHttp.request({
		url: `/api/ledger-service/indicator-cockpit-config/${id}`,
		method: 'GET',
	})
}

/**
 * 新增-数据关联同步配置
 * @param data
 * @returns
 */
export const associatedSyn = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/associatedSyn`,
		method: 'POST',
		data,
	})
}
/**
 * 修改-数据关联同步配置
 * @param data
 * @returns
 */
export const putAssociatedSyn = (id: any, sourceLedgerId: any, data: any) => {
	return defHttp.request({
		url: `/api/ledger/associatedSyn/${id}/${sourceLedgerId}`,
		method: 'put',
		data,
	})
}
// 获取数据关联同步配置
export const getAssociatedSyn = (regionId: string) => {
	return defHttp.request({
		url: `/api/ledger/associatedSyn/${regionId}`,
		method: 'GET',
	})
}

export const getcockpitpush = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/indicator-cockpit/push`,
		method: 'GET',
		params,
	})
}
/**
 * 台账数据列表-附带数据关联导入状态
 * @param id STRING
 * @returns
 */
export const incidentalStateList = (data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/incidental-state-list`,
		method: 'POST',
		data,
	})
}
/**
 * 台账数据列表-附带数据关联导入状态
 * @param id STRING
 * @returns
 */
// export const associatedSynImport = (data: any) => {
// 	return defHttp.request({
// 		url: `/api/ledger-service/dynamic-table/associated-syn-import`,
// 		method: 'POST',
// 		data,
// 		headers: { Urlkey },
// 	})
// }

/**
 * 台账数据列表-附带数据关联导入状态 新的
 * @param id STRING
 * @returns
 */
export const associatedSynImport = (data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/dynamic-table/associated-syn-datas-import`,
		method: 'POST',
		data,
	})
}

/**
 * 获取业务表提交审核数量
 * @param ledgerId
 * @returns
 */
export const GetLedgerSubmitAuditCount = (ledgerId: string) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${ledgerId}/my-pending-audit-data-count`,
		method: 'GET',
	})
}

/**
 * 获取报表头
 * @param ids
 * @returns
 */
export const GetReportHeaderByIds = (ids: string[]) => {
	return defHttp.request({
		url: `/api/filling/plan-task/table-templates-by-ids`,
		method: 'POST',
		data: ids,
	})
}
// /api/ledger/ledgerDepartment/auth-department-ledgerId
/**
 * 获取台账不可操作的字段
 */
export const getAuthFields = (id: string) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/auth-department-ledgerId?ledgerId=${id}`,
		method: 'GET',
	})
}

export const getNextAuthFields = (ledgerId: string, departmentId: string) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/${ledgerId}/${departmentId}/department-permission`,
		method: 'GET',
	})
}

/**
 * 判断新的周期是否已经生成
 */
export const checkTaskItemExists = (params?: any) => {
	return defHttp.request({
		url: `/api/platform/wf-plan-tasks/check-task-item-exists`,
		method: 'GET',
		params,
	})
}

/**
 * 	批量发送消息通知
 * @param data
 * @returns
 */
export const batchSendRemind = (data: any) => {
	return defHttp.request({
		url: '/api/ledger/ledgerDepartment/batch-send-remind',
		method: 'POST',
		data,
	})
}

/**
 * 获取可调整周期的列表
 */
export const adjustCycleList = (params: any) => {
	return defHttp.request({
		url: `/api/platform/wf-task-items`,
		method: 'GET',
		params,
	})
}

/**
 * 调整
 * @param id
 * @param data
 * @returns
 */
export const adjustCycleItem = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/platform/wf-task-items/${id}/deadline`,
		method: 'PUT',
		data,
	})
}
// 终止
export const terminateCycle = (id: string) => {
	return defHttp.request({
		url: `/api/platform/wf-task-items/${id}/terminate`,
		method: 'PUT',
	})
}
/**
 * 异步导出我的业务表数据
 */
export const exportMyLedgersLittleAsync = () => {
	return defHttp.request({
		url: `/api/ledger/ledger-user/export-my-ledgers-little-async`,
		method: 'POST',
		data: {},
	})
}

/**
 * 获取表格数据导出记录列表
 */
export const myLedgerExportRecord = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/myLedgerExportRecord`,
		method: 'GET',
		params,
	})
}
/**
 * 下载导出文件
 */
export const myLedgerExportRecordFile = (id: any, config?: any) => {
	return defHttp.request({
		url: `/api/ledger/myLedgerExportRecord/download/${id}/file`,
		method: 'GET',
		...config,
		// params,
	})
}

/**
 * 获取业务表管理更改记录
 */
export const ledgerhangeRecord = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerChangeRecord`,
		method: 'GET',
		params,
	})
}

/**
 * 复用台账
 */
export const multiplexAuthorized = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/multiplex-authorized`,
		method: 'POST',
		data,
	})
}

/**
 *  根据部门名称获取部门信息
 * @param departmentName
 * @returns
 */
export const getDepartmentByName = (departmentName: string) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/departments-by-name/${departmentName}`,
		method: 'GET',
	})
}

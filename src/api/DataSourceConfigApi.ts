import { DataSourceConfigItem, DATA_SOURCE_CONFIG_MAP } from '@/define/dataSourceConfig'

const STORAGE_KEY = 'data_source_configs'

// 生成默认数据
const generateDefaultData = (): DataSourceConfigItem[] => {
  const defaultConfigs: DataSourceConfigItem[] = []
  let index = 1
  
  Object.entries(DATA_SOURCE_CONFIG_MAP).forEach(([key, config]) => {
    // 让部分项目显示英文，其他显示中文
    const language = index % 3 === 0 ? '英文' : '中文'

    defaultConfigs.push({
      id: key,
      configKey: key,  // 添加配置键字段
      nameZh: config.nameZh,
      nameEn: config.nameEn,
      category: config.category,
      language: language,
      creator: '系统管理员',
      createTime: Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000 // 随机30天内的时间
    })
    index++
  })
  
  return defaultConfigs
}

// 初始化本地存储数据
const initializeStorage = () => {
  const existingData = localStorage.getItem(STORAGE_KEY)
  if (!existingData) {
    const defaultData = generateDefaultData()
    localStorage.setItem(STORAGE_KEY, JSON.stringify(defaultData))
    return defaultData
  }
  return JSON.parse(existingData)
}

// 获取所有配置项
export const getDataSourceConfigs = (params?: {
  name?: string
  skipCount?: number
  maxResultCount?: number
}): Promise<{ items: DataSourceConfigItem[]; totalCount: number }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let data = initializeStorage()
      
      // 搜索过滤
      if (params?.name) {
        const searchTerm = params.name.toLowerCase()
        data = data.filter((item: DataSourceConfigItem) => 
          item.nameZh.toLowerCase().includes(searchTerm) ||
          item.nameEn.toLowerCase().includes(searchTerm)
        )
      }
      
      const totalCount = data.length
      
      // 分页
      const skipCount = params?.skipCount || 0
      const maxResultCount = params?.maxResultCount || 10
      const items = data.slice(skipCount, skipCount + maxResultCount)
      
      resolve({ items, totalCount })
    }, 300) // 模拟网络延迟
  })
}

// 创建配置项
export const createDataSourceConfig = (data: Omit<DataSourceConfigItem, 'id' | 'createTime'> & { configKey: string }): Promise<DataSourceConfigItem> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const configs = initializeStorage()
      const newConfig: DataSourceConfigItem = {
        ...data,
        id: `custom_${Date.now()}`,
        createTime: Date.now()
      }

      configs.push(newConfig)
      localStorage.setItem(STORAGE_KEY, JSON.stringify(configs))

      resolve(newConfig)
    }, 200)
  })
}

// 更新配置项
export const updateDataSourceConfig = (id: string, data: Partial<DataSourceConfigItem>): Promise<DataSourceConfigItem> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const configs = initializeStorage()
      const index = configs.findIndex((item: DataSourceConfigItem) => item.id === id)
      
      if (index === -1) {
        reject(new Error('配置项不存在'))
        return
      }
      
      configs[index] = { ...configs[index], ...data }
      localStorage.setItem(STORAGE_KEY, JSON.stringify(configs))
      
      resolve(configs[index])
    }, 200)
  })
}

// 删除配置项
export const deleteDataSourceConfig = (id: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const configs = initializeStorage()
      const index = configs.findIndex((item: DataSourceConfigItem) => item.id === id)
      
      if (index === -1) {
        reject(new Error('配置项不存在'))
        return
      }
      
      configs.splice(index, 1)
      localStorage.setItem(STORAGE_KEY, JSON.stringify(configs))
      
      resolve()
    }, 200)
  })
}

// 获取单个配置项
export const getDataSourceConfigById = (id: string): Promise<DataSourceConfigItem> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const configs = initializeStorage()
      const config = configs.find((item: DataSourceConfigItem) => item.id === id)
      
      if (!config) {
        reject(new Error('配置项不存在'))
        return
      }
      
      resolve(config)
    }, 100)
  })
}

// 预览配置项（模拟加载过程）
export const previewDataSourceConfig = (id: string): Promise<{ content: string; language: string }> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const configs = initializeStorage()
      const config = configs.find((item: DataSourceConfigItem) => item.id === id)
      
      if (!config) {
        reject(new Error('配置项不存在'))
        return
      }
      
      resolve({
        content: `预览内容：${config.nameZh} / ${config.nameEn}`,
        language: config.language
      })
    }, 1500) // 模拟较长的加载时间
  })
}

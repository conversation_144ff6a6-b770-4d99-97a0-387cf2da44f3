import axios, {AxiosRequestConfig} from 'axios'
import {APIConfig} from './config'
import {useViewStore} from '$/useViewStore'
import {useUserStore} from '$/useUserStore'
import {Request} from './interface'
import {ElMessage} from 'element-plus'
import qs from 'qs'

declare global {
	interface Window {
		errMsg: (err: any, str: string, customMsg?: string) => void
	}
}

// 通过 .env配置全局接口, 也可以自行配置公共接口文件, 但要保证baseURL的配置, 因为组件内部会使用 axios
axios.defaults.baseURL = APIConfig('base')
axios.defaults.headers['Content-Type'] = 'application/json'
axios.defaults.timeout = Number(import.meta.env.VITE_TIMEOUT) * 1000 // 通过 .env 超时
axios.defaults.paramsSerializer = function (params) {
	return qs.stringify(params, {arrayFormat: 'repeat'})
}

// const nomarl = axios.create({
// 	baseURL: APIConfig('base'),
// 	headers: {'Content-Type': 'application/json'},
// 	timeout: Number(process.env.VITE_TIMEOUT) * 1000,
// 	paramsSerializer: function (params) {
// 		return qs.stringify(params, {arrayFormat: 'repeat'})
// 	},
// })

let timer: any = null
let msgTimer: any = null

const notLoadingApi: any = ['/api/platform/my-notifilers/notification-Unread-count']

// 当前请求计数
const requestCount = {
	count: 0,
	finish: 0,
	store: null as any,
	complete() {
		clearTimeout(timer)
		this.finish++
		console.log('请求完成', this.count, this.finish)
		requestCount.store?.setLoadCount({count: this.count, finish: this.finish})
		timer = setTimeout(() => {
			if (this.finish === this.count && this.count !== 0) {
				requestCount.store?.setLoadFinish(true)
				if (this.store) {
					this.store.visibleLoading = false
					this.store.aborts = null
					this.store = null
				}
				this.finish = 0
				this.count = 0
				console.log('所有请求完成', this.finish, this.count)
			}
		}, Number(process.env.VITE_TIMEOUT) * 1000)
	},
}

// 添加请求拦截器
axios.interceptors.request.use(
	(config) => {
		if (!window.GOV_CONFIG.ALLOWED_DOMAINS.includes(location.hostname)) {
			throw new Error('请求的域名不在白名单中')
		}

		config.baseURL = APIConfig(config.headers.Urlkey || 'base')
		// 在发送请求之前做些什么
		const store = useViewStore()
		const userStore = useUserStore()

		if (!store.aborts) {
			store.aborts = new AbortController()
		}

		config.signal = store.aborts.signal
		if (
			store.getLoadCount.count === 0 &&
			store.enableLoading &&
			store.visibleLoading === false
		) {
			store.visibleLoading = true
		}

		const token = userStore.getToken

		if (token) {
			config.headers.Authorization = `Bearer ${token}`
		}

		if (!notLoadingApi.includes(config.url)) {
			useViewStore().setLoadCount('count')
		}

		return config
	},
	function (error) {
		// 对请求错误做些什么
		useViewStore().setLoadCount('count')
		return Promise.reject(error)
	}
)

// 添加响应拦截器
axios.interceptors.response.use(
	function (response) {
		// 2xx 范围内的状态码都会触发该函数。

		if (!notLoadingApi.includes(response.config.url)) {
			useViewStore().setLoadCount('finish')
		}
		const headers = response.headers
		if (headers['x-api-version']?.includes('beta')) {
			const store = useViewStore()
			if (store.getBuild) {
				window.location.href = '/build'
			}
		}
		return response
	},
	function (error) {
		// 超出 2xx 范围的状态码都会触发该函数。
		useViewStore().setLoadCount('finish')
		getStatusResponse(error.response?.status, error)
		return Promise.reject(error)
	}
)

// 登录
const login = axios.create({baseURL: APIConfig('login')})
login.interceptors.request.use(function (config) {
	config.baseURL = APIConfig('login')
	// config.headers['Content-Type'] = 'application/x-www-form-urlencoded'
	return config
})
login.interceptors.response.use(
	function (response) {
		return response
	},
	function (error) {
		getStatusResponse(error.response?.status, error)
		return Promise.reject(error)
	}
)
const getStatusResponse = (status: number, error: any) => {
	let message = ''
	console.log('Response Error:', error)

	switch (status) {
		case 403:
			if (error.response?.data.error.code === 'Forbidden') {
				window.location.href = '/forbidden'
			}
			message = error?.response?.data?.error.message
			break
		default:
	}

	if (message) {
		clearTimeout(msgTimer)
		msgTimer = setTimeout(() => {
			ElMessage.error({
				message: message,
				duration: 2000,
			})

			if (message === '无效的授权码') {
				useViewStore().clearAbort()
				useViewStore().clear()
				// window.location.href = '/login'
				window.location.href = '/401'
			}
		}, 200)
	}
}

export const request: Request = {
	login<T = any>(url: string, data?: object, config?: AxiosRequestConfig): Promise<T> {
		return login.post(url, data, config)
	},
	request: (config: any) => axios.request(config),
	get: (url) => axios.get(url),
	put: (url, options) => axios.put(url, options),
	post: (url, options, config?: any) => axios.post(url, options, config),
	delete: (url) => axios.delete(url),
	batch: async (url, size: number = 5000, params?: {}, headers?: {}, method: string = 'GET') => {
		const option = {SkipCount: 0, MaxResultCount: size}
		const reslut = []

		while (true) {
			const res = await axios.request({
				url,
				method,
				params: Object.assign({}, option, params),
				headers: Object.assign({}, headers),
			})
			const {items} = res.data
			if (items.length === option.MaxResultCount) {
				reslut.push(...items)
				option.SkipCount += option.MaxResultCount
			} else {
				reslut.push(...items)
				break
			}
		}
		return reslut
	},
	batchData: async (config: any, size: number = 5000) => {
		const option = {SkipCount: 0, MaxResultCount: size}
		const reslut = []

		while (true) {
			if (config.method === 'GET') {
				config.params = Object.assign({}, config.params, option)
			} else if (config.method === 'POST') {
				config.data = Object.assign({}, config.data, option)
			}

			const res = await axios.request(config)
			const {items} = res.data
			if (items.length === option.MaxResultCount) {
				reslut.push(...items)
				option.SkipCount += option.MaxResultCount
			} else {
				reslut.push(...items)
				break
			}
		}
		return reslut
	},
	uploadFile: (config: any, params: any) => {
		const fromData = new window.FormData()
		const customFile = params.name || 'file'
		if (params.filename) {
			fromData.append(customFile, params.filename, params.filename)
		} else {
			fromData.append(customFile, params.file)
		}
		if (params.data) {
			Object.keys(params.data).forEach((key) => {
				const value = params.data![key]
				if (Array.isArray(value)) {
					value.forEach((item) => {
						fromData.append(`${key}[]`, item)
					})
					return
				}
				fromData.append(key, params.data![key])
			})
		}
		return axios.request({
			...config,
			method: 'POST',
			data: fromData,
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		})
		// return nomarl.post(url, options, config)
	},
}

window.errMsg = (err, str, customMsg?: string) => {
	if (err.response?.status === 500 || err.response?.status === 404) {
		ElMessage.error(`${customMsg || str + '异常,我们已记录本次异常信息,正在全力修复中'}`)
	}
}

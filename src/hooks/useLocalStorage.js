export const ACTION_KEY = {
	//临时报表创建
	FieldTypeDefinition: 'Field_Type_Definition', // - 字段类型定义
	TempReportPermission: 'TEMPORARY_REPORT_PERMISSION', // - 权限配置
	BatchDeleteReport: 'BATCH_DELETE_REPORT', // - 批量删除 - 删除全部
	BatchStopReport: 'BATCH_STOP_REPORT', // - 批量终止
	BatchRevokeReport: 'BATCH_REVOKE_REPORT', // - 批量撤回 - 撤回全部
	BatchResetReport: 'BATCH_RESET_REPORT', // - 批量重置
	BatchFlowDesReport: 'BATCH_FLOW_DESC_REPORT', // - 批量流程说明
	ReminderNotificationReport: 'REMINDER_NOTIFICATION', // - 提醒通知
	ReportHintRange: 'REPORT_HINT_RANGE', // - 操作提示范围
	ReportPromptManagement: 'REPORT_PROMPT_MANAGEMENT', // - 操作提示管理

	UpdateCycleEnable: 'UPDATE_CYCLE_ENABLE', // - 业务表更新周期启用停用
	RiskStrategyData: 'RISK_STRATEGY_DATA', // - 任务终止风险策略
	NamingConflictResolutionLog: 'NAMING_CONFLICT_RESOLUTION_LOG', //命名冲突解决日志
	TaskPromptSetting: 'TASK_PROMPT_SETTING', //任务必填项提示设置
	TaskNameGenerate: 'TASK_NAME_GENERATE', //任务名称自动生成设置
	PromptSetting: 'PROMPT_SETTING', //新建必填项提示设置
	CommentManagement: 'COMMENT_MANAGEMENT', //表格评论管理
	ConfigurationParameter: 'CONFIGURATION_PARAMETER', //表格配置参数

	TaskSupportManagement: 'Task_Support_Management', //任务支撑管理
	MuenList: 'Muen_List', //
	TaskProgressPermissionConfiguration: 'Task_progress_permission_configuration', //任务进度权限配置
	TableComparison: 'Table_Comparison', //表格对比
	AnalysisReport2: 'Analysis_Report2', //表格分析
	RecommendWords: 'Recommend_Words', //推荐词
	DataPermissions: 'Data_Permissions', //数据权限
	OperatePermissions: 'Operate_Permissions', //操作权限
	YJModel: 'YJ_Model', //预警模型
	YJProblem: 'YJ_Problem', //预警问题
	QXCategory: 'QX_Category', //权限类别
	QXAllocationBackup: 'QX_Allocation_Backup', //权限分配备份管理
	QXOperationRecord: 'QX_Operation_Record', //权限操作纪录
	TemporaryAuthorization: 'Temporary_Authorization', //临时授权
	TableCopy: 'Table_Copy', //表格副本
	CommentChart: 'Comment_Chart', //评论图表
	CommentChartPK: 'Comment_Chart_PK', //评论图表对比
	WarningRules: 'Warning_Rules', //预警规则
	WarningIssues: 'Warning_Issues', //预警问题
	ExportRecords: 'Export_Records', //导出纪录
	TableTemplate: 'Table_Template', //表格模板


	GenerateCycle: 'GENERATE_CYCLE', //表格生成周期管理
	TableContent: 'TABLE_CONTENT', //表格内容
	StatisticalScope: 'STATISTICAL_SCOPE', //统计范围
	Authorize: 'AUTHORIZE', //委托授权管理
	AnalysisReportLabel:'ANALYSIS_REPORT_LABEL',	//赋权标签
	AllocationRecord:'ALLOCATION_RECORD',	//权限分配记录
	AllocationReport:'ALLOCATION_REPORT',	//分配报告
	MenuPermissions:'MENU_PERMISSIONS',	//功能菜单
	AllocationExplanation:'ALLOCATION_EXPLANATION',	//分配说明
	OperationVideo:'OPERATION_VIDEO',	//操作视频
	LifeCycle:'LIFE_CYCLE',	//权限使用周期
	PermissionVersion:'PERMISSION_VERSION',	//权限版本
	OperatingManual:'OPERATING_MANUAL',	//操作手册
	ReminderContent:'REMINDER_CONTENT',	//提醒内容
	ChangeLog:'CHANGE_LOG',	//变更日志
	ReceiveSettings:'RECEIVE_SETTINGS',	//数据管理岗
	PermissionRecord:'PERMISSION_RECORD',	//权限修改记录
	PermissionCancel:'PERMISSION_CANCEL',	//取消授权
	PermissionUsageReport:'PERMISSION_USAGE_REPORT',	//权限运用报告
	PermissionTemplate:'PERMISSION_TEMPLATE',	//权限分配模板

	/***********************目标任务进程计算组件 */
	HistoryRecords:'HISTORY_RECORDS',// 查看历史记录
}

export const useLocalStorage = () => {
	/**
	 * 保存
	 * @param {ACTION_KEY} key
	 * @param {*} data
	 */
	const save = (key, data) => {
		localStorage.setItem(key, JSON.stringify(data))
	}

	/**
	 * 获取
	 * @param {ACTION_KEY} key
	 * @returns
	 */
	const get = (key) => {
		return JSON.parse(localStorage.getItem(key))
	}

	/**
	 * 获取属性值
	 * @param {ACTION_KEY} key
	 * @param {string} attr // 针对缓存是JSON对象的属性获取处理
	 * @returns
	 */
	const getByAttr = (key, attr) => {
		const data = get(key)
		return data[attr]
	}

	/**
	 * 获取数组元素
	 * @param {*} key
	 * @param {*} index
	 * @returns
	 */
	const getByIndex = (key, index) => {
		const data = get(key)
		return data[index]
	}

	/**
	 * 更新
	 * @param {ACTION_KEY} key
	 * @param {string} attr // 针对缓存是JSON对象的属性更新处理
	 * @param {*} value
	 */
	const setByAttr = (key, attr, value) => {
		const data = get(key)
		data[attr] = value
		save(key, data)
	}

	/**
	 * 更新
	 * @param {ACTION_KEY} key
	 * @param {number} index // 针对缓存是数组的更新处理
	 * @param {*} value
	 */
	const setByIndex = (key, index, value) => {
		const data = get(key)
		data[index] = value
		save(key, data)
	}

	/**
	 * 删除
	 * @param {ACTION_KEY} key
	 */
	const remove = (key) => {
		localStorage.removeItem(key)
	}

	/**
	 * 删除属性
	 * @param {ACTION_KEY} key
	 * @param {string} attr // 针对缓存是JSON对象的属性删除处理
	 */
	const removeByAttr = (key, attr) => {
		const data = get(key)
		delete data[attr]
		save(key, data)
	}

	/**
	 * 删除数组元素
	 * @param {ACTION_KEY} key
	 * @param {number} index // 针对缓存是数组的删除处理
	 */
	const removeByIndex = (key, index) => {
		const data = get(key)
		data.splice(index, 1)
		save(key, data)
	}

	return {
		save,
		get,
		getByAttr,
		getByIndex,
		setByAttr,
		setByIndex,
		remove,
		removeByAttr,
		removeByIndex,
	}
}
export default useLocalStorage

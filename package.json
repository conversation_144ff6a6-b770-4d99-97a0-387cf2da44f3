{"name": "inspur", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "node --max_old_space_size=4096 node_modules/vite/bin/vite.js build", "preview": "vite preview"}, "dependencies": {"@microsoft/signalr": "8.0.7"}, "devDependencies": {"@commitlint/cli": "19.6.0", "@commitlint/config-conventional": "19.6.0", "@element-plus/icons-vue": "2.3.1", "@kangc/v-md-editor": "2.3.18", "@vitejs/plugin-vue": "5.2.0", "akvts": "^0.0.841", "axios": "1.7.7", "commitlint": "19.8.0", "crypto-js": "4.2.0", "dayjs": "1.11.0", "echarts": "5.5.1", "element-plus": "2.8.8", "event-source-polyfill": "1.0.31", "exceljs": "4.4.0", "file-saver": "2.0.5", "gdt-jsapi": "1.9.51", "highlight.js": "11.10.0", "html-docx-js-typescript": "0.1.5", "js-cookie": "3.0.5", "markdown-it": "14.1.0", "pinia": "2.2.6", "qs": "6.13.1", "querystring": "0.2.1", "rollup-plugin-visualizer": "5.14.0", "sass": "1.81.0", "terser": "5.36.0", "unplugin-auto-import": "19.3.0", "unplugin-vue-components": "28.7.0", "vconsole": "3.15.1", "vite": "5.4.11", "vite-plugin-compression": "0.5.1", "vite-plugin-pages": "0.33.0", "vite-plugin-vue-setup-extend": "0.4.0", "vue": "3.5.13", "vue-router": "4.4.5", "vuedraggable": "4.1.0", "jszip": "3.10.1"}, "overrides": {"rollup": "4.24.0"}}